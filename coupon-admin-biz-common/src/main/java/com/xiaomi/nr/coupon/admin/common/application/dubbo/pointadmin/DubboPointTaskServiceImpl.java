package com.xiaomi.nr.coupon.admin.common.application.dubbo.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task.*;
import com.xiaomi.nr.coupon.admin.api.service.pointadmin.DubboPointTaskService;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.coupon.admin.common.application.dubbo.pointadmin.convert.PointTaskConvert;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.FillPointService;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task.po.SearchTaskParam;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 灌积分服务
 * @date 2024-08-14 21:02
 */
@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
@Component
@Slf4j
public class DubboPointTaskServiceImpl implements DubboPointTaskService {

    @Autowired
    private PointTaskConvert pointTaskConvert;

    @Autowired
    private FillPointService fillPointService;

    @Override
    public Result<BasePageResponse<PointTaskListVO>> pointFillTaskList(PointFillTaskListRequest request) {
        BasePageResponse<PointTaskListVO> response;

        try {
            // 封装查询参数
            SearchTaskParam searchTaskParam = pointTaskConvert.transferToSearchParameter(request);
            response = new BasePageResponse<>(searchTaskParam.getPageNo(), searchTaskParam.getPageSize());

            // 具体查询逻辑
            List<PointTaskListVO> fillPointList = fillPointService.searchFillPoint(searchTaskParam, response);
            response.setList(fillPointList);
            log.info("DubboPointService.pointFillTaskList execute success, request={}", request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboPointTaskService.pointFillTaskList request={}, error: ", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<CreatePointTaskResponse> pointFillTaskCreate(CreateFillPointTaskRequest request) {
        if(Objects.isNull(request.getAreaId())){
            request.setAreaId(I18nUtil.getGlobalAreaId());
        }
        log.info("DubboPointTaskService.pointFillTaskCreate request, request:{}", request);
        try {
            long taskId = fillPointService.createFillPointTask(request);
            log.info("DubboPointTaskService.pointFillTaskCreate response, request:{}, taskId:{}", request, taskId);
            return Result.success(new CreatePointTaskResponse(taskId));
        } catch (Exception e) {
            log.error("DubboFillCouponService.pointFillTaskCreate request={}, error: ", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<PointFillTaskDetailResponse> taskDetail(PointFillTaskDetailRequest request) {
        Long taskId = request.getTaskId();
        if(ObjectUtils.isEmpty(taskId)) {
            Result.fail(GeneralCodes.ParamError, "积分批次ID为NULL");
        }
        PointFillTaskDetailResponse response = new PointFillTaskDetailResponse();
        try {
            response.setPointTaskVO(fillPointService.taskDetail(taskId));
            log.info("DubboPointService.taskDetail execute success, request={}", request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboPointService.taskDetail request={}, error: ", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<Void> taskRetry(ReStartPointTaskRequest request) {
        if (ObjectUtils.isEmpty(request.getTaskId()) || request.getTaskId() <= CommonConstant.ZERO_LONG) {
            Result.fail(GeneralCodes.ParamError, "灌积分任务ID不合法");
        }
        try {
            fillPointService.retryFillPointTask(request);
            log.info("DubboPointService.taskRetry execute success, request={}", request);
            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboPointService.taskRetry request={}, error: ", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<CheckPointBatchConfigAndUploadResponse> checkPointBatchConfigAndUpload(CheckPointBatchConfigAndUploadRequest request) {
        if (ObjectUtils.isEmpty(request.getPointBatchId()) || request.getPlanPointCount() <= CommonConstant.ZERO_LONG) {
            Result.fail(GeneralCodes.ParamError, "积分批次ID或计划发放积分不合法");
        }
        if (request.isUpload() && StringUtils.isBlank(request.getFdsAddress())){
            Result.fail(GeneralCodes.ParamError, "FDS文件地址为空");
        }

        try {

            CheckPointBatchConfigAndUploadResponse response = new CheckPointBatchConfigAndUploadResponse();

            // 校验积分批次信息和库存
            fillPointService.checkPointBatchConfig(request.getPointBatchId(), request.getPlanPointCount());

            // 上传文件
            if (request.isUpload()) {
                response.setHdfsAddress(fillPointService.uploadFdsToHdfs(request.getFdsAddress()));
            }

            log.info("DubboPointService.checkPointBatchConfig execute success, request={}", request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboPointService.checkPointBatchConfig request={}, error: ", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<DownloadPointFillDetailResponse> downloadPointFillDetail(DownloadPointFillDetailRequest request) {
        Long taskId = request.getTaskId();
        if (ObjectUtils.isEmpty(taskId) || taskId <= CommonConstant.ZERO_LONG) {
            Result.fail(GeneralCodes.ParamError, "任务ID不合法");
        }
        try {
            DownloadPointFillDetailResponse response = new DownloadPointFillDetailResponse();
            response.setDownloadUrl(fillPointService.downloadPointFillDetail(taskId));
            log.info("DubboPointService.downloadPointFillDetail execute success, request={}", request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboPointService.downloadPointFillDetail request={}, error: ", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<DownloadPointFillDataResponse> downloadPointFillData(DownloadPointFillDataRequest request) {
        if (ObjectUtils.isEmpty(request) || StringUtils.isBlank(request.getHdfsAddr())) {
            Result.fail(GeneralCodes.ParamError, "hdfs地址不能为空");
        }
        try {
            String downloadUrl = fillPointService.downloadPointFillData(request.getHdfsAddr());
            log.info("DubboPointService.downloadPointFillData execute success, request={}", request);
            return Result.success(new DownloadPointFillDataResponse(downloadUrl));
        } catch (Exception e) {
            log.error("DubboPointService.downloadPointFillData request={}, error: ", request, e);
            throw new RuntimeException(e);
        }
    }
}
