package com.xiaomi.nr.coupon.admin.common.application.dubbo;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.redpacket.UserRedpacketVO;
import com.xiaomi.nr.coupon.admin.api.dto.redpacket.request.UserRedpacketListRequest;
import com.xiaomi.nr.coupon.admin.api.service.DubboUserRedpacketService;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.UserRedPacketRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.userredpacket.po.SeachUserRedpacketListResult;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.userredpacket.po.SearchUserRedpacketListParam;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@Service(timeout = 2000, group = "${dubbo.group}", version = "1.0")
@ApiModule(value = "红包服务", apiInterface = DubboUserRedpacketService.class)
public class DubboUserRedpacketServiceImpl implements DubboUserRedpacketService {

    @Autowired
    private UserRedPacketRepository userRedPacketRepository;

    @ApiDoc("查询用户红包列表")
    @Override
    public Result<BasePageResponse<UserRedpacketVO>> userRedpacketList(UserRedpacketListRequest request) {
        try {
            if (request.getUserId() < 1  && request.getRedpacketId() < 1){
                throw ExceptionHelper.create(GeneralCodes.ParamError, "用户id、用户红包id不能都为空");
            }
            SearchUserRedpacketListParam param =new SearchUserRedpacketListParam();
            param.setUserId(request.getUserId());
            param.setTypeId(request.getTypeId());
            param.setRedpacketId(request.getRedpacketId());
            param.setStatus(request.getStatus());
            param.setOrderDirection(request.getOrderDirection());
            param.setOffset((request.getPageNo()-1)*request.getPageSize());
            param.setLimit(request.getPageSize());
            SeachUserRedpacketListResult result = userRedPacketRepository.selectList(param);

            BasePageResponse response = new BasePageResponse();
            if(CollectionUtils.isNotEmpty(result.getUserRedpacketPOList())){
                List<UserRedpacketVO> userRedpacketVOS = result.getUserRedpacketPOList().stream().map(x->{
                    UserRedpacketVO userRedpacketVO = new UserRedpacketVO();
                    BeanMapper.copy(x,userRedpacketVO);
                    return userRedpacketVO;
                }).collect(Collectors.toList());
                response.setList(userRedpacketVOS);
            }

            response.setPageNo(request.getPageNo());
            response.setPageSize(request.getPageSize());
            response.setTotalPage(result.getTotalPage());
            response.setTotalCount(result.getTotalCount());
            return Result.success(response);
        } catch (Exception e) {
            log.info("DubboUserRedpacketService userRedpacketList Exception request:{}", request, e);
            return Result.fromException(e);
        }
    }
}
