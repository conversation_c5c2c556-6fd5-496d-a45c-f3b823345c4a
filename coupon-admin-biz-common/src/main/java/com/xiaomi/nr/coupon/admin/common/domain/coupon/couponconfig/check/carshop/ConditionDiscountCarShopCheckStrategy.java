package com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.check.carshop;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.check.PromotionValueValidator;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.AbstractCheckStrategy;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.CouponConfigCommonValidator;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.CouponConfigUpdateCommonValidator;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.promotiontype.ConditionDiscountCreateValidator;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import org.springframework.stereotype.Component;

/**
 * @author: zhangliwei6
 * @date: 2025/5/29 11:17
 * @description:
 */
@Component
public class ConditionDiscountCarShopCheckStrategy extends AbstractCheckStrategy {

    @Override
    public PromotionTypeEnum getPromotionType() {
        return PromotionTypeEnum.ConditionDiscount;
    }

    @Override
    public BizPlatformEnum getBizPlatformEnum() {
        return BizPlatformEnum.CAR_SHOP;
    }

    @Override
    public void initCheckList() {
        // common check
        CREATE_CHECK_LIST.addValidator(CouponConfigCommonValidator.class)
                // bizPlatform common check
                .addValidator(CarShopCommonValidator.class)
                // couponType check
                .addValidator(validatorFactory.getByCouponType(CouponTypeEnum.GOODS))
                // create common check
                .addValidator(ConditionDiscountCreateValidator.class)
                // create special check
                .addValidator(PromotionValueValidator.class)
                // goods check
                .addValidators(validatorFactory.getCouponScopes())
        ;

        // common check
        UPDATE_CHECK_LIST.addValidator(CouponConfigCommonValidator.class)
                // bizPlatform common check
                .addValidator(CarShopCommonValidator.class)
                // couponType check
                .addValidator(validatorFactory.getByCouponType(CouponTypeEnum.GOODS))
                // update base check
                .addValidator(CouponConfigUpdateCommonValidator.class)
                // update special check
                .addValidator(PromotionValueValidator.class)
                // goods check
                .addValidators(validatorFactory.getCouponScopes())
        ;
    }
}
