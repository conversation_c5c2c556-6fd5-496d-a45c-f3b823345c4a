package com.xiaomi.nr.coupon.admin.common.application.dubbo.coupon.convert;


import com.xiaomi.com.i18n.area.Area;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.ReviewGroupDTO;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.ReviewStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseTimeTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponConfigReviewPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponReviewRelPO;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class CouponReviewConvert {

    public static CouponConfigReviewPO convertReviewPO(CouponConfigVO couponConfigVO, List<ApplyAttachmentVO> applyAttachment, String creator, Integer bizPlatform) throws Exception {
        CouponConfigReviewPO po = new CouponConfigReviewPO();
        po.setConfigId(couponConfigVO.getId());
        //前端发版后可直接赋值
        po.setCouponType(couponConfigVO.getCouponType());
        po.setCouponName(couponConfigVO.getName());
        po.setBizPlatform(bizPlatform);
        PromotionRuleVO promotionRuleVO = couponConfigVO.getPromotionRuleVO();
        po.setPromotionType(promotionRuleVO.getPromotionType());
        po.setPromotionValue(promotionRuleVO.getPromotionValue());
        po.setBottomPrice(promotionRuleVO.getBottomPrice());
        po.setBottomCount(promotionRuleVO.getBottomCount());
        po.setBottomType(promotionRuleVO.getBottomType());
        DistributionRuleVO distributionRuleVO = couponConfigVO.getDistributionRuleVO();
        po.setApplyCount(distributionRuleVO.getApplyCount());
        UseTermVO useTermVO = couponConfigVO.getUseTermVO();
        if (UseTimeTypeEnum.RELATIVE.getValue() == useTermVO.getUseTimeType()) {
            useTermVO.setStartUseTime(couponConfigVO.getStartFetchTime());
            useTermVO.setEndUseTime(DateUtils.addHours(couponConfigVO.getEndFetchTime(), useTermVO.getUseDuration()));
        }
        po.setStartUseTime(useTermVO.getStartUseTime().getTime() / 1000);
        po.setEndUseTime(useTermVO.getEndUseTime().getTime() / 1000);
        if (UseTimeTypeEnum.CUSTOM.getValue() == useTermVO.getUseTimeType()) {
            po.setStartUseTime(0L);
            po.setEndUseTime(0L);
        } else {
            po.setStartUseTime(useTermVO.getStartUseTime().getTime() / 1000);
            po.setEndUseTime(useTermVO.getEndUseTime().getTime() / 1000);
        }
        po.setStatus(ReviewStatusEnum.UnderReview.getValue());
        po.setDepartmentId(couponConfigVO.getDepartmentId());
        po.setConfigCompress(CompressUtil.compress(GsonUtil.toJson(couponConfigVO)));
        if (CollectionUtils.isNotEmpty(applyAttachment)) {
            po.setApplyAttachment(GsonUtil.toJson(applyAttachment));
        } else {
            po.setApplyAttachment("");
        }
        po.setCreator(creator);
        po.setAreaId(couponConfigVO.getAreaId());
        po.setWorkflowId(couponConfigVO.getWorkflowId());
        return po;
    }

    public static CouponReviewListVO convertToReviewListVO(CouponConfigReviewPO configReviewPO) {
        CouponReviewListVO vo = new CouponReviewListVO();
        try {
            CouponConfigVO couponConfigVO = GsonUtil.fromJson(CompressUtil.decompress(configReviewPO.getConfigCompress()), CouponConfigVO.class);
            vo.setCost(couponConfigVO.getCost());
            Area area = Area.of(I18nUtil.getGlobalAreaId());
            if (Objects.nonNull(couponConfigVO.getCost()) && area != null) {
                vo.setCostStr(area.moneyFormat(couponConfigVO.getCost()));
                vo.setCurrency(String.format(area.getCurrency(), ""));
            }
            vo.setStartFetchTime(couponConfigVO.getStartFetchTime());
            vo.setEndFetchTime(couponConfigVO.getEndFetchTime());
            if (vo.getStartFetchTime() != null) {
                vo.setStartFetchTimeStamp(vo.getStartFetchTime().getTime() / 1000);
            }
            if (vo.getEndFetchTime() != null) {
                vo.setEndFetchTimeStamp(vo.getEndFetchTime().getTime() / 1000);
            }
            vo.setUseChannel(convertUseChannel(couponConfigVO.getUseChannel()));
        } catch (Exception e) {
            log.error("CouponReviewConvert convertToReviewListVO decompress error request:{}", configReviewPO, e);
        }
        BeanMapper.copy(configReviewPO, vo);
        if (vo.getCreateTime() != null) {
            vo.setCreateTimeStamp(vo.getCreateTime().getTime() / 1000);
        }
        vo.setReviewId(configReviewPO.getId());
        return vo;
    }

    /**
     * 使用渠道文案映射
     *
     * @param  useChannel
     * @return 返回文案信息
     */
    public static String convertUseChannel(Map<Integer, UseChannelVO> useChannel) {
        List<String> useChannelDesc = new LinkedList<>();
        for (Integer channel : useChannel.keySet()) {
            useChannelDesc.add(UseChannelsEnum.getNameByValue(channel));
        }
        return StringUtils.join(useChannelDesc, ",");
    }

    public static ReviewGroupDTO convertToReviewRelDTO(CouponReviewRelPO configReviewPO) {
        ReviewGroupDTO reviewGroupDTO = new ReviewGroupDTO();
        BeanMapper.copy(configReviewPO, reviewGroupDTO);
        return reviewGroupDTO;
    }

}
