package com.xiaomi.nr.coupon.admin.common.application.dubbo.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.UserPointCancelRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.UserPointsListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.UsersPointDto;
import com.xiaomi.nr.coupon.admin.api.service.pointadmin.DubboUserPointsService;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.UserPointsService;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 用户积分
 *
 * <AUTHOR>
 * @date 2023/12/5
 */
@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
@Component
@Slf4j
public class DubboUserPointsServiceImpl implements DubboUserPointsService {
    @Resource
    private UserPointsService userPointsService;

    /**
     * 获取用户积分列表
     *
     * @param request UserPointListRequest
     * @return Result
     */
    @Override
    public Result<BasePageResponse<UsersPointDto>> getUserPointsList(UserPointsListRequest request) {
        log.info("DubboUserPointsService.getUserPointsList, start, request:{}", request);
        try {
            if (Objects.isNull(request)) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_INFO_ERROR_ARG.getTranslateContent());
            }
            BasePageResponse<UsersPointDto> res = userPointsService.getUserPointsList(request);
            return Result.success(res);
        } catch (BizError e) {
            log.info("DubboUserPointsService.getUserPointsList, BizError, request:{}", request, e);
            return Result.fromException(e);
        } catch (Exception e) {
            log.error("DubboUserPointsService.getUserPointsList, Exception, request:{}", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<Void> cancelUserPoint(UserPointCancelRequest request) {
        try {
            userPointsService.cancelUserPoint(request.getMid(), request.getPointId(), request.getInvalidReason());
            return Result.success(null);
        } catch (BizError e) {
            log.info("DubboUserPointsService.cancelUserPoint, BizError, request:{}", request, e);
            return Result.fromException(e);
        } catch (Exception e) {
            log.info("DubboUserPointsService.cancelUserPoint Exception request:{}", request, e);
            return Result.fromException(e);
        }
    }
}
