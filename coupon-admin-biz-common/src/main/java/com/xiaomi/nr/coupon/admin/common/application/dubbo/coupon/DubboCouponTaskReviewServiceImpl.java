package com.xiaomi.nr.coupon.admin.common.application.dubbo.coupon;

import com.google.common.collect.Lists;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.CouponFillReviewDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.CouponFillReviewDetailDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillReviewCancelRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillReviewDetailRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillReviewListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CreateCouponFillReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponFillReviewCancelResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponFillReviewDetailResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CreateCouponFillReviewResponse;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponTaskReviewService;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.coupon.admin.common.application.dubbo.coupon.convert.TaskReviewConvert;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponreview.CouponTaskReviewService;
import com.xiaomi.nr.coupon.admin.enums.task.CustomizeGroupEnum;
import com.xiaomi.nr.coupon.admin.enums.task.UserGroupTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastructure.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponTaskRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponTaskReviewPO;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

@Component
@Slf4j
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
@ApiModule(value = "灌券任务审核服务", apiInterface = DubboCouponTaskReviewService.class)
public class DubboCouponTaskReviewServiceImpl implements DubboCouponTaskReviewService {

    @Autowired
    private CouponTaskReviewService couponTaskReviewService;

    @Autowired
    private TaskReviewConvert taskReviewConvert;

    @Autowired
    private CouponTaskRepository couponTaskRepository;

    @Override
    @ApiDoc("创建灌券任务审核")
    public Result<CreateCouponFillReviewResponse> createTaskReview(CreateCouponFillReviewRequest request) {
        try {
            if(Objects.isNull(request.getAreaId())){
                request.setAreaId(I18nUtil.getGlobalAreaId());
            }
            // 参数检验
            validateCreateFillTaskRequest(request);
            request.setCreator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());

            // 创建审核
            Long reviewId = couponTaskReviewService.createTaskReview(request);

            return Result.success(new CreateCouponFillReviewResponse(reviewId));
        } catch (Exception e) {
            log.error("DubboCouponTaskReviewService.createTaskReview error request:{}", request, e);
            return Result.fromException(e);
        }
    }


    @Override
    @ApiDoc("撤销灌券任务审核")
    public Result<CouponFillReviewCancelResponse> cancelTaskReview(CouponFillReviewCancelRequest request) {

        if (Objects.isNull(request.getReviewId())) {
            Result.fail(GeneralCodes.ParamError, TranslationEnum.COUPON_TASK_COMMIT_ERROR_AUTH_ID.getTranslateContent());
        }

        try {

            request.setOperator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());

            Boolean isSuccess = couponTaskReviewService.cancelTaskReview(request);

            return Result.success(new CouponFillReviewCancelResponse(isSuccess));
        } catch (Exception e) {
            log.error("DubboCouponTaskReviewService.cancelTaskReview error request:{}", request, e);
            return Result.fromException(e);
        }

    }
    @Override
    @ApiDoc("撤销灌券任务审核http")
    public Result<CouponFillReviewCancelResponse> cancelTaskReviewHttp(CouponFillReviewCancelRequest request) {

        if (Objects.isNull(request.getReviewId())) {
            Result.fail(GeneralCodes.ParamError, TranslationEnum.COUPON_TASK_COMMIT_ERROR_AUTH_ID.getTranslateContent());
        }

        try {

            request.setOperator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());

            Boolean isSuccess = couponTaskReviewService.cancelTaskReviewHttp(request);

            return Result.success(new CouponFillReviewCancelResponse(isSuccess));
        } catch (Exception e) {
            log.error("DubboCouponTaskReviewService.cancelTaskReview error request:{}", request, e);
            return Result.fromException(e);
        }

    }

    @Override
    @ApiDoc("灌券任务审核列表")
    public Result<BasePageResponse<CouponFillReviewDTO>> taskReviewList(CouponFillReviewListRequest request) {

        try {
            BasePageResponse<CouponFillReviewDTO> response = couponTaskReviewService.taskReviewList(request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponTaskReviewService.taskReviewList error request:{}", request, e);
            return Result.fromException(e);
        }
    }


    @Override
    @ApiDoc("灌券任务审核详情")
    public Result<CouponFillReviewDetailResponse> taskReviewDetail(CouponFillReviewDetailRequest request) {

        if (Objects.isNull(request) || Objects.isNull(request.getId()) || request.getId() <= CommonConstant.ZERO_LONG) {
            Result.fail(GeneralCodes.ParamError, TranslationEnum.COUPON_TASK_COMMIT_ERROR_AUTH_ID.getTranslateContent());
        }

        try {

            CouponTaskReviewPO reviewPO = couponTaskReviewService.getTaskReviewPO(request.getId());
            if(I18nUtil.isI18n() && !StringUtils.equals(Optional.of(reviewPO).map(CouponTaskReviewPO::getAreaId).orElse(""),I18nUtil.getGlobalAreaId())){
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_TASK_QUERY_ERROR_AREA.getTranslateContent());
            }

            CouponFillReviewDetailDTO reviewDetailDTO = taskReviewConvert.convertToReviewDTO(reviewPO);

            return Result.success(new CouponFillReviewDetailResponse(reviewDetailDTO));

        } catch (Exception e) {
            log.error("DubboCouponTaskReviewService.taskReviewDetail error request:{}", request, e);
            return Result.fromException(e);
        }

    }


    /**
     * 创建灌券任务入参校验
     *
     * @param request CreateFillCouponTaskRequest
     * @throws BizError 业务异常
     */
    private void validateCreateFillTaskRequest(CreateCouponFillReviewRequest request) throws BizError {
        // 任务id
        if (org.apache.commons.lang.StringUtils.isEmpty(request.getTaskName())) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_TASK_CREATE_VALIDATE_NAME.getTranslateContent());
        }

        // 优惠券id
        if (request.getConfigId() <= CommonConstant.ZERO_LONG) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_TASK_CREATE_VALIDATE_ID.getTranslateContent());
        }

        if (request.getPlanCount() < CommonConstant.ONE_INT) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_TASK_CREATE_VALIDATE_NUM.getTranslateContent());
        }

        // 校验灌券数据合法性
        if (request.getUserGroupType() == UserGroupTypeEnum.CUSTOMIZE_USER_GROUP.getCode()) {

            if (request.getCustomizeType() == CustomizeGroupEnum.UPLOAD_FILE.getCode() && CollectionUtils.isEmpty(request.getUidList())) {
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_TASK_CREATE_VALIDATE_TXT.getTranslateContent());
            }
            if (request.getCustomizeType() == CustomizeGroupEnum.CLIENT_INPUT.getCode() && CollectionUtils.isEmpty(request.getUidList())) {
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_TASK_CREATE_VALIDATE_UID.getTranslateContent());
            }

            // 人群mid去重
            if (request.getDistinct() == CommonConstant.ZERO_INT) {
                Set<Long> uidSet = new HashSet<>(request.getUidList());
                request.setUidList(Lists.newArrayList(uidSet));
            }
            //计划发放数量修正
            request.setPlanCount(request.getUidList().size());
        } else {
            if (Objects.isNull(request.getBatchId())) {
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_TASK_CREATE_VALIDATE_USER.getTranslateContent());
            }
        }

        if (I18nUtil.isI18n()) {
            if(Objects.isNull(request.getWorkflowId())){
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_CREATE_CHECK_WORKFLOW.getTranslateContent());
            }
        }
    }
}
