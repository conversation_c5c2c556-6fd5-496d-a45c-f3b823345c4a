package com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.event.posthandler;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.event.BaseCouponPostHandler;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.event.BaseDataPrepareWrapper;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.event.DataPrepareWrapperFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.EventContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class DataPreparePostHandler extends BaseCouponPostHandler {

    @Resource
    private DataPrepareWrapperFactory dataPrepareWrapperFactory;

    @Override
    public void createPost(CouponCreateEvent event) throws Exception {

        BizPlatformEnum bizPlatformEnum = BizPlatformEnum.valueOf(event.getBizPlatform());
        BaseDataPrepareWrapper dataPrepareWrapper = dataPrepareWrapperFactory.getDataPrepareWrapper(bizPlatformEnum);

        EventContext eventContext = dataPrepareWrapper.prepareGoodsInfo(event);
        event.setEventContext(eventContext);
    }

    @Override
    public void updatePost(CouponUpdateEvent event) throws Exception {

        BizPlatformEnum bizPlatformEnum = BizPlatformEnum.valueOf(event.getBizPlatform());
        BaseDataPrepareWrapper dataPrepareWrapper = dataPrepareWrapperFactory.getDataPrepareWrapper(bizPlatformEnum);

        EventContext eventContext = dataPrepareWrapper.prepareGoodsInfo(event);
        event.setEventContext(eventContext);
    }


    @Override
    public void updateStatusPost(CouponUpdateStatusEvent event) throws Exception {

    }
}
