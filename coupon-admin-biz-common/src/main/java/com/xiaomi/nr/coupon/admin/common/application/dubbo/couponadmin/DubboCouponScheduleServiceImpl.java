package com.xiaomi.nr.coupon.admin.common.application.dubbo.couponadmin;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.schedule.RefreshCouponUserWhiteListReq;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.schedule.RefreshCouponUserWhiteListResp;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponScheduleService;
import com.xiaomi.nr.coupon.admin.domain.coupon.CouponBatchScheduleService;
import com.xiaomi.nr.coupon.admin.domain.coupon.usercoupon.WhiteListScheduleService;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/03/25
 */
@Slf4j
@Component
@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
@ApiModule(value = "优惠券定时任务服务", apiInterface = DubboCouponScheduleService.class)
public class DubboCouponScheduleServiceImpl implements DubboCouponScheduleService {

    @Resource
    private CouponBatchScheduleService couponBatchScheduleService;

    @Resource
    private WhiteListScheduleService whiteListScheduleService;

    /**
     * 积分批次定时任务
     * 涉及：预算释放
     *
     * @return void
     */
    @Override
    @ApiDoc("预算释放")
    public Result<Void> budgetRelease() {

        log.info("DubboCouponScheduleService.budgetRelease started");

        try {
            couponBatchScheduleService.budgetRelease();
        } catch (Exception e) {
            log.info("DubboCouponScheduleService budgetRelease error. e ", e);
        }
        log.info("DubboCouponScheduleService.budgetRelease finished");
        return Result.success(null);
    }

    @Override
    @ApiDoc("刷新有券用户白名单")
    public Result<RefreshCouponUserWhiteListResp> refreshCouponUserWhiteList(RefreshCouponUserWhiteListReq req) {
        try {
            boolean fullRefresh = Optional.ofNullable(req).map(RefreshCouponUserWhiteListReq::getFullRefresh).orElse(false);
            RefreshCouponUserWhiteListResp resp = whiteListScheduleService.refreshCouponUserWhiteList(fullRefresh);
            return Result.success(resp);
        } catch (Exception e) {
            log.error("DubboCouponScheduleService refreshCouponUserWhiteList error. err: ", e);
            return Result.fromException(e);
        }
    }
}
