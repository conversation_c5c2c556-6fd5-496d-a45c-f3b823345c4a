package com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.check.carshop;

import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.CouponConfigValidator;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.springframework.stereotype.Component;

/**
 * @author: zhangliwei6
 * @date: 2025/5/29 15:35
 * @description:
 */
@Component
public class GiftBuyCarShopValidator implements CouponConfigValidator {

    @Override
    public void validate(CouponConfigItem couponConfigItem) throws Exception {
        CouponBaseInfo info = couponConfigItem.getCouponBaseInfo();
        if (info.getPromotionValue() != 0L) {
            throw ExceptionHelper.create(ErrCode.COUPON, "商品金额应直减至0元");
        }
    }
}
