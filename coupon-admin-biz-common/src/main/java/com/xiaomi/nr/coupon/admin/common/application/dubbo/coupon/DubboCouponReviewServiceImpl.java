package com.xiaomi.nr.coupon.admin.common.application.dubbo.coupon;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.OperateCouponConfigRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewDetailResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.CouponAdminService;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponReviewService;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.coupon.admin.common.application.dubbo.coupon.convert.CouponAdminConvert;
import com.xiaomi.nr.coupon.admin.common.application.dubbo.coupon.convert.CouponReviewConvert;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.check.CouponConfigCheckService;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponreview.CouponConfigReviewCompareService;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponreview.CouponReviewRelService;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponreview.CouponReviewService;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.bpm.IBpmProxy;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.CouponConfigCheckStrategy;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItemFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.goods.GoodsService;
import com.xiaomi.nr.coupon.admin.enums.WorkFlowEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.ReviewStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastructure.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponSketchRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.CouponWorkFlowMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.entity.SeachReviewGroupResult;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.entity.SeachReviewListResult;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.entity.SearchReviewGroupParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.entity.SearchReviewListParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponConfigReviewPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponReviewRelPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponWorkFlowPo;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.BrProxy;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.StoreProxyService;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
@ApiModule(value = "优惠券审核服务", apiInterface = DubboCouponReviewService.class)
public class DubboCouponReviewServiceImpl implements DubboCouponReviewService {

    @Autowired
    private CouponConfigReviewCompareService couponConfigReviewCompareService;

    @Autowired
    private CouponConfigCheckService couponCheckService;

    @Autowired
    private CouponSketchRepository couponSketchRepository;

    @Autowired
    private CouponReviewService couponReviewService;

    @Autowired
    private CouponReviewRelService couponReviewRelService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private IBpmProxy bpmProxy;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Autowired
    private CouponAdminService couponAdminService;

    @Autowired
    private CouponWorkFlowMapper couponWorkFlowMapper;

    @Autowired
    private BrProxy brProxy;

    @Autowired
    private StoreProxyService storeProxyService;

    /**
     * 创建券审核提交
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("创建优惠券提交审核")
    public Result<CouponReviewResponse> createCouponReview(CouponCreateReviewRequest request) {
        log.info("DubboCouponReviewServiceImpl.createCouponReview begin, request = {}", GsonUtil.toJson(request));

        try {
            String account = UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix();
            request.getCouponConfigVO().setCreator(account);
            request.setBizType(Optional.ofNullable(request.getBizType()).orElse(BizPlatformEnum.RETAIL.getCode()));

            request.getCouponConfigVO().setAreaId(I18nUtil.getGlobalAreaId());
            CouponConfigItem couponConfigItem = CouponConfigItemFactory.createCouponConfigItem(request.getCouponConfigVO(), request.getBizType());
            //基本信息校验
            CouponBaseInfo baseInfo = couponConfigItem.getCouponBaseInfo();
            CouponConfigCheckStrategy checkStrategy = couponCheckService.getCheckStrategy(baseInfo.getPromotionType() + "_" + baseInfo.getBizPlatform());
            if (Objects.isNull(checkStrategy)) {
                log.error("DubboCouponConfigReviewService.createCouponReview checkStrategy not exist, request = {}", request);
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_TASK_QUERY_ERROR_ARGS.getTranslateContent());
            }

            checkStrategy.createCheck(couponConfigItem);

            CouponConfigReviewPO reviewPO = CouponReviewConvert.convertReviewPO(request.getCouponConfigVO(), request.getApplyAttachment(), account, baseInfo.getBizPlatform());
            couponReviewService.save(reviewPO);

            //删草稿
            if (request.getSketchId() != null && request.getSketchId() > 0) {
                couponSketchRepository.delete(request.getSketchId());
            }

            CouponReviewResponse response = new CouponReviewResponse();
            response.setReviewId(reviewPO.getId());
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService createCouponReview error request:{}", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }

    /**
     * 更新券审核提交
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("更新优惠券提交审核")
    public Result<CouponReviewResponse> updateCouponReview(CouponUpdateReviewRequest request) {
        CouponReviewResponse response = new CouponReviewResponse();
        try {
            String account = UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix();
            request.setBizType(Optional.ofNullable(request.getBizType()).orElse(BizPlatformEnum.RETAIL.getCode()));
            log.info("DubboCouponConfigReviewService updateCouponReview request:{},account:{}", GsonUtil.toJson(request), account);
            request.getCouponConfigVO().setAreaId(I18nUtil.getGlobalAreaId());
            CouponConfigItem couponConfigItem = CouponConfigItemFactory.createCouponConfigItem(request.getCouponConfigVO(), request.getBizType());
            //基本信息校验
            CouponBaseInfo baseInfo = couponConfigItem.getCouponBaseInfo();
            CouponConfigCheckStrategy checkStrategy = couponCheckService.getCheckStrategy(baseInfo.getPromotionType() + "_" + baseInfo.getBizPlatform());

            if (Objects.isNull(checkStrategy)) {
                log.error("DubboCouponConfigReviewService.updateCouponReview checkStrategy not exist, request = {}", request);
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_TASK_COMMIT_ERROR_ARGS.getTranslateContent());
            }
            checkStrategy.updateCheck(couponConfigItem);

            //检查是否可创建审批
            long count = couponReviewService.count(request.getCouponConfigVO().getId(),
                    Arrays.asList(ReviewStatusEnum.ToBeReviewed.getValue(), ReviewStatusEnum.UnderReview.getValue()));
            if (count > 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_CONFIG_CHANGE_ERROR_AUDIT.getTranslateContent());
            }

            if (couponConfigReviewCompareService.changed(request)) {
                CouponConfigReviewPO po = CouponReviewConvert.convertReviewPO(request.getCouponConfigVO(), request.getApplyAttachment(), account, request.getBizType());
                couponReviewService.save(po);
                response.setReviewId(po.getId());
            } else {
                // 直接修改
                // 入参转换
                CouponConfigVO couponConfigVO = request.getCouponConfigVO();
                OperateCouponConfigRequest operateCouponConfigReq = CouponAdminConvert.convertOperateCouponConfigReq(couponConfigVO, request.getBizType());
                operateCouponConfigReq.setOperator(account);
                // 更新优惠券
                Result<Void> updateResult = couponAdminService.updateCouponConfig(operateCouponConfigReq);
                if (updateResult.getCode() != GeneralCodes.OK.getCode()) {
                    throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_TASK_COMMIT_ERROR_UPDATE.getTranslateContent());
                }
            }
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService updateCouponReview error request:{}", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }


    /**
     * 撤销券审核
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("取消券审核")
    public Result<Void> cancelCouponReview(CouponReviewRequest request) {
        try {
            request.setOperator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());
            log.info("DubboCouponConfigReviewService cancelCouponReview request:{}", GsonUtil.toJson(request));

            CouponConfigReviewPO reviewPO = couponReviewService.selectById(request.getReviewId());
            bpmProxy.cancelReview(reviewPO.getBpmKey(), request.getOperator());

            reviewPO.setStatus(ReviewStatusEnum.Canceled.getValue());
            couponReviewService.updateReviewStatus(reviewPO);
            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService cancelCouponReview error request:{}", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }

    /**
     * 撤销券审核
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("取消券审核http")
    public Result<Void> cancelCouponReviewHttp(CouponReviewRequest request) {
        try {
            request.setOperator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());
            log.info("DubboCouponConfigReviewService cancelCouponReviewHttp request:{}", GsonUtil.toJson(request));

            CouponConfigReviewPO reviewPO = couponReviewService.selectById(request.getReviewId());
            bpmProxy.cancelReview(reviewPO.getBpmKey(), request.getOperator());

            reviewPO.setStatus(ReviewStatusEnum.Canceled.getValue());
            couponReviewService.updateReviewStatus(reviewPO);
            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService cancelCouponReviewHttp error request:{}", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }

    /**
     * 查询券审核列表
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("优惠券审核列表")
    public Result<BasePageResponse<CouponReviewListVO>> couponReviewList(CouponReviewListRequest request) {
        BasePageResponse<CouponReviewListVO> response = new BasePageResponse<>();
        request.setAreaId(I18nUtil.getGlobalAreaId());
        try {
            SearchReviewListParam param = new SearchReviewListParam();
            BeanMapper.copy(request, param);
            param.setOrderBy(request.getOrderByMap().containsKey(request.getOrderBy()) ? request.getOrderByMap().get(request.getOrderBy()) : request.getOrderBy());
            SeachReviewListResult result = couponReviewService.selectList(param);
            if (CollectionUtils.isNotEmpty(result.getReviewPOList())) {
                response.setList(result.getReviewPOList().stream().map(x -> {
                    CouponReviewListVO couponReviewListVO = CouponReviewConvert.convertToReviewListVO(x);
                    couponReviewListVO.setConfigId(x.getConfigId() == 0 ? null : x.getConfigId());
                    return couponReviewListVO;
                }).collect(Collectors.toList()));
            }

            response.setPageSize(request.getPageSize());
            response.setPageNo(request.getPageNo());
            response.setTotalPage(result.getTotalPage());
            response.setTotalCount(result.getTotalCount());
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService couponReviewList error request:{}", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }

    /**
     * 券审核详情
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("优惠券审核详情")
    public Result<CouponReviewDetailResponse> couponReviewDetail(CouponReviewRequest request) {
        try {
            CouponConfigReviewPO reviewPO = couponReviewService.selectById(request.getReviewId());
            if (reviewPO == null) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_WORKFLOW.getTranslateContent());
            }
            CouponReviewDetailResponse response = new CouponReviewDetailResponse();

            CouponConfigVO couponConfigVO = GsonUtil.fromJson(CompressUtil.decompress(reviewPO.getConfigCompress()), CouponConfigVO.class);

            // goods detail
            GoodsRuleDetailVO goodsRuleDetailVO = goodsService.searchGoodsDetailInfo(
                    couponConfigVO.getGoodsRuleVO(), couponConfigVO.getPromotionRuleVO().getPromotionType(), reviewPO.getBizPlatform(), reviewPO.getAreaId());

            // 预算信息
            String budgetApplyNo = Optional.ofNullable(couponConfigVO.getBudgetInfoDto()).map(BudgetInfoDto::getBudgetApplyNo).orElse(null);
            Long lineNum = Optional.ofNullable(couponConfigVO.getBudgetInfoDto()).map(BudgetInfoDto::getLineNum).orElse(null);
            BudgetInfoDto budgetInfoDto = brProxy.queryBudgetDetail(budgetApplyNo, lineNum);
            couponConfigVO.setBudgetInfoDto(budgetInfoDto);

            couponConfigVO.setSendMode(couponSceneRepository.selectRelationSceneByCode(couponConfigVO.getSendScene()).getSendMode());
            couponConfigVO.getGoodsRuleVO().setGoodsDiscountLevelVO(null);
            couponConfigVO.getGoodsRuleVO().setGoodsSuitableVOs(null);
            couponConfigVO.getGoodsRuleVO().setGoodsInclude(null);
            couponConfigVO.setStartFetchTimeStamp(couponConfigVO.getStartFetchTime().getTime() / 1000);
            couponConfigVO.setEndFetchTimeStamp(couponConfigVO.getEndFetchTime().getTime() / 1000);
            couponConfigVO.getUseTermVO().setStartUseTimeStamp(couponConfigVO.getUseTermVO().getStartUseTime().getTime() / 1000);
            couponConfigVO.getUseTermVO().setEndUseTimeStamp(couponConfigVO.getUseTermVO().getEndUseTime().getTime() / 1000);

            //指定门店列表回显
            Optional<Integer> any = couponConfigVO.getUseChannel().keySet().stream().findAny();
            any.ifPresent(channel -> {
                UseChannelVO useChannelVO = couponConfigVO.getUseChannel().get(channel);
                if(CollectionUtils.isNotEmpty(useChannelVO.getLimitIds())){
                    couponConfigVO.setStoreInfoVoList(storeProxyService.selectStoreListByOrgCode(useChannelVO.getLimitIds()));
                }
            });

            response.setReviewId(reviewPO.getId());
            response.setStatus(reviewPO.getStatus());
            if (StringUtils.isNotBlank(reviewPO.getApplyAttachment())) {
                response.setApplyAttachment(GsonUtil.fromListJson(reviewPO.getApplyAttachment(), ApplyAttachmentVO.class));
            }
            response.setCouponConfigVO(couponConfigVO);
            response.setGoodsRuleDetailVO(goodsRuleDetailVO);
            response.setBpmReason(reviewPO.getBpmReason());

            log.info("DubboCouponConfigReviewService couponReviewDetail finished, req = {}, resp = {}", GsonUtil.toJson(request), GsonUtil.toJson(response));

            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService couponReviewDetail error request:{}", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }

    /**
     * 审核组列表
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("审核组列表")
    public Result<BasePageResponse<ReviewGroupDTO>> reviewGroupList(ReviewGroupListRequest request) {
        BasePageResponse<ReviewGroupDTO> response = new BasePageResponse<>();
        try {
            SearchReviewGroupParam param = SearchReviewGroupParam.buildSearchReviewGroupParam(request);
            SeachReviewGroupResult result = couponReviewRelService.selectList(param);
            if (CollectionUtils.isNotEmpty(result.getCouponReviewRelPOList())) {
                response.setList(result.getCouponReviewRelPOList().stream().map(x -> CouponReviewConvert.convertToReviewRelDTO(x)).collect(Collectors.toList()));
            }
            response.setPageSize(request.getPageSize());
            response.setPageNo(request.getPageNo());
            response.setTotalPage(result.getTotalPage());
            response.setTotalCount(result.getTotalCount());
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService reviewGroupList error request:{}", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }

    /**
     * 审核组关系保存
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("审核组关系保存")
    public Result<Void> reviewGroupSave(ReviewGroupSaveRequest request) {
        try {
            if (StringUtils.isEmpty(request.getCreator())) {
                return Result.fail(GeneralCodes.ParamError, "员工名称不能为空");
            }
            if (StringUtils.isEmpty(request.getReviewGroup())) {
                return Result.fail(GeneralCodes.ParamError, "审核组Id不能为空");
            }
            couponReviewRelService.saveOrUpdate(CouponReviewRelPO.buildCouponReviewRelPO(request));
            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService reviewGroupSave error request:{}", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }

    @Override
    @ApiDoc("审批流列表")
    public Result<List<WorkFlowConfigVo>> getWorkFlowConfigVoList() {
        List<CouponWorkFlowPo> poList = couponWorkFlowMapper.queryPromotionWorkFlowByAreaId(I18nUtil.getGlobalAreaId());
        if (CollectionUtils.isEmpty(poList)) {
            return null;
        }
        return Result.success(poList.stream().map(this::convertPo2Vo).collect(Collectors.toList()));
    }

    public WorkFlowConfigVo convertPo2Vo(CouponWorkFlowPo po) {
        WorkFlowConfigVo vo = new WorkFlowConfigVo();
        vo.setWorkFlowId(po.getWorkflowId().intValue());
        vo.setWorkFlowText(WorkFlowEnum.getTranslationByCode(po.getWorkflowId()));
        vo.setChannel(Integer.valueOf(Arrays.asList(po.getChannels().split(",")).get(0)));
        return vo;
    }
}
