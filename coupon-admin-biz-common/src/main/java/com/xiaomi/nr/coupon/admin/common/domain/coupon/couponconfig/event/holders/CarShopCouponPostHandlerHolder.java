package com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.event.holders;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.event.posthandler.*;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.event.BaseCouponPostHandler;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.event.ICouponPostHandlerHolder;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: zhangliwei6
 * @date: 2025/6/4 15:35
 * @description:
 */
@Component
public class CarShopCouponPostHandlerHolder implements ICouponPostHandlerHolder {

    @Override
    public List<Class<? extends BaseCouponPostHandler>> getCouponPostHandlers() {
        return Lists.newArrayList(DataPreparePostHandler.class, RedisRefreshPostHandler.class,
                CouponOptLogPostHandler.class, CacheRefreshPostHandler.class, TaskUpdatePostHandler.class,
                CouponChangeMQPostHandler.class);
    }

    @Override
    public BizPlatformEnum getBizPlatformEnum() {
        return BizPlatformEnum.CAR_SHOP;
    }
}
