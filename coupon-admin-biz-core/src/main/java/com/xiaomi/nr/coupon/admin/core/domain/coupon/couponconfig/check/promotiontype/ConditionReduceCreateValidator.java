package com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.promotiontype;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.OperateEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.springframework.stereotype.Component;

/**
 * @author: zhangliwei6
 * @date: 2025/5/28 14:46
 * @description:
 */
@Component
public class ConditionReduceCreateValidator extends AbstractPromotionTypeValidator {

    @Override
    public void validate(CouponConfigItem couponConfigItem) throws BizError {
        CouponBaseInfo info = couponConfigItem.getCouponBaseInfo();
        if (info.getId() > 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券id必须为空");
        }
    }

    @Override
    public PromotionTypeEnum getPromotionType() {
        return PromotionTypeEnum.ConditionReduce;
    }

    @Override
    public OperateEnum getOperateEnum() {
        return OperateEnum.Create;
    }
}
