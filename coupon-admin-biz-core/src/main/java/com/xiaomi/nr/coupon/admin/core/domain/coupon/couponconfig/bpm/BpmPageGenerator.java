package com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.bpm;

import com.xiaomi.newretail.bpm.api.model.dto.ProcessCreateDTO;
import com.xiaomi.nr.coupon.admin.enums.BpmPageEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.CommonConstant;
import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.InputStream;
import java.io.StringWriter;
import java.util.Map;

/**
 * @author: zhangliwei6
 * @date: 2025/5/21 20:26
 * @description:
 */
public abstract class BpmPageGenerator<T> {

    private static final String DEFAULT_ENCODING = "UTF-8";

    private static final String HTML_TEMPLATE_PATH = "html/";

    public abstract ProcessCreateDTO createRequest(T t) throws Exception;

    public abstract Map<String, Object> convertMap(T t) throws Exception;

    public abstract String convertMapJson(T t);

    public abstract BpmPageEnum getType();

    public String getI18n() {
        return CommonConstant.AREA_ID_CN;
    }

    /**
     * web信息填充
     */
    protected String renderWebContent(Map<String, Object> dataMap) throws Exception {
        Configuration configuration = new Configuration(Configuration.DEFAULT_INCOMPATIBLE_IMPROVEMENTS);
        configuration.setDefaultEncoding(DEFAULT_ENCODING);

        String templateName = this.getType().getName();

        // 模板resource路径
        String resourcePath = HTML_TEMPLATE_PATH + templateName;

        InputStream inputStream = new ClassPathResource(resourcePath).getInputStream();
        String templateContent = IOUtils.toString(inputStream, DEFAULT_ENCODING);

        StringTemplateLoader templateLoader = new StringTemplateLoader();
        templateLoader.putTemplate(templateName, templateContent);
        configuration.setTemplateLoader(templateLoader);

        Template template = configuration.getTemplate(templateName, DEFAULT_ENCODING);

        StringWriter writer = new StringWriter();
        template.process(dataMap, writer);
        writer.flush();
        return writer.toString();
    }
}