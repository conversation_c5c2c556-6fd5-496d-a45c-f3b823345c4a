package com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.couponservice;

import com.xiaomi.nr.coupon.admin.api.enums.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.TimesLimitEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * @author: zhangliwei6
 * @date: 2025/5/28 21:34
 * @description:
 */
@Component
public class GlassRepairValidator extends AbstractCouponServiceValidator{

    @Override
    void timesLimitCheck(CouponBaseInfo info) throws BizError {
        TimesLimitEnum timesLimitEnum = TimesLimitEnum.valueOf(info.getTimesLimit());

        if (null == timesLimitEnum) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用次数限制参数异常");
        }

        // 玻璃无忧券，使用次数限制为不限次
        if (!Objects.equals(timesLimitEnum, TimesLimitEnum.LIMIT)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用次数限制必须为有限制");
        }
    }

    @Override
    void couponTypeCheck(CouponBaseInfo info) throws BizError {
        // 券类型校验
        CouponTypeEnum couponTypeEnum = CouponTypeEnum.getByValue(info.getCouponType());

        if (null == couponTypeEnum) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券类型不存在");
        }

        // 玻璃无忧券，券类型必须为不限次服务卡
        if (!Objects.equals(couponTypeEnum, CouponTypeEnum.DEDUCTION)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券类型必须为抵扣券");
        }
    }

    @Override
    void goodsCheck(Map<Long, Integer> labourHourSsu, Map<Long, Integer> partsSsu) throws BizError {
        // 服务类型为漆面修复
        if (MapUtils.isEmpty(labourHourSsu)) {
            // 工时ssu不能为空
            throw ExceptionHelper.create(ErrCode.COUPON, "工时ssu不能为空");
        }
    }

    @Override
    public CouponServiceTypeEnum getCouponService() {
        return CouponServiceTypeEnum.GLASS_REPAIR;
    }
}
