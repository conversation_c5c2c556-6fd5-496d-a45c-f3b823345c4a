package com.xiaomi.nr.coupon.admin.application.dubbo;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.UserCouponVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.OrderInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.request.UserCouponCancelRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.request.UserCouponDetailRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.request.UserCouponListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.response.UserCouponDetailResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.service.DubboUserCouponService;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.UserCouponConvert;
import com.xiaomi.nr.coupon.admin.domain.coupon.usercoupon.CisService;
import com.xiaomi.nr.coupon.admin.enums.usercoupon.UserCouponStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CarCouponRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.UserCouponRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.SeachUserCouponListResult;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.SearchUserCouponListParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.UserCouponPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.UserCouponSharePO;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.order.OrderProxyService;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@Service(timeout = 2000, group = "${dubbo.group}", version = "1.0")
@ApiModule(value = "用户优惠券服务接口", apiInterface = DubboUserCouponService.class)
public class DubboUserCouponServiceImpl implements DubboUserCouponService {

    @Autowired
    private UserCouponRepository userCouponRepository;

    @Autowired
    private CarCouponRepository carCouponRepository;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private UserCouponConvert userCouponConvert;

    @Autowired
    private OrderProxyService orderProxyService;

    @Autowired
    private CisService cisService;

    @Override
    @ApiDoc("查询用户券列表")
    public Result<BasePageResponse<UserCouponVO>> userCouponList(UserCouponListRequest request) {
        try {

            // 业务类型默认为3C
            int bizPlatform = Optional.ofNullable(request.getBizType()).orElse(BizPlatformEnum.RETAIL.getCode());

            // 1、参数校验
            // 汽车售后场景校验vid
            if (BizPlatformEnum.CAR_AFTER_SALE.getCode().equals(bizPlatform)) {
                if(StringUtils.isBlank(request.getVid()) && StringUtils.isBlank(request.getVin())) {
                    throw ExceptionHelper.create(GeneralCodes.ParamError, "vid和vin不能同时为空");
                }
                if(StringUtils.isNotBlank(request.getVin())) {
                    String vid = cisService.getSingleVidByVin(request.getVin());
                    if (StringUtils.isBlank(vid)) {
                        throw ExceptionHelper.create(GeneralCodes.ParamError, "vin不存在");
                    }
                    if (StringUtils.isNotBlank(request.getVid()) && !StringUtils.equals(vid, request.getVid())) {
                        throw ExceptionHelper.create(GeneralCodes.ParamError, "填入的vid和vin不匹配");
                    }
                    request.setVid(vid);
                }
            }
            // 非汽车售后场景校验uid和couponId
            else if (request.getUid()<=0 && request.getCouponId() <=0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "用户id、券id均为空");
            }

            // 2、查询列表数据
            SearchUserCouponListParam param = new SearchUserCouponListParam();
            param.setUid(request.getUid());
            List<Long> configIdList = new ArrayList<>();
            if(StringUtils.isNotBlank(request.getConfigName()) || request.getCouponType() != null){
                List<Long> configIds = couponConfigRepository.getCouponIdByNameAndType(request.getConfigName(), request.getCouponType(), String.valueOf(bizPlatform));
                if(CollectionUtils.isNotEmpty(configIds)){
                    configIdList.addAll(configIds);
                }
            }
            if(request.getConfigId()>0){
                configIdList.add(request.getConfigId());
            }
            param.setConfigIds(configIdList);
            param.setCouponStatus(request.getCouponStatus());
            param.setOrderDirection(request.getOrderDirection());
            param.setOrderBy(request.getOrderByMap().containsKey(request.getOrderBy())?request.getOrderByMap().get(request.getOrderBy()):request.getOrderBy());
            param.setOffset((request.getPageNo()-1)*request.getPageSize());
            param.setLimit(request.getPageSize());
            param.setCouponId(request.getCouponId());
            param.setBizPlatform(bizPlatform);
            param.setVid(request.getVid());
            SeachUserCouponListResult result = userCouponRepository.selectList(param);

            // 3、构建出参
            BasePageResponse<UserCouponVO> response = new BasePageResponse<>();
            if(CollectionUtils.isNotEmpty(result.getUserCouponPOList())){
                // 4、查询批次信息
                List<Long> configIds = result.getUserCouponPOList().stream().map(UserCouponPO::getTypeId).distinct().collect(Collectors.toList());
                List<CouponConfigPO> couponConfigPOs = couponConfigRepository.searchCouponListById(configIds);
                Map<Long,CouponConfigPO> couponConfigMap = couponConfigPOs.stream().collect(Collectors.toMap(CouponConfigPO::getId, Function.identity(),(k1,k2)->k2));
                response.setList(result.getUserCouponPOList().stream().map(x -> userCouponConvert.convertToUserCouponVO(x, couponConfigMap.get(x.getTypeId()))).collect(Collectors.toList()));
            }

            response.setPageNo(request.getPageNo());
            response.setPageSize(request.getPageSize());
            response.setTotalPage(result.getTotalPage());
            response.setTotalCount(result.getTotalCount());
            return Result.success(response);
        } catch (Exception e) {
            log.info("UserCouponService userCouponList Exception request:{}", request,e);
            return Result.fromException(e);
        }
    }

    @Override
    @ApiDoc("作废用户券")
    public Result<Void> cancelUserCoupon(UserCouponCancelRequest request) {

        try {

            // 业务类型默认为3C
            int bizPlatform = Optional.ofNullable(request.getBizType()).orElse(BizPlatformEnum.RETAIL.getCode());

            // 1、参数校验
            long couponId = request.getCouponId();
            if (couponId <= 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "券id不能为空");
            }

            // 汽车售后场景校验vid
            if (BizPlatformEnum.CAR_AFTER_SALE.getCode().equals(bizPlatform)) {
                String vid = request.getVid();
                if (StringUtils.isBlank(vid)) {
                    throw ExceptionHelper.create(GeneralCodes.ParamError, "vid不能为空");
                }
                cancelCarCoupon(vid, couponId);
            }
            // 非售后场景校验mid
            else {
                long uid = request.getUid();
                if (uid <=0) {
                    throw ExceptionHelper.create(GeneralCodes.ParamError, "用户id不能为空");
                }
                cancelUserCoupon(uid, couponId);
            }

            return Result.success(null);
        } catch (Exception e) {
            log.info("UserCouponService cancelUserCoupon Exception request:{}", request,e);
            return Result.fromException(e);
        }
    }

    /**
     * 作废用户券
     *
     * @param uid       uid
     * @param couponId  couponId
     */
    private void cancelUserCoupon(long uid, long couponId) throws BizError {
        UserCouponPO userCouponPO = userCouponRepository.selectByCouponId(uid, couponId);
        if(Objects.isNull(userCouponPO) || !UserCouponStatusEnum.UNUSED.getValue().equals(userCouponPO.getStat())){
            throw ExceptionHelper.create(GeneralCodes.ParamError, "只能作废未使用的券");
        }
        long count =  userCouponRepository.destroyCoupon(uid, couponId, UserCouponStatusEnum.INVALID);
        if(count <= 0) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "作废失败");
        }
    }

    /**
     * 作废售后服务券
     *
     * @param vid       vid
     * @param couponId  couponId
     */
    private void cancelCarCoupon(String vid, long couponId) throws BizError {
        UserCouponPO userCouponPO = carCouponRepository.selectByCouponId(vid, couponId);
        if(Objects.isNull(userCouponPO) || !UserCouponStatusEnum.UNUSED.getValue().equals(userCouponPO.getStat())){
            throw ExceptionHelper.create(GeneralCodes.ParamError, "只能作废未使用的券");
        }
        long count =  carCouponRepository.destroyCoupon(vid, couponId, UserCouponStatusEnum.INVALID);
        if(count <= 0) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "作废失败");
        }
    }


    @Override
    @ApiDoc("用户券详情")
    public Result<UserCouponDetailResponse> queryUserCouponDetail(UserCouponDetailRequest request){

        if (request.getCouponId() == null || request.getCouponId() <= 0) {
            return Result.fail(GeneralCodes.ParamError, "用户券id不能为空");
        }

        try {

            //获取用户券&券配置信息
            UserCouponPO userCouponPO = userCouponRepository.selectInfoById(request.getCouponId());
            CouponConfigPO couponConfigPO = couponConfigRepository.searchCouponById(userCouponPO.getTypeId());

            //获取券分享信息
            List<UserCouponSharePO> sharePOList = null;
            if (UserCouponStatusEnum.RECEIVED.getValue().equals(userCouponPO.getStat()) || UserCouponStatusEnum.PRESENTED.getValue().equals(userCouponPO.getStat())) {
                sharePOList = userCouponRepository.selectShareByUid(userCouponPO.getUserId(), userCouponPO.getId());
            }

            //获取券订单使用信息
            OrderInfoVO orderInfoVO = null;
            if(UserCouponStatusEnum.USED.getValue().equals(userCouponPO.getStat())){
                orderInfoVO = orderProxyService.getOrderInfo(userCouponPO.getOffline(), userCouponPO.getUserId(), userCouponPO.getOrderId());
            }

            return Result.success(userCouponConvert.convertToUserCouponDetail(userCouponPO, couponConfigPO, sharePOList, orderInfoVO));
        } catch (Exception e) {
            log.info("UserCouponService queryUserCouponDetail Exception request:{}", request, e);
            return Result.fromException(e);
        }
    }


}
