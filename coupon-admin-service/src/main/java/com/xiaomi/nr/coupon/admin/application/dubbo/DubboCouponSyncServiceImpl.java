package com.xiaomi.nr.coupon.admin.application.dubbo;

import com.google.common.base.Stopwatch;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sync.request.SyncXiguaMarketCouponReceiveRecordRequest;
import com.xiaomi.nr.coupon.admin.api.service.DubboCouponSyncService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponsync.CouponSyncService;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/6/26 15:29
 */
@Component
@Slf4j
@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
public class DubboCouponSyncServiceImpl implements DubboCouponSyncService {
    @Resource
    private CouponSyncService couponSyncService;

    @Resource
    @Qualifier("asyncExecutor")
    private Executor asyncExecutor;

    /**
     * 券码信息同步
     */
    @Override
    public Result<Void> syncXiguaMarketCouponMessage() {
        log.info("DubboCouponSyncServiceImpl.syncXiguaMarketCouponMessage begin");

        try {
            CompletableFuture.runAsync(() -> {
                Stopwatch stopwatch = Stopwatch.createStarted();
                try {
                    couponSyncService.syncXiguaMarketCouponMessage();
                    log.info("DubboCouponSyncServiceImpl.syncXiguaMarketCouponMessage async task finished, costTime = {}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
                } catch (Exception e) {
                    log.error("DubboCouponSyncServiceImpl.syncXiguaMarketCouponMessage async task error: ", e);
                    throw new RuntimeException(e);
                } finally {
                    stopwatch.stop();
                }
            }, asyncExecutor);

            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboCouponSyncServiceImpl.syncXiguaMarketCouponMessage error: ", e);
            return Result.fromException(e);
        }
    }

    /**
     * 西瓜侧券码领取记录同步
     *
     * @param request request
     */
    @Override
    public Result<Void> syncXiguaMarketCouponReceiveRecord(SyncXiguaMarketCouponReceiveRecordRequest request) {
        log.info("DubboCouponSyncServiceImpl.syncXiguaMarketCouponReceiveRecord begin, request = {}", GsonUtil.toJson(request));
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            couponSyncService.syncXiguaMarketCouponReceiveRecord(request);

            log.info("DubboCouponSyncServiceImpl.syncXiguaMarketCouponReceiveRecord finished, request = {}, costTime = {}ms", GsonUtil.toJson(request), stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboCouponSyncServiceImpl.syncXiguaMarketCouponReceiveRecord Exception, request = {}, e = ", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }
}
