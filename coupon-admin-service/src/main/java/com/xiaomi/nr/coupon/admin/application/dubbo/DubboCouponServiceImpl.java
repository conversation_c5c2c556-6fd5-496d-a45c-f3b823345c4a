package com.xiaomi.nr.coupon.admin.application.dubbo;

import com.google.common.base.Stopwatch;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponCodeInfo;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponInfo;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponByTypeIdRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponByTypeIdResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponCodeRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponCodeResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponByIdRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponByIdResponse;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.BatchGetConfigInfoReponse;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.BatchGetConfigInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.CouponConfigInfoDTO;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.GetConfigInfoReponse;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.GetConfigInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.GoodsConfigRelRequest;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.GoodsConfigRelResponse;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.GoodsItem;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.MemberConfigRequest;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.*;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.service.DubboCouponService;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboAutoTestService;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponCodeConvert;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponConfigConvert;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.UserCouponConvert;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.CouponConfigQueryService;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponCodeRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.UserCouponRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.localcache.LocalCacheCommon;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.SearchConfigInfoParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.SearchConfigParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.UserCouponPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.coupon.po.CouponCodePO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponactivity.ProMemberRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigInfoCachePo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.tidbdao.po.CouponPo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service(timeout = 2000, group = "${dubbo.group}", version = "1.0")
@Component
@Slf4j
@ApiModule(value = "优惠券服务", apiInterface = DubboCouponService.class)
public class DubboCouponServiceImpl implements DubboCouponService {

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private CouponConfigQueryService couponConfigQueryService;

    @Autowired
    private LocalCacheCommon localCacheCommon;

    @Autowired
    private ProMemberRedisDao proMemberRedisDao;

    @Resource
    private CouponConfigConvert couponConfigConvert;

    @Resource
    private CouponCodeRepository couponCodeRepository;

    @Resource
    private CouponCodeConvert couponCodeConvert;

    @Resource
    private UserCouponRepository userCouponRepository;

    @Resource
    private UserCouponConvert userCouponConvert;


    @Override
    @ApiDoc("查询优惠券信息")
    public Result<GetConfigInfoReponse> getCouponConfigInfo(GetConfigInfoRequest request) {
        log.info("DubboCouponService.getCouponConfigInfo request:{}", request);
        try {

            if (request.getConfigId() == null || request.getConfigId() <= 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "参数错误");
            }

            CouponConfigPO couponConfigPO = couponConfigRepository.searchCouponById(request.getConfigId());

            if (couponConfigPO == null) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠券不存在");
            }

            Map<Long, Long> couponSendCount = couponConfigRepository.getCouponSendCount(Lists.newArrayList(request.getConfigId()));

            CouponConfigInfoDTO couponConfigInfoDTO = couponConfigConvert.convertToCouponConfigInfoDTO(couponConfigPO, Optional.ofNullable(couponSendCount.get(request.getConfigId())).orElse(0L));

            return Result.success(new GetConfigInfoReponse().setCouponConfigInfoDTO(couponConfigInfoDTO));

        } catch (BizError e) {
            return Result.fromException(e);
        } catch (Exception e) {
            log.error("DubboCouponService.getCouponConfigInfo fail request:{}", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    @ApiDoc("批量查询优惠券信息")
    public Result<BatchGetConfigInfoReponse> batchGetCouponConfigInfo(BatchGetConfigInfoRequest request) {
        log.info("DubboCouponService.batchGetCouponConfigInfo fail request:{}", request);
        try {

            // 入参校验
            List<Long> configIds = request.getConfigIds();
            if (CollectionUtils.isNotEmpty(configIds) && configIds.size() > 50) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠券id数量不能超过50");
            }

            SearchConfigInfoParam param = SearchConfigInfoParam.build(request);

            List<CouponConfigPO> couponConfigPOList = couponConfigRepository.searchCouponByParam(param);

            Map<Long, Long> couponSendCount = new HashMap<>();
            if (request.getWithSurplusCnt() == null || request.getWithSurplusCnt()) {
                couponSendCount = couponConfigRepository.getCouponSendCount(couponConfigPOList.stream().map(CouponConfigPO::getId).collect(Collectors.toList()));
            }

            List<CouponConfigInfoDTO> couponConfigInfoDTOList = couponConfigConvert.convertToCouponConfigInfoDTOs(couponConfigPOList, couponSendCount);

            return Result.success(new BatchGetConfigInfoReponse().setCouponConfigs(couponConfigInfoDTOList));

        } catch (BizError e) {
            return Result.fromException(e);
        } catch (Exception e) {
            log.error("DubboCouponService.batchGetCouponConfigInfo fail request:{}", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    @ApiDoc("查询商品可用券配置")
    public Result<GoodsConfigRelResponse> getGoodsCouponConfigRel(GoodsConfigRelRequest request) {
        log.info("DubboCouponService.GoodsConfigRelRequest fail request:{}", request);
        try {

            SearchConfigParam searchConfigParam = new SearchConfigParam();
            searchConfigParam.setBizPlatform(Optional.ofNullable(request.getBizPlatform()).orElse(BizPlatformEnum.RETAIL.getCode()));
            searchConfigParam.setUseChannel(request.getUseChannel());
            Map<String, List<GoodsItem>> goodsLevelMap = request.getGoodsItems().stream().collect(Collectors.groupingBy(GoodsItem::getLevel));

            Map<Long, Set<Long>> skuCouponConfigIds = couponConfigQueryService.getLongSetMap(goodsLevelMap.get(GoodsLevelEnum.Sku.getValue()), GoodsLevelEnum.Sku, searchConfigParam);

            Map<Long, Set<Long>> packageCouponConfigIds = couponConfigQueryService.getLongSetMap(goodsLevelMap.get(GoodsLevelEnum.Package.getValue()), GoodsLevelEnum.Package, searchConfigParam);

            Map<Long, Set<Long>> ssuCouponConfigIds = couponConfigQueryService.getLongSetMap(goodsLevelMap.get(GoodsLevelEnum.Ssu.getValue()), GoodsLevelEnum.Ssu, searchConfigParam);

            Set<Long> configIds = new HashSet<>();
            skuCouponConfigIds.values().forEach(configIds::addAll);
            packageCouponConfigIds.values().forEach(configIds::addAll);
            ssuCouponConfigIds.values().forEach(configIds::addAll);

            Map<Long, CouponConfigInfoDTO> couponConfigInfoDTOMap = new HashMap<>(configIds.size());
            for (Long configId : configIds) {
                if (configId == null) {
                    continue;
                }
                ConfigInfoCachePo configInfoCachePo = localCacheCommon.getConfigInfoCachePo(configId);
                if (configInfoCachePo == null) {
                    continue;
                }
                couponConfigInfoDTOMap.put(configId, couponConfigConvert.convertToCouponConfigInfoDTO(configInfoCachePo));
            }

            GoodsConfigRelResponse relResponse = GoodsConfigRelResponse.builder()
                    .skuCouponConfigIds(skuCouponConfigIds)
                    .packageCouponConfigIds(packageCouponConfigIds)
                    .ssuCouponConfigIds(ssuCouponConfigIds)
                    .couponConfigInfoDTOMap(couponConfigInfoDTOMap)
                    .build();

            return Result.success(relResponse);
        } catch (BizError e) {
            return Result.fromException(e);
        } catch (Exception e) {
            log.error("DubboCouponService.getGoodsCouponConfigRel fail request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 会员pro权益开关、黑名单信息同步接口
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("会员pro权益开关、黑名单信息同步接口")
    public Result<Boolean> pushProMemberConfig(MemberConfigRequest request) {
        log.info("pushProMemberConfig begin request:{}", request);
        // TODO 校验规则？
        try {
            setData(request);
        } catch (Exception e) {
            setData(request);
        }
        log.info("pushProMemberConfig end request:{}", request);
        return Result.success(true);
    }

    /**
     * 获取优惠券码信息
     *
     * @param request 包含获取优惠券码信息所需参数的请求对象
     * @return 包含优惠券码信息的响应结果
     */
    @Override
    @ApiDoc("获取优惠券码信息（财务系统使用）")
    public Result<GetCouponCodeResponse> getCouponCodeInfo(@Valid GetCouponCodeRequest request) {
        log.info("DubboCouponServiceImpl.getCouponCodeInfo begin, request = {}", GsonUtil.toJson(request));
        // 启动计时器
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 从仓库中获取优惠券码信息
            List<CouponCodePO> couponCodePoList = couponCodeRepository.getCouponCodeInfo(request.getCouponIndexList());

            // 将获取到的优惠券码信息转换为业务需要的格式
            List<CouponCodeInfo> couponCodeInfoList = couponCodeConvert.buildCouponCodeInfo(couponCodePoList);

            // 构建响应对象并设置优惠券码信息列表
            GetCouponCodeResponse response = new GetCouponCodeResponse();
            response.setCouponCodeInfoList(couponCodeInfoList);

            log.info("DubboCouponServiceImpl.getCouponCodeInfo finished, request = {}, response = {}, costTime = {}ms", GsonUtil.toJson(request), GsonUtil.toJson(response), stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return Result.success(response);
        } catch (Exception e) {
            // 记录错误日志，打印请求参数和异常信息
            log.error("DubboCouponServiceImpl.getCouponCodeInfo has Error, request = {}, e = ", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }

    /**
     * 根据优惠券ID获取优惠券信息（财务系统使用）
     *
     * @param request 获取优惠券信息的请求对象
     * @return 包含优惠券信息的响应结果
     */
    @Override
    @ApiDoc("根据优惠券ID获取优惠券信息（财务系统使用）")
    public Result<GetCouponByIdResponse> getCouponById(GetCouponByIdRequest request) {
        // 记录方法开始日志，打印请求参数
        log.info("DubboCouponServiceImpl.getCouponById begin, request = {}", GsonUtil.toJson(request));
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 根据ID列表批量获取用户优惠券
            List<UserCouponPO> couponPOList = userCouponRepository.batchGetById(request.getIdList());

            // 转换用户优惠券信息
            List<CouponInfo> couponInfoList = userCouponConvert.buildCouponInfo(couponPOList);

            // 构建响应对象
            GetCouponByIdResponse response = new GetCouponByIdResponse();
            response.setCouponInfoList(couponInfoList);

            // 记录方法结束日志，打印请求参数、响应参数和耗时
            log.info("DubboCouponServiceImpl.getCouponById finished, request = {}, response = {}, costTime = {}ms",
                    GsonUtil.toJson(request), GsonUtil.toJson(response), stopwatch.elapsed(TimeUnit.MILLISECONDS));

            // 返回成功结果
            return Result.success(response);
        } catch (Exception e) {
            // 记录错误日志，打印请求参数和异常信息
            log.error("DubboCouponServiceImpl.getCouponById has Error, request = {}, e = ", GsonUtil.toJson(request), e);
            // 返回异常结果
            return Result.fromException(e);
        }
    }

    /**
     * 根据类型ID获取优惠券（财务系统使用）
     *
     * @param request 包含类型ID的请求对象
     * @return 包含优惠券信息的响应结果
     */
    @Override
    @ApiDoc("根据类型ID获取优惠券（财务系统使用）")
    public Result<GetCouponByTypeIdResponse> getCouponByTypeId(GetCouponByTypeIdRequest request) {
        // 记录方法开始日志，打印请求参数
        log.info("DubboCouponServiceImpl.getCouponByTypeId begin, request = {}", GsonUtil.toJson(request));
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 根据类型ID列表和时间范围批量获取优惠券
            List<CouponPo> couponPOList = userCouponRepository.batchGetByTypeId(request.getTypeIdList(), request.getStartTime(), request.getEndTime());

            // 转换用户优惠券信息
            List<CouponInfo> couponInfoList = userCouponConvert.buildUserCouponInfo(couponPOList);

            // 构建响应对象
            GetCouponByTypeIdResponse response = new GetCouponByTypeIdResponse();
            response.setCouponInfoList(couponInfoList);

            // 记录方法结束日志，打印请求参数、响应参数和耗时
            log.info("DubboCouponServiceImpl.getCouponByTypeId finished, request = {}, response = {}, costTime = {}ms",
                    GsonUtil.toJson(request), GsonUtil.toJson(response), stopwatch.elapsed(TimeUnit.MILLISECONDS));

            // 返回成功结果
            return Result.success(response);
        } catch (Exception e) {
            // 记录错误日志，打印请求参数和异常信息
            log.error("DubboCouponServiceImpl.getCouponByTypeId has Error, request = {}, e = ", GsonUtil.toJson(request), e);
            // 返回异常结果
            return Result.fromException(e);
        }
    }

    private void setData(MemberConfigRequest request) {
        proMemberRedisDao.setPriceProtect(GsonUtil.toJson(request.getPriceProtect()));
        proMemberRedisDao.setPoints(GsonUtil.toJson(request.getPoints()));
    }


}
