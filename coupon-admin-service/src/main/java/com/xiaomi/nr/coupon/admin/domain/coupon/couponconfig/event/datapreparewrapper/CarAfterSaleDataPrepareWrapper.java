package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.datapreparewrapper;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.EventContext;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CarMaintenanceSsuInfo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.GoodsItemPo;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.CarAfterSaleQueryProxy;
import com.xiaomi.nr.goods.tob.dto.response.ssu.car.aftersale.CarMaintenanceSsuDTO;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/14
 */
@Component
public class CarAfterSaleDataPrepareWrapper extends BaseDataPrepareWrapper {

    @Resource
    private CarAfterSaleQueryProxy carAfterSaleQueryProxy;

    /**
     * 准备商品信息
     *
     * @param eventContext      后置事件处理器参数传递上下文
     * @param couponConfigPO    券配置po
     * @throws Exception        异常
     */
    @Override
    public void prepareGoodsInfo(EventContext eventContext, CouponConfigPO couponConfigPO) throws Exception {

        // 转换商品
        GoodsItemPo goodsItemPo = eventContext.getGoodsItemPo();

        // 查询工时ssu信息
        eventContext.setLabourHourSsuInfos(getCarMaintenanceSsuDTOList(goodsItemPo.getLabourHourSsu()));

        // 查询配件ssu信息
        eventContext.setPartsSsuInfos(getCarMaintenanceSsuDTOList(goodsItemPo.getPartsSsu()));
    }

    private List<CarMaintenanceSsuInfo> getCarMaintenanceSsuDTOList(Map<Long, Integer> carMaintenanceSsuMap) throws BizError {

        if (MapUtils.isEmpty(carMaintenanceSsuMap)) {
            return null;
        }

        List<CarMaintenanceSsuInfo> carMaintenanceInfoList = new ArrayList<>();
        List<Long> ssuIds = new ArrayList<>(carMaintenanceSsuMap.keySet());
        List<CarMaintenanceSsuDTO> labourHourSsuDtos = carAfterSaleQueryProxy.pageCarMaintenanceSsu(ssuIds);

        for (CarMaintenanceSsuDTO ssuDto : labourHourSsuDtos) {
            CarMaintenanceSsuInfo ssuInfo = new CarMaintenanceSsuInfo();
            BeanUtils.copyProperties(ssuDto, ssuInfo);
            ssuInfo.setCount(carMaintenanceSsuMap.getOrDefault(ssuDto.getSsuId(), 0));
            carMaintenanceInfoList.add(ssuInfo);
        }
        return carMaintenanceInfoList;
    }
}
