package com.xiaomi.nr.coupon.admin.enums.usercoupon;

import lombok.Getter;

/**
 * 优惠券状态 枚举
 *
 * <AUTHOR>
 */
@Getter
public enum UserCouponStatusEnum {

    /**
     * 优惠券已使用状态
     */
    USED("used", "已使用"),

    /**
     * 优惠券未使用状态
     */
    UNUSED("unused", "未使用"),

    /**
     * 优惠券已过期状态
     */
    EXPIRED("expired", "已过期"),

    /**
     * 优惠券已锁定状态（也是已使用的一种状态）
     */
    LOCKED("locked", "已锁定"),

    /**
     * 优惠券已作废状态
     */
    INVALID("invalid", "已作废"),

    /**
     * 优惠券已取消状态
     */
    CANCEL("cancel", "已取消"),

    /**
     * 优惠券已分享状态
     */
    PRESENTED("presented", "已分享"),

    /**
     * 优惠券分享已被领取状态
     */
    RECEIVED("received", "已领取"),

    /**
     * 优惠券已冻结状态
     */
    FREEZED("freezed","已冻结"),

    /**
     * 优惠券已分享+已领取状态
     */
    UNUSED_PRESENTED("unused_presented", "已分享+已领取"),

    /**
     * 优惠券已使用+已分享+已领取状态
     */
    USED_PRESENTED_RECEIVED("used_presented_received","已使用+已分享+已领取"),

    /**
     * 除作废（invalid）和取消（cancel）以外的券
     * 事实上是没有这个状态的，只是为了方便程序逻辑才这么设计了一个，后续可以优化掉它
     */
    ALL("all", "");
    private final String value;
    private final String name;

    UserCouponStatusEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

}

