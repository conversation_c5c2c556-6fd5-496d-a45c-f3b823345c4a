package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.posthandler;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.couponlog.CouponLogOptType;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponLogRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.optlog.po.CouponLogPO;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CouponOptLogPostHandler extends BaseCouponPostHandler{

    @Autowired
    private CouponLogRepository couponLogRepository;

    private final List<Integer> matchBizPlatform= Lists.newArrayList(
            BizPlatformEnum.RETAIL.getCode(),
            BizPlatformEnum.CAR.getCode(),
            BizPlatformEnum.CAR_AFTER_SALE.getCode(),
            BizPlatformEnum.CAR_SHOP.getCode()
    );

    @Override
    public void createPost(CouponCreateEvent event) {
        insertCouponLog(event.getIdempotentKey(),null, event.getData(),event.getData().getCreator(), CouponLogOptType.CREATE);
    }

    @Override
    public void updatePost(CouponUpdateEvent event) {
        insertCouponLog(event.getIdempotentKey(),event.getOldPo(), event.getData(),event.getOperator(), CouponLogOptType.UPDATE);
    }

    @Override
    public void updateStatusPost(CouponUpdateStatusEvent event) throws Exception {
        CouponConfigPO newPo = event.getData();

        CouponConfigStatusEnum couponConfigStatus = CouponConfigStatusEnum.findByCode(newPo.getStatus());
        CouponLogOptType optType = CouponLogOptType.convert2OptType(couponConfigStatus);

        insertCouponLog(event.getIdempotentKey(),event.getOldPo(), newPo, event.getOperator(),optType);
    }

    @Override
    public int order() {
        return 4;
    }

    @Override
    public Boolean bizPlatformMatch(Integer bizPlatform) {
        return matchBizPlatform.contains(bizPlatform);
    }

    /**
     * 记录log
     */
    private void insertCouponLog(String idempotentKey, CouponConfigPO originalCouponConfig, CouponConfigPO couponConfig, String operator, CouponLogOptType operateType) {
        CouponLogPO couponLog =  new CouponLogPO();
        couponLog.setConfigId(couponConfig.getId());
        couponLog.setReviewId(0L);
        couponLog.setOperationType(operateType.getCode());
        couponLog.setOperator(StringUtils.isBlank(operator) ? StringUtils.EMPTY : operator);
        couponLog.setIdempotentKey(idempotentKey);

        Map<String,Object> contentMap = new HashMap<>();
        try {
            switch (operateType){
                case CREATE:
                    contentMap.put("new", CompressUtil.compress(GsonUtil.toJson(couponConfig)));
                    break;
                case UPDATE:
                case ONLINE:
                case OFFLINE:
                case CANCEL:
                    contentMap.put("new", CompressUtil.compress(GsonUtil.toJson(couponConfig)));
                    contentMap.put("old", CompressUtil.compress(GsonUtil.toJson(originalCouponConfig)));

            }
        } catch (Exception e) {
            log.error("LogPostHandler insertCouponLog exception new:{},old:{}",couponConfig,originalCouponConfig,e);
        }

        couponLog.setOperationContent(GsonUtil.toJson(contentMap));
        try {
            couponLogRepository.insert(couponLog);
        } catch (DuplicateKeyException e) {
            log.error("insertCouponLog DuplicateKeyException couponLog:{}",couponLog);
        }
    }


}
