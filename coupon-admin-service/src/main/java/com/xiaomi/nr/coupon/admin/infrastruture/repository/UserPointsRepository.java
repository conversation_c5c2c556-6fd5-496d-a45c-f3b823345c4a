package com.xiaomi.nr.coupon.admin.infrastruture.repository;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.PointsStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.userpoint.UserPointsLogMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.userpoint.UserPointsMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.userpoint.po.UserPointsLogPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.userpoint.po.UserPointsPo;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/7
 */
@Component
@Slf4j
public class UserPointsRepository {

    @Resource
    private UserPointsMapper userPointsMapper;

    @Resource
    private UserPointsLogMapper userPointsLogMapper;



    /**
     * 获取批次发放记录总数
     *
     * @param mid     用户ID
     * @param batchId 批次ID
     * @return List
     */
    public Long getUserPointsCount(Long mid, Long batchId) {
        return userPointsMapper.getUserPointsCount(mid, batchId);
    }

    /**
     * 获取批次发放记录
     *
     * @param mid 用户ID
     * @param batchId 批次ID
     * @param limitStart 开始位置
     * @param pageSize 每页返回页数
     * @return List
     */

    public List<UserPointsPo> getUserPointsList(Long mid, Long batchId, int limitStart, int pageSize) {
        return userPointsMapper.getUserPointsList(mid, batchId, limitStart, pageSize);
    }

    /**
     * 获取批次积分发放列表
     * @param batchId
     * @return
     */
    public List<UserPointsPo> getBatchPointsList(Long batchId) {
        List<UserPointsPo> userPointsPoList = Lists.newArrayList();
        long lastId = 0;
        while (lastId >= 0) {
            List<UserPointsPo> recordList = userPointsMapper.getBatchPointsList(batchId, lastId, 50);
            if (CollectionUtils.isEmpty(recordList)) {
                break;
            }
            if (recordList.size() < 50) {
                lastId = -1;
            } else {
                lastId = recordList.get(recordList.size() - 1).getId();
            }
            userPointsPoList.addAll(recordList);
        }
        return userPointsPoList;
    }

    /**
     * 根据Id查询用户积分
     * @param mid
     * @param point
     * @return
     */
    public UserPointsPo getUserPointById(Long mid, Long point){
        return userPointsMapper.getUserPointById(mid, point);
    }


    public Long cancelUserPoints(Long mid, Long point){
        return userPointsMapper.cancelUserPoint(mid, point, PointsStatusEnum.INVALID.getCode(), TimeUtil.getNowUnixSecond());
    }

    public void addUserPointsLog(UserPointsLogPo userPointsLogPo){
        userPointsLogMapper.insert(userPointsLogPo);
    }


}
