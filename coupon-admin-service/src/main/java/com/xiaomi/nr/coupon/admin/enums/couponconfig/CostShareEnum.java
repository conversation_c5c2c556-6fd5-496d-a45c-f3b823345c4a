package com.xiaomi.nr.coupon.admin.enums.couponconfig;

import lombok.Getter;

@Getter
public enum CostShareEnum {

    COUPON_GOODS_BELONG(1, "","用券商品归属部门"),
    BALANCE_GOODS_BELONG (2, "", "申请补差商品归属部门"),
    MARKETING_DEPARTMENT(3, "dashich<PERSON><PERSON>", "大市场部"),
    SERVICE_DEPARTMENT(4, "service", "服务部"),
    HR_DEPARTMENT(5,  "","人力资源部"),
    TEST_DEPARTMENT(6, "", "测试组"),
    TIANXINGSHUKE(7, "tianxingshuke","天星数科"),
    COUPON_STAFF_BELONG(8,  "","用券员工所在部门"),
    OFFLINE_SUPERMARKETS(9,  "xianxia","线下商超"),
    INSURANCE_COMPANY(10, "baoxian", "保险公司"),
    SHANHUISHOU(11,  "shanhuishou","闪回收"),
    ZHUANZHUAN(12, "zhuanzhuan", "转转"),
    AIHUISHOU(13, "aihuishou", "爱回收"),
    ORDER_GOODS_BELONG(14, "", "赠券订单商品归属部门"),
    ;


    private final int value;
    private final String bpmValue;
    private final String name;

    CostShareEnum(int value,String bpmValue, String name) {
        this.value = value;
        this.bpmValue = bpmValue;
        this.name = name;
    }


    public static CostShareEnum getByValue(int value) {
        CostShareEnum[] values = CostShareEnum.values();
        for (CostShareEnum item : values) {
            if (item.getValue()==value) {
                return item;
            }
        }
        return null;
    }


}
