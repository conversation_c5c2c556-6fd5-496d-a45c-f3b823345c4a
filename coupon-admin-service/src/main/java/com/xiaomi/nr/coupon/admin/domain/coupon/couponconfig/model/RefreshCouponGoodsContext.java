package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model;

import lombok.Data;

import java.util.Map;
import java.util.Set;

@Data
public class RefreshCouponGoodsContext {

    /**
     * 券配置id列表
     */
    private Set<Long> configIds;

    /**
     * key:券id
     * value:三级类目id列表
     */
    private Map<Long, Set<Integer>> categoryIdMap;

    /**
     * key:券id
     * value：券类型(1:商品券 | 2:运费券 | 3:超级补贴券)
     */
    private Map<Long, Integer> couponTypeMap;

    /**
     * 老券对应的商品信息
     * key:券id
     * value:goodsInclude
     */
    private Map<Long, String> couponGoods;

    /**
     * 操作人
     */
    private String operator;

    public RefreshCouponGoodsContext(){}

    public RefreshCouponGoodsContext(Set<Long> configIds, Map<Long, Set<Integer>> categoryIdMap, Map<Long, Integer> couponTypeMap){
        this.configIds = configIds;
        this.categoryIdMap = categoryIdMap;
        this.couponTypeMap = couponTypeMap;
    }

    public RefreshCouponGoodsContext(Set<Long> configIds, Map<Long, Set<Integer>> categoryIdMap, Map<Long, Integer> couponTypeMap, Map<Long, String> couponGoods){
        this.configIds = configIds;
        this.categoryIdMap = categoryIdMap;
        this.couponTypeMap = couponTypeMap;
        this.couponGoods = couponGoods;
    }
}
