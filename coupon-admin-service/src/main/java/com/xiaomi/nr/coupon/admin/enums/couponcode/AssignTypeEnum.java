package com.xiaomi.nr.coupon.admin.enums.couponcode;

import com.xiaomi.nr.coupon.admin.api.enums.BizSubTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

/**
 * 外投方式枚举
 *
 * <AUTHOR>
 * @date 2024/6/26 16:14
 */
@Getter
@AllArgsConstructor
public enum AssignTypeEnum {
    /**
     * 1: 小米投放
     */
    XIAOMI(1, "小米投放"),

    /**
     * 2: 西瓜投放
     */
    XIGUA_MARKET(2, "西瓜投放"),
    ;
    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String desc;

    private static final HashMap<Integer, AssignTypeEnum> MAPPING = new HashMap<>();

    static {
        for (AssignTypeEnum e : AssignTypeEnum.values()) {
            MAPPING.put(e.getCode(), e);
        }
    }

    public static AssignTypeEnum valueOf(Integer code) {
        return MAPPING.get(code);
    }
}
