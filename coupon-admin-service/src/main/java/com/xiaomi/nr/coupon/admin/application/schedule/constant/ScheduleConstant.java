package com.xiaomi.nr.coupon.admin.application.schedule.constant;


/**
 * 定时任务常量
 *
 */
public class ScheduleConstant {

    /********************************************zk分布式锁路径*****************************************/

    /**
     * AppAuth缓存更新任务
     */
    public static final String APP_AUTH_UPDATE_CACHE_LOCK = "/cnzone/config/nr-mid-platform/coupon/lock/app_auth_update_cache_lock";

    /**
     * 商品与优惠券关系增量更新zk锁
     */
    public static final String GOODS_COUPON_RELATION_INCR_LOCK = "/cnzone/config/nr-mid-platform/coupon/lock/goods_coupon_relation_incr_lock";

    /**
     * 商品与优惠券关系全量更新zk锁
     */
    public static final String GOODS_COUPON_RELATION_FULL_LOCK = "/cnzone/config/nr-mid-platform/coupon/lock/goods_coupon_relation_full_lock";

    /**
     * 优惠券配置迁移到新库zk锁
     */
    public static final String COUPON_CONFIG_MOVE_GEN_LOCK = "/cnzone/config/nr-mid-platform/coupon/lock/coupon_config_move_gen_lock";

    /**
     * 优惠券任务配置迁移到新库zk锁
     */
    public static final String COUPON_CONFIG_MOVE_MISSION_GEN_LOCK = "/cnzone/config/nr-mid-platform/coupon/lock/coupon_config_move_mission_gen_lock";

    /**
     * 优惠券任务更新缓存zk锁
     */
    public static final String COUPON_MISSION_CACHE_GEN_LOCK = "/cnzone/config/nr-mid-platform/coupon/lock/coupon_mission_cache_gen_lock";

    /**
     * 优惠券补偿处理zk锁
     */
    public static final String COUPON_OPERATE_RECORD_LOCK = "/cnzone/config/nr-mid-platform/coupon/lock/coupon_operate_record_lock";

    /**
     * 优惠券新品提醒zk锁
     */
    public static final String COUPON_NEW_GOODS_NOTIFY_LOCK = "/cnzone/config/nr-mid-platform/coupon/lock/coupon_new_goods_notify_lock";


    /**
     * 优惠券配置缓存生成
     */
    public static final String COUPON_VALID_LIST_CACHE_LOCK = "/cnzone/config/nr-mid-platform/coupon/lock/coupon_valid_cache_gen_lock";


    /**
     * 优惠券配置缓存生成
     */
    public static final String COUPON_CONFIG_CACHE_GEN_PATH = "/cnzone/config/nr-mid-platform/coupon/coupon_config_cache_gen_lock";


    /**
     * 小米商城领券活动缓存任务
     */
    public static final String MI_SHOP_COUPON_ACTIVITY_CACHE_GEN_PATH = "/cnzone/config/nr-mid-platform/coupon/lock/mi_shop_coupon_activity_cache_gen_lock";


    /********************************************zk监控节点路径*****************************************/

    /**
     * 优惠券配置缓存更新记录
     */
    public static final String COUPON_CONFIG_CHANGE_RECORD_PATH = "/cnzone/config/nr-mid-platform/coupon/record/coupon_config_change_record";

    /**
     * 运费券即将过期push的zk锁
     */
    public static final String COUPON_POSTFEE_EXPIRE_PUSH_LOCK = "/cnzone/config/nr-mid-platform/coupon/lock/postfee_coupon_expire_push_lock";

    /**
     * 品类券适用商品更新zk锁
     */
    public static final String UPDATE_COUPON_GOODS_REFRESH_LOCK = "/cnzone/config/nr-mid-platform/coupon/lock/update_coupon_goods_refresh_lock";

}
