package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

/**
 * @Description: 优惠吗
 * @Date: 2022.03.11 0:24
 */
@Mapper
@Component
public interface CouponCodeMapper {

    /**
     * 更新优惠券的同步状态
     *
     * @param couponIndex 优惠券索引
     * @param couponCode 优惠券代码
     * @return 更新的行数
     */
    @Update("<script>" +
            "update tb_codecoupon set sync_status = 1 where sync_status = 0 and coupon_index = #{couponIndex} and coupon_code = #{couponCode}" +
            "</script>")
    int updateSyncStatus(@Param("couponIndex") String couponIndex, @Param("couponCode") String couponCode);


}
