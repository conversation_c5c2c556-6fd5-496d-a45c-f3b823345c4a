package com.xiaomi.nr.coupon.admin.domain.coupon.couponcode;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.fds.FileService;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.DownLoadCouponCodePO;
import com.xiaomi.nr.coupon.admin.util.FileUtils;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

/**
 * 优惠码服务
 */
@Service
@Slf4j
public class CouponCodeService {

    @Autowired
    private FileService fileService;

    /**
     * 优惠码上传fds
     * @param codeList 优惠码信息
     * @param configId 券配置id
     * @return 地址
     * @throws Exception
     */
    public String uploadCouponCode(List<DownLoadCouponCodePO> codeList, long configId) throws Exception {

        String fileName = "优惠码-" + configId+"-" + TimeUtil.getNowUnixSecond();

        File file = new File(fileName);

        FileUtils.writeExcelFile(file,"优惠码", codeList, DownLoadCouponCodePO.class);

        fileService.uploadFileToFds(getUploadCodeObjectName(fileName), file, true);

        return getUploadCouponCodeFdsUrl(fileName);
    }

    public String getUploadCouponCodeFdsUrl(String fileName){
        return "https://" + fileService.getEndpoint() + File.separator + fileService.getBucketName() + File.separator + getUploadCodeObjectName(fileName);
    }

    private String getUploadCodeObjectName(String fileName){
        return "coupon/uploadCoupon/code/"+fileName+".xlsx";
    }
}
