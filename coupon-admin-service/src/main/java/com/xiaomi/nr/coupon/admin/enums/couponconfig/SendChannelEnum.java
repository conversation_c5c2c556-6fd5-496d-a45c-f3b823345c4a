package com.xiaomi.nr.coupon.admin.enums.couponconfig;

/**
 * 优惠券发放渠道 枚举
 *
 * <AUTHOR>
 */
public enum SendChannelEnum {

    /**
     * 店长券活动
     */
    StoreManager("store_manager","7C0938DFA6023303195221E669AFDE24"),

    /**
     * 门店下单赠券活动
     */
    StoreOrderGift("store_order_gift", "0FBBAE2FD347172EFF6669E3BA1F82D4"),

    /**
     * 异业券活动
     */
    DiffBusiness("diff_business","504E45C3444F637331C8E6995F5725BE"),

    /**
     * 测试券渠道
     */
    Test("test", "DCCC8C085E79839661D9B93E9A996EC8"),

    /**
     * 其他
     */
    Others("other",  "");

    private final String value;
    private final String code;

    SendChannelEnum(String value, String code) {
        this.value = value;
        this.code = code;
    }


    public String getValue() {
        return value;
    }


    public String getCode() {
        return code;
    }

    public static SendChannelEnum findByCode(String code) {
        SendChannelEnum[] values = SendChannelEnum.values();
        for (SendChannelEnum item : values) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return Others;
    }

    public static SendChannelEnum findByValue(String value) {
        SendChannelEnum[] values = SendChannelEnum.values();
        for (SendChannelEnum item : values) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }

}

