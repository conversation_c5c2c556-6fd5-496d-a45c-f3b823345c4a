package com.xiaomi.nr.coupon.admin.domain.coupon.fillcoupontask;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponTaskListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponTaskVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CreateFillCouponTaskRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.ReStartTaskRequest;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.enums.task.DistinctEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.coupon.CouponConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.coupon.ZkPathConstant;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.task.CustomizeGroupEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponTaskRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.hdfs.HdfsHelper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.FillCouponTaskPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.Param;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.ResultOutPut;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.SearchTaskParam;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FillCouponService {

    @Autowired
    private CouponTaskRepository fillCouponTaskRepository;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private FillCouponConvert fillCouponConvert;

    @Autowired
    private HdfsHelper hdfsHelper;

    @Autowired
    private DownLoadHelper downLoadHelper;

    /**
     * 灌券人群数据异步写入HDFS
     */
    @Autowired
    @Qualifier("insertHdfsAsyncExecutor")
    private Executor insertHdfsAsyncExecutor;



    /**
     * 查询灌券任务列表
     *
     * @param searchParam 查询参数
     * @return 灌券任务信息
     */
    public List<CouponTaskListVO> searchFillCoupon(SearchTaskParam searchParam, BasePageResponse<CouponTaskListVO> response) {

        //判断是否需要查询券id
        String couponName = searchParam.getCouponName();
        if (StringUtils.isNotEmpty(couponName) || searchParam.getCouponType() != null) {
            List<Long> configIds = couponConfigRepository.getCouponIdByNameAndType(couponName, searchParam.getCouponType(), searchParam.getBizType());
            if (CollectionUtils.isNotEmpty(searchParam.getConfigIds())) {
                configIds = (List<Long>) CollectionUtils.intersection(searchParam.getConfigIds(), configIds);
            }
            if (CollectionUtils.isEmpty(configIds)) {
                return Collections.emptyList();
            }
            searchParam.setConfigIds(configIds);
        }

        PageHelper.startPage(searchParam.getPageNo(), searchParam.getPageSize());
        List<FillCouponTaskPO> parentTasks = fillCouponTaskRepository.searchTaskList(searchParam);
        if (CollectionUtils.isEmpty(parentTasks)) {
            log.info("searchFillCouponTaskList parentIds is empty. request:{}", searchParam);
            return Collections.emptyList();
        }

        List<Long> parentIds = parentTasks.stream().map(FillCouponTaskPO::getTaskId).collect(Collectors.toList());
        List<Long> configIds = parentTasks.stream().map(FillCouponTaskPO::getConfigId).collect(Collectors.toList());

        //获取子任务
        List<FillCouponTaskPO> childTasks = fillCouponTaskRepository.searchTaskByParentIds(parentIds);
        Map<Long, FillCouponTaskPO> idToChildTask = childTasks.stream().collect(Collectors.toMap(FillCouponTaskPO::getTaskId, Function.identity(), (before, after) -> before));

        //构建父任务与子任务映射关系
        Map<Long, List<Long>> parentIdToChildTask = new HashMap<>();
        for (FillCouponTaskPO item : idToChildTask.values()) {

            long parentId = item.getParentId();
            long childId = item.getTaskId();

            if (!parentIdToChildTask.containsKey(parentId)) {
                parentIdToChildTask.put(parentId, Lists.newArrayList());
            }
            parentIdToChildTask.get(parentId).add(childId);
        }

        //获取券配置信息
        List<CouponConfigPO> couponConfigs = couponConfigRepository.searchCouponListById(configIds);
        Map<Long, CouponConfigPO> idToCouponConfig = couponConfigs.stream().collect(Collectors.toMap(CouponConfigPO::getId, Function.identity(), (before, after) -> before));

        PageInfo<FillCouponTaskPO> pageInfo = new PageInfo<>(parentTasks);
        response.setTotalCount(pageInfo.getTotal());
        response.setTotalPage(pageInfo.getPages());
        //封装返回值
        return fillCouponConvert.buildTaskVOs(parentTasks, idToChildTask, parentIdToChildTask, idToCouponConfig);
    }


    /**
     * 创建灌券任务流程
     *
     * @param request 任务参数
     * @throws Exception io异常
     */
    public Long createFillCouponTask(CreateFillCouponTaskRequest request) throws BizError {

        //校验券可用性
        CouponConfigPO couponConfig = checkCouponConfig(request.getConfigId());

        if(request.getBizType() == null || request.getBizType() == 0) {
            request.setBizType(couponConfig.getBizPlatform());
        }

        upLoadFillData(request);

        //封装taskPO
        FillCouponTaskPO taskPO = fillCouponConvert.convertFillCouponTaskPO(request);

        //落库
        fillCouponTaskRepository.insert(taskPO);

        log.info("createFillCouponTask end. marketTask:{}", taskPO);
        return taskPO.getTaskId();
    }

    private void upLoadFillData(CreateFillCouponTaskRequest request) {
        if (request.getBizType() != BizPlatformEnum.CAR_AFTER_SALE.getCode()) {
            return;
        }
        String fileName = CustomizeGroupEnum.CLIENT_INPUT.getPrefix() + TimeUtil.getNowUnixMillis();
        List<String> dataList = Lists.newArrayList(request.getDataList());
        if(request.getDistinct() == DistinctEnum.DEDUPLICATION.getCode()){
            dataList = dataList.stream().distinct().collect(Collectors.toList());
        }
        try {
            downLoadHelper.uploadDataToHDFSAsync(dataList, fileName);
        } catch (Exception e) {
            log.info("上传数据集失败：fileName:{}", fileName, e);
        }
        request.setHdfsAddr(ZkPathConstant.FILL_COUPON_UPLOAD_PATH + fileName);
        request.setApplyCount(Long.valueOf(request.getDataList().size()));
    }


    /**
     * 查看灌券详情逻辑
     *
     * @param taskId 灌券任务id
     * @return CouponTaskVO
     */
    public CouponTaskVO taskDetail(long taskId) throws Exception {

        FillCouponTaskPO taskPO = fillCouponTaskRepository.getDetailTaskById(taskId);
        if(Objects.isNull(taskPO)){
            throw ExceptionHelper.create(ErrCode.COUPON, "灌券任务不存在");
        }

        //获取子任务
        List<FillCouponTaskPO> childTasks = fillCouponTaskRepository.searchTaskByParentIds(Collections.singletonList(taskId));

        CouponConfigPO couponPO = couponConfigRepository.searchCouponById(taskPO.getConfigId());

        return fillCouponConvert.convertCouponTaskVO(taskPO, couponPO.getName(), childTasks);

    }


    /**
     * 灌券任务重试逻辑
     *
     * @param request 重试参数
     * @throws Exception biz & io
     */
    public void retryFillCouponTask(ReStartTaskRequest request, String account) throws Exception {

        //获取父任务
        FillCouponTaskPO parentTask = fillCouponTaskRepository.getDetailTaskById(request.getTaskId());
        if (Objects.isNull(parentTask)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "灌券任务不存在");
        }

        Param param = GsonUtil.fromJson(parentTask.getParams(), Param.class);
        ResultOutPut resultOutPut = GsonUtil.fromJson(parentTask.getResult(), ResultOutPut.class);
        checkCouponConfig(parentTask.getConfigId());
        if(param.getApplyCount() > param.getCount() && param.getCount() == resultOutPut.getSuccessCount()){
            throw ExceptionHelper.create(ErrCode.COUPON, "当前成功率低于100%原因为任务申请发放数量大于用户数量，当前用户已全部灌券成功，无需重试");
        }

        List<FillCouponTaskPO> childTasks = fillCouponTaskRepository.getCouponTaskByParentId(request.getTaskId());
        long lastChildTaskId = parentTask.getTaskId();
        if (CollectionUtils.isNotEmpty(childTasks)) {
            //重试次数限制
            if (CouponConstant.FILL_COUPON_RETRY_TIMES < childTasks.size()) {
                log.warn("retryFillCouponTask more than retry times. request:{},size:{}", request, childTasks.size());
                throw ExceptionHelper.create(ErrCode.COUPON, "灌券任务超过重试次数");
            }

            lastChildTaskId = childTasks.get(CommonConstant.ZERO_INT).getTaskId();
        }

        //包装子任务
        FillCouponTaskPO childTask = fillCouponConvert.buildChildTask(parentTask, lastChildTaskId, account);

        //插入子任务
        fillCouponTaskRepository.insert(childTask);

        // 删除父任务灌券失败详情
        String hdfsPath = ZkPathConstant.FILL_COUPON_DETAIL_PATH + request.getTaskId();
        URI fileUri = hdfsHelper.getFileUri(hdfsPath);
        hdfsHelper.removeHDFSFile(fileUri, hdfsPath);

    }



    /**
     * 检验券信息
     *
     * @param configId   券配置id
     * @throws BizError  业务异常
     */
    private CouponConfigPO checkCouponConfig(Long configId) throws BizError {
        CouponConfigPO couponConfig = couponConfigRepository.searchCouponById(configId);
        if (couponConfig == null) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券不存在");
        }

        if (couponConfig.getStatus() != CouponConfigStatusEnum.ONLINE.getCode()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券活动未上线或已经终止");
        }

        if (couponConfig.getEndFetchTime() < TimeUtil.getNowUnixSecond()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券领取已结束");
        }
        //判断库存
        Map<Long, Long> sendCountMap = couponConfigRepository.getCouponSendCount(Collections.singletonList(configId));
        if (couponConfig.getApplyCount() <= sendCountMap.get(configId)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券库存为0");
        }
        return couponConfig;
    }


    /**
     * 生成人群数据文件名称
     * @param customizeType
     * @return
     */
    public String bulidFileName(int customizeType){
        if (Objects.equals(CustomizeGroupEnum.CLIENT_INPUT.getCode(), customizeType)) {
            return CustomizeGroupEnum.CLIENT_INPUT.getPrefix() + TimeUtil.getNowUnixMillis();
        }

        if (Objects.equals(CustomizeGroupEnum.UPLOAD_FILE.getCode(), customizeType)) {
            return CustomizeGroupEnum.UPLOAD_FILE.getPrefix() + TimeUtil.getNowUnixMillis();
        }
        return StringUtils.EMPTY;
    }

}
