package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.oldcoupon;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 券基本信息老缓存实体
 *
 * @date 2021/3/24
 */
@Data
public class TypeBase implements Serializable {

    /**
     * 优惠券编号
     */
    private Long id = 0L;

    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    @SerializedName("coupon_type")
    private Integer couponType = 1;

    /**
     * 履约方式 -1: 所有方式 139: 门店闪送 (这个跟交易中台统一定义)
     */
    @SerializedName("shipment_id")
    private Integer shipmentId = -1;

    /**
     * 优惠类型 (1:满减, 2:满折, 3:N元券, 4:立减)
     */
    @SerializedName("type")
    private Integer type = 0;

    /**
     * 审核状态
     */
    private String stat = "";

    /**
     * 抵扣类型：0-0元抵扣，1-0.01元抵扣
     */
    @SerializedName("deduct_type")
    private Integer deductType = 0;

    /**
     * 优惠券类型名称
     */
    private String name = "";

    /**
     * 活动类型描述
     */
    private String desc;

    /**
     * 范围描述
     */
    @SerializedName("range_desc")
    private String rangeDesc = "";

    /**
     * 范围长描述
     */
    @SerializedName("range_long_desc")
    private String rangeLongDesc;

    /**
     * 值描述
     */
    @SerializedName("value_desc")
    private String valueDesc;

    /**
     * 展示标题
     */
    @SerializedName("show_title")
    private String showTitle = "";

    /**
     * 展示单位
     */
    @SerializedName("show_unit")
    private String showUnit;
    /**
     * 类型码：
     * reduction-满减，gift-赠品，postFree-免邮，discount-折扣，sale-加价购
     */
    @SerializedName("type_code")
    private String typeCode = "";

    /**
     * 是否分享
     */
    @SerializedName("is_share")
    private Integer isShare = 2;

    /**
     * 是否包邮
     */
    @SerializedName("postfree")
    private Integer postFree = 2;

    /**
     * 是否会员券
     */
    @SerializedName("pro_member")
    private Integer proMember = 2;

    /**
     * 是否会员券
     */
    @SerializedName("special_store")
    private Integer specialStore = 2;

    /**
     * 政策描述列表
     */
    @SerializedName("desc_policy")
    private List<String> descPolicy;

    /**
     * 线上线下
     */
    @SerializedName("offline")
    private Integer offline;

    /**
     * 是否码券
     */
    @SerializedName("is_code")
    private int code;

    /**
     * 开始时间
     */
    @SerializedName("global_start_time")
    private long globalStartTime;

    /**
     * 结束时间
     */
    @SerializedName("global_end_time")
    private long globalEndTme;

    /**
     * 使用开始时间
     */
    @SerializedName("global_use_start_time")
    private long globalUseStartTime;

    /**
     * 使用结束时间
     */
    @SerializedName("global_use_end_time")
    private long globalUseEndTme;

    /**
     * 业务渠道  0:3c业务 3:汽车业务 4:汽车服务
     */
    @SerializedName("biz_platform")
    private Integer bizPlatform;
}
