package com.xiaomi.nr.coupon.admin.application.dubbo.couponadmin;

import com.google.common.base.Stopwatch;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.BizPlatformVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.bizPlatform.response.BizPlatformListResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboBizPlatformService;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 业务平台
 *
 * <AUTHOR>
 */
@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
@Component
@Slf4j
public class DubboBizPlatformServiceImpl implements DubboBizPlatformService {

    @Override
    public Result<BizPlatformListResponse> queryList() {

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            BizPlatformListResponse resp = new BizPlatformListResponse();
            List<BizPlatformVO> bizPlatformList = Arrays.stream(BizPlatformEnum.values())
                    .map(e -> new BizPlatformVO(e.getCode(), e.getDesc()))
                    .collect(Collectors.toList());
            resp.setBizPlatformList(bizPlatformList);
            log.info("DubboBizPlatformServiceImpl.queryList execute success, runTime={}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return Result.success(resp);
        } catch (Exception e){
            log.error("DubboBizPlatformServiceImpl.queryList error: ", e);
            return Result.fromException(e);
        }finally {
            stopwatch.stop();
        }

    }
}
