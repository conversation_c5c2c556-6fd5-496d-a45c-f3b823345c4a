package com.xiaomi.nr.coupon.admin.application.dubbo.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointBatchLogDto;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointLogRequest;
import com.xiaomi.nr.coupon.admin.api.service.pointadmin.DubboPointLogService;
import com.xiaomi.nr.coupon.admin.application.dubbo.pointadmin.convert.PointLogConvert;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CarPointsConfigLogRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsConfigLogPo;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 积分场景
 * @Date: 2022.03.03 17:03
 */
@Component
@Slf4j
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
public class DubboPointLogServiceImpl implements DubboPointLogService {

    @Autowired
    private CarPointsConfigLogRepository carPointsConfigLogRepository;

    @Autowired
    private PointLogConvert pointLogConvert;


    @Override
    public Result<BasePageResponse<PointBatchLogDto>> queryLogList(PointLogRequest request) {
        BasePageResponse basePageResponse = new BasePageResponse<>();
        try {
            log.info("DubboPointLogService.queryLogList begin request:{}", request);
            int totalCount = carPointsConfigLogRepository.getTotalByBatchId(request.getBatchId());
            List<CarPointsConfigLogPo> carPointsConfigLogPos = carPointsConfigLogRepository.getLogPageByBatch(request.getBatchId(), (request.getPageNo() - 1) * request.getPageSize(), request.getPageSize());
            basePageResponse.setPageNo(request.getPageNo());
            basePageResponse.setPageSize(request.getPageSize());
            basePageResponse.setTotalCount(totalCount);
            basePageResponse.setTotalPage(totalCount / request.getPageSize() + (totalCount % request.getPageSize() == 0 ? 0 : 1));
            basePageResponse.setList(pointLogConvert.convertToDto(carPointsConfigLogPos));
            return Result.success(basePageResponse);
        } catch (Exception e) {
            log.error("DubboPointLogService.queryLogList error request:{}", request, e);
            return Result.fromException(e);
        }
    }
}
