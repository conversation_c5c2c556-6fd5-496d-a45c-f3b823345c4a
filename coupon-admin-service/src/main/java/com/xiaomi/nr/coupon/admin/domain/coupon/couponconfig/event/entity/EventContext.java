package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity;

import com.xiaomi.goods.gms.dto.batch.BatchedInfoDto;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CarMaintenanceSsuInfo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.GoodsItemPo;
import com.xiaomi.nr.goods.tob.dto.response.ssu.SsuDTO;
import com.xiaomi.nr.goods.tob.dto.response.ssu.car.aftersale.CarMaintenanceSsuDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 后置事件处理器参数传递上下文
 * @author: hejiapeng
 * @Date 2022/3/30 9:49 上午
 * @Version: 1.0
 **/
@Data
public class EventContext implements Serializable {

    /**
     * 券配置原始商品
     */
    private GoodsItemPo goodsItemPo;

    /**
     * 货品列表
     */
    private List<SkuInfoDto> skuInfoDtos;

    /**
     * 套装列表
     */
    private List<BatchedInfoDto> batchedInfoDtos;

    /**
     * ssu列表
     */
    private List<SsuDTO> ssuDtos;

    /**
     * 工时ssu列表
     */
    private List<CarMaintenanceSsuInfo> labourHourSsuInfos;

    /**
     * 配件ssu列表
     */
    private List<CarMaintenanceSsuInfo> partsSsuInfos;
}
