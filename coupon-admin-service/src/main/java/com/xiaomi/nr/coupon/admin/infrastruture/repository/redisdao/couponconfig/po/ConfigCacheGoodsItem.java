package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po;

import lombok.Data;

import java.io.Serializable;

/**
 * 可用商品的单个item
 *
 * <AUTHOR>
 */
@Deprecated
@Data
public class ConfigCacheGoodsItem implements Serializable {

    private static final long serialVersionUID = -8489433182174447467L;

    /**
     * SKU或套装ID
     */
    private Long id;

    /**
     * 品级，sku代表SKU，package代表是套装
     */
    private String level;

    /**
     * 产品ID列表
     */
    private Long pid;
}