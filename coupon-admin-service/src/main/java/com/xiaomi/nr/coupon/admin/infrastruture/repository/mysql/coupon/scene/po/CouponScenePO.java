package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description: 优惠券场景
 * @Date: 2022.03.03 14:22
 */
@Data
public class CouponScenePO {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 场景ID生成方式 1:自动生成, 2:手动生成
     */
    private Integer idGenerationType;
    /**
     * 投放场景名称
     */
    private String name;
    /**
     * 关联场景ID 1:官方营销活动, 2:外部异业合作, 3:售后服务, 4:其他场景
     */
    private Integer relationSceneId;
    /**
     * 投放场景编码
     */
    private String sceneCode;
    /**
     * 场景描述
     */
    private String sceneDesc;
    /**
     * 投放方式 1:优惠券, 2:兑换码
     */
    private Integer sendMode;
    /**
     * 发放方式(以逗号分隔) 1:外部系统发券, 2:内部系统灌券
     */
    private String assignMode;
    /**
     * 可用状态 1:上线 2:下线
     */
    private Integer status;
    /**
     * 优惠券类型，(1:商品券｜2:运费券)
     */
    private String couponType;
    /**
     * 申请备注
     */
    private String applyMark;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人邮箱
     */
    private String modifier;
    /**
     * 修改时间
     */
    private Long updateTime;

    /**
     * 特殊规则  包邮 postFree,转增 share,指定区域 area,专店专用 specialStore
     */
    private String extProps;

    /**
     * 优惠券所属业务平台 @BizPlatformEnum
     */
    private Integer bizPlatform;
}
