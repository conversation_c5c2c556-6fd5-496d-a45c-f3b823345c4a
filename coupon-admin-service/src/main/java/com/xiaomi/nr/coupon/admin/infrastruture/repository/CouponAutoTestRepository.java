package com.xiaomi.nr.coupon.admin.infrastruture.repository;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.CouponConfigCheckService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneSendModeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.es.CouponInvertedTOCHelper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.es.po.CouponEsPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.CouponConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.CouponSceneMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.CouponTaskConvert;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.FillCouponTaskMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.CouponConfigRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.OldCouponRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigGoodsCachePo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigInfoCachePo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.NewConfigGoodsCachePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

/**
 * @description: 优惠券自动化测试资源库
 * @author: hejiapeng
 * @Date 2022/3/1 10:42 上午
 * @Version: 1.0
 **/
@Component
@Slf4j
public class CouponAutoTestRepository {

    @Autowired
    private CouponConfigMapper couponConfigMapper;

    @Autowired
    private CouponConfigRedisDao couponConfigRedisDao;

    @Autowired
    private CouponInvertedTOCHelper couponInvertedTOCHelper;

    @Autowired
    private OldCouponRedisDao oldCouponRedisDao;

    @Autowired
    private FillCouponTaskMapper fillCouponTaskMapper;

    @Autowired
    private CouponSceneMapper couponSceneMapper;

    @Autowired
    private CouponConfigCheckService couponConfigCheckService;


    /**
     * 保存优惠券,自动化测试使用
     *
     * @return
     */
    @Transactional(transactionManager = "xmPulseCouponTransactionManager", rollbackFor = {Exception.class, Error.class})
    public long insert(CouponConfigPO couponConfigPO) throws Exception {
        couponConfigMapper.insert(couponConfigPO);

        CouponScenePO couponScenePO = couponSceneMapper.selectBySceneCode(couponConfigPO.getSendScene());
        if (SceneSendModeEnum.COUPON_CODE.getCode() == couponScenePO.getSendMode()) {
            fillCouponTaskMapper.insert(CouponTaskConvert.convertFillCouponTaskPO(couponConfigPO));
        }

        CouponCreateEvent event =new CouponCreateEvent();
        event.setData(couponConfigPO);
        event.setBizPlatform(BizPlatformEnum.AUTO_TEST.getCode());
        event.setIdempotentKey(couponConfigPO.getId() + "_" + event.getTime() + "_" + UUID.randomUUID());

        couponConfigCheckService.handleCreateEvent(BizPlatformEnum.AUTO_TEST, event);

        return couponConfigPO.getId();
    }

    /**
     * 删除优惠券,自动化测试使用
     *
     * @return
     */
    @Transactional(transactionManager = "xmPulseCouponTransactionManager", rollbackFor = {Exception.class, Error.class})
    public void delete(List<Long> configIds) throws Exception {
        couponConfigMapper.deleteCoupon(configIds);

        oldCouponRedisDao.deleteOldCouponInfo(configIds);
        ConfigInfoCachePo configInfoCachePo =new ConfigInfoCachePo();
        ConfigGoodsCachePo couponGoodsCachePo =new ConfigGoodsCachePo();
        NewConfigGoodsCachePo newConfigGoodsCachePo =new NewConfigGoodsCachePo();
        CouponEsPO couponEsPO =new CouponEsPO();
        for (Long configId:configIds) {
            configInfoCachePo.setId(configId.intValue());
            couponGoodsCachePo.setConfigId(configId);
            newConfigGoodsCachePo.setConfigId(configId);
            couponEsPO.setId(configId);
            couponConfigRedisDao.setConfigInfoCache(configInfoCachePo, 1L);
            couponConfigRedisDao.setCouponGoodsCache(couponGoodsCachePo, 1L);
            couponConfigRedisDao.setCouponGoodsCacheV2(newConfigGoodsCachePo,1L);
            couponInvertedTOCHelper.saveCouponConfig(couponEsPO);
        }
    }

}
