package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.posthandler;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;

/**
 * 后置处理器基础类，所有后置处理器继承改类型
 *
 * <AUTHOR>
 */
public abstract class BaseCouponPostHandler {

    public abstract void createPost(CouponCreateEvent event) throws Exception;

    public abstract void updatePost(CouponUpdateEvent event) throws Exception;

    public abstract void updateStatusPost(CouponUpdateStatusEvent event) throws Exception;

    /**
     * 执行器顺序
     * @return
     */
    public abstract int order();


    public abstract Boolean bizPlatformMatch(Integer bizPlatform);
}
