package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po;

import lombok.Data;

import java.io.Serializable;

/**
 * 灌券任务实体
 */
@Data
public class FillCouponTaskPO implements Serializable {
    private static final long serialVersionUID = -4842376746318140710L;

    /**
     * 任务id
     */
    private long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 状态 (0等待,1就绪,2运行中,3失败,4成功,5错误,-1撤销)
     */
    private int status;

    /**
     * 任务类型 (11:优惠码，21:灌券)
     */
    private int type;

    /**
     * 优先级
     */
    private int priority;

    /**
     * 父任务ID
     */
    private long parentId;

    /**
     * 任务参数
     */
    private String params;

    /**
     * 任务来源
     */
    private String source;

    /**
     * 执行位置
     */
    private long offset;

    /**
     * 任务进度
     */
    private long processRate;

    /**
     * 进度描述
     */
    private String processDesc;

    /**
     * 任务创建时间
     */
    private long createTime;

    /**
     * 任务开始时间
     */
    private long startTime;

    /**
     * 任务结束时间
     */
    private long finishTime;

    /**
     * 执行结果
     */
    private String result;

    /**
     * 优惠券配置id
     */
    private long configId;

    /**
     * 任务更新时间
     */
    private long updateTime;

    /**
     * 重试次数
     */
    private int retryTimes;

    /**
     * 报警状态 (0:无需报警, 2:已报警)
     */
    private int alarmStatus;

    /**
     * 创建人归属部门
     */
    private String departmentId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 幂等版本号
     */
    private long version;

    /**
     * 业务平台  0:新零售  3:汽车 4:汽车售后 @BizPlatformEnum
     */
    private Integer bizPlatform;

    public void setTaskId(long taskId){
        this.taskId = taskId;
    }

}
