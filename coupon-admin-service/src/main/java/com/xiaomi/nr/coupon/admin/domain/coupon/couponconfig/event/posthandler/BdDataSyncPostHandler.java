package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.posthandler;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.infrastruture.nacosconfig.NacosSwitchConfig;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.BdCouponSyncRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BdDataSyncPostHandler extends BaseCouponPostHandler{

    @Autowired
    private BdCouponSyncRepository bdCouponSyncRepository;

    @Autowired
    private NacosSwitchConfig nacosSwitchConfig;



    @Override
    public void createPost(CouponCreateEvent event) {
        if(nacosSwitchConfig.isStopWriteBdData()){
            return;
        }
        // 保证幂等
        if(bdCouponSyncRepository.checkMallMarketCoupon(event.getData().getId())){
            return;
        }
        bdCouponSyncRepository.insertMallMarketCoupon(event.getData(), event.getEventContext());
        log.info("BdDataSyncPostHandler.createPost success, 写入b.d券数据成功, configId:{}", event.getData().getId());
    }

    @Override
    public void updatePost(CouponUpdateEvent event) {
        if(nacosSwitchConfig.isStopWriteBdData()){
            return;
        }
        bdCouponSyncRepository.updateMallMarketCoupon(event.getData(), event.getEventContext());
        log.info("BdDataSyncPostHandler.updatePost success, 更新b.d券数据成功, configId:{}", event.getData().getId());

    }



    @Override
    public void updateStatusPost(CouponUpdateStatusEvent event) {
        if(nacosSwitchConfig.isStopWriteBdData()){
            return;
        }
        bdCouponSyncRepository.updateMallMarketCouponStatus(event.getData());
        log.info("BdDataSyncPostHandler.updateStatusPost success, 更新b.d券数据成功, configId:{}", event.getData().getId());
    }

    @Override
    public int order() {
        return 2;
    }

    @Override
    public Boolean bizPlatformMatch(Integer bizPlatform) {
        return BizPlatformEnum.RETAIL.getCode().equals(bizPlatform);
    }

}
