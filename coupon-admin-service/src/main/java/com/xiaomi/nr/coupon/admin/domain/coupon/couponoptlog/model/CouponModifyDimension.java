package com.xiaomi.nr.coupon.admin.domain.coupon.couponoptlog.model;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.ExtPropVO;
import com.xiaomi.nr.coupon.admin.infrastruture.annotation.FieldsAlias;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.UseChannelPO;
import lombok.Data;

import java.util.Map;
import java.util.Set;

/**
 * @Description: 券修改范围
 * @Date: 2022.03.10 22:00
 */
@Data
public class CouponModifyDimension {
    /**
     * 优惠券类型名称
     */
    @FieldsAlias(alias = "优惠券名称")
    private String name;

    /**
     * 状态 (1:上线, 2:下线, 3:终止)  -- 前端不对比
     */
    @FieldsAlias(alias = "优惠券状态", dimension = 100)
    private Integer status;

    /**
     * 优惠券描述
     */
    @FieldsAlias(alias = "优惠券描述")
    private String couponDesc;

    /**
     * 优惠类型 (1:满减, 2:满折, 3:N元券, 4:立减)
     */
    @FieldsAlias(alias = "优惠类型", dimension = 100)
    private Integer promotionType;

    /**
     * 门槛类型 (1:满元, 2:满件, 3:每满元, 4:每满件, 5:满元且满件)
     */
    @FieldsAlias(alias = "门槛类型", dimension = 100)
    private Integer bottomType;

    /**
     * 满元门槛值 (单位分)
     */
    @FieldsAlias(alias = "满元门槛值", dimension = 100)
    private Long bottomPrice;

    /**
     * 满件门槛值 (单位个)
     */
    @FieldsAlias(alias = "满件门槛值", dimension = 100)
    private Integer bottomCount;

    /**
     * 优惠值 (单位分/折)
     */
    @FieldsAlias(alias = "优惠值", dimension = 100)
    private Long promotionValue;

    /**
     * 最大减免金额 (单位分)
     */
    @FieldsAlias(alias = "最大减免金额", dimension = 100)
    private Long maxReduce;

    /**
     * 投放场景
     */
    @FieldsAlias(alias = "投放场景")
    private String sendScene;

    /**
     * 投放目的
     */
    @FieldsAlias(alias = "投放目的", dimension = 100)
    private Integer sendPurpose;

    /**
     * 开始领取时间
     */
    @FieldsAlias(alias = "开始领取时间", dimension = 100)
    private Long startFetchTime;

    /**
     * 结束领取时间
     */
    @FieldsAlias(alias = "结束领取时间", dimension = 100)
    private Long endFetchTime;

    /**
     * 使用有效期类型 (1:固定有效, 2:相对有效期)
     */
    @FieldsAlias(alias = "使用有效期类型", dimension = 100)
    private Integer useTimeType;

    /**
     * 开始使用时间
     */
    @FieldsAlias(alias = "开始使用时间", dimension = 100)
    private Long startUseTime;

    /**
     * 结束使用时间
     */
    @FieldsAlias(alias = "结束使用时间", dimension = 100)
    private Long endUseTime;

    /**
     * 有效时长(单位小时)
     */
    @FieldsAlias(alias = "有效时长")
    private Integer useDuration;

    /**
     * 相对时间粒度，1-小时，2=天
     */
    @FieldsAlias(alias = "有效时长")
    private Integer timeGranularity;

    /**
     * 使用渠道 (1:小米商城 2:直营店 3:专卖店 4:授权店 5:堡垒店)
     */
    @FieldsAlias(alias = "使用渠道", dimension = 2)
    private Set<Integer> useChannel;

    /**
     * 使用平台
     */
    @FieldsAlias(alias = "使用平台", dimension = 2)
    private Map<Integer,UseChannelPO> usePlatform;

    /**
     * 使用门店
     */
    @FieldsAlias(alias = "使用门店", dimension = 2)
    private Map<Integer, UseChannelPO> useStore;

    /**
     * 商品范围类型 (1:商品券, 2:品类券)
     */
    @FieldsAlias(alias = "商品范围类型")
    private Integer scopeType;

    /**
     * 券可用商品
     */
    @FieldsAlias(alias = "券可用商品", dimension = 2)
    private Map<String, Set<Long>> goodsInclude;

    /**
     * 排除商品
     */
    @FieldsAlias(alias = "排除商品", dimension = 2)
    private Map<String, Set<Long>> goodsExclude;

    /**
     * 工时ssu
     */
    @FieldsAlias(alias = "工时ssu", dimension = 2)
    private Map<Long, Integer> labourHourSsu;

    /**
     * 配件ssu
     */
    @FieldsAlias(alias = "配件ssu", dimension = 2)
    private Map<Long, Integer> partsSsu;

    /**
     * 类目Id列表
     */
    @FieldsAlias(alias = "类目Id列表", dimension = 2)
    private Set<Integer> categoryIds;

    /**
     * 可发放总量
     */
    @FieldsAlias(alias = "发放总量", dimension = 100)
    private Integer applyCount;

    /**
     * 每人领取限制
     */
    @FieldsAlias(alias = "每人领取", dimension = 100)
    private Integer fetchLimit;

    /**
     * 附加属性 1可包邮 2可转增 3指定地区
     * 1-是  2-否
     */
    @FieldsAlias(alias = "附加属性", dimension = 100)
    private ExtPropVO extProp;

    /**
     * 可使用地区
     */
    @FieldsAlias(alias = "可使用地区", dimension = 2)
    private Set<Integer> areaIds;

    /**
     * 成本分摊 TODO map结构，确定key,value类型
     */
    @FieldsAlias(alias = "成本分摊")
    private Map<Integer, Integer> costShare;

    /**
     * 业务渠道  0:3c业务 3:汽车业务 4:汽车服务
     */
    @FieldsAlias(alias = "业务渠道")
    private Integer bizPlatform;

}
