package com.xiaomi.nr.coupon.admin.domain.coupon.couponmission;

import com.xiaomi.nr.coupon.admin.api.dto.mission.MissionDto;
import com.xiaomi.nr.coupon.admin.api.dto.mission.PageResponse;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

/**
 * 券发放任务相关服务
 *
 * <AUTHOR>
 */
public interface CouponMissionService {
    
    /**
     * 根据券发放渠道或发放任务id获取券发放任务列表
     * @param sendChannel 券发放渠道
     * @param missionId 券发放任务ID
     * @param lastMissionId 分页返回接口结果
     * @param pageSize 每页记录数
     * @return PageResponse<MissionDto>
     * @throws BizError
     */
    PageResponse<MissionDto> getMissionList(String sendChannel, Long missionId, Long lastMissionId, Integer pageSize) throws BizError;
}
