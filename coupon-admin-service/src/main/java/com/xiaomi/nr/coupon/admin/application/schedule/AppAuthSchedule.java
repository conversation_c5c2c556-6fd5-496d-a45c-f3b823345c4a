package com.xiaomi.nr.coupon.admin.application.schedule;


import com.xiaomi.nr.coupon.admin.application.schedule.constant.ScheduleConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.auth.AppAuthRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.auth.po.AppAuthInfo;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.nr.infra.aop.clustertask.ClusterTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * 更新AppAuth缓存定时任务
 */
@Slf4j
@Component
public class AppAuthSchedule {

    @Autowired
    private AppAuthRedisDao appAuthRedisDao;

    /**
     * 暂定appId信息10分钟更新一次
     */
    @Scheduled(fixedDelay = 1000 * 60 * 10, initialDelay = 1000 * 60 * 5)
    @ClusterTask(value = "", zkLockPath = ScheduleConstant.APP_AUTH_UPDATE_CACHE_LOCK)
    public void genAppAuthUpdateRedisCacheScheduleTask(){
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            log.info("AppAuthSchedule.genAppAuthUpdateRedisCacheScheduleTask, start");
            updateAppAuthCache();
            log.info("AppAuthSchedule.genAppAuthUpdateRedisCacheScheduleTask, execute success, runTime={}ms", TimeUtil.sinceMillis(runStartTime));
        } catch (Exception e) {
            log.error("CouponConfigSchedule.genConfigRedisCacheScheduleTask, execute fail, runTime={}ms, {}", TimeUtil.sinceMillis(runStartTime), e);
        }
    }


    /**
     * 取出老缓存转换信息并更新新缓存
     */
    private void updateAppAuthCache(){
        Map<String, AppAuthInfo> dataMap = appAuthRedisDao.get();
        appAuthRedisDao.setNewAppAuth(dataMap);
    }
}
