package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.bdmall.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.Map;

/**
 * @description:
 * @author: heji<PERSON><PERSON>
 * @Date 2022/3/30 12:58 下午
 * @Version: 1.0
 **/
@Data
public class MallCouponGoodsInfoPO {

    @SerializedName("select_goods")
    private Map<Long, MallGoodsItemPO> selectGoods;

    @SerializedName("select_batchs")
    private Map<Long, MallGoodsItemPO> selectBatchs;
}
