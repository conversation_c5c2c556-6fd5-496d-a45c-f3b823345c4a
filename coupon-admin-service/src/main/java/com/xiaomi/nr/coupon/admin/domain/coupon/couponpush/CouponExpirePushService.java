package com.xiaomi.nr.coupon.admin.domain.coupon.couponpush;


import api.producer.NormalProducer;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponpush.model.CouponExpireMsg;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponpush.model.CouponExtendInfo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.CouponConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.oceanbasedao.OceanBaseCouponMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponpush.CouponExpirePushRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.tidbdao.TidbCouponMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.tidbdao.po.CouponPo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 优惠券过期push
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CouponExpirePushService {

    @Resource
    private TidbCouponMapper tidbCouponMapper;

    @Resource
    private CouponConfigMapper couponConfigMapper;

    @Resource
    private CouponExpirePushRedisDao couponExpirePushRedisDao;

    @Resource
    @Qualifier("xmRocketNormalProducer")
    private NormalProducer normalProducer;

    @Value("${nr.rocketmq.producer.coupon.expire.topic}")
    private String expireTopic;

    @Resource
    private OceanBaseCouponMapper oceanBaseCouponMapper;

    /**
     * 每12小时内提醒一次的标识
     */
    public static final int TIME_MARK_12 = 12;
    private static final int DB_GET_PAGE_LIMIT = 1000;
    /**
     * 获取近*天创建的运费券的配置ID列表
     */
    private static final int NEAR_DAY = 90;


    /**
     * 即将过期提醒（未使用的）
     * 《云店社群营销活动》《营销互动平台》投放场景的券
     *
     * @param timeMark      String 每*小时内提醒一次的标识
     * @param filterEndTime long 结束时间
     */
    public void postFeeExpiringSoon(String timeMark, long filterEndTime) {
        long configFilterTime = TimeUtil.getNowUnixSecond() - NEAR_DAY * 24 * 3600;
        List<CouponConfigPO> configs = couponConfigMapper.getPostFeeConfigs(configFilterTime);
        if (configs == null || configs.isEmpty()) {
            log.info("postFeeExpirePushScheduleTask, 没有需要处理的运费券配置列表信息, timeMark={}, filterEndTime={}", timeMark, filterEndTime);
            return;
        }


        Map<Long, CouponConfigPO> configMaps = configs.stream().collect(Collectors.toMap(CouponConfigPO::getId, (p) -> p));
        List<Long> configIds = new LinkedList<>(configMaps.keySet());

        long nowTime = TimeUtil.getNowUnixSecond();
        long filterStartTime = couponExpirePushRedisDao.getLastEndTime(timeMark);
        try {
            int page = 0;
            while (true) {
                //1小时以后～12小时之内的数据，使用oceanbase代替tidb
                // List<CouponPo> coupons = tidbCouponMapper.getExpiringSoonData(filterStartTime, filterEndTime, configIds, page * DB_GET_PAGE_LIMIT, DB_GET_PAGE_LIMIT);
                List<CouponPo> coupons = oceanBaseCouponMapper.getExpiringSoonData(filterStartTime, filterEndTime, configIds, page * DB_GET_PAGE_LIMIT, DB_GET_PAGE_LIMIT);
                if (coupons == null || coupons.isEmpty()) {
                    log.info("postFeeExpirePushScheduleTask, 没有需要处理的运费券ID了, timeMark={}, startTime={}, endTime={}, configIds={}", timeMark, filterStartTime, filterEndTime, configIds);
                    break;
                } else {
                    log.info("postFeeExpirePushScheduleTask, 需要处理的运费券ID, timeMark={}, startTime={}, endTime={}, couponListCount={}, configIds={}", timeMark, filterStartTime, filterEndTime, coupons.size(), configIds);
                }

                //第一个券ID
                long firstCouponId = 0L;
                List<CouponExpireMsg> messages = new ArrayList<>();
                for (CouponPo item : coupons) {
                    if (item.getTypeId() == null) {
                        continue;
                    }

                    if (!configMaps.containsKey(item.getTypeId())) {
                        continue;
                    }

                    long endTime;
                    try {
                        endTime = Long.parseLong(item.getEndTime());
                    } catch (Exception e) {
                        continue;
                    }

                    //离过期还有10分钟的就不要推提醒了
                    if (endTime <= nowTime + 10 * 60) {
                        continue;
                    }
                    CouponExpireMsg message = makeMessage(item, configMaps.get(item.getTypeId()));
                    if (message != null) {
                        messages.add(message);
                        if (firstCouponId == 0) {
                            firstCouponId = item.getId();
                        }
                    }
                }

                //push MQ
                asyncPushBatch(messages, firstCouponId, timeMark);
                page++;
            }
        } catch (Exception e) {
            log.error("postFeeExpirePushScheduleTask, 处理过程中出错了, timeMark={}, filterStartTime={}, filterEndTime={}, configIds={}", timeMark, filterStartTime, filterEndTime, configIds, e);
        }

        //lastEndTime写入redis
        couponExpirePushRedisDao.setLastEndTime(timeMark, filterEndTime);
    }

    /**
     * 构建MQ消息
     *
     * @param coupon CouponPo 券信息
     * @return String
     */
    private CouponExpireMsg makeMessage(CouponPo coupon, CouponConfigPO config) {
        String name = config.getName() == null ? "优惠券" : config.getName();
        long couponId = coupon.getId() == null ? 0 : coupon.getId();
        long userId = coupon.getUserId() == null ? 0 : coupon.getUserId();
        int couponType = config.getCouponType() == null ? 0 : config.getCouponType();

        //必须值
        long endTime = 0L;
        try {
            endTime = Long.parseLong(coupon.getEndTime());
        } catch (Exception e) {
            return null;
        }

        CouponExtendInfo ext = GsonUtil.fromJson(coupon.getExtendInfo(), CouponExtendInfo.class);

        //非必须值
        long shareUserId = 0L;
        try {
            if (ext.getShareUserId() != null && !ext.getShareUserId().isEmpty()) {
                shareUserId = Long.parseLong(ext.getShareUserId());
            }
        } catch (Exception e) {
        }

        if (couponId <= 0 || userId <= 0 || couponType <= 0 || endTime <= 0) {
            return null;
        }

        CouponExpireMsg result = new CouponExpireMsg();
        result.setConfigId(coupon.getTypeId());
        result.setSendScene(config.getSendScene());
        result.setCouponId(couponId);
        result.setCouponName(name);
        result.setCouponType(couponType);
        result.setUserId(userId);
        result.setShareUserId(shareUserId);
        result.setEndTime(endTime);
        result.setMessageTime(TimeUtil.getNowUnixSecond());
        return result;
    }

    /**
     * 批量同步推送MQ消息
     *
     * @param expireMessages List<CouponExpireMsg> 消息内容
     * @param firstCouponId  long 第一个优惠券ID
     * @param timeMark       String 时间标识
     */
    public SendResult asyncPushBatch(List<CouponExpireMsg> expireMessages, long firstCouponId, String timeMark) {
        if (expireMessages == null || expireMessages.isEmpty()) {
            return null;
        }

        List<Message> messages = new ArrayList<>();
        for (CouponExpireMsg item : expireMessages) {
            Message msg = new Message(expireTopic, GsonUtil.toJson(item).getBytes(StandardCharsets.UTF_8));
            msg.setKeys(item.getCouponId().toString());
            messages.add(msg);
        }

        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            SendResult sendResult = normalProducer.sendBatch(messages);
            if (SendStatus.SEND_OK != sendResult.getSendStatus()) {
                log.warn("postFeeExpireScheduleTask, 批量往MQ发送优惠券过期消息失败, runTime={}毫秒, timeMark={}, firstCouponId={}, messages.size()={}, result.SendStatus:{}", TimeUtil.sinceMillis(runStartTime), timeMark, firstCouponId, messages.size(), sendResult.getSendStatus());
            } else {
                log.info("postFeeExpireScheduleTask, 批量往MQ发送优惠券过期消息成功, runTime={}毫秒, timeMark={}, firstCouponId={}, messages.size()={}, result.SendStatus:{}", TimeUtil.sinceMillis(runStartTime), timeMark, firstCouponId, messages.size(), sendResult.getSendStatus());
            }
            return sendResult;
        } catch (Exception e) {
            log.warn("postFeeExpireScheduleTask, 批量往MQ发送优惠券过期消息报错, runTime={}毫秒, timeMark={}, firstCouponId={}, messages.size()={}", TimeUtil.sinceMillis(runStartTime), timeMark, firstCouponId, messages.size(), e);
            throw new RuntimeException(e);
        }
    }

}
