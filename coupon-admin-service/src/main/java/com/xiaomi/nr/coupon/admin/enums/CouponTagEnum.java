package com.xiaomi.nr.coupon.admin.enums;

public enum CouponTagEnum {

    MISHOP_ONLINE("MISHOP_ONLINE", "小米商城"),
    MIHOME_OFFLINE("MIHOME_OFFLINE", ""),
    YOUPIN_ONLINE("YOUPIN_ONLINE", "小米有品"),
    <PERSON>OR<PERSON><PERSON>("STOREGO",""),
    MIHOME_ONLINE("MIHOME_ONLINE", ""),
    MIHOME_ONLINE_ZM("MIHOME_ONLINE_ZM", ""),
    MIHOME_ONLINE_SQ("MIHOME_ONLINE_SQ", ""),
    TV_MALL("TV_MALL", "");

    private final String value;
    private final String name;

    CouponTagEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static CouponTagEnum findByValue(String value) {
        CouponTagEnum[] values = CouponTagEnum.values();
        for (CouponTagEnum couponTagEnum : values) {
            if (value.equals(couponTagEnum.value)) {
                return couponTagEnum;
            }
        }
        return null;
    }
}
