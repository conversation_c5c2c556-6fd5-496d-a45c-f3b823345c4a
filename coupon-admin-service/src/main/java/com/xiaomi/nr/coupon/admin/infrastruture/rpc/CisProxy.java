package com.xiaomi.nr.coupon.admin.infrastruture.rpc;

import com.xiaomi.nr.cis.api.dto.GetVinRelationMapRequest;
import com.xiaomi.nr.cis.api.dto.GetVinRelationMapResponse;
import com.xiaomi.nr.cis.api.service.CisVinVidService;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.ResultValidator;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * @description 整车中心服务
 * <AUTHOR>
 * @date 2024-12-25 20:20
*/
@Slf4j
@Service
public class CisProxy {

    @Reference(check = false, interfaceClass = CisVinVidService.class, group = "${cis.dubbo.group}", version = "1.0", timeout = 5000)
    private CisVinVidService cisVinVidService;

    /**
     * 根据vin查询vid
     *
     * @param request request
     * @return GetVinRelationMapResponse
     * @throws BizError 业务异常
     */
    public GetVinRelationMapResponse getVinRelationMap(GetVinRelationMapRequest request) throws BizError {
        log.info("CisProxy.getVinRelationMap begin request = {}", GsonUtil.toJson(request));
        try {
            Result<GetVinRelationMapResponse> response = cisVinVidService.getVinRelationMap(request);

            ResultValidator.validate(response, "调用CIS服务根据VIN查询VID失败");

            log.info("CisProxy.getVinRelationMap finished request = {}, response = {}", GsonUtil.toJson(request), GsonUtil.toJson(response));

            return response.getData();
        } catch (BizError bizError) {
            log.error("CisProxy.getVinRelationMap bizError request = {}, bizError = ", GsonUtil.toJson(request), bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("CisProxy.getVinRelationMap error request = {}, exception = ", GsonUtil.toJson(request), e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "调用CIS服务根据VIN查询VID失败");
        }
    }
}
