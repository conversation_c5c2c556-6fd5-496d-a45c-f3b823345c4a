package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.datapreparewrapper;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.BaseDomainEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.EventContext;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.CouponConfigPoConvert;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.GoodsItemPo;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/10/12
 */
public abstract class BaseDataPrepareWrapper {

    @Resource
    private CouponConfigRepository couponConfigRepository;

    @Resource
    private CouponConfigPoConvert couponConfigPoConvert;

    /**
     * 准备商品信息
     *
     * @param event
     * @return
     */
    public EventContext prepareGoodsInfo(BaseDomainEvent<CouponConfigPO> event) throws Exception {

        // 商品信息采用最新商品信息
        CouponConfigPO couponConfigPO = event.getData();
        if (event.isCompensateFlag()) {
            couponConfigPO = couponConfigRepository.searchCouponById(couponConfigPO.getId());
        }

        EventContext eventContext = new EventContext();

        // 转换商品
        GoodsItemPo goodsItemPo = couponConfigPoConvert.serializeGoodsItemPo(couponConfigPO);
        eventContext.setGoodsItemPo(goodsItemPo);

        prepareGoodsInfo(eventContext, couponConfigPO);

        return eventContext;
    }

    protected abstract void prepareGoodsInfo(EventContext eventContext, CouponConfigPO couponConfigPO) throws Exception;

}
