package com.xiaomi.nr.coupon.admin.enums.pointadmin;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

/**
 * 积分状态
 *
 * <AUTHOR>
 * @date 2023/12/7
 */
@Getter
@AllArgsConstructor
public enum PointsStatusEnum {
    /**
     * 未激活（库中原始状态）
     */
    NO_ACTIVATE(0, "未激活"),

    /**
     * 已激活（库中原始状态）
     */
    ACTIVATED(1, "已激活"),

    /**
     * 已作废（库中原始状态）
     */
    INVALID(2, "已作废"),

    /**
     * 已过期（逻辑判断后状态）
     */
    EXPIRED(3, "已过期"),

    /**
     * 可使用（逻辑判断后状态）
     */
    VALID(4, "可使用"),

    /**
     * 已使用（逻辑判断后状态）
     */
    USED(5, "已使用"),
    ;

    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String desc;

    private static final HashMap<Integer, PointsStatusEnum> MAPPING = new HashMap<>();

    static {
        for (PointsStatusEnum e : PointsStatusEnum.values()) {
            MAPPING.put(e.getCode(), e);
        }
    }

    public static PointsStatusEnum valueOf(Integer code) {
        return MAPPING.get(code);
    }


}
