package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po;

import lombok.Data;

import java.util.List;

/**
 * @Description: 券码列表查询参数
 * @Date: 2022.03.22 15:56
 */
@Data
public class CodeTaskSearchParam {
    /**
     * 优惠券id
     */
    private Long configId;
    /**
     * 优惠券名称
     */
    private String couponName;
    /**
     * 任务状态
     */
    private List<Integer> status;
    /**
     * 任务类型
     */
    private Integer type;
    /**
     * 排序字段
     */
    private String orderBy = "id";

    /**
     * 排序顺序  desc倒序   asc顺序
     */
    private String orderDirection = "desc";
}
