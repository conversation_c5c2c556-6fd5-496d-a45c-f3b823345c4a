package com.xiaomi.nr.coupon.admin.enums.couponconfig;

/**
 * 优惠券使用渠道 枚举
 *
 * <AUTHOR>
 */
public enum UseChannelEnum {

    /**
     * 小米商城渠道
     */
    MiShop(1, "mi_shop","小米商城"),

    /**
     * 小米之家渠道
     */
    MiHome(2, "mi_home","小米之家"),

    /**
     * 授权店
     */
    MiAuthorized(4, "mi_authorized","授权店"),

    /**
     * 车商城
     */
    CarShop(6, "car_shop", "车商城"),
    ;


    private final int code;
    private final String value;
    private final String name;

    UseChannelEnum(int code, String value, String name) {
        this.code = code;
        this.value = value;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static UseChannelEnum findByCode(int code) {
        UseChannelEnum[] values = UseChannelEnum.values();
        for (UseChannelEnum item : values) {
            if (item.getCode() == code) {
                return item;
            }
        }
        return null;
    }

    public static UseChannelEnum findByValue(String value) {
        UseChannelEnum[] values = UseChannelEnum.values();
        for (UseChannelEnum item : values) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}

