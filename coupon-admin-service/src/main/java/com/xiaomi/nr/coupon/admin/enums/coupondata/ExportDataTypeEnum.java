package com.xiaomi.nr.coupon.admin.enums.coupondata;

public enum ExportDataTypeEnum {

    /**
     * 所有发放数据
     */
    ALL_COUPON_DATA(1,"券使用明细数据"),

    /**
     * 灌券发放数据
     */
    FILL_COUPON_DATA(2,"灌券效果数据");

    private final int code;
    private final String name;

    ExportDataTypeEnum(int code, String name) {
        this.code =code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }

    public static String findNameByCode(Integer code) {
        ExportDataTypeEnum[] values = ExportDataTypeEnum.values();
        for (ExportDataTypeEnum item : values) {
            if(item.getCode().equals(code)) {
                return item.name;
            }
        }
        return ALL_COUPON_DATA.name;
    }
}
