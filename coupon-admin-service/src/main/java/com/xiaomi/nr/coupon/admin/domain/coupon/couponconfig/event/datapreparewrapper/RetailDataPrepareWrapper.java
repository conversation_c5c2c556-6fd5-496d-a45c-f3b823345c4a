package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.datapreparewrapper;

import com.xiaomi.nr.coupon.admin.api.enums.BizSubTypeEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.EventContext;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.GoodsItemPo;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.goods.GmsProxyService;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/14
 */
@Component
public class RetailDataPrepareWrapper extends BaseDataPrepareWrapper {

    @Resource
    private GmsProxyService gmsProxyService;

    /**
     * 准备商品信息
     *
     * @param eventContext      后置事件处理器参数传递上下文
     * @param couponConfigPO    券配置po
     * @throws Exception        异常
     */
    @Override
    public void prepareGoodsInfo(EventContext eventContext, CouponConfigPO couponConfigPO) throws Exception {

        // 转换商品
        GoodsItemPo goodsItemPo = eventContext.getGoodsItemPo();
        List<Long> skuList = goodsItemPo.getSkuList();
        if (CollectionUtils.isNotEmpty(skuList)) {
            eventContext.setSkuInfoDtos(gmsProxyService.queryListBySkuIds(skuList, false, false, false, Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode(), BizSubTypeEnum.CARRIER_PAN_COMPLETE.getCode())));
        }

        // 老套装
        List<Long> packageList = goodsItemPo.getPackageList();
        if (CollectionUtils.isNotEmpty(packageList)) {
            eventContext.setBatchedInfoDtos(gmsProxyService.queryListByPackageIds(packageList, false));
        }

        // 新套装依赖goodsItemPo
    }
}
