package com.xiaomi.nr.coupon.admin.domain.coupon;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.CouponDetailResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.CouponAdminService;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant.SQL_BATCH_SIZE;

/**
 * moon定时任务
 *
 * <AUTHOR>
 * @date 2024/03/25
 */
@Service
@Slf4j
public class CouponBatchScheduleService {

    @Resource
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private CouponAdminService couponAdminService;
    /**
     * 预算释放
     */
    public void budgetRelease() {

        log.info("CouponBatchScheduleService budgetRelease start");

        long startId = 0L;
        int batchSize = SQL_BATCH_SIZE;
        List<CouponConfigPO> curBatch = null;
        long queryTime = System.currentTimeMillis() / 1000;

        // 1、扫描
        do {
            curBatch = couponConfigRepository.selectCompletedBatch(startId, queryTime, batchSize);
            if (CollectionUtils.isEmpty(curBatch)) {
                break;
            }

            // 2、遍历记录
            for (CouponConfigPO batchConfigPo : curBatch) {
                try {
                    if (checkBudget(batchConfigPo)) {
                        // 有剩余积分且未释放过预算
                        CouponInfoRequest req = new CouponInfoRequest();
                        req.setId(batchConfigPo.getId());
                        Result<CouponDetailResponse> res = couponAdminService.couponConfigDetail(req);
                        long releaseCount = batchConfigPo.getApplyCount() - res.getData().getSendCount();
                        if (releaseCount > 0 && batchConfigPo.getReleaseCount() == 0) {
                            couponConfigRepository.budgetRelease(batchConfigPo, releaseCount);
                        }
                    }
                } catch (Exception e) {
                    log.error("budgetRelease failed. batchConfigPo {}, e ", GsonUtil.toJson(batchConfigPo), e);
                }
            }

            startId = curBatch.get(curBatch.size() - 1).getId();
        } while (curBatch.size() == batchSize);

        log.info("CouponBatchScheduleService budgetRelease finished");
    }

    public boolean checkBudget(CouponConfigPO batchConfigPo) {
        return StringUtils.isNotEmpty(batchConfigPo.getBrApplyNo()) && batchConfigPo.getLineNum() != null && StringUtils.isNotEmpty(batchConfigPo.getBudgetCreateTime()) && StringUtils.isNotEmpty(batchConfigPo.getBrApplyNo());
    }
}
