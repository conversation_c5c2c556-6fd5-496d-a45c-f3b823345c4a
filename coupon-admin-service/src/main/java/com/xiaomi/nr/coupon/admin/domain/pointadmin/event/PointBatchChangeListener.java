package com.xiaomi.nr.coupon.admin.domain.pointadmin.event;

import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.handler.BasePointBatchPostHandler;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/12/6 19:27
 */
@Slf4j
@Component
public class PointBatchChangeListener {

    @Autowired
    private List<BasePointBatchPostHandler> pointBatchPostHandlers;

    @Async("asyncExecutor")
    @TransactionalEventListener(fallbackExecution = true)
    public void handleCreateEvent(PointBatchConfigCreateEvent event) {
        for (BasePointBatchPostHandler pointBatchPostHandler : pointBatchPostHandlers) {
            try {
                pointBatchPostHandler.createPost(event);
            } catch (Exception e) {
                log.error("pointBatchPostHandler createPost failed. event {}, e ", GsonUtil.toJson(event), e);
            }
        }
        log.info("handleCreateEvent done batchId {} ", Optional.ofNullable(event.getData()).map(CarPointsBatchConfigPo::getId).orElse(null));
    }

    @Async("asyncExecutor")
    @TransactionalEventListener(fallbackExecution = true)
    public void handleUpdateEvent(PointBatchConfigUpdateEvent event) {
        for (BasePointBatchPostHandler pointBatchPostHandler : pointBatchPostHandlers) {
            try {
                pointBatchPostHandler.updatePost(event);
            } catch (Exception e) {
                log.error("pointBatchPostHandler updatePost failed. event {}, e ", GsonUtil.toJson(event), e);
            }
        }
        log.info("handleUpdateEvent done batchId {} ", Optional.ofNullable(event.getData()).map(CarPointsBatchConfigPo::getId).orElse(null));
    }

    @Async("asyncExecutor")
    @TransactionalEventListener(fallbackExecution = true)
    public void handleUpdateStatusEvent(PointBatchConfigUpdateStatusEvent event) {
        for (BasePointBatchPostHandler pointBatchPostHandler : pointBatchPostHandlers) {
            try {
                pointBatchPostHandler.updateStatus(event);
            } catch (Exception e) {
                log.error("pointBatchPostHandler updateStatus failed. event {}, e ", GsonUtil.toJson(event), e);
            }
        }
        log.info("handleUpdateStatusEvent done batchId {} ", Optional.ofNullable(event.getData()).map(CarPointsBatchConfigPo::getId).orElse(null));
    }
}
