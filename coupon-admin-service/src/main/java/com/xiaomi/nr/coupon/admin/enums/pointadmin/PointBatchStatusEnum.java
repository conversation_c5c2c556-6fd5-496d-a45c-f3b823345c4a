package com.xiaomi.nr.coupon.admin.enums.pointadmin;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2023/12/6 10:09
 */
@Getter
@AllArgsConstructor
public enum PointBatchStatusEnum {
    /**
     * 1: 上线
     */
    ONLINE(1, "上线"),

    /**
     * 2: 下线
     */
    OFFLINE(2, "下线"),
    ;

    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String desc;

    private static final HashMap<Integer, PointBatchStatusEnum> MAPPING = new HashMap<>();

    static {
        for (PointBatchStatusEnum e : PointBatchStatusEnum.values()) {
            MAPPING.put(e.getCode(), e);
        }
    }

    public static PointBatchStatusEnum valueOf(Integer code) {
        return MAPPING.get(code);
    }


}
