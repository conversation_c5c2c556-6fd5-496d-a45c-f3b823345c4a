package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.SearchConfigInfoParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.SearchConfigParam;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
 * 券配置操作mapper
 */
@Mapper
@Component
public interface CouponConfigMapper {


    String COUPON_CONFIG_TABLE = "nr_coupon_config";


    /**
     * 不包含商品信息
     */
    String COUPON_CONFIG_FIELD = "id,name,status,coupon_desc,budget_apply_no,line_num,budget_create_time,br_apply_no,promotion_type,send_scene,send_purpose,start_fetch_time,end_fetch_time,use_time_type,start_use_time," +
            "end_use_time,use_duration,use_channel,use_platform,use_store,bottom_type,bottom_price,bottom_count,promotion_value,max_reduce,scope_type,update_time," +
            "category_ids,apply_count,release_count,fetch_limit,fetch_limit_type,ext_prop,area_ids,cost_share,creator,create_time,department_id,source,code,send_channel,coupon_type,shipment_id,time_granularity,auto_update_goods";

    /**
     * 插入优惠券
     * @return
     */
    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("insert into " + COUPON_CONFIG_TABLE + " (name,status,coupon_desc,budget_apply_no,line_num,budget_create_time,br_apply_no,promotion_type,send_scene,send_purpose,start_fetch_time,end_fetch_time,use_time_type,start_use_time" +
            ",end_use_time,use_duration,use_channel,use_platform,use_store,bottom_type,bottom_price,bottom_count,promotion_value,max_reduce,scope_type,goods_include" +
            ",goods_departments,goods_exclude,category_ids,apply_count,release_count,fetch_limit,ext_prop,area_ids,cost_share,creator,create_time,department_id,source,code,send_channel,coupon_type,shipment_id,time_granularity" +
            ",auto_update_goods,biz_platform,service_type,fetch_limit_type,times_limit,public_promotion)"+
            " values " +
            "(#{name},#{status},#{couponDesc},#{budgetApplyNo},#{lineNum},#{budgetCreateTime},#{brApplyNo},#{promotionType},#{sendScene},#{sendPurpose},#{startFetchTime},#{endFetchTime},#{useTimeType},#{startUseTime},#{endUseTime},#{useDuration},#{useChannel}," +
            "#{usePlatform},#{useStore},#{bottomType},#{bottomPrice},#{bottomCount},#{promotionValue},#{maxReduce},#{scopeType},#{goodsInclude},#{goodsDepartments},#{goodsExclude},#{categoryIds}," +
            "#{applyCount},#{releaseCount},#{fetchLimit},#{extProp},#{areaIds},#{costShare},#{creator},#{createTime},#{departmentId},#{source},#{code},#{sendChannel},#{couponType},#{shipmentId},#{timeGranularity},#{autoUpdateGoods},#{bizPlatform},#{serviceType},#{fetchLimitType},#{timesLimit},#{publicPromotion})")
    Long insert(CouponConfigPO po);

    /**
     * 更新优惠券
     * @param po
     * @return
     */
    @Update("<script>update " + COUPON_CONFIG_TABLE + " <set>" +
            "<if test='name!=null'>name=#{name},</if>" +
            "<if test='couponDesc!=null'>coupon_desc=#{couponDesc},</if>" +
            "<if test='budgetApplyNo!=null'>budget_apply_no=#{budgetApplyNo},</if>" +
            "<if test='lineNum!=null'>line_num=#{lineNum},</if>" +
            "<if test='budgetCreateTime!=null'>budget_create_time=#{budgetCreateTime},</if>" +
            "<if test='brApplyNo!=null'>br_apply_no=#{brApplyNo},</if>" +
            "<if test='startFetchTime!=null'>start_fetch_time=#{startFetchTime},</if>" +
            "<if test='endFetchTime!=null'>end_fetch_time=#{endFetchTime},</if>" +
            "<if test='startUseTime!=null'>start_use_time=#{startUseTime},</if>" +
            "<if test='endUseTime!=null'>end_use_time=#{endUseTime},</if>" +
            "<if test='timeGranularity!=null'>time_granularity=#{timeGranularity},</if>" +
            "<if test='useDuration!=null'>use_duration=#{useDuration},</if>" +
            "<if test='useChannel!=null'>use_channel=#{useChannel},</if>" +
            "<if test='usePlatform!=null'>use_platform=#{usePlatform},</if>" +
            "<if test='useStore!=null'>use_store=#{useStore},</if>" +
            "<if test='bottomPrice!=null'>bottom_price=#{bottomPrice},</if>" +
            "<if test='bottomCount!=null'>bottom_count=#{bottomCount},</if>" +
            "<if test='maxReduce!=null'>max_reduce=#{maxReduce},</if>" +
            "<if test='goodsInclude!=null'>goods_include=#{goodsInclude},</if>" +
            "<if test='goodsDepartments!=null'>goods_departments=#{goodsDepartments},</if>" +
            "<if test='goodsExclude!=null'>goods_exclude=#{goodsExclude},</if>" +
            "<if test='categoryIds!=null'>category_ids=#{categoryIds},</if>" +
            "<if test='applyCount!=null'>apply_count=#{applyCount},</if>" +
            "<if test='releaseCount!=null'>release_count=#{releaseCount},</if>" +
            "<if test='fetchLimit!=null'>fetch_limit=#{fetchLimit},</if>" +
            "<if test='extProp!=null'>ext_prop=#{extProp},</if>" +
            "<if test='areaIds!=null'>area_ids=#{areaIds},</if>" +
            "<if test='costShare!=null'>cost_share=#{costShare},</if>" +
            "<if test='shipmentId!=null'>shipment_id=#{shipmentId},</if>" +
            "<if test='autoUpdateGoods!=null'>auto_update_goods=#{autoUpdateGoods},</if>" +
            "<if test='bizPlatform!=null'>biz_platform=#{bizPlatform},</if>" +
            "<if test='publicPromotion!=null'>public_promotion=#{publicPromotion},</if>" +
            "</set><where> id=#{id}</where></script>")
    Long update(CouponConfigPO po);



    /**
     * 根据券更新时间
     */
    @Select(" select update_time from " + COUPON_CONFIG_TABLE + " where id = #{id}")
    Timestamp searchUpdateTimeById(@Param("id") long id);


    /**
     * 批量id查询优惠券信息
     * @param ids 券配置id
     * @return    券配置列表
     */
    @Select("<script>select  * from " + COUPON_CONFIG_TABLE + " where id in " +
            "<foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>#{id}</foreach> " +
            "</script>")
    List<CouponConfigPO> getConfigByIds(@Param("ids")List<Long> ids);


    /**
     * 批量id查询优惠券信息(排序)
     * @param ids       券配置id
     * @param couponType 券类型
     * @param name      券名称
     * @param sortField 排序字段
     * @param sortValue 排序方式(asc/desc)
     * @return 券配置列表
     */
    @Select("<script>select * from " +COUPON_CONFIG_TABLE +
            " where id in <foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>#{id}</foreach>" +
            "<if test='couponType!=null'>and coupon_type=#{couponType}</if>" +
            "<if test='name!=null'> and name like '%${name}%' </if>" +
            " order by ${sortField}  ${sortValue}" +
            "</script>")
    List<CouponConfigPO> getConfigByIdsSort(@Param("ids") Set<Long> ids, @Param("couponType") Integer couponType, @Param("name") String name,
                                            @Param("sortField") String sortField, @Param("sortValue") String sortValue);



    /**
     * 根据条件查询优惠券列表
     *
     * @param searchConfigListParam 查询参数
     * @return 优惠券信息
     */
    @Select("<script>" +
                "select * from "+ COUPON_CONFIG_TABLE +" where id!=0 " +
                "<if test='id!=null'>and id=#{id}</if>" +
                "<if test='couponType!=null'>and coupon_type=#{couponType}</if>" +
                "<if test='bizPlatform!=null'>and biz_platform=#{bizPlatform}</if>" +
                "<if test='startUseTime !=null and endUseTime!=null'>and end_use_time &gt;= #{startUseTime} and start_use_time &lt;= #{endUseTime} </if>" +
                "<if test='startFetchTime!=null and endFetchTime!=null'>and end_fetch_time &gt;= #{startFetchTime} and start_fetch_time &lt;= #{endFetchTime}</if>" +
                "<if test='status!=null and status!=0'>and status=#{status} </if>" +
                "<if test='sendScene!=null'>and send_scene like concat(concat('%',#{sendScene}),'%') </if>" +
                "<if test='timeStatus!=null and startFetchTime==null and endFetchTime==null'>" +
                "    <if test='timeStatus==1'>and start_fetch_time &gt;= unix_timestamp(now()) and status != 3</if>" +
                "    <if test='timeStatus==2'>" +
                "        and ((start_fetch_time &lt;= unix_timestamp(now()) and end_fetch_time &gt;= unix_timestamp(now()))" +
                "        or (start_fetch_time=0 and end_fetch_time=0)) and status != 3" +
                "    </if>" +
                "    <if test='timeStatus==3'> and end_fetch_time!=0 and end_fetch_time &lt;= unix_timestamp(now()) and status != 3</if>" +
                "</if>" +
                "<if test='promotionType!=null'>and promotion_type=#{promotionType}</if>" +
                "<if test='name!=null'> and name like concat(concat('%',#{name}),'%') </if>" +
                "<if test='creator!=null'> and creator like concat(concat('%',#{creator}),'%') </if>" +
                "<if test='serviceType!=null and serviceType != 0'> and service_type = #{serviceType} </if>" +
                "<if test='bizPlatformList!=null and bizPlatformList.size() > 0'> and biz_platform in <foreach item='item' index='index' collection='bizPlatformList' open='(' separator=',' close=')'>#{item}</foreach> </if>" +
                " order by ${orderBy}  ${orderDirection}" +
            "</script>")
    List<CouponConfigPO> searchConfig(SearchConfigParam searchConfigListParam);


    /**
     * 根据券配置场景获取多个券配置信息
     *
     * @param sendScene 券配置场景
     * @return   CouponConfigPO
     */
    @Select("select * from "+ COUPON_CONFIG_TABLE +" where send_scene = #{sendScene} order by id desc limit 2000")
    List<CouponConfigPO> getConfigByScene(@Param("sendScene") String sendScene);

    /**
     * 根据券配置场景获取多个券配置信息
     *
     * @param param 券配置查询参数
     * @return   CouponConfigPO
     */
    @Select("<script>" +
            "select " + COUPON_CONFIG_FIELD + " from " + COUPON_CONFIG_TABLE + " where id!=0 " +
            "<if test='configIds!=null'> and id in <foreach item='configId' index='index' collection='configIds' open='(' separator=',' close=')'>#{configId}</foreach> </if>" +
            "<if test='sceneCode!=null'>and send_scene = #{sceneCode}</if>" +
            "<if test='startTime!=null and endTime!=null'>and update_time &gt;= #{startTime} and update_time &lt;= #{endTime}</if>" +
            "<if test='onlyAvailable'> and status = 1 and end_fetch_time &gt;= unix_timestamp(now())</if>" +
            "</script>")
    List<CouponConfigPO> getConfigByParam(SearchConfigInfoParam param);


    @Select("<script>select * from "+ COUPON_CONFIG_TABLE +" where send_scene = #{sendScene}" +
            "<if test='configIdList!=null'> and id in <foreach item='configId' index='index' collection='configIdList' open='(' separator=',' close=')'>#{configId}</foreach></if>" +
            " order by id desc limit 2000</script>")
    List<CouponConfigPO> getConfigBySceneConfigId(@Param("sendScene") String sendScene, @Param("configIdList") List<Long> configIdList);


    /**
     * 修改券状态
     * @param operateType 新的状态值
     * @param id 券配置id
     * @return int
     */
    @Update("update "+ COUPON_CONFIG_TABLE +" set status=#{operateType} where id =#{id}")
    Integer updateStatus(@Param("id") long id, @Param("operateType") int operateType);


    /**
     * 分页获取有效券配置
     * @param validFinalTime
     * @param lastUpdateTime
     * @param lastId
     * @param limit
     * @return
     */
    @Select("select * from "+ COUPON_CONFIG_TABLE +" where id>#{lastId} and end_use_time>#{validFinalTime} and update_time> #{lastUpdateTime} order by id asc limit #{limit}")
    List<CouponConfigPO> getValidConfigByOffset(@Param("validFinalTime") long validFinalTime, @Param("lastUpdateTime") String lastUpdateTime, @Param("lastId") long lastId, @Param("limit") long limit);


    /**
     * 根据券名称、使用渠道获取券id
     *
     * @param couponName
     * @return
     */
    @Select("<script>" +
            "select id from "+ COUPON_CONFIG_TABLE +" where id!=0 " +
            "<if test='couponName!=null'> and name like concat(concat('%',#{couponName}),'%') </if>" +
            "</script>")
    List<Long> getCouponIdByName(@Param("couponName") String couponName);

    /**
     * 根据券名称获取优惠
     *
     * @param couponName
     * @return
     */
    @Select("select * from "+ COUPON_CONFIG_TABLE +" where name like concat(concat('%',#{couponName}),'%') ")
    List<CouponConfigPO> getCouponByName(@Param("couponName") String couponName);

    /**
     * 根据券名称、使用渠道获取券id
     *
     * @param couponName
     * @return
     */
    @Select("<script>" +
            "select id from "+ COUPON_CONFIG_TABLE +" where biz_platform in (${bizType}) " +
            "<if test='couponName!=null'> and name like concat(concat('%',#{couponName}),'%') </if>" +
            "<if test='couponType!=null'> and coupon_type=#{couponType}</if>" +
            "</script>")
    List<Long> getCouponIdByNameAndType(@Param("couponName") String couponName, @Param("couponType") Integer couponType, @Param("bizType") String bizType);


    /**
     * 根据id查询券配置(d单个)
     * @param id 券配置id
     * @return CouponConfigPO
     */
    @Select("select * from "+ COUPON_CONFIG_TABLE +" where id=#{id}")
    CouponConfigPO getCouponConfigById(@Param("id") long id);

    /**
     * 根据id查询券名称(只查询灌券渠道的优惠券)
     * @param id 券配置id
     * @return 券名称
     */
    @Select("<script>" +
            "select id,name,apply_count from "+ COUPON_CONFIG_TABLE +
            " where status = 1 and end_fetch_time > #{endFetchTime} and id like '${id}%' " +
            "<if test='sceneCodeList!=null'> and send_scene in <foreach item='sceneCode' index='index' collection='sceneCodeList' open='(' separator=',' close=')'>#{sceneCode}</foreach> </if>" +
            "order by id desc"+
            "</script>")
    List<CouponConfigPO> getCouponNameById(@Param("id") long id, @Param("sceneCodeList") List<String> sceneCodeList,@Param("endFetchTime") long endFetchTime);

    /**
     * 根据使用时间查询有效券id
     *
     * @param validFinalTime
     * @return
     */
    @Select("select id from nr_coupon_config where end_use_time>#{validFinalTime} and source=2 and code = 2")
    List<Long> getValidConfigIdByUseTime(@Param("validFinalTime") long validFinalTime);

    /**
     * 分页获取所有优惠券
     * @param lastId
     * @param limit
     * @return
     */
    @Select("select * from "+ COUPON_CONFIG_TABLE +" where id>#{lastId}  order by id asc limit #{limit}")
    List<CouponConfigPO> getAllConfigByOffset(@Param("lastId") long lastId, @Param("limit") long limit);


    /**
     * 获取所有有效券配置ID
     * @param validFinalTime
     * @return
     */
    @Select("select id from " + COUPON_CONFIG_TABLE + " where status=1 and end_fetch_time>#{validFinalTime} and biz_platform=0 order by id asc")
    List<Long> getValidConfigIdByEndFetchTime(@Param("validFinalTime") long validFinalTime);

    @Select("select * from "+ COUPON_CONFIG_TABLE + " where id > #{startId} and end_fetch_time < #{queryTime} limit #{batchSize} ")
    List<CouponConfigPO> selectCompletedBatch(@Param("startId") long startId, @Param("queryTime") long queryTime, @Param("batchSize") int batchSize);


    /********************************老数据迁移使用 迁移完删掉 ****************************/
    /**
     * 查询券迁移的更新时间
     * @return
     */
    @Select("select max(update_time) from "+ COUPON_CONFIG_TABLE +" where source= 1")
    Timestamp getCouponMoveLastTime();

    /**
     * 判断数据是否存在
     * @param id
     * @return
     */
    @Select("select id from "+ COUPON_CONFIG_TABLE +" where id=#{id}")
    Long checkoutCouponExit(@Param("id") long id);


    /**
     * 老券写入新表
     * @return
     */
    @Insert("insert into " + COUPON_CONFIG_TABLE + " (id,name,status,coupon_desc,promotion_type,send_scene,send_purpose,start_fetch_time,end_fetch_time,use_time_type,start_use_time,end_use_time,use_duration,use_channel," +
            "use_platform,use_store,bottom_type,bottom_price,bottom_count,promotion_value,max_reduce,scope_type,goods_include,goods_exclude,category_ids," +
            "apply_count,fetch_limit,ext_prop,area_ids,cost_share,creator,create_time,department_id,source,code,send_channel)"+
            " values " +
            "(#{id},#{name},#{status},#{couponDesc},#{promotionType},#{sendScene},#{sendPurpose},#{startFetchTime},#{endFetchTime},#{useTimeType},#{startUseTime},#{endUseTime},#{useDuration},#{useChannel}," +
            "#{usePlatform},#{useStore},#{bottomType},#{bottomPrice},#{bottomCount},#{promotionValue},#{maxReduce},#{scopeType},#{goodsInclude},#{goodsExclude},#{categoryIds}," +
            "#{applyCount},#{fetchLimit},#{extProp},#{areaIds},#{costShare},#{creator},#{createTime},#{departmentId},#{source},#{code},#{sendChannel})")
    Long insertOldCoupon(CouponConfigPO po);


    /**
     * 更新优惠券
     * @param po
     * @return
     */
    @Update("<script>update " + COUPON_CONFIG_TABLE + " <set>" +
            "<if test='name!=null'>name=#{name},</if>" +
            "<if test='status!=null'>status=#{status},</if>" +
            "<if test='couponDesc!=null'>coupon_desc=#{couponDesc},</if>" +
            "<if test='sendScene!=null'>send_scene=#{sendScene},</if>" +
            "<if test='startFetchTime!=null'>start_fetch_time=#{startFetchTime},</if>" +
            "<if test='endFetchTime!=null'>end_fetch_time=#{endFetchTime},</if>" +
            "<if test='useTimeType!=null'>use_time_type=#{useTimeType},</if>" +
            "<if test='startUseTime!=null'>start_use_time=#{startUseTime},</if>" +
            "<if test='endUseTime!=null'>end_use_time=#{endUseTime},</if>" +
            "<if test='useDuration!=null'>use_duration=#{useDuration},</if>" +
            "<if test='useChannel!=null'>use_channel=#{useChannel},</if>" +
            "<if test='usePlatform!=null'>use_platform=#{usePlatform},</if>" +
            "<if test='useStore!=null'>use_store=#{useStore},</if>" +
            "<if test='bottomPrice!=null'>bottom_price=#{bottomPrice},</if>" +
            "<if test='bottomCount!=null'>bottom_count=#{bottomCount},</if>" +
            "<if test='promotionValue!=null'>promotion_value=#{promotionValue},</if>" +
            "<if test='maxReduce!=null'>max_reduce=#{maxReduce},</if>" +
            "<if test='goodsInclude!=null'>goods_include=#{goodsInclude},</if>" +
            "<if test='goodsDepartments!=null'>goods_departments=#{goodsDepartments},</if>" +
            "<if test='goodsExclude!=null'>goods_exclude=#{goodsExclude},</if>" +
            "<if test='categoryIds!=null'>category_ids=#{categoryIds},</if>" +
            "<if test='applyCount!=null'>apply_count=#{applyCount},</if>" +
            "<if test='fetchLimit!=null'>fetch_limit=#{fetchLimit},</if>" +
            "<if test='extProp!=null'>ext_prop=#{extProp},</if>" +
            "<if test='areaIds!=null'>area_ids=#{areaIds},</if>" +
            "<if test='costShare!=null'>cost_share=#{costShare},</if>" +
            "</set><where> id=#{id}</where></script>")
    Long updateOldCoupon(CouponConfigPO po);


    /**
     * 更新优惠券
     * @param po
     * @return
     */
    @Update("<script>update " + COUPON_CONFIG_TABLE + " <set>" +
            "<if test='status!=null'>status=#{status},</if>" +
            "<if test='sendScene!=null'>send_scene=#{sendScene},</if>" +
            "<if test='useTimeType!=null'>use_time_type=#{useTimeType},</if>" +
            "<if test='startUseTime!=null'>start_use_time=#{startUseTime},</if>" +
            "<if test='endUseTime!=null'>end_use_time=#{endUseTime},</if>" +
            "<if test='useDuration!=null'>use_duration=#{useDuration},</if>" +
            "</set><where> id=#{id}</where></script>")
    Long modifyOldCoupon(CouponConfigPO po);

    /**
     * 查询有效券id
     * @return
     */
    @Select("select id from "+ COUPON_CONFIG_TABLE +" where scope_type=#{scopeType} and status != 3 and end_fetch_time > #{endFetchTime} and create_time <= #{createTime}")
    List<Long> selectValidConfigId(@Param("scopeType") int scopeType,@Param("endFetchTime") long endFetchTime,@Param("createTime") long createTime);

    /**
     * 获取近期创建的《云店社群营销活动》《营销互动平台》投放场景的券
     * @return List<CouponConfigPO>
     */
    @Select("select id,name,coupon_type,send_scene from "+ COUPON_CONFIG_TABLE +" where create_time>#{filterTime} and send_scene in('8D5D96C7D9E680EF29FF1F2FC87B8DF8','7C783BEBB0D1C882E09DA5031B8EAEBF')")
    List<CouponConfigPO> getPostFeeConfigs(@Param("filterTime") long filterTime);


    /**
     * 获取需要刷新的券的信息
     * @param ids
     * @return
     */
    @Select("<script>" +
            " select id,coupon_type,category_ids from " +COUPON_CONFIG_TABLE +
            " where scope_type = 2 " +
            " and id in <foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>#{id}</foreach>" +
            "</script>")
    List<CouponConfigPO> getConfigForRefresh(@Param("ids") Set<Long> ids);


    /**
     * 更新优惠券品类
     * @param pos
     * @return
     */
    /*@Update("<script>" +
            "<foreach item='po' collection='pos' separator=';'>" +
            " update " + COUPON_CONFIG_TABLE +
            "<set> goods_include =#{po.goodsInclude, jdbcType=VARCHAR} </set>" +
            "<where> id=#{po.id}< /where>" +
            "</foreach>" +
            "</script>")
    Integer updateGoodsIncludes(@Param("pos") List<UpdateGoodsIncludePO> pos);*/


    /**
     * 更新优惠券品类
     * @param po
     * @return
     */
    @Update(" update " + COUPON_CONFIG_TABLE +
            " set goods_include = #{po.goodsInclude, jdbcType=VARCHAR} " +
            " where id=#{po.id}")
    Integer updateGoodsInclude(@Param("po") CouponConfigPO po);


    /**
     * 获取需要刷新的券的信息
     * @param nowTime 当前时间
     * @return
     */
    @Select(" select id,coupon_type,category_ids from " +COUPON_CONFIG_TABLE +
            " where scope_type=2 " +
            " and auto_update_goods=1 " +
            " and status != 3 " +
            " and (end_use_time > #{nowTime} or end_fetch_time+use_duration*3600 > #{nowTime}) " +
            " order by id desc" +
            " limit #{startSize,jdbcType=BIGINT},#{pageSize,jdbcType=BIGINT}")
    List<CouponConfigPO> getConfigForRefreshSchedule(@Param("nowTime") long nowTime, @Param("startSize") int startSize, @Param("pageSize") int pageSize);


    /**
     * 获取需要刷新有效的券id
     * @param ids
     * @return
     */
    @Select("<script>" +
            " select id from " +COUPON_CONFIG_TABLE +
            " where scope_type = 2 " +
            " and id in <foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>#{id}</foreach>" +
            "</script>")
    List<Long> getConfigForRefreshIds(@Param("ids") Set<Long> ids);



    /**
     * 批量id&时间查询优惠券信息
     * @param ids 券配置id
     * @return    券配置列表
     */
    @Select("<script>select  * from " + COUPON_CONFIG_TABLE + " where id in " +
            "<foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>#{id}</foreach> "+
            "<if test='nowTime>0 '>and end_use_time &gt;= #{nowTime} and start_use_time &lt;= #{nowTime} </if>" +
            "<if test='couponType!=null'>and coupon_type=#{couponType}</if>" +
            "<if test='sceneList!=null'> and send_scene in <foreach item='scene' index='index' collection='sceneList' open='(' separator=',' close=')'>#{scene}</foreach> </if>" +
            "order by id desc " +
            "</script>")
    List<CouponConfigPO> getConfigByTimeIds(@Param("ids")List<Long> ids, @Param("nowTime") long nowTime,
                                            @Param("couponType") Integer couponType, @Param("sceneList") List<String> sceneList);


    /**
     * 获取创建人信息
     * @param configId
     * @return
     */
    @Select("select creator,name from " +COUPON_CONFIG_TABLE+ " where id=#{configId}")
    CouponConfigPO getCreatorInfoByConfigId(@Param("configId") long configId);

    /**
     * 获取该创建人下所有券id
     * @param creator
     * @return
     */
    @Select("select id from "+ COUPON_CONFIG_TABLE +" where creator=#{creator}")
    List<Long> selectIdByCreator(@Param("creator") String creator);

    @Select("SELECT * FROM nr_coupon_config " +
            "WHERE send_scene LIKE CONCAT('%', #{sendScene}, '%') " +
            "AND status = 1 " +
            "AND start_use_time < UNIX_TIMESTAMP(NOW()) " +
            "AND UNIX_TIMESTAMP(NOW()) < end_use_time " +
            "AND biz_platform = 0")
    List<CouponConfigPO> getCouponConfigBySendScene(@Param("sendScene") String sendScene);

    /**
     * 自动化测试使用，删除券信息
     */
    @Delete("<script>delete from "+ COUPON_CONFIG_TABLE +" where id in <foreach item='id' index='index' collection='configIds' open='(' separator=',' close=')'>#{id}</foreach> </script>")
    Long deleteCoupon(@Param("configIds") List<Long> configIds);



}
