package com.xiaomi.nr.coupon.admin.application.dubbo.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.ApplyAttachmentVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponSketchListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GoodsRuleDetailVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request.CouponCreateSketchRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request.CouponSketchDeleteRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request.CouponSketchListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request.CouponSketchRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.response.CouponCreateSketchResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.response.CouponSketchDetailResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponSketchService;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponSketchConvert;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.CouponConfigCheckService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.CouponCheckFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.CouponConfigBaseCheck;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItemFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.goods.GoodsService;
import com.xiaomi.nr.coupon.admin.enums.sketch.CouponSketchOptType;
import com.xiaomi.nr.coupon.admin.infrastruture.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSketchRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.sketch.po.CouponSketchListParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.sketch.po.CouponSketchPO;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.BrProxy;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description: 优惠券草稿
 * @Date: 2022.03.01 15:50
 */
@Component
@Slf4j
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
public class DubboCouponSketchServiceImpl implements DubboCouponSketchService {

    @Autowired
    private CouponConfigCheckService couponCheckService;

    @Autowired
    private CouponSketchConvert couponSketchConvert;

    @Autowired
    private CouponSketchRepository couponSketchRepository;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Autowired
    private CouponCheckFactory couponCheckFactory;

    @Autowired
    private BrProxy brProxy;

    /**
     * 创建券草稿
     *
     * @param request
     * @return
     */
    @Override
    public Result<CouponCreateSketchResponse> createSketch(CouponCreateSketchRequest request) {
        try {
            request.getCouponConfigVO().setCreator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());
            request.setBizType(Optional.ofNullable(request.getBizType()).orElse(BizPlatformEnum.RETAIL.getCode()));

            log.info("CouponSketchService.createSketch begin request:{}", request);
            CouponCreateSketchResponse response = new CouponCreateSketchResponse();
            CouponConfigItem couponConfigItem = CouponConfigItemFactory.createCouponConfigItem(request.getCouponConfigVO(), request.getBizType());

            //基本信息校验
            CouponBaseInfo baseInfo = couponConfigItem.getCouponBaseInfo();
//            Integer bizPlatform = Optional.ofNullable(baseInfo.getBizPlatform()).orElse(BizPlatformEnum.RETAIL.getCode());
            CouponConfigBaseCheck checkHandler = couponCheckService.getCheckHandler(baseInfo.getPromotionType() + "_" + baseInfo.getBizPlatform());
            if (Objects.isNull(checkHandler)) {
                log.error("CouponSketchService.createSketch checkHandler not exist, request = {}", request);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "入参校验失败");
            }

            checkHandler.createCheck(couponConfigItem);
            CouponSketchPO couponSketchPO = couponSketchConvert.convertTOPO(request);
            if (request.getType() == CouponSketchOptType.CREATE.getCode()) {
                couponSketchRepository.insert(couponSketchPO);
            } else {
                couponSketchRepository.update(couponSketchPO);
            }
            response.setSketchId(couponSketchPO.getId());
            log.info("CouponSketchService.createSketch end request:{} response:{}", request, response);
            return Result.success(response);
        } catch (Exception e) {
            log.error("CouponSketchService.createSketch error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 券草稿列表
     *
     * @param request
     * @return
     */
    @Override
    public Result<BasePageResponse<CouponSketchListVO>> querySketchList(CouponSketchListRequest request) {
        try {
            log.info("CouponSketchService.querySketchList begin request:{}", request);
            int pageNo = request.getPageNo();
            int pageSize = request.getPageSize();
            CouponSketchListParam param = couponSketchConvert.convertToParam(request);
            int totalCount = couponSketchRepository.searchCount(param);
            int totalPage = totalCount / pageSize + (totalCount % pageSize == 0 ? 0 : 1);
            List<CouponSketchPO> couponSketchPOList = couponSketchRepository.searchSketchList(param);
            List<CouponSketchListVO> couponSketchListVOList = couponSketchConvert.convertToVOList(couponSketchPOList);
            BasePageResponse<CouponSketchListVO> response = new BasePageResponse<>(pageNo, pageSize, totalCount, totalPage, couponSketchListVOList);
            log.info("CouponSketchService.querySketchList end request:{} response:{}", request, couponSketchListVOList);
            return Result.success(response);
        } catch (Exception e) {
            log.error("CouponSketchService.querySketchList error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 券草稿详情
     *
     * @param request
     * @return
     */
    @Override
    public Result<CouponSketchDetailResponse> querySketchDetail(CouponSketchRequest request) {
        try {
            log.info("CouponSketchService.querySketchDetail begin request:{}", request);
            CouponSketchPO couponSketchPO = couponSketchRepository.getSketchById(request.getSketchId());
            CouponConfigVO couponConfigVO = GsonUtil.fromJson(CompressUtil.decompress(couponSketchPO.getConfigCompress()), CouponConfigVO.class);
            couponConfigVO.setSendMode(couponSceneRepository.selectRelationSceneByCode(couponConfigVO.getSendScene()).getSendMode());

            // 预算信息
            String budgetApplyNo = Optional.ofNullable(couponConfigVO.getBudgetInfoDto()).map(BudgetInfoDto::getBudgetApplyNo).orElse(null);
            Long lineNum = Optional.ofNullable(couponConfigVO.getBudgetInfoDto()).map(BudgetInfoDto::getLineNum).orElse(null);
            BudgetInfoDto budgetInfoDto = brProxy.queryBudgetDetail(budgetApplyNo, lineNum);
            couponConfigVO.setBudgetInfoDto(budgetInfoDto);

            GoodsRuleDetailVO goodsRuleDetailVO = goodsService.searchGoodsDetailInfo(couponConfigVO.getGoodsRuleVO(), couponConfigVO.getPromotionRuleVO().getPromotionType(), couponSketchPO.getBizPlatform());

            CouponSketchDetailResponse response = new CouponSketchDetailResponse();
            response.setSketchId(request.getSketchId());
            response.setCouponConfigVO(couponConfigVO);
            response.setGoodsRuleDetailVO(goodsRuleDetailVO);
            response.setApplyAttachment(GsonUtil.fromListJson(couponSketchPO.getApplyAttachment(), ApplyAttachmentVO.class));

            log.info("CouponSketchService.querySketchDetail end request:{} response:{}", request, response);
            return Result.success(response);
        } catch (Exception e) {
            log.error("CouponSketchService.querySketchDetail error request:{}", request);
            return Result.fromException(e);
        }
    }

    /**
     * 删除草稿
     *
     * @param request
     * @return
     */
    @Override
    public Result<Boolean> deleteSketch(CouponSketchDeleteRequest request) {
        try {
            request.setOperator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());
            log.info("CouponSketchService.deleteSketch begin request:{}", request);
            couponSketchRepository.delete(request.getSketchId());
            log.info("CouponSketchService.deleteSketch end request:{}", request);
            return Result.success(true);
        } catch (Exception e) {
            log.error("CouponSketchService.deleteSketch error request:{}", request);
            return Result.fromException(e);
        }
    }
}
