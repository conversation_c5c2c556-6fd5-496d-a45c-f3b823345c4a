package com.xiaomi.nr.coupon.admin.infrastruture.rpc.order;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.OrderInfoVO;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.util.ResultValidator;
import com.xiaomi.nr.order.api.dto.request.order.LightOrderInfoReq;
import com.xiaomi.nr.order.api.dto.response.order.LightOrderInfoResp;
import com.xiaomi.nr.order.api.dto.response.orderquery.OrderDetailDto;
import com.xiaomi.nr.order.api.service.order.LightOrderInfoService;
import com.xiaomi.nr.order.api.service.orderquery.OrderQueryProvider;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OrderProxyService {

    //商城订单查询服务
    @Reference(check = false, interfaceClass = LightOrderInfoService.class, group = "${order.dubbo.group}", version = "1.0", timeout = 3000)
    private LightOrderInfoService lightOrderInfoService;

    //门店订单查询服务
    @Reference(check = false, interfaceClass = OrderQueryProvider.class, group = "${order.dubbo.group}", timeout = 3000)
    private OrderQueryProvider orderQueryProvider;

    @Autowired
    private OrderConvert orderConvert;


    /**
     * 商城订单查询
     * @param userId  用户id
     * @param orderId 订单id
     * @return LightOrderInfoResp
     * @throws BizError 业务异常
     */
    public LightOrderInfoResp onlineOrderInfo(long userId, long orderId) throws BizError {

        LightOrderInfoReq req = new LightOrderInfoReq();
        req.setUserId(userId);
        req.setOrderId(orderId);
        req.setType(1);
        req.setTradeFrom(0);

        try{
            Result<LightOrderInfoResp> result =  lightOrderInfoService.lightOrderInfo(req);
            ResultValidator.validate(result, "商城订单信息查询返回值code不为0");
            return result.getData();
        }catch (BizError bizError){
            log.error("OrderProxyService onlineOrderInfo bizError request:{}", req, bizError);
            throw bizError;
        }catch (Exception e){
            log.error("OrderProxyService onlineOrderInfo Exception request:{}", req, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "商城订单信息查询异常");
        }
    }



    /**
     * 门店订单查询
     * @param orderId 订单id
     * @return LightOrderInfoResp
     * @throws BizError 业务异常
     */
    public OrderDetailDto offlineOrderInfo(String orderId) throws BizError {

        try{
            Result<OrderDetailDto> result =  orderQueryProvider.detail(orderId);
            ResultValidator.validate(result, "门店订单信息查询返回值code不为0");
            return result.getData();
        }catch (BizError bizError){
            log.error("OrderProxyService offlineOrderInfo bizError request:{}", orderId, bizError);
            throw bizError;
        }catch (Exception e){
            log.error("OrderProxyService offlineOrderInfo Exception request:{}", orderId, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "门店订单信息查询异常");
        }
    }


    /**
     * 获取订单信息
     * @param offline  线上或线下使用
     * @param userId   用户id
     * @param orderId  订单id
     * @return OrderInfoVO
     * @throws BizError 业务异常
     */
    public OrderInfoVO getOrderInfo(int offline, long userId, long orderId) throws BizError {

        //线上订单
        if(offline == CommonConstant.ZERO_INT) {
            return orderConvert.convertOnlineOrderInfoVO(onlineOrderInfo(userId, orderId));
        }

        //线下订单
        return orderConvert.convertOfflineOrderInfoVO(offlineOrderInfo("SA" + orderId));
    }

}
