package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.posthandler;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.datapreparewrapper.BaseDataPrepareWrapper;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.datapreparewrapper.DataPrepareWrapperFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.EventContext;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class DataPreparePostHandler extends BaseCouponPostHandler {

    @Resource
    private DataPrepareWrapperFactory dataPrepareWrapperFactory;

    private final List<Integer> matchBizPlatform = Lists.newArrayList(
            BizPlatformEnum.AUTO_TEST.getCode(),
            BizPlatformEnum.RETAIL.getCode(),
            BizPlatformEnum.CAR.getCode(),
            BizPlatformEnum.CAR_AFTER_SALE.getCode(),
            BizPlatformEnum.CAR_SHOP.getCode()
    );

    @Override
    public void createPost(CouponCreateEvent event) throws Exception {

        BizPlatformEnum bizPlatformEnum = BizPlatformEnum.valueOf(event.getBizPlatform());
        BaseDataPrepareWrapper dataPrepareWrapper = dataPrepareWrapperFactory.getDataPrepareWrapper(bizPlatformEnum);

        EventContext eventContext = dataPrepareWrapper.prepareGoodsInfo(event);
        event.setEventContext(eventContext);
    }

    @Override
    public void updatePost(CouponUpdateEvent event) throws Exception {

        BizPlatformEnum bizPlatformEnum = BizPlatformEnum.valueOf(event.getBizPlatform());
        BaseDataPrepareWrapper dataPrepareWrapper = dataPrepareWrapperFactory.getDataPrepareWrapper(bizPlatformEnum);

        EventContext eventContext = dataPrepareWrapper.prepareGoodsInfo(event);
        event.setEventContext(eventContext);
    }


    @Override
    public void updateStatusPost(CouponUpdateStatusEvent event) throws Exception {

    }

    @Override
    public int order() {
        return 0;
    }

    @Override
    public Boolean bizPlatformMatch(Integer bizPlatform) {
        return matchBizPlatform.contains(bizPlatform);
    }

}
