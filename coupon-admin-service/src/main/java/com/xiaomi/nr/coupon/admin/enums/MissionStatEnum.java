package com.xiaomi.nr.coupon.admin.enums;

/**
 * 发券任务状态枚举类
 */
public enum MissionStatEnum {

    ADD("add", "已添加"),

    HISTORY("history", "历史任务"),

    APPROVED("approved","已通过"),

    REJECT("reject","已拒绝"),

    CANCEL("cancel","已取消"),

    SEND("send","已发放"),

    FAILED("failed","失败");

    private final String value;
    private final String name;

    MissionStatEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static String findByValue(String value) {
        MissionStatEnum[] values = MissionStatEnum.values();
        for (MissionStatEnum item : values) {
            if (item.getValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }
}
