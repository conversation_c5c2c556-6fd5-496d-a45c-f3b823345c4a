package com.xiaomi.nr.coupon.admin.enums.task;

import com.xiaomi.nr.coupon.admin.infrastruture.constant.coupon.ZkPathConstant;

/**
 * 下载类型枚举
 */
public enum DownloadTypeEnum {

    TASK_DETAIL(1, "灌券失败详情", ZkPathConstant.FILL_COUPON_DETAIL_PATH),
    FAIL_UID(2, "灌券失败名单", ZkPathConstant.FILL_COUPON_FAIL_UID_DATA_PATH),
    FILL_USER_LIST(3, "灌券用户集", ZkPathConstant.FILL_COUPON_UPLOAD_PATH);

    public int code;
    public String prefix;
    public String hdfsPath;

    public int getCode(){
        return this.code;
    }
    public String getPrefix(){
        return this.prefix;
    }
    public String getHdfsPath(){
        return this.hdfsPath;
    }

    DownloadTypeEnum(int code, String prefix, String hdfsPath) {
        this.code = code;
        this.prefix = prefix;
        this.hdfsPath = hdfsPath;
    }


    public static DownloadTypeEnum getByCode(int code) {
        DownloadTypeEnum[] values = DownloadTypeEnum.values();
        for (DownloadTypeEnum item : values) {
            if (item.getCode()==code) {
                return item;
            }
        }
        return null;
    }
}
