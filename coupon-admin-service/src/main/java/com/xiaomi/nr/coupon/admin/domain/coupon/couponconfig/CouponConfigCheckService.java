package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.CouponConfigBaseCheck;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.goods.CouponGoodsBaseHandler;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.posthandler.BaseCouponPostHandler;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class CouponConfigCheckService implements InitializingBean {

    @Autowired
    private List<CouponConfigBaseCheck> couponCheckHandlers;

    @Autowired
    private List<CouponGoodsBaseHandler> couponGoodsBaseHandlers;

    @Autowired
    private List<BaseCouponPostHandler> baseCouponPostHandlers;

    private final Map<String, CouponConfigBaseCheck> checkMap =new HashMap<>();

    private final Map<Integer, CouponGoodsBaseHandler> goodsMap =new HashMap<>();

    /**
     * 自动化测试后置处理器列表
     */
    private final List<BaseCouponPostHandler> autoTestCouponPostList = new ArrayList<>();

    /**
     * 3C后置处理器列表
     */
    private final List<BaseCouponPostHandler> retailCouponPostList = new ArrayList<>();

    /**
     * 整车后置处理器列表
     */
    private final List<BaseCouponPostHandler> carCouponPostList = new ArrayList<>();

    /**
     * 售后服务后置处理器列表
     */
    private final List<BaseCouponPostHandler> carServiceCouponPostList = new ArrayList<>();

    /**
     * 车商城后置处理器列表
     */
    private final List<BaseCouponPostHandler> carShopCouponPostList = new ArrayList<>();


    public  CouponConfigBaseCheck getCheckHandler(String s){
        return checkMap.get(s);
    }

    public  CouponGoodsBaseHandler getGoodsHandler(Integer i){
        return goodsMap.get(i);
    }

    /**
     * 获取后置处理器
     *
     * @param bizPlatformEnum   业务领域
     * @return                  后置处理器列表
     * @throws BizError         业务异常
     */
    public List<BaseCouponPostHandler> getCouponPostHandlers(BizPlatformEnum bizPlatformEnum) throws BizError {

        switch (bizPlatformEnum) {
            case AUTO_TEST:
                return autoTestCouponPostList;
            case RETAIL:
                return retailCouponPostList;
            case CAR:
                return carCouponPostList;
            case CAR_AFTER_SALE:
                return carServiceCouponPostList;
            case CAR_SHOP:
                return carShopCouponPostList;
            default:
                throw ExceptionHelper.create(GeneralCodes.InternalError, "非法业务领域");
        }
    }

    /**
     * 处理创建事件
     *
     * @param bizPlatformEnum   业务领域
     * @param event             创建事件
     * @throws Exception        异常
     */
    public void handleCreateEvent(BizPlatformEnum bizPlatformEnum, CouponCreateEvent event) throws Exception {
        List<BaseCouponPostHandler> couponPostHandlers = getCouponPostHandlers(bizPlatformEnum);
        for (BaseCouponPostHandler couponPostHandler : couponPostHandlers) {
            couponPostHandler.createPost(event);
        }
    }

    /**
     * 处理更新事件
     *
     * @param bizPlatformEnum   业务领域
     * @param event             创建事件
     * @throws Exception        异常
     */
    public void handleUpdateEvent(BizPlatformEnum bizPlatformEnum, CouponUpdateEvent event) throws Exception {
        List<BaseCouponPostHandler> couponPostHandlers = getCouponPostHandlers(bizPlatformEnum);
        for (BaseCouponPostHandler couponPostHandler : couponPostHandlers) {
            couponPostHandler.updatePost(event);
        }
    }

    /**
     * 处理上下线事件
     *
     * @param bizPlatformEnum   业务领域
     * @param event             创建事件
     * @throws Exception        异常
     */
    public void handleUpdateStatusEvent(BizPlatformEnum bizPlatformEnum, CouponUpdateStatusEvent event) throws Exception {
        List<BaseCouponPostHandler> couponPostHandlers = getCouponPostHandlers(bizPlatformEnum);
        for (BaseCouponPostHandler couponPostHandler : couponPostHandlers) {
            couponPostHandler.updateStatusPost(event);
        }
    }

    @Override
    public void afterPropertiesSet() {
        for (CouponConfigBaseCheck check : couponCheckHandlers) {
            checkMap.put(check.getPromotionType().getValue() + "_" + check.getBizPlatformEnum().getCode(), check);
        }

        for (CouponGoodsBaseHandler goodsHandler : couponGoodsBaseHandlers) {
            goodsMap.put(goodsHandler.getScopeType().getValue(), goodsHandler);
        }

        // 先根据执行顺序进行排序，再根据业务领域进行分发
        List<BaseCouponPostHandler> sortedCouponPostHandlers = baseCouponPostHandlers.stream()
                .sorted(Comparator.comparing(BaseCouponPostHandler::order))
                .collect(Collectors.toList());
        for(BaseCouponPostHandler couponPostHandler : sortedCouponPostHandlers) {
            if(couponPostHandler.bizPlatformMatch(BizPlatformEnum.AUTO_TEST.getCode())) {
                autoTestCouponPostList.add(couponPostHandler);
            }
            if(couponPostHandler.bizPlatformMatch(BizPlatformEnum.RETAIL.getCode())) {
                retailCouponPostList.add(couponPostHandler);
            }
            if (couponPostHandler.bizPlatformMatch(BizPlatformEnum.CAR.getCode())) {
                carCouponPostList.add(couponPostHandler);
            }
            if (couponPostHandler.bizPlatformMatch(BizPlatformEnum.CAR_AFTER_SALE.getCode())) {
                carServiceCouponPostList.add(couponPostHandler);
            }
            if (couponPostHandler.bizPlatformMatch(BizPlatformEnum.CAR_SHOP.getCode())) {
                carShopCouponPostList.add(couponPostHandler);
            }

        }
    }
}
