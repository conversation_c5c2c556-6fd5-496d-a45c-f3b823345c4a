package com.xiaomi.nr.coupon.admin.util;

import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * 数字类型工具类
 */
@Slf4j
public class NumberUtil {
    /**
     * 所有数据进行汇总Long
     *
     * @param nums 数据集, 遇到NULL转为0
     * @return 汇总后
     */
    public static long sum(Long... nums) {
        if (nums == null || nums.length == 0) {
            return 0L;
        }
        long total = 0L;
        for (Long num : nums) {
            total += (num == null ? 0L : num);
        }
        return total;
    }

    /**
     * 所有数据进行汇总Integer
     *
     * @param nums 数据集, 遇到NULL转为0
     * @return 汇总后
     */
    public static int sum(Integer... nums) {
        if (nums == null || nums.length == 0) {
            return 0;
        }
        int total = 0;
        for (Integer num : nums) {
            total += (num == null ? 0 : num);
        }
        return total;
    }

    /**
     * 是否在范围内
     *
     * @param target 目标数
     * @param begin  开始
     * @param end    结束
     * @return 左闭右闭
     */
    public static boolean inRange(Long target, long begin, long end) {
        if (target < begin) {
            return false;
        }
        if (target > end) {
            return false;
        }
        return true;
    }



    /**
     * 金额单位转换(分转元)
     *
     * @param value        单位是分的金额
     * @param roundingMode 舍入模式 (RoundingMode.UP/RoundingMode.DOWN)
     * @return String      单位是元的金额
     */
    public static String centToYuan(long value, int scale, RoundingMode roundingMode){
        try{
            BigDecimal showValue = new BigDecimal(String.valueOf(value));
            return showValue.divide(new BigDecimal("100"), scale, roundingMode).stripTrailingZeros().toPlainString();
        }catch (Exception e){
            log.warn("coupon.NumberUtil.centToYuan(), Exception error:", e);
        }
        return null;
    }


    /**
     * 金额单位转换(角转元)
     *
     * @param value        单位是角的金额
     * @param roundingMode 舍入模式 (RoundingMode.UP/RoundingMode.DOWN)
     * @return String      单位是元的金额
     */
    public static String tenCentToYuan(long value, int scale, RoundingMode roundingMode){
        try{
            BigDecimal showValue = new BigDecimal(String.valueOf(value));
            return showValue.divide(new BigDecimal("10"), scale, roundingMode).stripTrailingZeros().toPlainString();
        }catch (Exception e){
            log.warn("coupon.NumberUtil.tenCentToYuan(), Exception error:", e);
        }
        return null;
    }

    /**
     * 金额单位转换(元转分)
     *
     * @param value        单位是元的金额
     * @param roundingMode 舍入模式 (RoundingMode.UP/RoundingMode.DOWN)
     * @return String      单位是分的金额
     */
    public static String yuanToCent(String value, int newScale, RoundingMode roundingMode){
        try{
            BigDecimal showValue = new BigDecimal(value);
            return showValue.multiply(new BigDecimal("100")).setScale(newScale, roundingMode).stripTrailingZeros().toPlainString();
        }catch (Exception e){
            log.warn("coupon.NumberUtil.yuanToCent(), Exception error:", e);
        }

        return null;
    }


    /**
     * 金额单位转换(元转角)
     *
     * @param value        单位是元的金额
     * @param roundingMode 舍入模式 (RoundingMode.UP/RoundingMode.DOWN)
     * @return String      单位是角的金额
     */
    public static String yuanToTenCent(String value, int newScale, RoundingMode roundingMode){
        try{
            BigDecimal showValue = new BigDecimal(value);
            return showValue.multiply(new BigDecimal("10")).setScale(newScale, roundingMode).stripTrailingZeros().toPlainString();
        }catch (Exception e){
            log.warn("coupon.NumberUtil.yuanToCent(), Exception error:", e);
        }
        return null;
    }

    /**
     * 金额单位转换(分转元)
     *
     * @param value        单位是分的金额
     * @param roundingMode 舍入模式 (RoundingMode.UP/RoundingMode.DOWN)
     * @return BigDecimal  单位是元的金额
     */
    public static BigDecimal centToYuanBigDecimal(long value, int scale, RoundingMode roundingMode){
        try{
            BigDecimal showValue = new BigDecimal(String.valueOf(value));
            return showValue.divide(new BigDecimal("100"), scale, roundingMode);
        }catch (Exception e){
            log.warn("coupon.NumberUtil.centToYuan(), Exception error:", e);
        }
        return null;
    }

    /**
     * 保留两位小数
     * @param price
     * @return
     */
    public static String format2DecimalPlaces(BigDecimal price , boolean isFormat){
        try{
            if(isFormat && price.compareTo(BigDecimal.ZERO) > CommonConstant.ZERO_INT){
                return "+" + new DecimalFormat("0.00").format(price);
            }
            return new DecimalFormat("0.00").format(price);
        }catch (Exception e){
            log.warn("admin.NumberUtil.format2DecimalPlaces(), Exception error:", e);
        }
        return StringUtils.EMPTY;
    }

}
