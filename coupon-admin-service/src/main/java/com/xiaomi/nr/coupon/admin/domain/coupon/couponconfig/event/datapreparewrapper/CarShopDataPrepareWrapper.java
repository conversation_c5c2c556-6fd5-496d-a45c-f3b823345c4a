package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.datapreparewrapper;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.EventContext;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/14
 */
@Component
public class CarShopDataPrepareWrapper extends BaseDataPrepareWrapper {


    @Override
    protected void prepareGoodsInfo(EventContext eventContext, CouponConfigPO couponConfigPO) throws Exception {
        // 车商城空跑即可，只依赖goodsItemPo
    }
}
