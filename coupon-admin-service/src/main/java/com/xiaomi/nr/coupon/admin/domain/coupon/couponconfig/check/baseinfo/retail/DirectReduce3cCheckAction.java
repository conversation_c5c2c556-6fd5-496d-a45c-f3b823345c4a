package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.retail;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.UseChannelVO;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.CouponConfigCheckService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.CouponCheckFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.DirectReduceCheckAction;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/22 19:31
 */
@Component
public class DirectReduce3cCheckAction extends DirectReduceCheckAction {
    @Autowired
    private CouponConfigCheckService couponCheckService;

    /**
     * 初始化（用来工厂和策略模式的注册）
     */
    @PostConstruct
    @Override
    public void init() {
        CouponCheckFactory.register(BizPlatformEnum.RETAIL, this);
    }

    /**
     * 券创建特殊校验
     */
    @Override
    public void createSpecialCheck(CouponBaseInfo info) throws BizError {
        // 3c商品使用渠道校验
        if (MapUtils.isEmpty(info.getUseChannel())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用渠道不能为空");
        }

        // 3c商品指定门店校验
        for (Map.Entry<Integer, UseChannelVO> entry : info.getUseChannel().entrySet()) {
            if (!entry.getValue().isAll() && CollectionUtils.isEmpty(entry.getValue().getLimitIds())) {
                throw ExceptionHelper.create(ErrCode.COUPON, "指定门店不能为空");
            }
        }

        if (info.getPromotionValue() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠值必须大于0");
        }
    }

    /**
     * 券修改特殊校验
     */
    @Override
    public void updateSpecialCheck(CouponBaseInfo info) throws BizError {
        if (info.getPromotionValue() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠值必须大于0");
        }
    }

    /**
     * 券创建商品校验
     */
    @Override
    public void goodsCheck(CouponConfigItem configItem) throws Exception {
        couponCheckService.getGoodsHandler(configItem.getCouponGoodsInfo().getScopeType()).handleGoods(configItem);
    }

    /**
     * 业务平台类型
     */
    @Override
    public BizPlatformEnum getBizPlatformEnum() {
        return BizPlatformEnum.RETAIL;
    }
}
