package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.posthandler;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.infrastruture.nacosconfig.NacosSwitchConfig;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.OmsCouponSyncRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OmsDataSyncPostHandler extends BaseCouponPostHandler{

    @Autowired
    private OmsCouponSyncRepository omsCouponSyncRepository;

    @Autowired
    private NacosSwitchConfig nacosSwitchConfig;

    @Override
    public void createPost(CouponCreateEvent event) {
        log.info("OmsDataSyncPostHandler.createPost start configId:{}", event.getData().getId());
        if(nacosSwitchConfig.isStopWriteOmsData()){
            return;
        }
        // 保证幂等  两张表处于一个事务，可只判断一张表
        if(omsCouponSyncRepository.checkXmStoreMarketCoupon(event.getData().getId())){
            log.info("OmsDataSyncPostHandler.createPost idempotent configId:{}", event.getData().getId());
            return;
        }
        omsCouponSyncRepository.insertXmStoreCoupon(event.getData(), event.getEventContext());
        log.info("OmsDataSyncPostHandler.createPost success, 写入b.d券数据成功, configId:{}", event.getData().getId());
    }

    @Override
    public void updatePost(CouponUpdateEvent event) {
        if(nacosSwitchConfig.isStopWriteOmsData()){
            return;
        }
        omsCouponSyncRepository.updateXmStoreCoupon(event.getData(), event.getEventContext());
        log.info("OmsDataSyncPostHandler.updatePost success, 更新b.d券数据成功, configId:{}", event.getData().getId());

    }



    @Override
    public void updateStatusPost(CouponUpdateStatusEvent event) {
        if(nacosSwitchConfig.isStopWriteOmsData()){
            return;
        }
        omsCouponSyncRepository.updateXmStoreCouponStatus(event.getData());
        log.info("OmsDataSyncPostHandler.updateStatusPost success, 更新b.d券数据成功, configId:{}", event.getData().getId());
    }

    @Override
    public int order() {
        return 3;
    }

    @Override
    public Boolean bizPlatformMatch(Integer bizPlatform) {
        return BizPlatformEnum.RETAIL.getCode().equals(bizPlatform);
    }

}
