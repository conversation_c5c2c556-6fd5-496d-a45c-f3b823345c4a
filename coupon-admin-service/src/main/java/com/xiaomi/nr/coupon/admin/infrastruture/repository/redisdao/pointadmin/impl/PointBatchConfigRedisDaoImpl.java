package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.pointadmin.impl;

import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.pointadmin.PointBatchConfigRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.pointadmin.po.PointBatchConfigCachePo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/6 16:57
 */
@Component
@Slf4j
public class PointBatchConfigRedisDaoImpl implements PointBatchConfigRedisDao {
    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringPointAdminRedisTemplate")
    private StringRedisTemplate redisStringTemplate;

    /**
     * 批次配置缓存cache key
     */
    private static final String KEY_POINT_BATCH_CONFIG_CACHE = "nr:car:point:config:";

    /**
     * 单个批次积分已发放额度cache key
     */
    private static final String KEY_POINT_BATCH_DISTRIBUTE_CACHE = "nr:car:point:distribute:";

    /**
     * 更新积分批次配置缓存数据
     *
     * @param cachePo cachePo
     */
    @Override
    public void setPointBatchConfigCache(PointBatchConfigCachePo cachePo) throws BizError {
        String configStr = GsonUtil.toJson(cachePo);
        ValueOperations<String, String> operations = redisStringTemplate.opsForValue();
        String key = KEY_POINT_BATCH_CONFIG_CACHE + cachePo.getId();
        Date endDate = TimeUtil.getOneYearLaterDate(cachePo.getEndTime());

        // 重试一次
        int maxRetries = 2;
        for (int i = 0; i < maxRetries; i++) {
            try {
                Date now = new Date();
                Duration duration = Duration.between(now.toInstant(), endDate.toInstant());
                operations.set(key, configStr, duration);
                break;
            } catch (Exception e) {
                if (i == maxRetries - 1) {
                    // 失败次数 > 最大重试次数 抛异常
                    log.error("PointBatchConfigRedisDaoImpl.setPointBatchConfigCache 写入redis积分批次配置信息缓存失败，err = ", e);
                    throw ExceptionHelper.create(ErrCode.POINT, "写入redis积分批次配置信息缓存失败");
                }
            }
        }
    }

    /**
     * 批量更新积分批次配置缓存数据
     *
     * @param cachePoList cachePoList
     */
    @Override
    public void setPointBatchConfigCache(List<PointBatchConfigCachePo> cachePoList) throws BizError {
        ValueOperations<String, String> operations = redisStringTemplate.opsForValue();

        for (PointBatchConfigCachePo cachePo : cachePoList) {
            String key = KEY_POINT_BATCH_CONFIG_CACHE + cachePo.getId();
            String configStr = GsonUtil.toJson(cachePo);
            Date endDate = TimeUtil.getOneYearLaterDate(cachePo.getEndTime());

            // 重试一次
            int maxRetries = 2;
            for (int i = 0; i < maxRetries; i++) {
                try {
                    Date now = new Date();
                    Duration duration = Duration.between(now.toInstant(), endDate.toInstant());
                    operations.set(key, configStr, duration);
                    break;
                } catch (Exception e) {
                    if (i == maxRetries - 1) {
                        // 失败次数 > 最大重试次数 抛异常
                        log.error("PointBatchConfigRedisDaoImpl.setPointBatchConfigCache 写入redis积分批次配置信息缓存失败，err = ", e);
                        throw ExceptionHelper.create(ErrCode.POINT, "写入redis积分批次配置信息缓存失败");
                    }
                }
            }

        }

    }

    /**
     * 批量更新积分批次发放数量缓存数据
     *
     * @param batchId batchId
     * @param value value
     */
    @Override
    public void updatePointBatchDistributeCache(Long batchId, int value) throws BizError {
        String key = KEY_POINT_BATCH_DISTRIBUTE_CACHE + batchId;
        ValueOperations<String, String> operations = redisStringTemplate.opsForValue();

        // 重试一次
        int maxRetries = 2;
        for (int i = 0; i < maxRetries; i++) {
            try {
                operations.set(key, String.valueOf(value));
                break;
            } catch (Exception e) {
                if (i == maxRetries - 1) {
                    // 失败次数 > 最大重试次数 抛异常
                    log.warn("PointBatchConfigRedisDaoImpl.updatePointBatchDistributeCache 写入redis 单个批次积分已发放额度信息缓存失败，key = {}, err = ", key, e);
                    throw ExceptionHelper.create(ErrCode.POINT, "写入redis单个批次积分已发放额度信息缓存失败");
                }
            }
        }

    }

    /**
     * 获取积分批次配置缓存数据
     *
     * @param batchId batchId
     * @return PointBatchConfigCachePo
     */
    @Override
    public PointBatchConfigCachePo getPointBatchConfigCache(Long batchId) {
        ValueOperations<String, String> operations = redisStringTemplate.opsForValue();
        String key = KEY_POINT_BATCH_CONFIG_CACHE + batchId;

        String configStr = operations.get(key);
        if (StringUtils.isBlank(configStr)){
            return null;
        }
        return GsonUtil.fromJson(configStr, PointBatchConfigCachePo.class);
    }

    /**
     * 获取积分批次配置已发数量缓存数据
     *
     * @param batchId batchId
     * @return 已发数量
     */
    @Override
    public Long getPointBatchDistributeCache(Long batchId) {
        ValueOperations<String, String> operations = redisStringTemplate.opsForValue();
        String key = KEY_POINT_BATCH_DISTRIBUTE_CACHE + batchId;

        String val = operations.get(key);
        if (StringUtils.isBlank(val)){
            return null;
        }
        return Long.valueOf(val);
    }

    /**
     * 批量获取积分批次配置已发数量缓存数据
     *
     * @param batchIdList batchIdList
     * @return Map<String, Long> key:batchId,value:已发数量
     */
    @Override
    public Map<Long, Long> getPointBatchDistributeCache(List<Long> batchIdList) {

        ValueOperations<String, String> operations = redisStringTemplate.opsForValue();
        List<String> keyList = batchIdList.stream().map(batchId -> KEY_POINT_BATCH_DISTRIBUTE_CACHE + batchId).collect(Collectors.toList());
        List<String> valueList = operations.multiGet(keyList);

        Map<Long, Long> sendCountMap = new HashMap<>(batchIdList.size());
        for (int i = 0; i < batchIdList.size(); i++) {
            Long batchId = batchIdList.get(i);
            String sendCount = valueList.get(i);
            if (StringUtils.isNotBlank(sendCount)) {
                sendCountMap.put(batchId, Long.parseLong(sendCount));
            }
        }

        return sendCountMap;
    }
}
