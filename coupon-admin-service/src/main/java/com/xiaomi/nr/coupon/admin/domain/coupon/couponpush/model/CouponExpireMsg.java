package com.xiaomi.nr.coupon.admin.domain.coupon.couponpush.model;

import lombok.Data;

/**
 * 券过期消息体
 * <AUTHOR>
 */
@Data
public class CouponExpireMsg {

    /**
     * 优惠券配置ID
     */
    private Long configId;

    /**
     * 投放场景
     */
    private String sendScene;

    /**
     * 用户优惠券ID（可以作为业务唯一标识，进行消息去重使用）
     */
    private Long couponId;

    /**
     * 券类型 1:商品券 2:运费券
     */
    private Integer couponType;

    /**
     * 券名称
     */
    private String couponName;

    /**
     * 过期时间
     */
    private Long endTime;

    /**
     * 拥有券的用户ID
     */
    private Long userId;

    /**
     * 分享人小米ID
     */
    private Long shareUserId;

    /**
     * 消息创建时间
     */
    private Long messageTime;
}
