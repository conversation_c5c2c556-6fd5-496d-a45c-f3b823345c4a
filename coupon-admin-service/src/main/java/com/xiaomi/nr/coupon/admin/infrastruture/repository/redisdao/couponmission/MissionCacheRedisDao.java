package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponmission;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponmission.po.MissionMapType;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponmission.po.MissionCacheItemPo;

import java.util.List;
import java.util.Map;

public interface MissionCacheRedisDao {

    /**
     * 写redis
     * 将发券任务基础信息写入缓存
     * @param missionCacheList
     */
    void setMissionDesc(List<MissionCacheItemPo> missionCacheList);

    /**
     * 写redis
     * 将发券任务id写入缓存
     * @param missionMapTypeList
     */
    void setMissionIdList(List<MissionMapType> missionMapTypeList);

    /**
     * 写redis
     * 将发券任务id写入缓存
     * @param missionMapTypeList
     */
    void setMissionIdListV2(List<MissionMapType> missionMapTypeList);

    /**
     * 写redis 将发放任务id和券id映射写入redis
     * @param map
     */
    void setMissionIdMap(Map<Long, Long> map);

    /**
     * 读redis
     * 获取发放任务缓存id和券配置id关系列表
     * @return List<Long>
     */
    List<MissionMapType> getMissionIds();

    /**
     * 读redis
     * 获取单个发放任务缓存信息
     * @param missionId 发放任务id
     * @return SingleMissionCache
     */
    MissionCacheItemPo getMissionCacheById(long missionId);

    /**
     * 读redis
     * 批量获取券发放任务信息
     * @param missionIds 发放任务id列表
     * @return List<SingleMissionCache>
     */
    List<MissionCacheItemPo> getMissionCacheList(List<Long> missionIds);


}
