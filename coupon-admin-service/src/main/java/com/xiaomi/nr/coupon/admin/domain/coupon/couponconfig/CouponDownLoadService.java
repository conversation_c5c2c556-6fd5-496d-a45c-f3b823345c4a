package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.fds.FileService;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.GoodsCouponPO;
import com.xiaomi.nr.coupon.admin.util.FileUtils;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

/**
 * 优惠券下载service
 *
 * <AUTHOR>
 * @date 2023-01-06
 */

@Service
public class CouponDownLoadService {

    @Autowired
    private FileService fileService;

    /**
     * 商品可用券信息上传fds
     * @param poList 券配置信息
     * @param goodsId 商品id
     * @return 地址
     * @throws Exception
     */
    public String uploadGoodsCoupon(List<GoodsCouponPO> poList, long goodsId) throws Exception {

        String fileName = "商品可用券-" + goodsId +"-" + TimeUtil.getNowUnixSecond();

        File file = new File(fileName);

        FileUtils.writeExcelFile(file,"商品可用券", poList, GoodsCouponPO.class);

        fileService.uploadFileToFds(getUploadCodeObjectName(fileName), file, true);

        return getUploadCouponCodeFdsUrl(fileName);
    }

    public String getUploadCouponCodeFdsUrl(String fileName){
        return "https://" + fileService.getEndpoint() + File.separator + fileService.getBucketName() + File.separator + getUploadCodeObjectName(fileName);
    }

    private String getUploadCodeObjectName(String fileName){
        return "coupon/uploadCoupon/goodsCoupon/"+fileName+".xlsx";
    }
}
