package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.warning.handler;

import com.xiaomi.goods.gms.dto.batch.BatchedInfoDto;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.nr.coupon.admin.api.enums.BizSubTypeEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.warning.WarnCheckResult;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.WarningType;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsOnlineStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.CouponConfigPoConvert;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.GoodsItemPo;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.goods.GmsProxyService;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 低库存校验
 * @author: hejiapeng
 * @Date 2022/7/11 9:04 下午
 * @Version: 1.0
 **/
@Slf4j
@Component
public class NoGoodsWarningCheckHandler extends AbstractWarningCheckHandler {

    @Autowired
    private CouponConfigPoConvert couponConfigPoConvert;

    @Autowired
    private GmsProxyService gmsProxyService;

    
    @Override
    public WarnCheckResult doWarningCheck(CouponConfigPO couponConfigPO) throws BizError {

        WarnCheckResult warnCheckResult;
        AbstractWarningCheckHandler next = super.next();
        if (next != null) {
            warnCheckResult = next.doWarningCheck(couponConfigPO);
        } else {
            warnCheckResult = new WarnCheckResult();
        }

        GoodsItemPo goodsItemPo = couponConfigPoConvert.serializeGoodsItemPo(couponConfigPO);

        try {

            if (couponGoodConfigCheck(warnCheckResult, goodsItemPo)) {
                return warnCheckResult;
            }

            if (couponGoodOnShelfCheck(warnCheckResult, goodsItemPo)) {
                return warnCheckResult;
            }

        } catch (Exception e) {
            log.error("NoGoodsWarningCheckHandler check goods error, configId:{}", couponConfigPO.getId(), e);
        }
        return warnCheckResult;
    }

    /**
     * 券商品配置校验
     * @param warnCheckResult
     * @param goodsItemPo
     */
    private boolean couponGoodConfigCheck(WarnCheckResult warnCheckResult, GoodsItemPo goodsItemPo) {
        if (CollectionUtils.isEmpty(goodsItemPo.getSkuList()) && CollectionUtils.isEmpty(goodsItemPo.getPackageList())) {
            warnCheckResult.getCodes().add(WarningType.NO_GOODS.getCode());
            return true;
        }

        if (CollectionUtils.isNotEmpty(goodsItemPo.getSkuList()) && goodsItemPo.getSkuList().size() > 500) {
            return true;
        }

        if (CollectionUtils.isNotEmpty(goodsItemPo.getPackageList()) && goodsItemPo.getPackageList().size() > 500) {
            return true;
        }
        return false;
    }

    /**
     * 券配置商品上下架状态校验
     *
     * @param warnCheckResult
     * @param goodsItemPo
     * @throws Exception
     */
    private boolean couponGoodOnShelfCheck(WarnCheckResult warnCheckResult, GoodsItemPo goodsItemPo) throws Exception {

        if (CollectionUtils.isNotEmpty(goodsItemPo.getSkuList())) {
            List<SkuInfoDto> skuInfoDtos = gmsProxyService.queryListBySkuIds(goodsItemPo.getSkuList(), false, false, false, Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode()));
            for (SkuInfoDto skuInfoDto : skuInfoDtos) {
                if (skuInfoDto.getOnShelfOnline() == GoodsOnlineStatusEnum.ON_SHELF_ONLINE.getStat()
                        || skuInfoDto.getOnShelfOffline() == GoodsOnlineStatusEnum.ON_SHELF_OFFLINE.getStat()) {
                    return true;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(goodsItemPo.getPackageList())) {
            List<BatchedInfoDto> batchedInfoDtos = gmsProxyService.queryListByPackageIds(goodsItemPo.getPackageList(), false);
            for (BatchedInfoDto batchedInfoDto : batchedInfoDtos) {
                if (batchedInfoDto.getOnShelfOnline() == GoodsOnlineStatusEnum.ON_SHELF_ONLINE.getStat()
                        || batchedInfoDto.getOnShelfOffline() == GoodsOnlineStatusEnum.ON_SHELF_OFFLINE.getStat()) {
                    return true;
                }
            }
        }
        warnCheckResult.getCodes().add(WarningType.NO_GOODS.getCode());
        return true;
    }
}
