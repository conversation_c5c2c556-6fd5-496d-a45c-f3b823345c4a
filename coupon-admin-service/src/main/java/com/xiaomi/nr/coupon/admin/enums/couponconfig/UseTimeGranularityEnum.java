package com.xiaomi.nr.coupon.admin.enums.couponconfig;

import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 使用时间粒度枚举
 * @Date: 2022.06.08 12:48
 */
@Getter
public enum UseTimeGranularityEnum {
    HOUR(1, "小时"),
    DAY(2, "天");

    private int value;
    private String name;

    UseTimeGranularityEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }


    public static UseTimeGranularityEnum getByValue(int value) {
        UseTimeGranularityEnum[] values = UseTimeGranularityEnum.values();
        for (UseTimeGranularityEnum item : values) {
            if (item.getValue()==value) {
                return item;
            }
        }
        return null;
    }


    public static String getDesc(UseTimeGranularityEnum useTimeGranularityEnum, int useDuration) {
        /*switch (useTimeGranularityEnum){
            case HOUR:
                return "领取后" + useDuration + "小时有效";
            case DAY:
                return "领取后" + (useDuration / 24) + "天有效";
        }*/
        if (useDuration < 24) {
            return "领取后" + useDuration + "小时有效";
        }

        if (useDuration % 24 == CommonConstant.ZERO_INT) {
            return "领取后" + (useDuration / 24) + "天有效";
        }

        return "领取后" + (useDuration / 24) + "天" + (useDuration % 24) + "小时有效";
    }

    public static String getDescByValue(int value) {
        UseTimeGranularityEnum[] values = UseTimeGranularityEnum.values();
        for (UseTimeGranularityEnum item : values) {
            if (item.getValue()==value) {
                return item.getName();
            }
        }
        return StringUtils.EMPTY;
    }

}
