package com.xiaomi.nr.coupon.admin.domain.coupon.datastatistic;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.statistic.request.CouponDataStatisticRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.statistic.response.CouponDataStatisticVO;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.enums.coupondata.CouponDataTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.CouponDataStatisticPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.FileInfo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.adsTidb.entity.SearchCouponDataParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.adsTidb.po.CouponStatisticPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.util.FileUtils;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 优惠券数据分析领域服务
 * @author: hejiapeng
 * @Date 2022/5/26 9:53 上午
 * @Version: 1.0
 **/
@Service
public class CouponDataStatisticService {

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    /**
     * 优惠券数据聚合计算
     *
     * @param couponStatisticPos
     * @return
     */
    public List<CouponDataStatisticVO> calCouponAggreateData(List<CouponStatisticPo> couponStatisticPos, SearchCouponDataParam searchCouponDataParam) {

        if (CollectionUtils.isEmpty(couponStatisticPos)) {
            return Lists.newArrayList();
        }

        Map<String, List<CouponStatisticPo>> recordMap = couponStatisticPos.stream().collect(Collectors.groupingBy(i->String.valueOf(i.getCouponId())));

        return calCouponAggreateData(recordMap, searchCouponDataParam);
    }


    /**
     * 灌券数据聚合计算
     *
     * @param couponStatisticPos
     * @return
     */
    public List<CouponDataStatisticVO> calFillCouponAggreateData(List<CouponStatisticPo> couponStatisticPos, SearchCouponDataParam searchCouponDataParam) {

        if (CollectionUtils.isEmpty(couponStatisticPos)) {
            return Lists.newArrayList();
        }

        Map<String, List<CouponStatisticPo>> recordMap = couponStatisticPos.stream().collect(Collectors.groupingBy(CouponStatisticPo::getActId));

        return calCouponAggreateData(recordMap, searchCouponDataParam);
    }

    /**
     * 单页聚合后数据排序
     *
     * @param couponDataStatisticVOS
     * @param request
     * @return
     */
    public void sortCouponDataStatisticVOS(List<CouponDataStatisticVO> couponDataStatisticVOS, CouponDataStatisticRequest request) {
        if (SearchCouponDataParam.applyCount.equals(request.getOrderBy())) {
            couponDataStatisticVOS.sort((o1, o2) -> o2.getApplyCount() - o1.getApplyCount());
        } else if (SearchCouponDataParam.activityId.equals(request.getOrderBy())) {
            couponDataStatisticVOS.sort((o1, o2) -> o2.getActivityId().compareTo(o1.getActivityId()));
        } else {
            couponDataStatisticVOS.sort((o1, o2) -> o2.getConfigId() - o1.getConfigId());
        }
        if ("asc".equals(request.getOrderDirection())) {
            Collections.reverse(couponDataStatisticVOS);
        }
    }

    /**
     * 数据到处转换
     *
     * @param dataStatisticVOList
     * @param fileName
     * @return
     */
    public File getExportCouponDataFile(List<CouponDataStatisticVO> dataStatisticVOList, String fileName) {

        File file = new File(fileName);

        if (CollectionUtils.isEmpty(dataStatisticVOList)) {
            return file;
        }

        FileInfo fileInfo = new FileInfo();

        fileInfo.setSourceList(dataStatisticVOList.stream().map(x -> {
            CouponDataStatisticPO couponDataStatisticPO = new CouponDataStatisticPO();
            BeanMapper.copy(x, couponDataStatisticPO);
            couponDataStatisticPO.setUseRatio(x.getUseRatio() + "%");
            couponDataStatisticPO.setTotalUseRatio(x.getTotalUseRatio() + "%");
            return couponDataStatisticPO;
        }).collect(Collectors.toList()));
        fileInfo.setDestinationClass(CouponDataStatisticPO.class);

        FileUtils.writeExcelFile(file, Lists.newArrayList(fileInfo));
        return file;
    }

    /**
     * 券数据聚合计算
     *
     * @param recordMap
     * @return
     */
    private List<CouponDataStatisticVO> calCouponAggreateData(Map<String, List<CouponStatisticPo>> recordMap, SearchCouponDataParam searchCouponDataParam) {

        List<CouponScenePO> couponScenePOList = couponSceneRepository.searchAllBriefPO(false, BizPlatformEnum.RETAIL.getCode());

        Map<String, String> sceneMap = couponScenePOList.stream().collect(Collectors.toMap(CouponScenePO::getSceneCode, CouponScenePO::getName));

        List<CouponDataStatisticVO> dataStatisticVOList = Lists.newArrayList();

        Date endTime = searchCouponDataParam.getEndTime();
        Date startTime = searchCouponDataParam.getStartTime();

        for (List<CouponStatisticPo> statisticPos : recordMap.values()) {

            CouponDataStatisticVO couponDataStatisticVO = new CouponDataStatisticVO();

            Set<String> useChannel = new HashSet<>();

            Set<Long> calcaluteTime = new HashSet<>();

            fillBaseDataInfo(sceneMap, couponDataStatisticVO, statisticPos.get(0));

            for (CouponStatisticPo statisticPo : statisticPos) {

                boolean sendAdd = checkAddSendCnt(calcaluteTime, statisticPo);
                // 计算累积结果
                fillCouponDataTotalSum(statisticPo, couponDataStatisticVO, sendAdd);
                // 过滤出有效数据
                if (checkTimeInvalidData(endTime, startTime, statisticPo)) {
                    continue;
                }
                // 计算增量结果
                fillCouponDataSum(statisticPo, couponDataStatisticVO, sendAdd);
                UseChannelsEnum useChannelsEnum = UseChannelsEnum.getByAdsValue(statisticPo.getUsedChannel());
                if (useChannelsEnum != null) {
                    useChannel.add(useChannelsEnum.getName());
                }
            }

            couponDataStatisticVO.setSendType(CouponDataTypeEnum.findNameByCode(searchCouponDataParam.getSendType()));
            couponDataStatisticVO.setUseChannel(StringUtils.join(useChannel, ","));
            couponDataStatisticVO.calculateUseRatio();
            couponDataStatisticVO.calculateTotalUseRatio();
            couponDataStatisticVO.calculateRoi();

            dataStatisticVOList.add(couponDataStatisticVO);
        }

        return dataStatisticVOList;
    }


    /**
     * 填充基本展示信息
     *
     * @param sceneMap
     * @param couponDataStatisticVO
     * @param couponStatisticPo
     */
    private void fillBaseDataInfo(Map<String, String> sceneMap, CouponDataStatisticVO couponDataStatisticVO, CouponStatisticPo couponStatisticPo) {
        couponDataStatisticVO.setConfigId(couponStatisticPo.getCouponId());
        couponDataStatisticVO.setName(couponStatisticPo.getCouponName());
        couponDataStatisticVO.setSendScene(Optional.ofNullable(sceneMap.get(couponStatisticPo.getSendScene())).orElse(StringUtils.EMPTY));
        couponDataStatisticVO.setPromotionType(couponStatisticPo.getCouponTypeId());
        couponDataStatisticVO.setPromotionTypeName(couponStatisticPo.getCouponTypeName());
        couponDataStatisticVO.setPromotionDesc(couponStatisticPo.getUsageCondition());
        couponDataStatisticVO.setApplyCount(couponStatisticPo.getApplyCnt());
        couponDataStatisticVO.setActivityId(couponStatisticPo.getActId());
    }

    /**
     * 填充累加计算数据
     *
     * @param statisticPo
     * @param couponDataStatisticVO
     */
    private void fillCouponDataTotalSum(CouponStatisticPo statisticPo, CouponDataStatisticVO couponDataStatisticVO, boolean sendAdd) {
        if (sendAdd) {
            couponDataStatisticVO.addTotalSendCount(statisticPo.getFetchCnt());
        }
        couponDataStatisticVO.addTotalUseCount(statisticPo.getUsedCnt());
    }

    /**
     * 填充累加计算数据
     *
     * @param statisticPo
     * @param couponDataStatisticVO
     */
    private void fillCouponDataSum(CouponStatisticPo statisticPo, CouponDataStatisticVO couponDataStatisticVO, boolean sendAdd) {
        if (sendAdd) {
            couponDataStatisticVO.addSendCount(statisticPo.getFetchCnt());
        }
        couponDataStatisticVO.addUseCount(statisticPo.getUsedCnt());
        couponDataStatisticVO.addOrderAmount(statisticPo.getOrdAmt());
        couponDataStatisticVO.addReduceAmount(statisticPo.getReduceAmt());
    }

    /**
     * 数据时间过滤
     *
     * @param endTime
     * @param startTime
     * @param statisticPo
     * @return
     */
    private boolean checkTimeInvalidData(Date endTime, Date startTime, CouponStatisticPo statisticPo) {
        if (startTime != null && endTime != null && (statisticPo.getApplyTime().before(startTime) || statisticPo.getApplyTime().after(endTime))) {
            return true;
        }
        return false;
    }

    /**
     * 是否累加领取数量
     *
     * @param calcaluteTime
     * @param statisticPo
     * @return
     */
    private boolean checkAddSendCnt(Set<Long> calcaluteTime, CouponStatisticPo statisticPo) {
        long applyTime = TimeUtil.convertDateToLong(statisticPo.getApplyTime());
        boolean sendAdd = !calcaluteTime.contains(applyTime);
        if (sendAdd) {
            calcaluteTime.add(applyTime);
        }
        return sendAdd;
    }
}
