package com.xiaomi.nr.coupon.admin.domain.coupon.couponactivity.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class EventApiCouponItem implements Serializable {

    private static final long serialVersionUID = 3100024047111138445L;

    /**
     * mission_id 或 config_id
     */
    @SerializedName("id")
    private Integer id;

    /**
     * id的类型，1:发放任务ID, 23:配置ID
     */
    @SerializedName("type")
    private String type = "1";

    /**
     * 活动名称
     */
    @SerializedName("name")
    private String name;

    /**
     * sid
     */
    @SerializedName("sid")
    private String sid;

    /**
     * 使用渠道
     */
    @SerializedName("channel")
    private String channel;

    /**
     * client_id
     */
    @SerializedName("client_id")
    private String clientId;

    /**
     * 活动tag
     */
    @SerializedName("activity_tag")
    private String activityTag;

    /**
     * 券tag
     */
    @SerializedName("coupon_tag")
    private String couponTag;


    /**
     * bingo活动奖品人群Id
     */
    @SerializedName("data_block_id")
    private String dataBlockId;
}
