package com.xiaomi.nr.coupon.admin.domain.coupon.couponactivity.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ActivityCacheCouponItem implements Serializable {

    private static final long serialVersionUID = 3100044047111138445L;

    /**
     * mission_id 或 config_id
     */
    @SerializedName("id")
    private Integer id;

    /**
     * config_id
     */
    @SerializedName("coupon_type_id")
    private Integer configId;

    /**
     * 活动名称
     */
    @SerializedName("name")
    private String name;

    /**
     * sid
     */
    @SerializedName("sid")
    private String sid;

    /**
     * 使用渠道
     */
    @SerializedName("channel")
    private String channel;

    /**
     * client_id
     */
    @SerializedName("client_id")
    private String clientId;

    /**
     * 活动tag
     */
    @SerializedName("activity_tag")
    private String activityTag;

    /**
     * 券tag
     */
    @SerializedName("coupon_tag")
    private String couponTag;

    /**
     * 人群Id
     */
    @SerializedName("crowd")
    private String crowd;
}
