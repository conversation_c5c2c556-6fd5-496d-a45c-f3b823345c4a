package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale;

import com.xiaomi.nr.coupon.admin.api.enums.CouponServiceTypeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/10 09:58
 */
@Component
public class ServiceCouponCheckToolsFactory {
    /**
     * ServiceCouponCheckTools 工厂
     * Map<CouponServiceTypeEnum, ServiceCouponCheckTools>
     */
    private static final Map<CouponServiceTypeEnum, ServiceCouponCheckTools> SERVICE_COUPON_CHECK_MAP = new HashMap<>();

    public static void register(CouponServiceTypeEnum serviceTypeEnum, ServiceCouponCheckTools serviceCouponCheck) {
        if (Objects.nonNull(serviceTypeEnum)) {
            SERVICE_COUPON_CHECK_MAP.put(serviceTypeEnum, serviceCouponCheck);
        }
    }

    public ServiceCouponCheckTools getServiceCouponCheckTools(CouponServiceTypeEnum serviceTypeEnum) {
        return SERVICE_COUPON_CHECK_MAP.get(serviceTypeEnum);
    }
}
