package com.xiaomi.nr.coupon.admin.enums.goods;

/**
 * 商品品级 枚举
 *
 * <AUTHOR>
 */
public enum GoodsLevelEnum {
    /**
     * SKU
     */
    Sku("sku", "SKU"),

    /**
     * 货品
     */
    Goods("goods", "货品"),

    /**
     * 商品
     */
    Commodity("commodity", "商品"),

    /**
     * 套装
     */
    Package("package", "套装"),

    /**
     * 商品
     */
    Ssu("ssu", "ssu"),

    /**
     * 新套装
     */
    Suit("suit", "新套装"),

    /**
     * 品类
     */
    Group("group", "品类"),

    /**
     * 工时ssu
     */
    LabourHourSsu("labourHour", "工时ssu"),

    /**
     * 配件ssu
     */
    PartsSsu("parts", "配件ssu"),
    ;

    private final String value;
    private final String name;

    GoodsLevelEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static boolean findByValue(String value) {
        GoodsLevelEnum[] values = GoodsLevelEnum.values();
        for (GoodsLevelEnum item : values) {
            if (item.getValue().equals(value)) {
                return true;
            }
        }
        return false;
    }
}

