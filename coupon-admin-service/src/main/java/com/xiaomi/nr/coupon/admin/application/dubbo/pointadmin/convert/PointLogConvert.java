package com.xiaomi.nr.coupon.admin.application.dubbo.pointadmin.convert;

import com.google.gson.reflect.TypeToken;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.ModifyContentVO;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointBatchLogDto;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.PointsConfigLogOptTypeEnum;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.nr.coupon.admin.util.beandiff.BeanDiffEntity;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.model.PointModifyDimension;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsConfigLogPo;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.beandiff.BeanDiffUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Description: 场景
 * @Date: 2022.03.03 17:33
 */
@Component
public class PointLogConvert {

    public List<PointBatchLogDto> convertToDto(List<CarPointsConfigLogPo> carPointsConfigLogPos) throws Exception {
        List<PointBatchLogDto> couponLogListVOList = new ArrayList<>();
        for (CarPointsConfigLogPo carPointsConfigLogPo : carPointsConfigLogPos) {

            PointBatchLogDto pointBatchLogDto = new PointBatchLogDto();
            pointBatchLogDto.setLogId(carPointsConfigLogPo.getId());
            pointBatchLogDto.setOptType(carPointsConfigLogPo.getOptType());
            pointBatchLogDto.setOperator(carPointsConfigLogPo.getOperator());
            pointBatchLogDto.setOptTime(carPointsConfigLogPo.getCreateTime().getTime() / 1000);
            pointBatchLogDto.setOptContent(modifyContent(carPointsConfigLogPo.getOptType(), carPointsConfigLogPo.getContent()));

            couponLogListVOList.add(pointBatchLogDto);
        }
        return couponLogListVOList;
    }

    private List<ModifyContentVO> modifyContent(Integer OptType,String content) throws Exception {
        if (StringUtils.isEmpty(content)) {
            return null;
        }
        if (!OptType.equals(PointsConfigLogOptTypeEnum.UPDATE.getCode())) {
            return null;
        }
        List<ModifyContentVO> modifyContentVOList = new ArrayList<>();
        Map<Object, byte[]> configPOMap = GsonUtil.get().fromJson(content, new TypeToken<Map<String, byte[]>>() {}.getType());
        CarPointsBatchConfigPo oldPo = GsonUtil.get().fromJson(CompressUtil.decompress(configPOMap.get("old")), CarPointsBatchConfigPo.class);
        CarPointsBatchConfigPo newPo = GsonUtil.get().fromJson(CompressUtil.decompress(configPOMap.get("new")), CarPointsBatchConfigPo.class);
        PointModifyDimension oldDimension = convertPOToCouponDimension(oldPo);
        PointModifyDimension newDimension = convertPOToCouponDimension(newPo);

        // 填充基本信息和规则 diff
        Map<String, BeanDiffEntity> objectDiffs = BeanDiffUtil.compareObjectDiff(oldDimension, newDimension);
        if(MapUtils.isNotEmpty(objectDiffs)){
            for (Map.Entry<String, BeanDiffEntity> diffEntityEntry : objectDiffs.entrySet()) {
                BeanDiffEntity beanDiffEntity = diffEntityEntry.getValue();
                modifyContentVOList.add(new ModifyContentVO(beanDiffEntity.getFieldDesc(), generateEditDesc(beanDiffEntity.getOldValue(), beanDiffEntity.getNewValue())));
            }
        }
        return modifyContentVOList;
    }


    /**
     * 将po转为对比对象
     *
     * @param couponConfigPO
     * @return
     */
    private PointModifyDimension convertPOToCouponDimension(CarPointsBatchConfigPo couponConfigPO) {
        PointModifyDimension couponModifyDimension = new PointModifyDimension();
        couponModifyDimension.setName(couponConfigPO.getName());
        couponModifyDimension.setApplyCount(couponConfigPO.getApplyCount());
        couponModifyDimension.setStartTime(TimeUtil.formatSecond(couponConfigPO.getStartTime()));
        couponModifyDimension.setEndTime(TimeUtil.formatSecond(couponConfigPO.getEndTime()));
        couponModifyDimension.setWarningRatio(couponConfigPO.getWarningRatio());
        return couponModifyDimension;

    }

    /**
     * 生成类似 由...修改为...的描述
     *
     * @param origin
     * @param now
     * @param <T>
     * @param <K>
     * @return
     */
    private <T, K> String generateEditDesc(T origin, K now) {
        return "由" + "“" + origin + "”" + "修改为" + "“" + now + "”";
    }

}
