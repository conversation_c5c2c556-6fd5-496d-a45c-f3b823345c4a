package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * suffix item
 *
 * <AUTHOR>
 */
@Data
public class SuffixItem implements Serializable {

    private static final long serialVersionUID = 4491730842669719659L;

    /**
     * code max_price
     */
    @SerializedName("code")
    private String code;

    /**
     * value
     */
    @SerializedName("value")
    private String value;
}