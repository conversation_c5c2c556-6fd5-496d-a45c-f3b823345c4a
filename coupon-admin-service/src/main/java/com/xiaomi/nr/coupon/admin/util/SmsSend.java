package com.xiaomi.nr.coupon.admin.util;

import com.google.gson.Gson;
import com.xiaomi.miliao.zookeeper.EnvironmentType;
import com.xiaomi.miliao.zookeeper.ZKFacade;
import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.sms.SmsSDK;
import com.xiaomi.sms.constants.SdkConstatns;
import com.xiaomi.sms.result.SmsRespData;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * 按短信模板给手机发短信
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SmsSend {

    @Data
    public static class SendCommonRequest {
        private String userTel;
        private String templateId;
        private SmsSendData smsSendData;
    }

    @Data
    public static class SmsSendData implements Serializable {
        private String money;
        private String userId;
    }

    /**
     * 标准现金券,根据手机号发放短信通知
     */
    public String downlinkSms(SendCommonRequest sendCommonRequest) {
        //下发短信的手机号
        String address = sendCommonRequest.getUserTel();
        //下发短信的模版Id
        String templateId = sendCommonRequest.getTemplateId();
        //自定义参数，要求为json格式。
        // 1.locale参数表示发送区域，若想指定下发区域则自定义参数中必须携带locale参数，如未指定系统将按照默认模板发送。一般针对国际模版，需要指定下发国家的场景是用到，locale对应关系见附件1
        // 2.若模版中包含有自定义参数，则在此处按照key-value组装成json格式字符串进行传递，如：{"name":"张三"}。根据实际模版情况添加参数，没有自定义参数传空即可。
        SmsSendData smsSendData = sendCommonRequest.getSmsSendData();
        String paramJsonStr = GsonUtil.toJson(smsSendData);
        //短信下发
        SmsRespData res = null;
        try {
            res = SmsSDK.getInstance().sendMessage(address, templateId, paramJsonStr);
        } catch (TException e) {
            log.error("短信发送失败!");
            throw new BaseException(-1, "短信发送失败!");
        }
        String smsResult = GsonUtil.toJson(res);
        log.info("发放标准现金券用户通知信息发放成功,返回值如下:" + smsResult);
        return smsResult;
    }

    /**
     * 根据小米账号下发短信
     *
     * @throws TException TException
     */
    public void downlinkSmsByUid() throws TException {
        //设置zk环境，正式使用时会根据部署环境自动读取，不需要设置
        ZKFacade.getZKSettings().setEnviromentType(EnvironmentType.C3);
        // 小米账号
        String uid = "3150096657";
        // 下发短信的模版Id
        String templateId = "CL11111_100001";
        //自定义参数，要求同downlinkSms
        String paramJsonStr = "";
        //短信下发,传入小米账号
        SmsRespData res = SmsSDK.getInstance().sendMessageByUid(uid, templateId, paramJsonStr);
        Gson gson = new Gson();
        System.out.println(gson.toJson(res));
    }

    /**
     * 混合发送，可以传入不同的sendType,底层按照type不同分派执行不同的接口下发
     *
     * @throws TException TException
     */
    public void downlinkSmsByMix() throws TException {
        //设置zk环境，正式使用时会根据部署环境自动读取，不需要设置
        ZKFacade.getZKSettings().setEnviromentType(EnvironmentType.C3);
        //混合发送，0：代表用手机号发送；1：代表用uid发送。底层按照type不同分派执行不同的接口下发
        String sendType = SdkConstatns.SendType.Uid;
        // 下发短信的手机号或小米账号
        String uidOrAddress = "3150096657";
        // 下发短信的模版Id
        String templateId = "CL11111_100001";
        //自定义参数，要求同downlinkSms
        String paramJsonStr = "";
        //短信下发
        SmsRespData res = SmsSDK.getInstance().sendMessageByMix(sendType, uidOrAddress, templateId, paramJsonStr);
        Gson gson = new Gson();
        System.out.println(gson.toJson(res));
    }
}
