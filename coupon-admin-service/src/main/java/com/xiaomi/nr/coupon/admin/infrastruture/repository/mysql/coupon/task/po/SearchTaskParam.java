package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SearchTaskParam implements Serializable {
    private static final long serialVersionUID = -8156660810269395423L;

    /**
     * 任务类型
     */
    private Integer type;

    /**
     * 灌券任务id
     */
    private Long taskId;

    /**
     * 优惠券id
     */
    private List<Long> configIds;

    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    private Integer couponType;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 使用渠道
     */
    private String useChannel;

    /**
     * 灌券任务状态
     */
    private List<Integer> statusList;

    /**
     * 任务创建人
     */
    private String creator;

    /**
     * 灌券开始时间
     */
    private Long startTime;

    /**
     * 灌券结束时间
     */
    private Long endTime;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序顺序 (desc倒序, asc顺序)
     */
    private String orderDirection;

    /**
     * 当前页码
     */
    private Integer pageNo;

    /**
     * 页面大小
     */
    private Integer pageSize;

    /**
     * 特殊逻辑 合并3c和汽车 后续去掉
     */
    private String bizType;

}
