package com.xiaomi.nr.coupon.admin.application.dubbo.convert;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.request.CreatePermissionRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.request.QuerySceneListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.response.*;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.ScenePermissionStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.SceneListParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.ScenePermissionPO;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 场景
 * @Date: 2022.03.03 17:33
 */
@Component
public class CouponSceneConvert {

    /**
     * 场景查询不过滤标志
     */
    private static final int NOT_FILTER = -1;
    /**
     * 将场景PO列表转后台前端VO
     *
     * @param couponScenePOList
     * @return
     */
    public List<SceneListVO> convertToSceneVOList(List<CouponScenePO> couponScenePOList) {
        List<SceneListVO> sceneListVOList = new ArrayList<>();
        for (CouponScenePO couponScenePO : couponScenePOList) {
            SceneListVO sceneListVO = new SceneListVO();
            sceneListVO.setSceneId(couponScenePO.getId());
            sceneListVO.setSceneCode(couponScenePO.getSceneCode());
            sceneListVO.setName(couponScenePO.getName());
            sceneListVO.setSceneType(couponScenePO.getRelationSceneId());
            sceneListVO.setStatus(couponScenePO.getStatus());
            sceneListVO.setModifier(couponScenePO.getModifier());
            sceneListVO.setCouponTypeList(StringUtil.convertToIntegerList(couponScenePO.getCouponType()));
            sceneListVO.setBizPlatform(couponScenePO.getBizPlatform());
            if (couponScenePO.getUpdateTime() > 0) {
                sceneListVO.setUpdateTime(new Date(couponScenePO.getUpdateTime() * 1000));
            }
            sceneListVOList.add(sceneListVO);
        }
        return sceneListVOList;
    }

    /**
     * 将场景PO转详情response
     *
     * @param couponScenePO
     * @param response
     */
    public void convertToResponse(CouponScenePO couponScenePO, SceneDetailResponse response) {
        response.setSceneId(couponScenePO.getId());
        response.setName(couponScenePO.getName());
        response.setRelationSceneId(couponScenePO.getRelationSceneId());
        response.setIdGenerationType(couponScenePO.getIdGenerationType());
        response.setSceneDesc(couponScenePO.getSceneDesc());
        response.setSceneCode(couponScenePO.getSceneCode());
        response.setIdGenerationType(couponScenePO.getIdGenerationType());
        response.setSendMode(couponScenePO.getSendMode());
        response.setAssignMode(StringUtil.convertToIntegerList(couponScenePO.getAssignMode()));
        response.setApplyMark(couponScenePO.getApplyMark());
        response.setCreator(couponScenePO.getCreator());
        response.setCreateTime(couponScenePO.getCreateTime());
        response.setCouponTypeList(StringUtil.convertToIntegerList(couponScenePO.getCouponType()));
        response.setExtProps(StringUtil.convertToStringList(couponScenePO.getExtProps()));
        response.setBizPlatform(couponScenePO.getBizPlatform());
    }

    /**
     * 将授权PO列表转VO列表
     *
     * @param scenePermissionPOList
     * @return
     */
    public List<PermissionListVO> convertToPermissionVOList(List<ScenePermissionPO> scenePermissionPOList) {
        List<PermissionListVO> permissionListVOList = new ArrayList<>();
        for (ScenePermissionPO scenePermissionPO : scenePermissionPOList) {
            PermissionListVO permissionListVO = new PermissionListVO();
            permissionListVO.setPermissionId(scenePermissionPO.getId());
            permissionListVO.setAppId(scenePermissionPO.getAppId());
            permissionListVO.setAppName(scenePermissionPO.getAppName());
            permissionListVO.setAppContact(scenePermissionPO.getAppContact());
            permissionListVO.setCreator(scenePermissionPO.getCreator());
            permissionListVO.setAddTime(scenePermissionPO.getAddTime());
            permissionListVO.setStatus(scenePermissionPO.getStatus());
            permissionListVOList.add(permissionListVO);
        }
        return permissionListVOList;
    }

    /**
     * 转换授权PO实体
     *
     * @param request
     * @return
     */
    public ScenePermissionPO convertToPermissionPO(CreatePermissionRequest request) {
        ScenePermissionPO scenePermissionPO = new ScenePermissionPO();
        scenePermissionPO.setSceneId(request.getSceneId());
        scenePermissionPO.setAppId(request.getAppId());
        scenePermissionPO.setAppName(request.getAppName());
        scenePermissionPO.setAppContact(request.getAppContact());
        scenePermissionPO.setCreator(request.getCreator());
        scenePermissionPO.setStatus(ScenePermissionStatusEnum.INVALID.getCode());
        return scenePermissionPO;
    }

    /**
     * 转换场景类型列表
     * @param couponType -1 时返回所有场景，否则根据入参过滤
     * @param couponScenePOList
     * @param bizPlatform
     * @return
     */
    public List<CouponSceneTypeVO> convertCouponSceneTypeVOS(List<CouponScenePO> couponScenePOList, Integer couponType, Integer bizPlatform) {
        return this.convertCouponSceneTypeVOS(couponScenePOList, couponType, Lists.newArrayList(bizPlatform));
    }


    /**
     * 转换场景类型列表
     * @param couponType -1 时返回所有场景，否则根据入参过滤
     * @param couponScenePOList
     * @param bizPlatformList
     * @return
     */
    public List<CouponSceneTypeVO> convertCouponSceneTypeVOS(List<CouponScenePO> couponScenePOList, Integer couponType, List<Integer> bizPlatformList) {
        List<CouponSceneTypeVO> couponChannelTypeVOList = new ArrayList<>();
        if (couponType != NOT_FILTER) {
            couponScenePOList = couponScenePOList.stream().filter(couponScenePO -> {
                List<Integer> couponTypeList = StringUtil.convertToIntegerList(couponScenePO.getCouponType());
                return couponTypeList.contains(couponType);
            }).collect(Collectors.toList());
        }
        Map<Integer, List<CouponScenePO>> couponScenePOMap = couponScenePOList.stream()
                .collect(Collectors.groupingBy(CouponScenePO::getRelationSceneId));
        List<SceneEnum> sceneList = SceneEnum.getSceneList(bizPlatformList);
        for (SceneEnum sceneEnum : sceneList) {
            int type = sceneEnum.getCode();
            CouponSceneTypeVO couponSceneTypeVO = new CouponSceneTypeVO(type, sceneEnum.getDesc(), convertTOCatVO(couponScenePOMap.get(type)));
            couponChannelTypeVOList.add(couponSceneTypeVO);
        }
        return couponChannelTypeVOList;
    }


    public List<SceneCatVO> convertTOCatVO(List<CouponScenePO> couponScenePOList) {
        List<SceneCatVO> sceneCatVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(couponScenePOList)) {
            for (CouponScenePO couponScenePO : couponScenePOList) {
                List<String> extProps = StringUtil.convertToStringList(couponScenePO.getExtProps());
                SceneCatVO sceneCatVO = new SceneCatVO(couponScenePO.getSceneCode(), couponScenePO.getName(),
                        couponScenePO.getSceneDesc(), extProps, couponScenePO.getSendMode());
                sceneCatVOList.add(sceneCatVO);
            }
        }
        return sceneCatVOList;
    }

    public void convertToParam(QuerySceneListRequest request, SceneListParam sceneListParam) {
        sceneListParam.setSceneCode(request.getSceneCode());
        sceneListParam.setSceneType(request.getSceneType());
        sceneListParam.setName(request.getName());
        sceneListParam.setPageNo(request.getPageNo());
        sceneListParam.setPageSize(request.getPageSize());
        sceneListParam.setBizPlatform(request.getBizPlatform());
        if (request.getCouponType() != null) {
            sceneListParam.setCouponTypeInclude(String.valueOf(request.getCouponType()));
        }

    }

}
