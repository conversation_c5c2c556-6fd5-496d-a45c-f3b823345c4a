package com.xiaomi.nr.coupon.admin.domain.coupon.fillcoupontask;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponTaskListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponTaskVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.FillCouponDetailVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CreateFillCouponTaskRequest;
import com.xiaomi.nr.coupon.admin.enums.task.CouponTaskTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.task.FillCouponSpeedEnum;
import com.xiaomi.nr.coupon.admin.enums.task.TaskStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.task.UserGroupTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.coupon.ZkPathConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponTaskRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.FillCouponTaskPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.Param;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.ResultOutPut;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.TaskIdParentIdParamPO;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class FillCouponConvert {

    @Autowired
    private CouponTaskRepository fillCouponTaskRepository;

    /**
     * 包装任务信息返回值
     *
     * @param parentTasks     父任务信息列表
     * @param childTasks      子任务信息
     * @param parentIdToChildTask 父任务对应子任务id
     * @param idToCouponConfig  券配置id对应券信息
     * @return 任务列表返回值
     */
    public List<CouponTaskListVO> buildTaskVOs(List<FillCouponTaskPO> parentTasks, Map<Long, FillCouponTaskPO> childTasks, Map<Long, List<Long>> parentIdToChildTask, Map<Long, CouponConfigPO> idToCouponConfig) {

        //排队任务排队序号映射
        Map<Long, Integer> waitPositionMap = new HashMap<>();
        List<CouponTaskListVO> taskVOS = new ArrayList<>();
        for (FillCouponTaskPO taskPO : parentTasks) {
            CouponTaskListVO taskVO = new CouponTaskListVO();
            long configId = taskPO.getConfigId();
            taskVO.setTaskId(taskPO.getTaskId());
            taskVO.setName(taskPO.getTaskName());
            taskVO.setConfigId(configId);
            taskVO.setCouponType(idToCouponConfig.get(configId).getCouponType());
            taskVO.setCreateTime(new Date(taskPO.getCreateTime()*1000L));
            taskVO.setCouponName(idToCouponConfig.containsKey(configId) ? idToCouponConfig.get(configId).getName() : "--");
            if (taskPO.getStartTime() > 0L) {
                taskVO.setStartTime(TimeUtil.convertLongToDate(taskPO.getStartTime()));
            }
            taskVO.setCreator(StringUtils.isNotEmpty(taskPO.getCreator()) ? taskPO.getCreator() : "--");

            if (StringUtils.isNotEmpty(taskPO.getParams())) {
                Param param = GsonUtil.fromJson(taskPO.getParams(), Param.class);
                taskVO.setAddress(Objects.isNull(param) ? null : param.getAddress());
            }

            //申请总数
            Param param = GsonUtil.fromJson(taskPO.getParams(), Param.class);
            taskVO.setTotalCount(param.getApplyCount());

            //成功数
            long parentSuccessCount = CommonConstant.ZERO_LONG;
            if (StringUtils.isNotEmpty(taskPO.getResult())) {
                ResultOutPut resultOutPut = GsonUtil.fromJson(taskPO.getResult(), ResultOutPut.class);
                parentSuccessCount = Objects.isNull(resultOutPut) ? CommonConstant.ZERO_LONG : resultOutPut.getSuccessCount();
            }


            long parentId = taskPO.getTaskId();
            if (parentIdToChildTask.containsKey(parentId)) {
                //计算成功总数(包含子任务)
                long successCount = parentIdToChildTask.get(parentId).stream().mapToLong(childId -> {
                    if (childTasks.containsKey(childId) && StringUtils.isNotEmpty(childTasks.get(childId).getResult())) {
                        ResultOutPut resultOutPut = GsonUtil.fromJson(childTasks.get(childId).getResult(), ResultOutPut.class);
                        return Objects.isNull(resultOutPut) ? CommonConstant.ZERO_LONG : resultOutPut.getSuccessCount();
                    }
                    return CommonConstant.ZERO_LONG;
                }).sum();
                taskVO.setSuccessCount(successCount + parentSuccessCount);

                //获取最新的子任务,填充展示信息
                long lastTaskId = parentIdToChildTask.get(parentId).stream().mapToLong(childId -> childId).max().orElse(-1);
                if (childTasks.containsKey(lastTaskId)) {
                    FillCouponTaskPO childTask = childTasks.get(lastTaskId);
                    if (childTask.getFinishTime()>0) {
                        taskVO.setEndTime(TimeUtil.convertLongToDate(childTask.getFinishTime()));
                    }
                    taskVO.setProcessRate(childTask.getProcessRate());
                    taskVO.setStatus(convertStatus(childTask));
                }
            } else {
                //不包含子任务
                if (taskPO.getFinishTime()>0) {
                    taskVO.setEndTime(TimeUtil.convertLongToDate(taskPO.getFinishTime()));
                }
                taskVO.setProcessRate(taskPO.getProcessRate());
                taskVO.setStatus(convertStatus(taskPO));
                taskVO.setSuccessCount(parentSuccessCount);
            }
            //当且仅当查询的任务中有等待中任务才查询等待位置
            if (taskVO.getStatus() == TaskStatusEnum.READY.code) {
                if (waitPositionMap.isEmpty()) {
                    waitPositionMap = getPositionMap();
                }
                taskVO.setWaitPosition(Optional.ofNullable(waitPositionMap.get(taskVO.getTaskId())).orElse(CommonConstant.ZERO_INT));
            }

            if(taskVO.getSuccessCount() >= taskVO.getTotalCount()){
                taskVO.setProcessRate(100L);
            }
            taskVOS.add(taskVO);
        }
        return taskVOS;
    }



    /**
     * 灌券任务状态转换
     *
     * @param taskPO 任务实体
     * @return 灌券状态值
     */
    private int convertStatus(FillCouponTaskPO taskPO) {
        int status = taskPO.getStatus();

        //进行中，运行状态或者失败状态，并且重试次数没有超过三次，前端显示进行中
        if (status == TaskStatusEnum.RUNNING.getCode() || status == TaskStatusEnum.AWAIT.getCode()
                || status == TaskStatusEnum.READY.getCode() || status == TaskStatusEnum.PRE_AWAIT.getCode()
                || (status == TaskStatusEnum.FAIL.getCode() && taskPO.getRetryTimes() < CommonConstant.THREE_INT)) {
            return TaskStatusEnum.RUNNING.getCode();
        }

        //失败
        if (status == TaskStatusEnum.ERROR.getCode() || status == TaskStatusEnum.FAIL.getCode()) {
            return TaskStatusEnum.FAIL.getCode();
        }

        //已完成
        return TaskStatusEnum.SUCCESS.getCode();
    }



    /**
     * <p>获取等待中任务的排队位置，由于分布式环境获取任务的随机性
     * 排序结果为粗略估计：慢任务， id小的排前面</>
     * @return
     */
    private Map<Long, Integer> getPositionMap() {
        Map<Long, Integer> waitPositionMap = new HashMap<>();
        //获取等待中的任务id,父id
        List<TaskIdParentIdParamPO> taskIdParentIdParamVOList = fillCouponTaskRepository.getReadyTask(CommonConstant.THRESHOLD_TIME);
        taskIdParentIdParamVOList.forEach(taskIdParentIdParamVO -> {
            Map paramMap = GsonUtil.fromJson(taskIdParentIdParamVO.getParams(), Map.class);
            String taskSpeed = (String) Optional.ofNullable(paramMap.get("taskSpeed")).orElse("slowTask");
            taskIdParentIdParamVO.setSpeed(taskSpeed.trim().equals("slowTask") ? FillCouponSpeedEnum.SLOW.getCode():
                    FillCouponSpeedEnum.FAST.getCode());
        });
        //前端显示：慢任务优先，id小优先
        taskIdParentIdParamVOList.sort((a, b) -> {
            if (a.getSpeed() == b.getSpeed()) {
                return (int) (a.getTaskId() - b.getTaskId());
            } else {
                return a.getSpeed() - b.getSpeed();
            }
        });
        //排队数=正在运行中的任务数+等待位置
        int runningCount = fillCouponTaskRepository.getRunningCount(CommonConstant.THRESHOLD_TIME);
        for (int position = CommonConstant.ZERO_INT; position < taskIdParentIdParamVOList.size(); position++) {
            Long taskId = taskIdParentIdParamVOList.get(position).getTaskId();
            Long parentId = taskIdParentIdParamVOList.get(position).getParentId();
            if (parentId != CommonConstant.ZERO_LONG) {
                waitPositionMap.put(parentId, runningCount + position);
            } else {
                waitPositionMap.put(taskId, runningCount + position);
            }
        }
        return waitPositionMap;
    }



    /**
     * 封装灌券任务实体
     *
     * @param req 创建灌券任务参数
     * @return 灌券任务实体
     */
    public FillCouponTaskPO convertFillCouponTaskPO(CreateFillCouponTaskRequest req){
        if(Objects.isNull(req)){
            return null;
        }

        FillCouponTaskPO taskPO = new FillCouponTaskPO();
        taskPO.setTaskName(req.getTaskName());
        taskPO.setConfigId(req.getConfigId());
        taskPO.setCreateTime(TimeUtil.getNowUnixSecond());
        taskPO.setType(CouponTaskTypeEnum.COUPON_FILL.getCode());
        taskPO.setCreator(req.getCreator());
        taskPO.setStatus(TaskStatusEnum.AWAIT.getCode());
        taskPO.setDepartmentId(Objects.isNull(req.getDepartmentId()) ? String.valueOf(CommonConstant.ZERO_INT) : req.getDepartmentId());
        taskPO.setSource("2");
        taskPO.setBizPlatform(req.getBizType());

        Param param = new Param();
        param.setApplyCount(req.getApplyCount());
        param.setAddress(req.getHdfsAddr());
        param.setBatchId(req.getBatchId());
        param.setBatchName(req.getBatchName());
        param.setDataType(req.getUserGroupType());
        param.setCustomizeGroup(req.getCustomizeType());
        param.setCount(req.getUserGroupSize());
        param.setDistinct(req.getDistinct());
        param.setSendScene(req.getSendScene());

        taskPO.setParams(GsonUtil.toJson(param));
        return taskPO;
    }



    /**
     * 封装灌券任务详情信息
     *
     * @param taskPO     灌券任务实体
     * @param couponName 券配置名称
     * @return CouponTaskVO 灌券详情返回值
     */
    public CouponTaskVO convertCouponTaskVO(FillCouponTaskPO taskPO, String couponName, List<FillCouponTaskPO> childTasks) throws Exception {
        if(Objects.isNull(taskPO)){
            throw ExceptionHelper.create(ErrCode.COUPON, "灌券任务为空");
        }

        CouponTaskVO taskVO = new CouponTaskVO();
        taskVO.setName(taskPO.getTaskId() + "_" + taskPO.getTaskName());
        taskVO.setCouponName(taskPO.getConfigId() + "_" +couponName);

        Param param = GsonUtil.fromJson(taskPO.getParams(), Param.class);
        taskVO.setPlanCount(Objects.isNull(param) ? null : param.getApplyCount());
        taskVO.setUserGroupType(Objects.isNull(param) ? null : param.getDataType());
        taskVO.setCustomizeType(Objects.isNull(param) ? null : param.getCustomizeGroup());
        taskVO.setHdfsAddr(Objects.isNull(param) ? null : param.getAddress());
        taskVO.setDistinct(Objects.isNull(param) ? 1 : param.getDistinct());

        if(Objects.equals(UserGroupTypeEnum.DMI_USER_GROUP.getCode(), param.getDataType())){
            taskVO.setBatchId(param.getBatchId());
            taskVO.setBatchName(param.getBatchName());
        }

        if(CollectionUtils.isNotEmpty(childTasks)){
            FillCouponTaskPO lastTask = childTasks.stream().max(Comparator.comparing(FillCouponTaskPO::getTaskId)).get();
            taskVO.setProcessRate(lastTask.getProcessRate());
            taskVO.setStatus(convertStatus(lastTask));
        }else {
            taskVO.setProcessRate(taskPO.getProcessRate());
            taskVO.setStatus(convertStatus(taskPO));
        }
        return taskVO;
    }



    /**
     * 灌券任务重试构造子任务
     *
     * @param parentTask    父任务
     * @param lastChildTaskId 最近一次的子任务id
     * @return FillCouponTaskPO
     */
    public FillCouponTaskPO buildChildTask(FillCouponTaskPO parentTask, long lastChildTaskId, String creator){
        FillCouponTaskPO childTask = new FillCouponTaskPO();
        childTask.setConfigId(parentTask.getConfigId());
        childTask.setStatus(TaskStatusEnum.AWAIT.getCode());
        childTask.setType(CouponTaskTypeEnum.COUPON_FILL.getCode());
        childTask.setParentId(parentTask.getTaskId());

        Param parentParam = GsonUtil.fromJson(parentTask.getParams(), Param.class);
        Param param = new Param();
        param.setApplyCount(parentParam == null ? 0 : parentParam.getApplyCount());
        param.setAddress(ZkPathConstant.FILL_COUPON_FAIL_UID_DATA_PATH + parentTask.getTaskId() + "_" + lastChildTaskId);
        param.setDataType(UserGroupTypeEnum.CUSTOMIZE_USER_GROUP.getCode());

        childTask.setParams(GsonUtil.toJson(param));
        childTask.setTaskName(parentTask.getTaskName());
        childTask.setCreateTime(TimeUtil.getNowUnixSecond());
        childTask.setCreator(StringUtils.isNotEmpty(creator) ? creator : "bug");
        childTask.setDepartmentId(String.valueOf(CommonConstant.ZERO_INT));
        childTask.setSource("2");
        childTask.setBizPlatform(parentTask.getBizPlatform());
        return childTask;
    }


    /**
     * 灌券实体数据转换合并
     * @param allEntities  总实体集
     * @param failEntityDetails   灌券失败实体集
     * @return list
     * @throws BizError 业务异常
     */
    public List<FillCouponDetailVO> convertToFillCouponDetail(List<String> allEntities, List<FillCouponDetailVO> failEntityDetails) throws BizError {

        if(CollectionUtils.isNotEmpty(allEntities) && allEntities.size() > 10000){
            throw ExceptionHelper.create(ErrCode.COUPON, "灌券实体集大于1W, 请联系RD同学下载");
        }

        Map<String, Integer> failUserMap = new HashMap<>();
        Map<String, LinkedList<String>> failUserDetailMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(failEntityDetails)){
            for(FillCouponDetailVO item : failEntityDetails){

                if(failUserMap.containsKey(item.getEntity())){
                    failUserMap.put(item.getEntity(), failUserMap.get(item.getEntity()) + CommonConstant.ONE_INT);
                    failUserDetailMap.get(item.getEntity()).add(item.getReason());
                    continue;
                }

                failUserMap.put(item.getEntity(), CommonConstant.ONE_INT);
                failUserDetailMap.put(item.getEntity(), Lists.newLinkedList(Collections.singletonList(item.getReason())));
            }
        }

        List<FillCouponDetailVO> result = Lists.newLinkedList();
        for(String entity : allEntities){

            if(failUserMap.containsKey(entity) && failUserMap.get(entity) > CommonConstant.ZERO_INT){
                result.add(new FillCouponDetailVO(entity, CommonConstant.ZERO_INT, failUserDetailMap.get(entity).getLast()));
                failUserMap.put(entity, failUserMap.get(entity) - CommonConstant.ONE_INT);
                failUserDetailMap.get(entity).removeLast();
                continue;
            }

            result.add(new FillCouponDetailVO(entity, CommonConstant.ONE_INT));

        }

        return result;
    }


}
