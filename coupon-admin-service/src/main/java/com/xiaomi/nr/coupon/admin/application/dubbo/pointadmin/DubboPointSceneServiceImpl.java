package com.xiaomi.nr.coupon.admin.application.dubbo.pointadmin;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.request.OperateSceneRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.response.SceneTypeVO;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.*;
import com.xiaomi.nr.coupon.admin.api.service.pointadmin.DubboPointSceneService;
import com.xiaomi.nr.coupon.admin.application.dubbo.pointadmin.convert.PointSceneConvert;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.PointSceneService;
import com.xiaomi.nr.coupon.admin.infrastruture.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CarPointParentSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CarPointSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsParentScenePo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsScenePo;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 积分场景
 * @Date: 2022.03.03 17:03
 */
@Component
@Slf4j
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
public class DubboPointSceneServiceImpl implements DubboPointSceneService {

    @Autowired
    private PointSceneService pointSceneService;

    @Autowired
    private CarPointSceneRepository pointSceneRepository;

    @Autowired
    private PointSceneConvert pointSceneConvert;

    @Autowired
    private CarPointParentSceneRepository carPointParentSceneRepository;


    @Override
    public Result<Boolean> createOrUpdateScene(PointSceneRequest request) {
        try {
            request.setOperator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());
            log.info("DubboPointSceneService.createOrUpdateSendScene begin request:{}", request);
            // 2、更新场景
            pointSceneService.createOrUpdateScene(request);
            return Result.success(true);
        } catch (Exception e) {
            log.error("DubboPointSceneService.createOrUpdateSendScene error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 积分场景新建与编辑
     *
     * @param request
     * @return
     */
    @Override
    public Result<Boolean> createOrUpdateSceneOpen(PointSceneOpenRequest request) {
        try {
            request.setOperator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());
            log.info("DubboPointSceneService.createOrUpdateSceneOpen begin request:{}", request);
            // 更新场景
            pointSceneService.createOrUpdateSceneOpen(request);
            return Result.success(true);
        } catch (Exception e) {
            log.error("DubboPointSceneService.createOrUpdateSceneOpen error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 上下线场景
     *
     * @param request
     * @return
     */
    @Override
    public Result<Boolean> operateScene(OperateSceneRequest request) {
        try {
            request.setOperator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());
            log.info("DubboPointSceneService.operateScene begin request:{}", request);
            if (request.getSceneId() <= 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "无效id");
            }
            pointSceneRepository.updateStatusById(request.getSceneId(), request.getType());
            log.info("DubboPointSceneService.operateScene end request:{}", request);
            return Result.success(true);
        } catch (Exception e) {
            log.error("DubboPointSceneService.operateScene error request:{}", request, e);
            return Result.fromException(e);
        }
    }


    /**
     * 查询所有场景并且分类 - 根据支持的券类型查询
     *
     * @param request
     * @return
     */
    @Override
    public Result<SceneWithCatResponse> searchSceneWithCat(SceneWithCatRequest request) {
        SceneWithCatResponse response = new SceneWithCatResponse();
        try {
            List<CarPointsScenePo> pointScenePOList = pointSceneRepository.getAllSceneList(request.getOnlyValid());
            // 查询一级场景类型，名称
            List<CarPointsParentScenePo> parentScenePOList = carPointParentSceneRepository.getAllParentSceneList();
            List<PointCategoryScene> couponChannelTypeVOList = pointSceneConvert.convertCouponSceneTypeVOS(pointScenePOList, parentScenePOList);
            response.setPointCategorySceneList(couponChannelTypeVOList);
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboPointSceneService.searchSceneWithCat error ", e);
            return Result.fromException(e);
        }
    }

    /**
     * 分页查询所有场景
     *
     * @param request
     * @return
     */
    @Override
    public Result<BasePageResponse<SceneTypeVO>> searchScenePage(SceneWithCatPageRequest request) {
        try {
            // 分页查询二级场景
            PageHelper.startPage(request.getPageNo(), request.getPageSize());
            List<CarPointsScenePo> pointScenePOList = pointSceneRepository.getAllSceneListPage(request.getOnlyValid(), request.getKeyWord());
            // 查询一级场景类型，名称
            List<CarPointsParentScenePo> parentScenePOList = carPointParentSceneRepository.getAllParentSceneList();
            List<SceneTypeVO> couponChannelTypeVOList = pointSceneConvert.convertSceneTypeVOS(pointScenePOList, parentScenePOList);

            // 处理分页结果
            PageInfo<CarPointsScenePo> pageInfo = new PageInfo<>(pointScenePOList);
            BasePageResponse<SceneTypeVO> basePageResponse = new BasePageResponse<>();
            basePageResponse.setPageNo(request.getPageNo());
            basePageResponse.setPageSize(request.getPageSize());
            basePageResponse.setList(couponChannelTypeVOList);
            basePageResponse.setTotalCount((int) pageInfo.getTotal());
            basePageResponse.setTotalPage(pageInfo.getPages());
            return Result.success(basePageResponse);
        } catch (Exception e) {
            log.error("DubboPointSceneService.searchSceneWithCat error ", e);
            return Result.fromException(e);
        }
    }

    /**
     * 查询关联预算池
     *
     * @return Result<SearchRelationBudgetResponse>
     */
    @Override
    public Result<SearchRelationBudgetResponse> searchRelationBudget() {
        try {
            SearchRelationBudgetResponse response = new SearchRelationBudgetResponse();

            // TODO: 使用mock数据
            List<RelationBudgetDto> budgetDtoList = Lists.newArrayList();
            budgetDtoList.add(new RelationBudgetDto(1001L, "测试预算池1"));
            budgetDtoList.add(new RelationBudgetDto(1002L, "测试预算池2"));
            budgetDtoList.add(new RelationBudgetDto(1003L, "测试预算池3"));
            budgetDtoList.add(new RelationBudgetDto(1004L, "测试预算池4"));
            budgetDtoList.add(new RelationBudgetDto(1005L, "测试预算池5"));
            budgetDtoList.add(new RelationBudgetDto(1006L, "测试预算池6"));

            response.setBudgetList(budgetDtoList);

            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboPointSceneService.searchRelationBudget error:", e);
            return Result.fromException(e);
        }
    }

}
