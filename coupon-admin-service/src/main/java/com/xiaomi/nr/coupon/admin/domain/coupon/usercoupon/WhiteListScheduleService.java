package com.xiaomi.nr.coupon.admin.domain.coupon.usercoupon;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.schedule.RefreshCouponUserWhiteListResp;
import com.xiaomi.nr.coupon.admin.infrastruture.nacosconfig.NacosSwitchConfig;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.UserCouponRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.whitelist.CouponUserWhiteListRedisDao;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.whitelist.impl.CouponUserWhiteListRedisDaoImpl.SHARD_COUNT;

/**
 * <AUTHOR>
 * @description
 * @date 2025-03-31 20:57
 */
@Slf4j
@Service
public class WhiteListScheduleService {
    
    private static final int PAGE_SIZE = 1000;

    @Autowired
    private UserCouponRepository userCouponRepository;

    @Autowired
    private CouponUserWhiteListRedisDao couponUserWhiteListRedisDao;

    @Autowired
    private NacosSwitchConfig nacosSwitchConfig;

    /**
     * 刷新用户券白名单
     */
    public RefreshCouponUserWhiteListResp refreshCouponUserWhiteList(boolean fullRefresh) throws Exception {
        RefreshCouponUserWhiteListResp result = new RefreshCouponUserWhiteListResp();
        long runStartTime = TimeUtil.getNowUnixMillis();
        if (CollectionUtils.isEmpty(nacosSwitchConfig.getCouponConfigIdList())) {
            log.warn("WhiteListScheduleService.refreshCouponUserWhiteList, 未获取到券配置id");
            return result;
        }
        int pageNo = 1;
        List<Long> userIdList;
        Set<Long> allUserIds = new HashSet<>();

        List<Long> configIds = nacosSwitchConfig.getCouponConfigIdList();
        long lastTime = 0;
        if (fullRefresh) {
            couponUserWhiteListRedisDao.clearUserWhiteListCache();
        } else {
            lastTime = couponUserWhiteListRedisDao.getLastMaxAddTime();
        }
        Long maxAddTime = userCouponRepository.getMaxAddTime(configIds, lastTime);

        if (Objects.isNull(maxAddTime) || lastTime >= maxAddTime) {
            log.info("WhiteListScheduleService.refreshCouponUserWhiteList, 白名单未更新, lastTime={}, maxAddTime={}", lastTime, maxAddTime);
            result.setMaxAddTime(maxAddTime);
            result.setWhiteListSize(0);
            return result;
        }

        // 分页查询用户ID，写入redis
        while (CollectionUtils.isNotEmpty(userIdList = fetchUserIds(configIds, pageNo, lastTime))) {
            Map<Integer, List<Long>> shardMap = userIdList.stream().collect(Collectors.groupingBy(userId -> (int) (userId % SHARD_COUNT)));
            for (Map.Entry<Integer, List<Long>> entry : shardMap.entrySet()) {
                couponUserWhiteListRedisDao.addUserToWhiteListCache(entry.getValue(), entry.getKey());
            }
            allUserIds.addAll(userIdList);
            pageNo++;
        }
        // 记录最新时间
        couponUserWhiteListRedisDao.setLastMaxAddTime(maxAddTime);
        result.setMaxAddTime(maxAddTime);
        result.setWhiteListSize(allUserIds.size());
        log.info("WhiteListScheduleService.refreshCouponUserWhiteList, 写入用户id白名单成功, size={}, costTime={}ms, fullRefresh:{}, userIds={}", allUserIds.size(), TimeUtil.sinceMillis(runStartTime), (lastTime == 0), allUserIds);
        return result;
    }

    private List<Long> fetchUserIds(List<Long> configIds, int pageNo, long lastTime) {
        return userCouponRepository.getUserIdByCouponType(configIds, PAGE_SIZE, (pageNo - 1) * PAGE_SIZE, lastTime);
    }

}
