package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.posthandler;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.SendSceneEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.WatermelonCouponServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CouponSyncPostHandler extends BaseCouponPostHandler{

    @Autowired
    private WatermelonCouponServiceProxy watermelonCouponServiceProxy;

    @Override
    public void createPost(CouponCreateEvent event) throws Exception {

    }

    @Override
    public void updatePost(CouponUpdateEvent event) throws Exception {

    }

    @Override
    public void updateStatusPost(CouponUpdateStatusEvent event) throws Exception {
        String sendScene = event.getData().getSendScene();
        Integer operateType= event.getData().getStatus();
        Integer promotionType = event.getData().getPromotionType();
        if (sendScene.equals(SendSceneEnum.Watermelon_Send.getCode())
                && operateType.equals(CouponConfigStatusEnum.ONLINE.getCode())
                && (promotionType.equals(PromotionTypeEnum.ConditionReduce.getValue())
                ||promotionType.equals(PromotionTypeEnum.ConditionDiscount.getValue())
                ||promotionType.equals(PromotionTypeEnum.DirectReduce.getValue()))) {
            watermelonCouponServiceProxy.syncCouponTemplate(event.getData());
        }
    }

    @Override
    public int order() {
        return 6;
    }

    @Override
    public Boolean bizPlatformMatch(Integer bizPlatform) {
        return BizPlatformEnum.RETAIL.getCode().equals(bizPlatform);
    }
}
