package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.auth.impl;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.auth.AppAuthRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.auth.po.AppAuthInfo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * AppAuth 缓存操作类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AppAuthRedisDaoImpl implements AppAuthRedisDao {
    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringKarosRedisTemplate")
    private StringRedisTemplate redisTemplate;


    /**
     * 新缓存操作对象
     */
    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisNewTemplate;

    /**
     * AppAuth缓存信息key
     */
    private static final String APPID_REDIS_KEY = "MINOS_APP_AUTH";

    /**
     * 新的AppAuth缓存信息key
     */
    private static final String APPID_NEW_REDIS_KEY = "minos:app:auth:{appId}";


    /**
     * 每次写入100个缓存
     */
    private static final int LIMIT_REDIS_SET_COUNT = 100;


    /**
     * 从缓存获取，AppAuth信息(用于鉴权、发券渠道校验等)
     *
     * @return Map<String, AppAuthInfo> AppAuthInfo缓存信息 map
     */
    @Override
    public Map<String, AppAuthInfo> get() {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String appAuthStr = operations.get(APPID_REDIS_KEY);
        if (StringUtils.isEmpty(appAuthStr)) {
            log.error("get appAuth cache fail, result is null, key={}", APPID_REDIS_KEY);
            return Collections.emptyMap();
        }

        Map<String, AppAuthInfo> resultMap = GsonUtil.fromMapJsonAppAuth(appAuthStr);
        if (CollectionUtils.isEmpty(resultMap)) {
            log.error("get appAuth cache fail, 解析json数据出错, key={}, value={}", APPID_REDIS_KEY, appAuthStr);
            return Collections.emptyMap();
        }
        return resultMap;
    }


    /**
     * 将格式化后的app信息写入新缓存
     *
     * @param map Map<String, AppAuthInfo>
     */
    @Override
    public void setNewAppAuth (Map<String, AppAuthInfo> map){
        if(CollectionUtils.isEmpty(map)){
            return;
        }

        ValueOperations<String, String> operations = redisNewTemplate.opsForValue();
        Map<String, String> data = new HashMap<>(LIMIT_REDIS_SET_COUNT);
        for(AppAuthInfo info : map.values()){

            if(StringUtils.isEmpty(info.getAppId())){
                continue;
            }

            String key = StringUtil.formatContent(APPID_NEW_REDIS_KEY, info.getAppId());
            data.put(key, GsonUtil.toJson(info));

            if (data.size() >= LIMIT_REDIS_SET_COUNT) {
                operations.multiSet(data);
                data = new HashMap<>();
            }

        }

        if (data.size() > 0) {
            operations.multiSet(data);
        }

    }


    /**
     * 获取新缓存的AppAuth
     *
     * @param appId  appId
     * @return AppAuthInfo
     */
    @Override
    public AppAuthInfo getNewAppAuth(String appId){
        if(StringUtils.isEmpty(appId)){
            return null;
        }

        String key = StringUtil.formatContent(APPID_NEW_REDIS_KEY, appId);
        ValueOperations<String, String> operations = redisNewTemplate.opsForValue();
        String appStr = operations.get(key);

        return GsonUtil.fromJson(appStr, AppAuthInfo.class);
    }

}

