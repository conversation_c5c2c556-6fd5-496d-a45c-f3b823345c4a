package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.userpoint.po;

import lombok.Data;

import java.io.Serializable;

/**
 * UserPointPo
 *
 * <AUTHOR>
 * @date 2023/12/7
 */
@Data
public class UserPointsPo implements Serializable {

    private static final long serialVersionUID = 7463489033392359239L;

    /**
     * 积分记录ID
     */
    private Long id;

    /**
     * mid
     */
    private Long mid;

    /**
     * 批次ID
     */
    private Long batchId;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 开始时间
     */
    private Long StartTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 状态
     */
    private Integer stat;

    /**
     * 场景编码
     */
    private String sendScene;

    /**
     * 总积分
     */
    private Long totalCount;

    /**
     * 余额
     */
    private Long balanceCount;

    /**
     * 扩展信息
     */
    private String extendInfo;

    /**
     * 激活时间
     */
    private Long activateTime;

    /**
     * 作废时间
     */
    private Long invalidTime;

    /**
     * 发放时间
     */
    private Long sendTime;

}