package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.GoodsItem;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import lombok.extern.slf4j.Slf4j;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.SearchConfigParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 提供查询使用的领域service
 * @author: hejiapeng
 * @Date 2023/3/30 11:15 上午
 * @Version: 1.0
 **/

@Service
@Slf4j
public class CouponConfigQueryService {

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    public Map<Long, Set<Long>> getLongSetMap(List<GoodsItem> skuList, GoodsLevelEnum sku, SearchConfigParam searchConfigParam) throws Exception {
        if(CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyMap();
        }

        Map<Long, Set<Long>> skuCouponRelMap = new HashMap<>();

        List<Long> skuIdList = skuList.stream().map(GoodsItem::getId).collect(Collectors.toList());

        List<List<Long>> skuPartitionList = Lists.partition(skuIdList, 10);

        for (List<Long> skuPartList : skuPartitionList) {
            try {
                Map<Long, Set<Long>> partSkuCouponRelMap = couponConfigRepository.getGoodsConfigSetMap(skuPartList, sku, searchConfigParam);
                skuCouponRelMap.putAll(partSkuCouponRelMap);
            } catch (Exception e) {
                log.error("couponConfigRepository.getGoodsConfigSetMap fail， good:{}, level:{}", skuPartList, sku.getName(), e);
            }
        }
        return skuCouponRelMap;
    }

}
