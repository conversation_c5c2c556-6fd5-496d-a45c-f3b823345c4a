package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponConfigDTO;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.FetchLimitTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseTimeTypeEnum;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Objects;
import java.util.Optional;

public class CouponConfigItemFactory {

    // 新建券配置聚合
    public static CouponConfigItem createCouponConfigItem(CouponConfigVO vo, Integer bizPlatform){
        CouponConfigItem couponConfigItem = new CouponConfigItem();
        CouponBaseInfo couponBaseInfo =new CouponBaseInfo();
        BeanMapper.copy(vo,couponBaseInfo);
        BeanMapper.copy(vo.getDistributionRuleVO(),couponBaseInfo);
        BeanMapper.copy(vo.getPromotionRuleVO(),couponBaseInfo);
        BeanMapper.copy(vo.getUseTermVO(),couponBaseInfo);
        if(UseTimeTypeEnum.RELATIVE.getValue() == couponBaseInfo.getUseTimeType()){
            couponBaseInfo.setEndUseTime(DateUtils.addHours(couponBaseInfo.getEndFetchTime(),couponBaseInfo.getUseDuration()));
        }

        CouponGoodsInfo couponGoodsInfo =new CouponGoodsInfo();
        BeanMapper.copy(vo.getGoodsRuleVO(),couponGoodsInfo);

        UserInfo userInfo =new UserInfo();
        BeanMapper.copy(vo,userInfo);

        if (null == couponBaseInfo.getServiceType()) {
            // 服务场景添加默认值，0
            couponBaseInfo.setServiceType(0);
        }

        if (null == couponBaseInfo.getFetchLimitType()) {
            // 限领类型添加默认值，1
            couponBaseInfo.setFetchLimitType(FetchLimitTypeEnum.LIMIT.getCode());
        }

        if (StringUtils.isEmpty(couponBaseInfo.getSendScene())) {
            // 投放场景，补充默认值
            couponBaseInfo.setSendScene(StringUtils.EMPTY);
        }

        // 财务BR信息
        if (Objects.nonNull(vo.getBudgetInfoDto())) {
            couponBaseInfo.setBudgetApplyNo(vo.getBudgetInfoDto().getBudgetApplyNo());
            couponBaseInfo.setLineNum(vo.getBudgetInfoDto().getLineNum());
            couponBaseInfo.setBudgetCreateTime(vo.getBudgetInfoDto().getBudgetCreateTime());
        }

        // fill biz platform
        couponBaseInfo.setBizPlatform(bizPlatform);

        couponConfigItem.setCouponBaseInfo(couponBaseInfo);
        couponConfigItem.setCouponGoodsInfo(couponGoodsInfo);
        couponConfigItem.setUserInfo(userInfo);
        return couponConfigItem;
    }

    /**
     * 新建券配置聚合
     *
     * @param dto
     * @param bizPlatform
     * @return
     */
    public static CouponConfigItem createCouponConfigItem(CouponConfigDTO dto, Integer bizPlatform){
        CouponConfigItem couponConfigItem = new CouponConfigItem();
        CouponBaseInfo couponBaseInfo = new CouponBaseInfo();
        couponBaseInfo.setBizPlatform(bizPlatform);
        BeanMapper.copy(dto, couponBaseInfo);
        BeanMapper.copy(dto.getDistributionRuleVO(), couponBaseInfo);
        BeanMapper.copy(dto.getPromotionRuleVO(), couponBaseInfo);
        BeanMapper.copy(dto.getUseTermVO(), couponBaseInfo);
        if (UseTimeTypeEnum.RELATIVE.getValue() == couponBaseInfo.getUseTimeType()) {
            couponBaseInfo.setEndUseTime(DateUtils.addHours(couponBaseInfo.getEndFetchTime(), couponBaseInfo.getUseDuration()));
        }

        CouponGoodsInfo couponGoodsInfo =new CouponGoodsInfo();
        BeanMapper.copy(dto.getGoodsRuleVO(), couponGoodsInfo);

        UserInfo userInfo = new UserInfo();
        BeanMapper.copy(dto, userInfo);

        couponConfigItem.setCouponBaseInfo(couponBaseInfo);
        couponConfigItem.setCouponGoodsInfo(couponGoodsInfo);
        couponConfigItem.setUserInfo(userInfo);
        return couponConfigItem;
    }




}
