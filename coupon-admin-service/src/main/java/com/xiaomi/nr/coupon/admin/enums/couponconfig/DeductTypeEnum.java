package com.xiaomi.nr.coupon.admin.enums.couponconfig;

/**
 * 是否调价商品可用 枚举
 *
 * <AUTHOR>
 */
public enum DeductTypeEnum {

    /**
     * 0元抵扣
     */
    Zero("zero_cent", 0, "0元抵扣"),

    /**
     * 1分钱抵扣
     */
    OneCent("one_cent", 1, "1分钱抵扣");

    private final String redisValue;
    private final Integer mysqlValue;
    private final String name;

    DeductTypeEnum(String redisValue, Integer mysqlValue, String name) {
        this.redisValue = redisValue;
        this.mysqlValue = mysqlValue;
        this.name = name;
    }

    public String getRedisValue() {
        return this.redisValue;
    }

    public Integer getMysqlValue() {
        return this.mysqlValue;
    }

    public String getName() {
        return name;
    }

    public static String findNameByRedisValue(String value) {
        DeductTypeEnum[] values = DeductTypeEnum.values();
        for (DeductTypeEnum item : values) {
            if (item.getRedisValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }

    public static String findRedisValueByMysqlValue(Integer value) {
        DeductTypeEnum[] values = DeductTypeEnum.values();
        for (DeductTypeEnum item : values) {
            if (item.getMysqlValue().equals(value)) {
                return item.getRedisValue();
            }
        }
        return null;
    }
}

