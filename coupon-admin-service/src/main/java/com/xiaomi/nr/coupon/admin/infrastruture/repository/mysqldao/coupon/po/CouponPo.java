package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.coupon.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CouponPo implements Serializable {
    private static final long serialVersionUID = -4820252929662680350L;

    /**
     * 优惠券ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 优惠券配置ID
     */
    private Long typeId;

    /**
     * 活动编号
     */
    private String activityId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 过期时间
     */
    private String endTime;

    /**
     * 有效天数
     */
    private Integer days;

    /**
     * 优惠券状态
     */
    private String stat;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 使用时间
     */
    private Long useTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 是否可用
     */
    private Integer isPass;

    /**
     * 后台人员编号
     */
    private Long adminId;

    /**
     * 后台人员姓名
     */
    private String adminName;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 优惠券发送类型
     */
    private String sendType;

    /**
     * 发券的订单号
     */
    private String fromOrderId;

    /**
     * 实际抵用金额
     */
    private BigDecimal replaceMoney;

    /**
     * 作废时间
     */
    private Long invalidTime;

    /**
     * 线下使用
     */
    private Integer offline;

    /**
     * 减免邮费
     */
    private BigDecimal reduceExpress;

    /**
     * 券的父id
     */
    private Long parentId;

    /**
     * 最后更新时间
     */
    private String lastUpdateTime;

    /**
     * 优惠券发放渠道，store_manager：店长券，store_order_gift：下单赠券，空：其他渠道
     */
    private String sendChannel;

    /**
     * 请求id（长度限制在64个字符内，非常重要，要求单用户级的全局唯一）
     */
    private String requestId;

    /**
     * 扩展json信息
     */
    private String extendInfo;
}
