package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.whitelist.impl;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.whitelist.CouponUserWhiteListRedisDao;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @description 整车优惠券白名单redis缓存
 * <AUTHOR>
 * @date 2025-03-31 19:37
*/
@Slf4j
@Component
public class CouponUserWhiteListRedisDaoImpl implements CouponUserWhiteListRedisDao {

    private static final String WHITE_LIST_SHARD_KEY = "car:coupon:user:whitelist:{shardId}";

    private static final String WHITE_LIST_LAST_TIME_KEY = "coupon:user:whitelist:last:time";

    public static final int SHARD_COUNT = 64;

    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("numberNewCouponRedisTemplate")
    private RedisTemplate<String, Number> redisTemplate;

    @Override
    public void addUserToWhiteListCache(List<Long> userIds, int shardId) throws BizError {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        SetOperations<String, Number> operations = redisTemplate.opsForSet();
        String key = StringUtil.formatContent(WHITE_LIST_SHARD_KEY, String.valueOf(shardId));
        Long[] userIdsArray = userIds.toArray(new Long[0]);
        try {
            operations.add(key, userIdsArray);
        } catch (Exception e) {
            log.error("CouponUserWhiteListRedisDaoImpl.addUserToWhiteListCache, 写入用户id白名单失败, userIds: {}, shardId: {}, err: ", userIds, shardId, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "白名单写入失败");
        }
    }

    @Override
    public void clearUserWhiteListCache() throws BizError {
        try {
            // 删除所有分片key
            for (int i = 0; i < SHARD_COUNT; i++) {
                String key = StringUtil.formatContent(WHITE_LIST_SHARD_KEY, String.valueOf(i));
                redisTemplate.delete(key);
            }
            // 删除最近添加时间
            redisTemplate.delete(WHITE_LIST_LAST_TIME_KEY);
        } catch (Exception e) {
            log.error("CouponUserWhiteListRedisDaoImpl.clearUserWhiteListCache error", e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "白名单缓存删除失败");
        }
    }


    @Override
    public void setLastMaxAddTime(long maxAddTime) {
        ValueOperations<String, Number> ops = redisTemplate.opsForValue();
        try {
            ops.set(WHITE_LIST_LAST_TIME_KEY, maxAddTime);
        } catch (Exception e) {
            log.error("CouponUserWhiteListRedisDaoImpl.setLastMaxAddTime error, maxAddTime: {}", maxAddTime, e);
        }
    }

    @Override
    public long getLastMaxAddTime() {
        try {
            ValueOperations<String, Number> ops = redisTemplate.opsForValue();
            Number time = ops.get(WHITE_LIST_LAST_TIME_KEY);
            if (Objects.isNull(time)) {
                return 0L;
            }
            return time.longValue();
        } catch (Exception e) {
            log.error("CouponUserWhiteListRedisDaoImpl.getLastMaxAddTime error", e);
            return 0L;
        }
    }
}
