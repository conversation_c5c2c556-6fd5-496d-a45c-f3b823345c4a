package com.xiaomi.nr.coupon.admin.domain.pointadmin;


import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.ChangePointBatchStatusRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointBatchDetailDto;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointBatchDetailRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.SavePointBatchRequest;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.PointConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.pointadmin.po.PointBatchConfigCachePo;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.BrProxy;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/5 17:24
 */
@Service
@Slf4j
public class PointBatchConfigService {
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private PointConfigRepository pointConfigRepository;

    @Autowired
    private BrProxy brProxy;

    /**
     * 单次批量更新积分批次配置数量 = 50
     */
    private static final int RENEW_POINT_BATCH_CONFIG_LIMIT = 50;

    /**
     * 保存积分批次配置信息 入参校验
     *
     * @param request request
     * @param isCreate isCreate
     */
    public void checkSavePointBatchRequest(SavePointBatchRequest request, boolean isCreate) throws BizError {
        log.info("PointBatchConfigService.SavePointBatchRequest begin, request = {}", request);

        if (Objects.isNull(request)) {
            log.error("PointBatchConfigService.checkSavePointBatchRequest request为空");
            throw ExceptionHelper.create(ErrCode.POINT, "request为空");
        }

        if (isCreate) {
            if (Objects.nonNull(request.getBatchId())) {
                log.error("PointBatchConfigService.checkSavePointBatchRequest batchId不为空");
                throw ExceptionHelper.create(ErrCode.POINT, "batchId不为空");
            }
        }

        Long startTime = request.getStartTime();
        Long endTime = request.getEndTime();
        Long currentTime = System.currentTimeMillis() / 1000;

        // 1、校验发放周期
//        if (startTime < currentTime) {
//            // 开始时间需大于当前时间
//            log.error("PointBatchConfigService.checkSavePointBatchRequest 发放周期开始时间需大于当前时间");
//            throw ExceptionHelper.create(ErrCode.POINT, "发放周期开始时间需大于当前时间");
//        }

        if (startTime > endTime) {
            // 结束时间需大于开始时间
            log.error("PointBatchConfigService.checkSavePointBatchRequest 发放周期结束时间需大于发放周期开始时间");
            throw ExceptionHelper.create(ErrCode.POINT, "发放周期结束时间需大于发放周期开始时间");
        }

        if (TimeUtil.getYearFromTimestamp(startTime) != TimeUtil.getYearFromTimestamp(endTime)) {
            // 禁止夸自然年
            log.error("PointBatchConfigService.checkSavePointBatchRequest 发放周期禁止夸自然年");
            throw ExceptionHelper.create(ErrCode.POINT, "发放周期禁止夸自然年");
        }

        // TODO: 2、积分关联预算池校验

        // TODO：3、积分发放场景校验

        // 4、积分总额校验
        // 4.1 积分金额需为大于0的正整数（已通过valid入参校验）
        // TODO:  4.2 金额上限需低于BR可用金额*10

        // 互斥规则
        // 存在交集的时间内，处于上线状态的积分批次配置
        /*List<CarPointsBatchConfigPo> allValidPointConfigs = pointConfigRepository.findTimeIntersectionPointConfig(request.getStartTime(), request.getEndTime(), request.getSceneCode(), request.getBatchId());
        if (CollectionUtils.isNotEmpty(allValidPointConfigs)) {
            // 存在交集时间内
            log.error("PointBatchConfigService.checkSavePointBatchRequest 存在交集的时间内，处于上线状态的积分批次配置, request = {}, allValidPointConfigs = {}", request, allValidPointConfigs);
            throw ExceptionHelper.create(ErrCode.POINT, "存在交集的时间内，处于上线状态的积分批次配置");
        }*/
    }

    /**
     * 新增积分批次
     *
     * @param batchConfigPo batchConfigPo
     * @return batchId
     */
    public long insertPointBatch(CarPointsBatchConfigPo batchConfigPo) throws BizError {
        log.info("PointBatchConfigService.insertPointBatch begin, batchConfigPo = {}", batchConfigPo);

        PointBatchConfigCreateEvent event = new PointBatchConfigCreateEvent();

        long batchId = pointConfigRepository.insert(batchConfigPo, event);

        applicationEventPublisher.publishEvent(event);

        log.info("PointBatchConfigService.insertPointBatch finished, batchConfigPo = {}, batchId = {}", batchConfigPo, batchId);

        return batchId;
    }

    /**
     * 修改积分批次
     *
     * @param batchConfigPo batchConfigPo
     * @param operator operator
     * @return batchId
     */
    public long updatePointBatch(CarPointsBatchConfigPo batchConfigPo, String operator) throws Exception {
        log.info("PointBatchConfigService.updatePointBatch begin, batchConfigPo = {}", batchConfigPo);

        Long batchId = batchConfigPo.getId();
        CarPointsBatchConfigPo oldPo = pointConfigRepository.findById(batchId);

        PointBatchConfigUpdateEvent event = new PointBatchConfigUpdateEvent();

        // 设置修改前po
        event.setOldPo(oldPo);

        // 设置操作人
        event.setOperator(operator);

        // 从缓存中读取已发数量
        Long sendCount = pointConfigRepository.getPointBatchDistributeCache(batchId);
        if (Objects.isNull(sendCount)) {
            log.error("PointBatchConfigService.updatePointBatch 缓存中积分批次已发数量为空, batchId = {}", batchId);
            throw ExceptionHelper.create(ErrCode.POINT, "缓存中积分批次已发数量为空");
        }

        batchConfigPo.setSendCount(sendCount);

        Long releaseCount = oldPo.getReleaseCount();
        batchConfigPo.setReleaseCount(releaseCount);
        batchConfigPo.setBrApplyNo(oldPo.getBrApplyNo());
        // 更新
        pointConfigRepository.update(oldPo, batchConfigPo, event);

        // 后置处理
        applicationEventPublisher.publishEvent(event);

        log.info("PointBatchConfigService.updatePointBatch finished, batchConfigPo = {}", batchConfigPo);

        return batchId;
    }

    /**
     * 更新积分批次配置信息
     */
    public void renewPointBatchConfig() throws BizError {
        log.info("PointBatchConfigService.renewPointBatchConfig begin");

        List<CarPointsBatchConfigPo> pointsBatchConfigPoList = pointConfigRepository.findAllValidPointConfig();

        for (List<CarPointsBatchConfigPo> configPoList : Lists.partition(pointsBatchConfigPoList, RENEW_POINT_BATCH_CONFIG_LIMIT)) {
            // 批量更新积分批次配置
            pointConfigRepository.updatePointBatchConfigCache(configPoList);

            // List<CarPointsBatchConfigPo> -> List<batchId>
            List<Long> batchIdList = configPoList.stream().map(CarPointsBatchConfigPo::getId).collect(Collectors.toList());

            // 批量获取积分批次已发放额度
            Map<Long, Long> batchDistributeCache = pointConfigRepository.getPointBatchDistributeCache(batchIdList);

            for (Map.Entry<Long, Long> entry : batchDistributeCache.entrySet()) {
                Long batchId = entry.getKey();
                Long sendCount = entry.getValue();

                // 更新mysql中已发总额
                CarPointsBatchConfigPo updatePo = new CarPointsBatchConfigPo();
                updatePo.setId(batchId);
                updatePo.setSendCount(sendCount);
                pointConfigRepository.update(updatePo);
            }

        }

        log.info("PointBatchConfigService.renewPointBatchConfig finished");
    }

    /**
     * 修改积分批次状态
     *
     * @param req
     */
    public void updateStatus(ChangePointBatchStatusRequest req) throws BizError {

        // 1、查询
        Long batchId = req.getBatchId();
        CarPointsBatchConfigPo oldPo = pointConfigRepository.findById(req.getBatchId());
        int oldStatus = oldPo.getStatus();
        if (oldStatus == req.getOptType()) {
            log.info("updateStatus target status same as db, req {}", GsonUtil.toJson(req));
            return;
        }

        // 2、构造event
        PointBatchConfigUpdateStatusEvent updateStatusEvent = new PointBatchConfigUpdateStatusEvent();
        updateStatusEvent.setOldPo(oldPo);
        updateStatusEvent.setOperator(req.getOperator());
        CarPointsBatchConfigPo newPo = new CarPointsBatchConfigPo();
        BeanMapper.copy(oldPo,newPo);
        newPo.setStatus(req.getOptType());
        updateStatusEvent.setData(newPo);

        // 3、更新
        CarPointsBatchConfigPo updatePo = new CarPointsBatchConfigPo();
        updatePo.setId(batchId);
        updatePo.setStatus(req.getOptType());
        pointConfigRepository.update(updatePo);

        // 4、后置处理
        applicationEventPublisher.publishEvent(updateStatusEvent);
    }

    /**
     * 积分批次配置详情
     *
     * @param request request
     * @return Result<PointBatchDetailDto>
     */
    public PointBatchDetailDto pointBatchDetail(PointBatchDetailRequest request) throws BizError {
        log.info("PointBatchConfigService.pointBatchDetail begin, request = {}", request);

        Long batchId = request.getBatchId();
        PointBatchDetailDto batchDetailDto = new PointBatchDetailDto();

        // 获取积分配置信息缓存
        PointBatchConfigCachePo pointBatchConfigCache = pointConfigRepository.getPointBatchConfigCache(batchId);
        CarPointsBatchConfigPo batchConfigPo = null;
        if (Objects.isNull(pointBatchConfigCache)) {
            // redis中积分批次配置信息不存在，读db数据
            log.warn("PointBatchConfigService.pointBatchDetail redis中积分批次配置信息不存在, batchId = {}", batchId);
            batchConfigPo = pointConfigRepository.findById(batchId);

            if (Objects.isNull(batchConfigPo)) {
                log.error("PointBatchConfigService.pointBatchDetail 积分批次配置信息不存在, batchId = {}", batchId);
                throw ExceptionHelper.create(ErrCode.POINT, "积分批次配置信息不存在");
            }

            BeanUtils.copyProperties(batchConfigPo, batchDetailDto);
            batchDetailDto.setBatchId(batchConfigPo.getId());
            batchDetailDto.setBatchName(batchConfigPo.getName());
            batchDetailDto.setBudgetInfoDto(brProxy.queryBudgetDetail(batchConfigPo.getBudgetApplyNo(), batchConfigPo.getLineNum()));
        } else {
            // redis中积分批次配置信息存在
            log.info("PointBatchConfigService.pointBatchDetail pointBatchConfigCache = {}", pointBatchConfigCache);

            BeanUtils.copyProperties(pointBatchConfigCache, batchDetailDto);
            batchDetailDto.setBatchId(pointBatchConfigCache.getId());
            batchDetailDto.setBatchName(pointBatchConfigCache.getName());
            batchDetailDto.setApplyCount(pointBatchConfigCache.getTotalPoints());
            batchDetailDto.setBudgetInfoDto(brProxy.queryBudgetDetail(pointBatchConfigCache.getBudgetApplyNo(), pointBatchConfigCache.getLineNum()));
        }

        // 获取积分批次已发数量缓存
        Long sendCount = pointConfigRepository.getPointBatchDistributeCache(batchId);
        if (Objects.isNull(sendCount)) {
            // redis中不存在获取积分批次已发数量
            log.warn("PointBatchConfigService.pointBatchDetail redis中积分批次已发数量不存在, batchId = {}", batchId);

            if (Objects.isNull(batchConfigPo)) {
                batchConfigPo = pointConfigRepository.findById(batchId);

                if (Objects.isNull(batchConfigPo)) {
                    log.error("PointBatchConfigService.pointBatchDetail 积分批次配置信息不存在, batchId = {}", batchId);
                    throw ExceptionHelper.create(ErrCode.POINT, "积分批次配置信息不存在");
                }
            }

            sendCount = batchConfigPo.getSendCount();
        }

        // 已发数量
        batchDetailDto.setSendCount(sendCount);
        // 预计抵扣（积分和现金10:1）
        BigDecimal predictDeduction = BigDecimal.valueOf(sendCount / 10).setScale(1,  RoundingMode.DOWN);
        batchDetailDto.setPredictDeduction(predictDeduction);
        // 积分余额
        Long balanceCount = batchDetailDto.getApplyCount() - sendCount;
        batchDetailDto.setBalanceCount(balanceCount);

        return batchDetailDto;
    }

    /**
     * 修改积分批次配置信息 入参校验
     *
     * @param request request
     */
    public void checkUpdatePointBatchRequest(SavePointBatchRequest request) throws BizError {
        log.info("PointBatchConfigService.checkUpdatePointBatchRequest begin, request = {}", request);

        if (Objects.isNull(request)) {
            log.error("PointBatchConfigService.checkUpdatePointBatchRequest request为空");
            throw ExceptionHelper.create(ErrCode.POINT, "request为空");
        }

//        if (Objects.isNull(request.getBudgetId())) {
//            log.error("PointBatchConfigService.checkUpdatePointBatchRequest batchId为空");
//            throw ExceptionHelper.create(ErrCode.POINT, "batchId为空");
//        }

        Long startTime = request.getStartTime();
        Long endTime = request.getEndTime();
        Long currentTime = System.currentTimeMillis() / 1000;
        Long batchId = request.getBatchId();

        CarPointsBatchConfigPo batchConfigPo = pointConfigRepository.findById(batchId);
        if (Objects.isNull(batchConfigPo)) {
            log.error("PointBatchConfigService.checkUpdatePointBatchRequest 积分批次配置不存在，request = {}", request);
            throw ExceptionHelper.create(ErrCode.POINT, "积分批次配置不存在");
        }

        // 积分批次配置修改
        if (currentTime > batchConfigPo.getEndTime()) {
            // 1、已结束的批次
            // 禁止编辑和删除
            log.error("PointBatchConfigService.checkUpdatePointBatchRequest 已结束的批次禁止编辑, request = {}", request);
            throw ExceptionHelper.create(ErrCode.POINT, "已结束的批次禁止编辑");
        }

        if (batchConfigPo.getStartTime() < currentTime && currentTime < batchConfigPo.getEndTime()) {
            // 2、进行中的批次

            if (!Objects.equals(batchConfigPo.getStartTime(), request.getStartTime())) {
                // 进行中的积分批次禁止修改开始时间
                log.error("PointBatchConfigService.checkUpdatePointBatchRequest 进行中的积分批次禁止修改开始时间, request = {}", request);
                throw ExceptionHelper.create(ErrCode.POINT, "进行中的积分批次禁止修改开始时间");
            }

            if (!Objects.equals(batchConfigPo.getEndTime(), endTime)) {
                // 结束时间修改
                if (startTime > endTime) {
                    // 结束时间需大于开始时间
                    log.error("PointBatchConfigService.checkUpdatePointBatchRequest 发放周期结束时间需大于发放周期开始时间");
                    throw ExceptionHelper.create(ErrCode.POINT, "发放周期结束时间需大于发放周期开始时间");
                }

                if (TimeUtil.getYearFromTimestamp(startTime) != TimeUtil.getYearFromTimestamp(endTime)) {
                    // 禁止夸自然年
                    log.error("PointBatchConfigService.checkUpdatePointBatchRequest 发放周期禁止夸自然年");
                    throw ExceptionHelper.create(ErrCode.POINT, "发放周期禁止夸自然年");
                }
            }

//            if (!Objects.equals(batchConfigPo.getBudgetId(), request.getBudgetId())) {
//                // 进行中的积分批次禁止修改预算池
//                log.error("PointBatchConfigService.checkUpdatePointBatchRequest 进行中的积分批次禁止修改预算池, request = {}", request);
//                throw ExceptionHelper.create(ErrCode.POINT, "进行中的积分批次禁止修改预算池");
//            }

            if (!Objects.equals(batchConfigPo.getSendScene(), request.getSceneCode())) {
                // 进行中的积分批次禁止修改发放场景
                log.error("PointBatchConfigService.checkUpdatePointBatchRequest 进行中的积分批次禁止修改发放场景, request = {}", request);
                throw ExceptionHelper.create(ErrCode.POINT, "进行中的积分批次禁止修改发放场景");
            }

//            if (batchConfigPo.getApplyCount() > request.getApplyCount()) {
//                // 进行中的批次，修改积分总额时需大于上次配置的积分总额
//                log.error("PointBatchConfigService.checkUpdatePointBatchRequest 进行中的批次，修改积分总额时需大于上次配置的积分总额, request = {}", request);
//                throw ExceptionHelper.create(ErrCode.POINT, "进行中的批次，修改积分总额时需大于上次配置的积分总额");
//            }

        }

        if (currentTime < batchConfigPo.getStartTime()) {
            // 3、未开始的批次
            checkSavePointBatchRequest(request, false);
        }
    }
}
