package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SearchUserCouponListParam implements Serializable {
    private static final long serialVersionUID = 8741304516698345964L;

    /**
     * uid
     */
    private long uid;

    /**
     * 券配置id
     */
    private List<Long> configIds;

    /**
     * 券状态
     */
    private String couponStatus;

    /**
     * 当前页码
     */
    private int offset;
    /**
     * 页面条数
     */
    private int limit;

    /**
     * 排序字段
     */
    private String orderBy = "type_id";

    /**
     * 排序顺序  desc倒序   asc顺序
     */
    private String orderDirection = "desc";

    /**
     * 用户券id
     */
    private long couponId;

    /**
     * vid
     */
    private String vid;

    /**
     * 优惠券所属业务平台
     */
    private int bizPlatform;


}
