package com.xiaomi.nr.coupon.admin.infrastruture.repository.es;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.es.po.CouponEsPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.SearchConfigParam;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.action.DocWriteRequest;
import org.elasticsearch.action.admin.indices.forcemerge.ForceMergeRequest;
import org.elasticsearch.action.admin.indices.forcemerge.ForceMergeResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.MultiSearchRequest;
import org.elasticsearch.action.search.MultiSearchResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 优惠券ES配置依赖
 * @author: hejiapeng
 * @Date 2022/2/28 5:00 下午
 * @Version: 1.0
 **/
@Slf4j
@Configuration
public abstract class CouponConfigESHelper {

    @Value("${spring.coupon.es.index: #{null}}")
    private String index;

    @Resource
    private RestHighLevelClient esClient;

    /**
     * 单个保存到ES
     *
     * @param couponEsPO
     * @throws Exception
     */
    public void saveCouponConfig(CouponEsPO couponEsPO) throws Exception {
        final IndexRequest request = new IndexRequest(index).id(couponEsPO.getId().toString());
        final String data = GsonUtil.toJson(couponEsPO);
        request.source(data, XContentType.JSON);
        //index表示如果存在则替换
        request.opType(DocWriteRequest.OpType.INDEX);
        esClient.index(request, RequestOptions.DEFAULT);
        log.info("CouponConfigESHelper saveCouponConfig configId:{}", couponEsPO.getId());
    }

    /**
     * 批量查询倒排
     *
     * @param items
     * @param timeNow
     * @return
     * @throws IOException
     */
    public Map<Long, Set<Long>> batchGetInverted(String level, List<Long> items, long timeNow, SearchConfigParam searchConfigParam) throws Exception {

        Map<Long, Set<Long>> ret = new HashMap<>(items.size());

        if (CollectionUtils.isEmpty(items)) {
            return ret;
        }

        // 查询
        MultiSearchRequest request = new MultiSearchRequest();
        for (Long item : items) {
            SearchRequest searchRequest = new SearchRequest(index);
            searchRequest.source(buildSourceBuilder(searchConfigParam, level, item, timeNow));
            request.add(searchRequest);
        }
        MultiSearchResponse response;
        try {
            response = esClient.msearch(request, RequestOptions.DEFAULT);
            log.info("CouponConfigESHelper.batchGetCouponConfigInfo request:{}， items.size:{}", GsonUtil.toJson(request), items.size());
        } catch (IOException e) {
            response = esClient.msearch(request, RequestOptions.DEFAULT);
        }

        if (response == null) {
            throw new Exception("es 批量获取倒排失败");
        }
        //结果封装
        int index = 0;
        for (MultiSearchResponse.Item result : response.getResponses()) {
            //结果包装
            SearchHit[] hits = result.getResponse().getHits().getHits();
            if (hits == null || hits.length == 0) {
                index++;
                continue;
            }
            Set<Long> configIds = Arrays.stream(hits).map(hit -> Long.valueOf(hit.getId())).collect(Collectors.toSet());
            ret.put(items.get(index), configIds);
            index++;
        }
        log.debug("batchGetInverted end ret:{}", ret);
        return ret;
    }

    /**
     * 构造查询条件
     *
     * @param item
     * @param timeNow
     * @return
     */
    public abstract SearchSourceBuilder buildSourceBuilder(SearchConfigParam searchConfigParam, String level, long item, long timeNow);


    /**
     * 批量查询券配置id
     *
     * @param searchConfigParam 查询条件
     * @return Set<Integer>
     * @throws IOException
     */
    public Set<Long> batchGetConfigIds(SearchConfigParam searchConfigParam, long nowTime) throws IOException {

        Set<Long> configIds = new HashSet<>();

        if (Objects.isNull(searchConfigParam)) {
            return configIds;
        }

        // 查询
        MultiSearchRequest request = new MultiSearchRequest();
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.source(buildSourceBuilder(searchConfigParam, nowTime));
        request.add(searchRequest);

        MultiSearchResponse response = esClient.msearch(request, RequestOptions.DEFAULT);
        //结果封装
        int index = 0;
        for (MultiSearchResponse.Item result : response.getResponses()) {
            //结果包装
            SearchHit[] hits = result.getResponse().getHits().getHits();
            if (hits == null || hits.length == 0) {
                index++;
                continue;
            }
            configIds = Arrays.stream(hits).map(hit -> Long.valueOf(hit.getId())).collect(Collectors.toSet());
            index++;
        }
        log.debug("batchGetInverted end ids:{}", configIds);
        return configIds;
    }


    /**
     * 构造券列表查询条件
     *
     * @param searchConfigParam 查询条件
     * @return SearchSourceBuilder
     */
    public abstract SearchSourceBuilder buildSourceBuilder(SearchConfigParam searchConfigParam, long nowTime);


    /**
     * 构造预估券折扣力度匹配对应商品券
     * @param searchConfigParam 查询条件
     * @param isRelativeTime 是否为相对时间
     * @return
     */
    public abstract SearchSourceBuilder buildSourceBuilder(SearchConfigParam searchConfigParam, boolean isRelativeTime, long itemId);


    /**
     * 批量查询商品对应券倒排
     *
     * @param searchConfigParam
     * @param isRelativeTime
     * @return
     * @throws IOException
     */
    public Map<Long, Set<Long>> batchGetGoodsCoupon(SearchConfigParam searchConfigParam, List<Long> itemIds, boolean isRelativeTime) throws Exception {

        if(Objects.isNull(searchConfigParam)){
            return null;
        }

        Map<Long, Set<Long>> ret = new HashMap<>(itemIds.size());

        if (CollectionUtils.isEmpty(itemIds)) {
            return ret;
        }

        // 查询
        MultiSearchRequest request = new MultiSearchRequest();
        for (Long item : itemIds) {
            SearchRequest searchRequest = new SearchRequest(index);
            searchRequest.source(buildSourceBuilder(searchConfigParam, isRelativeTime, item));
            request.add(searchRequest);
        }

        MultiSearchResponse response;
        try {
            response = esClient.msearch(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            response = esClient.msearch(request, RequestOptions.DEFAULT);
        }

        if (response == null) {
            throw new Exception("es 批量获取商品对应券倒排失败");
        }
        //结果封装
        int index = 0;
        for (MultiSearchResponse.Item result : response.getResponses()) {
            //结果包装
            SearchHit[] hits = result.getResponse().getHits().getHits();
            if (hits == null || hits.length == 0) {
                index++;
                continue;
            }
            Set<Long> configIds = Arrays.stream(hits).map(hit -> Long.valueOf(hit.getId())).collect(Collectors.toSet());
            ret.put(itemIds.get(index), configIds);
            index++;
        }
        log.debug("batchGetInverted end ret:{}", ret);
        return ret;
    }


    public void forceMergeIndex() {
        try {
            // 创建ForceMergeRequest
            ForceMergeRequest request = new ForceMergeRequest(index);
            // 设置maxNumSegments参数，表示合并后的段数
            request.maxNumSegments(1);
            // 执行force_merge操作
            ForceMergeResponse response = esClient.indices().forcemerge(request, RequestOptions.DEFAULT);

            log.info("forceMergeIndex completed, failed shards num:{}", response.getFailedShards());
        } catch (Exception e) {
            log.error("forceMergeIndex fail error:{}", e.getMessage());
        }
    }
}
