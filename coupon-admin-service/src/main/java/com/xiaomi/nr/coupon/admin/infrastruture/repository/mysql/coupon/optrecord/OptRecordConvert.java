package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.optrecord;

import com.xiaomi.nr.coupon.admin.enums.optrecord.OptRecordTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.optrecord.po.CouponOptRecordPO;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;


public class OptRecordConvert {


    public static <T> CouponOptRecordPO convertToOptRecordPO(OptRecordTypeEnum optRecordTypeEnum, T event) throws Exception{
        CouponOptRecordPO couponOptRecordPO =new CouponOptRecordPO();
        couponOptRecordPO.setOptType(optRecordTypeEnum.getValue());
        couponOptRecordPO.setOptInfo(CompressUtil.compress(GsonUtil.toJson(event)));
        return couponOptRecordPO;
    }





}
