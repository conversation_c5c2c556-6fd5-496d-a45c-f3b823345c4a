package com.xiaomi.nr.coupon.admin.enums.scene;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发送类型枚举
 *
 * <AUTHOR>
 * @date 2024/6/24 15:19
 */
@Getter
@AllArgsConstructor
public enum SendTypeEnum {
    /**
     * pulse用户中心补发优惠券
     */
    REISSUE("reissue", "pulse用户中心补发优惠券"),

    /**
     * 活动发券
     */
    MARKETING("marketing", "活动发券"),

    /**
     * 外部调用pulse接口发券
     */
    EXTERNAL("external", "外部调用pulse接口发券"),

    /**
     * 追单发送的优惠券
     */
    ORDER_ENGINE("orderEngine", "追单发送的优惠券"),

    /**
     * 西瓜商超投放
     */
    XIGUA_MARKET("xiguaMarket", "西瓜商超投放");

    private final String code;
    private final String desc;
}
