package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.SceneListParam;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 优惠券场景mapper
 * @Date: 2022.03.03 14:20
 */
@Mapper
@Component
public interface CouponSceneMapper {
    /**
     * 根据后台前端列表参数查询
     *
     * @param sceneListParam
     * @param offset
     * @param limit
     * @return
     */
    @Select("<script>select * from nr_coupon_scene where 1=1 " +
            "<if test='param.sceneCode != null'> and scene_code = #{param.sceneCode} </if>" +
            "<if test='param.sceneType != null'> and relation_scene_id = #{param.sceneType} </if>" +
            "<if test='param.couponTypeInclude != null'> and FIND_IN_SET(${param.couponTypeInclude}, coupon_type) </if>" +
            "<if test='param.name != null'> and name like '%${param.name}%'</if>" +
            "<if test='param.bizPlatform != null'> and biz_platform = #{param.bizPlatform} </if>" +
            " order by id desc limit #{offset},#{limit}</script>")
    List<CouponScenePO> searchSceneByListReq(@Param("param") SceneListParam sceneListParam, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 根据后台前端列表参数查询数量
     *
     * @param sceneListParam
     * @return
     */
    @Select("<script>select count(*) from nr_coupon_scene where 1=1 " +
            "<if test='param.sceneCode!= null'> and scene_code = #{param.sceneCode} </if>" +
            "<if test='param.sceneType != null'> and relation_scene_id = #{param.sceneType} </if>" +
            "<if test='param.couponTypeInclude != null'> and FIND_IN_SET(${param.couponTypeInclude}, coupon_type) </if>" +
            "<if test='param.name != null'> and name like '%${param.name}%'</if>" +
            "<if test='param.bizPlatform != null'> and biz_platform = #{param.bizPlatform} </if>" +
            "</script>")
    Integer searchSceneCountByListReq(@Param("param") SceneListParam sceneListParam);

    @Select("select * from nr_coupon_scene where id=#{id}")
    CouponScenePO searchSceneById(long id);

    @Select("select * from nr_coupon_scene where name=#{name}")
    CouponScenePO searchSceneByName(String name);

    @Select("<script>select coupon_type,scene_code,name,scene_desc,relation_scene_id,ext_props,send_mode,biz_platform " +
            "from nr_coupon_scene " +
            "where 1=1 " +
            "<if test='online'> and status=1 </if>" +
            "<if test='bizPlatformList != null and bizPlatformList.size() > 0'> and biz_platform in " +
            "<foreach item='bizPlatform' index='index' collection='bizPlatformList' open='(' separator=',' close=')'>#{bizPlatform}</foreach> " +
            "</if>" +
            "</script>")
    List<CouponScenePO> searchAllBriefSceneBiz(@Param("online") boolean online, @Param("bizPlatformList") List<Integer> bizPlatformList);

    @Select("<script>select coupon_type,scene_code,name,scene_desc,relation_scene_id,ext_props,send_mode from nr_coupon_scene " +
            "<if test='online'> where status=1 </if>" +
            "</script>")
    List<CouponScenePO> searchAllBriefScene(@Param("online") boolean online);

    @Update("update nr_coupon_scene set status=#{status} where id=#{id}")
    Integer updateStatusById(@Param("id") long id, @Param("status") int status);

    @Insert("insert into nr_coupon_scene (id_generation_type,name,relation_scene_id,scene_code,scene_desc,send_mode,assign_mode," +
            "status, apply_mark, creator,coupon_type,ext_props,biz_platform) values " +
            "(#{idGenerationType},#{name},#{relationSceneId},#{sceneCode},#{sceneDesc},#{sendMode},#{assignMode},#{status},#{applyMark},#{creator},#{couponType},#{extProps},#{bizPlatform})")
    Integer insert(CouponScenePO couponScenePO);

    @Update("update nr_coupon_scene set name=#{name},relation_scene_id=#{relationSceneId},scene_desc=#{sceneDesc},apply_mark=#{applyMark},modifier=#{modifier},update_time=#{updateTime},assign_mode=#{assignMode},coupon_type=#{couponType},ext_props=#{extProps},biz_platform=#{bizPlatform} where id=#{id}")
    Integer update(CouponScenePO couponScenePO);

    @Select("select * from nr_coupon_scene where scene_code=#{sceneCode}")
    CouponScenePO selectBySceneCode(@Param("sceneCode")String sceneCode);

    @Select("<script> " +
            "select * from nr_coupon_scene where scene_code in " +
            "<foreach item='sceneCode' index='index' collection='sceneCodes' open='(' separator=',' close=')'>#{sceneCode}</foreach> " +
            "</script>")
    List<CouponScenePO> selectBySceneCodes(@Param("sceneCodes") List<String> sceneCodes);

    @Select("select relation_scene_id,send_mode from nr_coupon_scene where scene_code=#{sceneCode}")
    CouponScenePO selectRelationSceneByCode(@Param("sceneCode")String sceneCode);

    @Select("select * from nr_coupon_scene where assign_mode like '%${assignMode}%' and status = #{status}")
    List<CouponScenePO> selectSceneByAssignMode(@Param("assignMode")Integer assignMode,@Param("status")Integer status);

    @Select("select name from nr_coupon_scene where scene_code=#{sceneCode}")
    String searchNameSceneByCode(@Param("sceneCode")String sceneCode);

}
