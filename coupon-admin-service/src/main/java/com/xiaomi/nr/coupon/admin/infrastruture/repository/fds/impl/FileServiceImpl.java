package com.xiaomi.nr.coupon.admin.infrastruture.repository.fds.impl;

import com.xiaomi.infra.galaxy.fds.client.GalaxyFDS;
import com.xiaomi.infra.galaxy.fds.model.FDSObjectMetadata;
import com.xiaomi.infra.galaxy.fds.result.PutObjectResult;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.fds.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;

/**
 * 接入文档
 * http://docs.api.xiaomi.net/fds/object-api/put-object.html
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Value("${fds.endpoint}")
    private String endpoint;

    @Value("${fds.bucketName}")
    private String bucketName;

    @Autowired
    private GalaxyFDS galaxyFDSClient;



    @Override
    public String uploadFileToFds(String objectName, File file,boolean publicFlag) throws Exception{
        PutObjectResult result = galaxyFDSClient.putObject(bucketName, objectName, file);
        // 设置文件为public
        if (publicFlag){
            galaxyFDSClient.setPublic(bucketName, result.getObjectName());
        }
        String downloadUrl = "https://" + endpoint + File.separator + bucketName + File.separator + result.getObjectName();
        return downloadUrl;
    }

    @Override
    public String uploadFileToFds(String fileName, InputStream inputStream, boolean publicFlag) throws Exception {
        FDSObjectMetadata fdsObjectMetadata = new FDSObjectMetadata();
        PutObjectResult result = galaxyFDSClient.putObject(bucketName, fileName, inputStream, fdsObjectMetadata);
        // 设置文件为public
        if (publicFlag){
            galaxyFDSClient.setPublic(bucketName, result.getObjectName());
        }
        String downloadUrl = "https://" + endpoint + File.separator + bucketName + File.separator + result.getObjectName();
        return downloadUrl;
    }

    @Override
    public String getEndpoint(){
        return endpoint;
    }

    @Override
    public String getBucketName(){
        return bucketName;
    }








}
