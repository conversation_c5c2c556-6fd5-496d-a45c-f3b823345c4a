package com.xiaomi.nr.coupon.admin.util;

import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public class CouponCollectionUtil {

    /**
     * 剔除元素
     * @param <T>从from中删除delete
     * @return
     */
    public static<T> List<T> removeAll(List<T> from, List<T> delete) {
        List<T> result = new LinkedList<>();
        Set<T> targetSet = new HashSet<>(delete);
        for(T t : from) {
            if (!targetSet.contains(t)) {
                result.add(t);
            }
        }
        return result;
    }


    /**
     * 比较两个list是否相同
     * @return
     */
    public static boolean equalsLongList(List<Long> listA, List<Long> listB) {
        if (!(CollectionUtils.isEmpty(listA) && CollectionUtils.isEmpty(listB))) {
            if (CollectionUtils.isNotEmpty(listA) && CollectionUtils.isNotEmpty(listB)) {
                if (!listA.stream().sorted(Comparator.naturalOrder()).collect(Collectors.toList()).equals(
                        listB.stream().sorted(Comparator.naturalOrder()).collect(Collectors.toList())
                )) {
                    return false;
                }
            } else {
                return false;
            }
        }
        return true;
    }

    /**
     * 比较两个list是否相同
     * @return
     */
    public static boolean equalsIntegerList(List<Integer> listA, List<Integer> listB) {
        if (!(CollectionUtils.isEmpty(listA) && CollectionUtils.isEmpty(listB))) {
            if (CollectionUtils.isNotEmpty(listA) && CollectionUtils.isNotEmpty(listB)) {
                if (!listA.stream().sorted(Comparator.naturalOrder()).collect(Collectors.toList()).equals(
                        listB.stream().sorted(Comparator.naturalOrder()).collect(Collectors.toList())
                )) {
                    return false;
                }
            } else {
                return false;
            }
        }
        return true;
    }

    /**
     * 比较两个list是否相同
     * @return
     */
    public static boolean equalsStringList(List<String> listA, List<String> listB) {
        if (!(CollectionUtils.isEmpty(listA) && CollectionUtils.isEmpty(listB))) {
            if (CollectionUtils.isNotEmpty(listA) && CollectionUtils.isNotEmpty(listB)) {
                if (!listA.stream().sorted(Comparator.naturalOrder()).collect(Collectors.toList()).equals(
                        listB.stream().sorted(Comparator.naturalOrder()).collect(Collectors.toList())
                )) {
                    return false;
                }
            } else {
                return false;
            }
        }
        return true;
    }


    /**
     * 剔除元素(SET版)
     * @param <T>从from中删除delete
     * @return
     */
    public static<T> Set<T> removeAllSet(Set<T> from, Set<T> delete) {

        if(CollectionUtils.isEmpty(from)){
            return new HashSet<>();
        }

        if(CollectionUtils.isEmpty(delete)){
            return from;
        }

        Set<T> result = new HashSet<>();
        Set<T> targetSet = new HashSet<>(delete);
        for(T t : from) {
            if (!targetSet.contains(t)) {
                result.add(t);
            }
        }
        return result;
    }



}
