package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po;

import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.BatchGetConfigInfoRequest;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 获取券详情入参
 * @author: hejiapeng
 * @Date 2022/3/3 2:12 下午
 * @Version: 1.0
 **/
@Data
public class SearchConfigInfoParam implements Serializable {

    private static final long serialVersionUID = -3871104047111138445L;

    /**
     * 券配置
     */
    private List<Long> configIds;

    /**
     * 领券场景
     */
    private String sceneCode;

    /**
     * 开始创建时间
     */
    private String startTime;

    /**
     * 结束创建时间
     */
    private String endTime;

    /**
     * 只返回有效可领券配置
     */
    private Boolean onlyAvailable = false;

    public static SearchConfigInfoParam build(BatchGetConfigInfoRequest request){

        SearchConfigInfoParam param = new SearchConfigInfoParam();
        param.setSceneCode(request.getSceneCode());
        if (request.getBeginTime() != null && request.getBeginTime() > 0) {
            param.setStartTime(TimeUtil.formatSecond(request.getBeginTime().longValue()));
        }
        if (request.getEndTime() != null && request.getEndTime() > 0) {
            param.setEndTime(TimeUtil.formatSecond(request.getEndTime().longValue()));
        }
        if (CollectionUtils.isNotEmpty(request.getConfigIds())) {
            param.setConfigIds(request.getConfigIds());
        }
        param.setOnlyAvailable(request.getOnlyAvailable());

        return param;
    }


}
