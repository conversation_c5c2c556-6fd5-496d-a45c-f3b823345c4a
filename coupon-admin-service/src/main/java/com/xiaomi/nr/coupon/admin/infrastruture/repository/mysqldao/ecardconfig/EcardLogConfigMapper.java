package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecardconfig;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecardconfig.po.EcardLogPo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Component
public interface EcardLogConfigMapper {

    String ECARD_LOG_SQL = "card_id,user_id,order_id,refund_no,log_type,income,old_balance,new_balance,add_time,description,operator_id,hash_code";

    /**
     * 获取日志
     * @param cardIds List<Long>
     * @return ArrayList<EcardLogPo>
     */
    @Select("<script>" +
            "select * from tb_ecard_log " +
            "where log_type = 1 " +
            "<if test=\"cardIds != null and cardIds.size >0\"> and card_id in " +
            "        <foreach item='card_id' index='index' collection='cardIds' open='(' separator=',' close=')'>" +
            "            #{card_id}" +
            "        </foreach> " +
            "</if>" +
            "order by add_time asc" +
            "</script>")
    ArrayList<EcardLogPo> queryByCardId(@Param("cardIds") List<Long> cardIds);

    /**
     * 写入礼品卡日志
     *
     * @param ecardLogPo ecardLogPo
     * @return
     */
    @Insert("insert into tb_ecard_log (" + ECARD_LOG_SQL + ") " +
            " values " +
            "(#{ecardLogPo.cardId},#{ecardLogPo.userId},#{ecardLogPo.orderId},#{ecardLogPo.refundNo}," +
            "#{ecardLogPo.logType},#{ecardLogPo.income},#{ecardLogPo.oldBalance},#{ecardLogPo.newBalance}," +
            "#{ecardLogPo.addTime},#{ecardLogPo.description},#{ecardLogPo.operatorId},#{ecardLogPo.hashCode})")
    int insertEcardLog(@Param("ecardLogPo") EcardLogPo ecardLogPo);


    /**
     * 删除用户礼品卡，自动化测试
     * @return
     */
    @Delete("<script>delete from tb_ecard_log where user_id=#{uid} and card_id =#{cardId}</script>")
    Long deleteEcardLog(@Param("uid") long uid, @Param("cardId") long cardId);

}
