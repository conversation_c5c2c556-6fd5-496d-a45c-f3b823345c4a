package com.xiaomi.nr.coupon.admin.infrastruture.repository;

import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.updateCoupondataRequest;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.BaseData;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.MakeCacheBaseData;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.UseChannelClientRelationDo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.coupon.CouponConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.nacosconfig.NacosSwitchConfig;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.es.CouponInvertedTOCHelper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.es.po.CouponEsPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.CouponConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.ExtPropPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.GoodItemPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.UseChannelPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.mission.CouponMissionMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.mission.po.CouponMissionPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig.OldCouponConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig.po.*;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponmission.MissionMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponmission.po.MissionPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.goods.po.GoodsPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.goods.po.PackagePo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.CouponConfigRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.OldCouponRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.*;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.oldcoupon.OldCouponConvert;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.oldcoupon.OldCouponInfo;
import com.xiaomi.nr.coupon.admin.infrastruture.staticdata.SendSceneAppIdRel;
import com.xiaomi.nr.coupon.admin.util.CouponCollectionUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 优惠券配置资源库 todo 使用完会删除 无需强求代码领域规则
 * @author: hejiapeng
 * @Date 2022/3/1 10:42 上午
 * @Version: 1.0
 **/
@Component
@Slf4j
public class CouponMoveRepository {

    /**
     * 366天秒数  3600 * 24 * 366
     */
    private static final int LIMT = 50;


    @Autowired
    private OldCouponConfigMapper oldCouponConfigMapper;

    @Autowired
    private MissionMapper missionMapper;

    @Autowired
    private CouponConfigMapper couponConfigMapper;

    @Autowired
    private CouponMissionMapper couponMissionMapper;

    @Autowired
    private MakeCacheBaseData makeCacheBaseData;

    @Autowired
    private SendSceneAppIdRel sendSceneAppIdRel;

    @Autowired
    private CouponInvertedTOCHelper couponInvertedTOCHelper;

    @Autowired
    private CouponConfigPoConvert couponConfigPoConvert;

    @Autowired
    private CouponConfigRedisDao couponConfigRedisDao;

    @Autowired
    private OldCouponRedisDao oldCouponRedisDao;

    @Autowired
    private OldCouponConvert oldCouponConvert;

    @Autowired
    private NacosSwitchConfig nacosSwitchConfig;


    /**
     * 老优惠券迁移
     * @throws BizError
     */
    public void moveOldCoupon(Boolean autoMove,List<Long> configIds) throws BizError {
        List<Long> oldConfigIds;
        if(autoMove){
            String lastMoveTime = "0";
            oldConfigIds = oldCouponConfigMapper.getConfigIdByUpdateTime(lastMoveTime);
            if(CollectionUtils.isEmpty(oldConfigIds)) {
                return;
            }
        } else {
            if (CollectionUtils.isEmpty(configIds)) {
                Timestamp maxUpdateTime = couponConfigMapper.getCouponMoveLastTime();
                String lastMoveTime = "0";
                if (maxUpdateTime != null) {
                    //往前数1分钟 插入时间会变化
                    lastMoveTime = TimeUtil.formatSecond(maxUpdateTime.getTime() / 1000 - 60);
                }

                oldConfigIds = oldCouponConfigMapper.getConfigIdByUpdateTime(lastMoveTime);
                if(CollectionUtils.isEmpty(oldConfigIds)) {
                    return;
                }
            } else {
                oldConfigIds = configIds;
            }
        }

        log.info("moveOldCoupon begin oldConfigIds:{}",oldConfigIds);

        BaseData common = makeCacheBaseData.getV2();

        int size = oldConfigIds.size();
        int loopTime = size / LIMT + (size % LIMT == 0 ? 0 : 1);
        long validTime = TimeUtil.getNowUnixSecond() - (12 * 3600);

        List<Long> moreMCouponId = oldCouponConfigMapper.getMoreMissionCoupon(validTime);
        List<Long> moreMCouponIdList = Lists.newArrayList();

        StringBuilder updateCouponSql = new StringBuilder();
        for (int i = 0; i < loopTime; i++) {
            List<Long> subConfigIds= oldConfigIds.subList(i * LIMT, Math.min((i + 1) * LIMT, size));
            List<CouponConfigPo> couponConfigPoList = oldCouponConfigMapper.getConfigByIds(subConfigIds);
            List<MissionPo> missionPoList = missionMapper.getMissionByTypeIds(subConfigIds);
            Map<Long, List<MissionPo>> idToMission = missionPoList.stream().collect(Collectors.groupingBy(MissionPo::getTypeId));

            for (CouponConfigPo couponConfigPo : couponConfigPoList) {
                Long configId = couponConfigMapper.checkoutCouponExit(couponConfigPo.getId());
                List<MissionPo> missionPos = idToMission.get(couponConfigPo.getId());
                MissionPo missionPo = getMissionPo(couponConfigPo, missionPos);
                if (couponConfigPo.getGlobalEndTime() < validTime) {
                    //已迁移过的券跳过
                    if (Optional.ofNullable(configId).orElse(0L) > 0) {
                        continue;
                    }
                    try {
                        CouponConfigPO couponConfigPO = convertCouponConfigPO(couponConfigPo, missionPo, common);
                        couponConfigMapper.insertOldCoupon(couponConfigPO);

                        GoodsItemPo goodsItemPo = couponConfigPoConvert.serializeGoodsItemPo(couponConfigPO);
                        CouponEsPO couponEsPO = couponConfigPoConvert.serializeCouponEsPO(couponConfigPO, goodsItemPo);
                        couponInvertedTOCHelper.saveCouponConfig(couponEsPO);
                    } catch (Exception e) {
                        log.error("invalidcoupon config error configId:{}", couponConfigPo.getId());
                    }
                } else {
                    try {
                        CouponConfigPO couponConfigPO = convertCouponConfigPO(couponConfigPo, missionPo, common);
                        couponConfigPO.setApplyCount(getApplyCount(couponConfigPo, missionPos).intValue());
                        if(!CollectionUtils.isEmpty(missionPos) && missionPos.size() > 1) {
                            addMoreMissionCoupon(updateCouponSql, couponConfigPO);
                            couponConfigPO.setStatus(CouponConfigStatusEnum.OFFLINE.code);
                            moreMCouponIdList.add(couponConfigPo.getId());
                        }
                        if (StringUtils.isEmpty(couponConfigPO.getSendChannel())) {
                            addNoSceneCoupon(updateCouponSql, couponConfigPO);
                        }
                        if (Optional.ofNullable(configId).orElse(0L) > 0) {
                            couponConfigMapper.updateOldCoupon(couponConfigPO);
                        } else {
                            couponConfigMapper.insertOldCoupon(couponConfigPO);
                        }
                        updateCouponCacheEsData(common, couponConfigPO);
                    } catch (Exception e) {
                        log.error("validcoupon config error configId:{}", couponConfigPo.getId());
                    }
                }
            }
        }

        log.info("autoMove_{}, move_coupon_sql:{}", autoMove, updateCouponSql.toString());

        List<Long> diff1 = CouponCollectionUtil.removeAll(moreMCouponId, moreMCouponIdList);
        List<Long> diff2 = CouponCollectionUtil.removeAll(moreMCouponIdList, moreMCouponId);

        log.info("moveOldCoupon end ,autoMove_{}, diff1:{}, diff2:{}",autoMove, diff1, diff2);
    }


    /**
     * 老mission任务迁移
     * @throws BizError
     */
    public void moveOldMission(Boolean autoMove,List<Long> missionIds) throws BizError {
        List<Long> oldMissionIds = Lists.newArrayList();
        if(autoMove){
            Timestamp maxUpdateTime = couponMissionMapper.getMissionMoveLastTime();
            String lastMoveTime = "0";
            if (maxUpdateTime != null) {
                //往前数5分钟 插入时间会变化
                lastMoveTime = TimeUtil.formatSecond(maxUpdateTime.getTime() / 1000 - 300);
            }

            oldMissionIds = missionMapper.getMissionIdByUpdateTime(lastMoveTime);
        } else {
            if(CollectionUtils.isEmpty(missionIds)) {
                return;
            }
            oldMissionIds = missionIds;
        }

        if (CollectionUtils.isEmpty(oldMissionIds)) {
            return;
        }
        int size = oldMissionIds.size();
        int loopTime = size / LIMT + (size % LIMT == 0 ? 0 : 1);

        for (int i = 0; i < loopTime; i++) {
            List<Long> subMissions = oldMissionIds.subList(i * LIMT, Math.min((i + 1) * LIMT, size));
            List<MissionPo> missionPoList = missionMapper.getMissionByMissionIds(subMissions);

            for (MissionPo missionPo : missionPoList) {
                CouponMissionPO couponMissionPO = new CouponMissionPO();
                BeanMapper.copy(missionPo, couponMissionPO);
                Long configId = couponMissionMapper.checkoutMissionExit(missionPo.getId());

                if(Optional.ofNullable(configId).orElse(0L) > 0){
                    couponMissionMapper.updateOldMission(couponMissionPO);
                } else {
                    couponMissionMapper.insertOldMission(couponMissionPO);
                }
            }
        }
    }


    /**
     * 老mission任务迁移
     * 上线之后增量迁移老任务，如果该券之前没有老任务，刷新券缓存
     * @throws BizError
     */
    public void moveOldMissionV2(Boolean autoMove,List<Long> missionIds) throws BizError {
        long validTime = TimeUtil.getNowUnixSecond() - (12 * 3600);
        List<Long> oldMissionIds = Lists.newArrayList();
        if(autoMove){
            Timestamp maxUpdateTime = couponMissionMapper.getMissionMoveLastTime();
            String lastMoveTime = "0";
            if (maxUpdateTime != null) {
                //往前数5分钟 插入时间会变化
                lastMoveTime = TimeUtil.formatSecond(maxUpdateTime.getTime() / 1000 - 300);
            }

            oldMissionIds = missionMapper.getMissionIdByUpdateTime(lastMoveTime);
        } else {
            if(CollectionUtils.isEmpty(missionIds)) {
                return;
            }
            oldMissionIds = missionIds;
        }

        if (CollectionUtils.isEmpty(oldMissionIds)) {
            return;
        }
        int size = oldMissionIds.size();
        int loopTime = size / LIMT + (size % LIMT == 0 ? 0 : 1);

        BaseData common = makeCacheBaseData.getV2();

        StringBuilder updateCouponSql = new StringBuilder();

        for (int i = 0; i < loopTime; i++) {
            List<Long> subMissions = oldMissionIds.subList(i * LIMT, Math.min((i + 1) * LIMT, size));
            List<MissionPo> missionPoList = missionMapper.getMissionByMissionIds(subMissions);

            List<Long> subConfigIds = missionPoList.stream().map(MissionPo::getTypeId).distinct().collect(Collectors.toList());
            List<CouponConfigPo> couponConfigPoList = oldCouponConfigMapper.getConfigByIds(subConfigIds);
            Map<Long,CouponConfigPo> couponConfigPoMap = couponConfigPoList.stream().collect(Collectors.toMap(CouponConfigPo::getId, Function.identity(),(k1,k2)->k2));

            for (MissionPo missionPo : missionPoList) {
                CouponMissionPO couponMissionPO = new CouponMissionPO();
                BeanMapper.copy(missionPo, couponMissionPO);
                boolean newMissionExist = Optional.ofNullable(couponMissionMapper.checkoutMissionExit(missionPo.getId())).orElse(0L) > 0;

                if(newMissionExist){
                    couponMissionMapper.updateOldMission(couponMissionPO);
                } else {
                    couponMissionMapper.insertOldMission(couponMissionPO);
                }


                //检查该任务是否是该券的第一条任务,第一条任务需要刷缓存
               Long minMissionId =  missionMapper.getMissionIdByTypeId(missionPo.getTypeId());
                if(minMissionId !=null && minMissionId.equals(missionPo.getId())){
                    Long couponConfigId = missionPo.getTypeId();
                    try {
                        if(couponConfigPoMap.containsKey(couponConfigId) && couponConfigPoMap.get(couponConfigId) !=null){
                            CouponConfigPo couponConfigPo = couponConfigPoMap.get(couponConfigId);

                            CouponConfigPO newCouponConfigPO = couponConfigMapper.getCouponConfigById(couponConfigId);
                            boolean newConfigExist = newCouponConfigPO!=null && newCouponConfigPO.getId() >0;

                            if (couponConfigPo.getGlobalEndTime() < validTime) {
                                //已迁移过的券跳过
                                if (newConfigExist) {
                                    continue;
                                }
                                try {
                                    CouponConfigPO couponConfigPO = convertCouponConfigPO(couponConfigPo, missionPo, common);
                                    couponConfigMapper.insertOldCoupon(couponConfigPO);

                                    GoodsItemPo goodsItemPo = couponConfigPoConvert.serializeGoodsItemPo(couponConfigPO);
                                    CouponEsPO couponEsPO = couponConfigPoConvert.serializeCouponEsPO(couponConfigPO, goodsItemPo);
                                    couponInvertedTOCHelper.saveCouponConfig(couponEsPO);
                                } catch (Exception e) {
                                    log.error("moveOldMissionV2 invalidcoupon config error configId:{}", couponConfigPo.getId());
                                }
                            } else {
                                CouponConfigPO couponConfigPO ;
                                try {
                                    if (newConfigExist) {
                                        couponConfigPO = newCouponConfigPO;
                                        couponConfigPO.setApplyCount(missionPo.getSendNum().intValue());
                                        couponConfigPO.setSendScene(convertSendScene(couponConfigPo, missionPo));
                                        couponConfigPO.setUseTimeType(convertUseTimeType(missionPo));
                                        if (missionPo.getCouponStartTime() <= 0) {
                                            couponConfigPO.setStartUseTime(couponConfigPO.getStartFetchTime());
                                        } else {
                                            couponConfigPO.setStartUseTime(missionPo.getCouponStartTime());
                                        }
                                        if (missionPo.getCouponEndTime() <= 0) {
                                            couponConfigPO.setEndUseTime(couponConfigPO.getEndFetchTime());
                                        } else {
                                            couponConfigPO.setEndUseTime(missionPo.getCouponEndTime());
                                        }
                                        couponConfigPO.setUseDuration(missionPo.getCouponDays() * 24);
                                        couponConfigPO.setCreator(missionPo.getAdminName());
                                        couponConfigMapper.updateOldCoupon(couponConfigPO);
                                    } else {
                                        couponConfigPO = convertCouponConfigPO(couponConfigPo, missionPo, common);
                                        couponConfigPO.setApplyCount(missionPo.getSendNum().intValue());

                                        addMoreMissionCoupon(updateCouponSql, couponConfigPO);
                                        //couponConfigPO.setStatus(CouponConfigStatusEnum.OFFLINE.code);

                                        if (StringUtils.isEmpty(couponConfigPO.getSendChannel())) {
                                            addNoSceneCoupon(updateCouponSql, couponConfigPO);
                                        }
                                        couponConfigMapper.insertOldCoupon(couponConfigPO);
                                    }
                                    updateCouponCacheEsData(common, couponConfigPO);
                                } catch (Exception e) {
                                    log.error("moveOldMissionV2 validcoupon config error configId:{}", couponConfigPo.getId());
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("moveOldMissionV2 validcoupon config error missionId:{}", missionPo.getId());
                    }
                }




            }
        }
    }

    /**
     * 更新新模型数据
     * @param common
     * @param couponConfigPO
     * @throws Exception
     */
    private void updateCouponCacheEsData(BaseData common, CouponConfigPO couponConfigPO) throws Exception {
        // 更新券redis缓存
        ConfigInfoCachePo configInfoCachePo = couponConfigPoConvert.serializeConfigInfoCachePo(couponConfigPO);
        couponConfigRedisDao.setConfigInfoCache(configInfoCachePo, getExpireTimeStamp(couponConfigPO));

        GoodsItemPo goodsItemPo = couponConfigPoConvert.serializeGoodsItemPo(couponConfigPO);

        if(nacosSwitchConfig.isRefreshOldGoodsData()){
            ConfigGoodsCachePo couponGoodsCachePo = couponConfigPoConvert.serializeConfigGoodsCachePo(couponConfigPO, goodsItemPo, common);
            couponConfigRedisDao.setCouponGoodsCache(couponGoodsCachePo, getExpireTimeStamp(couponConfigPO));
        }

        NewConfigGoodsCachePo newCouponGoodsCachePo = couponConfigPoConvert.serializeConfigGoodsCachePoV2(couponConfigPO, goodsItemPo, common);
        couponConfigRedisDao.setCouponGoodsCacheV2(newCouponGoodsCachePo, getExpireTimeStamp(couponConfigPO));

        CouponEsPO couponEsPO = couponConfigPoConvert.serializeCouponEsPO(couponConfigPO, goodsItemPo);
        couponInvertedTOCHelper.saveCouponConfig(couponEsPO);

        OldCouponInfo oldCouponCachePo = oldCouponConvert.serializeOldCouponInfo(couponConfigPO);
        oldCouponRedisDao.setOldCouponInfo(oldCouponCachePo);
    }

    /**
     * 获取任务信息
     * @param couponConfigPo
     * @param missionPos
     * @return
     */
    private MissionPo getMissionPo(CouponConfigPo couponConfigPo, List<MissionPo> missionPos) {
        MissionPo missionPo;
        if (CollectionUtils.isEmpty(missionPos)) {
            missionPo = new MissionPo();
            missionPo.setCouponDays(0);
            missionPo.setCouponStartTime(couponConfigPo.getGlobalStartTime());
            missionPo.setCouponEndTime(couponConfigPo.getGlobalEndTime());
            missionPo.setAdminName("unknown");
            log.error("no mission coupon configId:{}", couponConfigPo.getId());
        } else if (missionPos.size() == 1) {
            missionPo= missionPos.get(0);
        } else {
            missionPo = missionPos.stream().max(Comparator.comparing(MissionPo::getCouponEndTime)).get();
        }
        return missionPo;
    }

    private Long getApplyCount(CouponConfigPo couponConfigPo, List<MissionPo> missionPos){
        if (CollectionUtils.isEmpty(missionPos)) {
            return couponConfigPo.getSendLimit();
        } else if (missionPos.size() == 1) {
            return missionPos.get(0).getMaxNum();
        } else {
            return missionPos.stream().mapToLong(MissionPo::getMaxNum).sum();
        }
    }

    private CouponConfigPO convertCouponConfigPO(CouponConfigPo couponConfigPo, MissionPo missionPo, BaseData common){

        CouponConfigPO couponConfigPO = new CouponConfigPO();

        couponConfigPO.setId(couponConfigPo.getId());
        couponConfigPO.setName(couponConfigPo.getName());
        couponConfigPO.setCouponDesc(couponConfigPo.getRangeDesc());
        couponConfigPO.setStatus(convertStatus(couponConfigPo, TimeUtil.getNowUnixSecond()));
        couponConfigPO.setPromotionType(couponConfigPo.getType());

        couponConfigPO.setSendScene(convertSendScene(couponConfigPo, missionPo));
        couponConfigPO.setSendPurpose(0);
        couponConfigPO.setStartFetchTime(couponConfigPo.getGlobalStartTime());
        couponConfigPO.setEndFetchTime(couponConfigPo.getGlobalEndTime());
        couponConfigPO.setUseTimeType(convertUseTimeType(missionPo));
        if (missionPo.getCouponStartTime() <= 0) {
            couponConfigPO.setStartUseTime(couponConfigPO.getStartFetchTime());
        } else {
            couponConfigPO.setStartUseTime(missionPo.getCouponStartTime());
        }
        if (missionPo.getCouponEndTime() <= 0) {
            couponConfigPO.setEndUseTime(couponConfigPO.getEndFetchTime());
        } else {
            couponConfigPO.setEndUseTime(missionPo.getCouponEndTime());
        }
        couponConfigPO.setUseDuration(missionPo.getCouponDays() * 24);

        // 使用渠道
        Set<Long> clients = convertClients(couponConfigPo, common);
        List<Integer> channels = convertUsechannel(couponConfigPo.getUseChannel(), clients, common);
        couponConfigPO.setUseChannel(StringUtils.join(channels,","));
        couponConfigPO.setUsePlatform(convertUsePlatform(channels));
        couponConfigPO.setUseStore(convertUseStore(clients));

        // 使用门槛转换
        List<QuotaItem> quotaItemList =parseQuota(couponConfigPo.getQuota());
        int bottomType = convertBottomType(quotaItemList);
        couponConfigPO.setBottomType(bottomType);
        couponConfigPO.setBottomPrice(convertBottomPrice(bottomType, quotaItemList));
        couponConfigPO.setBottomCount(convertBottomCount(bottomType, quotaItemList));

        // 优惠政策
        List<PolicyItem> policy = parsePolicy(couponConfigPo.getPolicy());
        couponConfigPO.setPromotionValue(convertPromotionValue(couponConfigPo, policy));
        couponConfigPO.setMaxReduce(convertMaxReduce(couponConfigPo.getType(), policy));
        if (policy.get(0) == null || policy.get(0).getRule() == null) {
            throw new BaseException(-1, String.format("非法的券配置，政策里存在空记录，policy=%s", policy));
        }

        // 可用商品
        couponConfigPO.setScopeType(1);
        String configCacheGoods;
        if (PromotionTypeEnum.NyuanBuy.getValue() == couponConfigPo.getType()) {
            // todo 是否所有类型都有 会影响到老缓存的数据写入
            configCacheGoods = convertDeductTargetGoods(common, couponConfigPo.getId(), policy.get(0).getRule().getTargetGoods());
        } else {
            configCacheGoods = convertGoodsInclude(common, couponConfigPo, false);
        }
        couponConfigPO.setGoodsInclude(configCacheGoods);
        couponConfigPO.setGoodsExclude(StringUtils.EMPTY);
        couponConfigPO.setCategoryIds(StringUtils.EMPTY);

        couponConfigPO.setApplyCount(Math.toIntExact(couponConfigPo.getSendLimit()));
        couponConfigPO.setFetchLimit(99);
        couponConfigPO.setExtProp(convertExtPropPO(couponConfigPo));
        couponConfigPO.setAreaIds(StringUtils.EMPTY);

        couponConfigPO.setCostShare(StringUtils.EMPTY);
        couponConfigPO.setCreateTime(couponConfigPo.getAddTime());
        //couponConfigPO.setUpdateTime(TimeUtil.parseDateTime(couponConfigPo.getLastUpdateTime()));
        couponConfigPO.setDepartmentId(couponConfigPo.getDepartmentId());
        couponConfigPO.setCode(Integer.parseInt(couponConfigPo.getIsCode()));
        couponConfigPO.setSendChannel(couponConfigPo.getSendChannel());
        couponConfigPO.setCreator(missionPo.getAdminName());
        couponConfigPO.setSource(1);

        return couponConfigPO;
    }

    private String convertSendScene(CouponConfigPo couponConfigPo, MissionPo missionPo) {
        SendChannelEnum sendChannelEnum = SendChannelEnum.findByValue(couponConfigPo.getSendChannel().trim());
        if (sendChannelEnum != null && sendChannelEnum != SendChannelEnum.Others) {
            return sendChannelEnum.getCode();
        }
        if (StringUtils.isEmpty(missionPo.getDepartment())) {
            return StringUtils.EMPTY;
        }
        try {
            String[] split = StringUtil.split(missionPo.getDepartment(), ":");
            if (split.length == 0) {
                return StringUtils.EMPTY;
            } else {
                return Optional.ofNullable(sendSceneAppIdRel.getSendSceneAppIdRel().get(split[0].trim())).orElse(StringUtils.EMPTY);
            }
        } catch (Exception e) {
            return StringUtils.EMPTY;
        }

    }

    private Set<Long> convertClients(CouponConfigPo couponConfigPo, BaseData common) {
        final String client = couponConfigPo.getClient();
        if (client == null || client.isEmpty()) {
            return new HashSet<>();
        }
        if(CouponConstant.DB_FIELD_ALL.equals(client)) {
            Set<Long> clients = new HashSet<>();
            for (UseChannelClientRelationDo clientRelationDo : common.getUseChannelClientRelation().values()) {
                if(CollectionUtils.isEmpty(clientRelationDo.getClientIds())){
                    continue;
                }
                clients.addAll(clientRelationDo.getClientIds());
            }
            return clients;
        }

        List<String> clientJson = GsonUtil.fromListJson(client, String.class);
        if (CollectionUtils.isEmpty(clientJson)) {
            log.error(String.format("非法的券配置，可用券的client列表无法正常解析，clients=%s", client));
        }
        return clientJson.stream().map(Long::parseLong).collect(Collectors.toSet());
    }


    private int convertUseTimeType(MissionPo missionPo) {
        return missionPo.getCouponDays() <= 0 ? UseTimeTypeEnum.ABSOLUTE.getValue() : UseTimeTypeEnum.RELATIVE.getValue();
    }

    private Integer convertStatus(CouponConfigPo couponConfigPo, long nowTime) {
        if(couponConfigPo.getGlobalEndTime() > nowTime && StatusEnum.Approved.getMysqlValue().equals(couponConfigPo.getStat())) {
            return CouponConfigStatusEnum.ONLINE.code;
        } else if(StatusEnum.Cancel.getMysqlValue().equals(couponConfigPo.getStat())) {
            return CouponConfigStatusEnum.STOP_FETCHING.code;
        } else {
            return CouponConfigStatusEnum.OFFLINE.code;
        }
    }

    private List<Integer> convertUsechannel(String useChannel, Set<Long> clients, BaseData common){
        List<Integer> newChannels = Lists.newArrayList();
        Set<String> channels = Arrays.stream(StringUtils.split(useChannel,",")).map(String::trim).collect(Collectors.toSet());
        //老券配置兼容处理：凡是发现原券配置里的client存在现使用渠道对应的client_id列表里，则代码选择了此使用渠道。产品的 @镜谕 确定的
        for(Map.Entry<String, UseChannelClientRelationDo> item : common.getUseChannelClientRelation().entrySet()) {
            if(item.getValue() == null) {
                continue;
            }
            if(channels.contains(item.getValue().getChannel())){
                continue;
            }
            if(CollectionUtils.isEmpty(clients)){
                break;
            }
            Set<Long> ids = item.getValue().getClientIds();
            for (Long client : clients) {
                if(ids.contains(client)){
                    channels.add(item.getKey());
                    break;
                }
            }
        }

        for (String channel : channels) {
            UseChannelEnum useChannelEnum = UseChannelEnum.findByValue(channel);
            if(useChannelEnum == null) {
                continue;
            }
            if(useChannelEnum == UseChannelEnum.MiHome){
                newChannels.add(UseChannelsEnum.DIRECTSALE_STORE.getValue());
                newChannels.add(UseChannelsEnum.EXCLUSIVE_SHOP.getValue());
            } else {
                newChannels.add(useChannelEnum.getCode());
            }
        }
        return newChannels;
    }


    private String convertExtPropPO(CouponConfigPo couponConfigPo){
        String extPropStr = couponConfigPo.getExtProp();
        if(StringUtils.isEmpty(extPropStr)){
            return StringUtils.EMPTY;
        }
        ExtProp extProp = GsonUtil.fromJson(extPropStr, ExtProp.class);

        ExtPropPO extPropPO = new ExtPropPO();
        extPropPO.setPostFree(Integer.parseInt(extProp.getIsPostFree()));
        extPropPO.setShare(Integer.parseInt(extProp.getIsShare()));
        extPropPO.setCheckPackage(2);
        extPropPO.setArea(2);
        extPropPO.setCheckPrice(Integer.parseInt(couponConfigPo.getCheckPrice()));

        return GsonUtil.toJson(extPropPO);
    }

    private String convertUsePlatform(List<Integer> channels){
        Map<Integer,UseChannelPO> useStorePOMap = new HashMap<>();
        UseChannelPO useStorePO = new UseChannelPO();
        useStorePO.setAll(true);
        if(channels.contains(UseChannelsEnum.XIAOMI_SHOP.getValue())){
            useStorePOMap.put(UseChannelsEnum.XIAOMI_SHOP.getValue(), useStorePO);
        }
        return GsonUtil.toJson(useStorePOMap);
    }

    private String convertUseStore(Set<Long> clients){
        Map<Integer,UseChannelPO> useStorePOMap = new HashMap<>();

        UseChannelPO useStorePO = new UseChannelPO();
        useStorePO.setAll(true);
        if(clients.contains(180100041075L)){
            useStorePOMap.put(UseChannelsEnum.DIRECTSALE_STORE.getValue(), useStorePO);
            useStorePOMap.put(UseChannelsEnum.EXCLUSIVE_SHOP.getValue(), useStorePO);

        }
        if(clients.contains(180100041157L)){
            useStorePOMap.put(UseChannelsEnum.AUTHORIZED_STORE.getValue(), useStorePO);
        }

        return GsonUtil.toJson(useStorePOMap);
    }

    /**
     * parse
     *
     * @param quota String
     * @return List<QuotaItem>
     */
    private List<QuotaItem> parseQuota(String quota) {
        if (StringUtils.isEmpty(quota)) {
            throw new BaseException(-1, String.format("非法的券配置，配额不能为空，quota=%s", quota));
        }

        List<QuotaItem> quotaJson = GsonUtil.fromListJson(quota, QuotaItem.class);
        if (CollectionUtils.isEmpty(quotaJson)) {
            throw new BaseException(-1, String.format("非法的券配置，配额无法正常解析，quota=%s", quota));
        }

        return quotaJson;
    }

    /**
     * convert
     *
     * @param quotas List<QuotaItem>
     * @return String
     */
    private Integer convertBottomType(List<QuotaItem> quotas) {
        if (quotas.get(0) == null) {
            throw new BaseException(-1, String.format("非法的券配置，配额里存在空记录，quotas=%s", quotas));
        }

        QuotaTypeEnum val = QuotaTypeEnum.findByMysqlValue(quotas.get(0).getCode());
        if (val == null) {
            throw new BaseException(-1, String.format("非法的券配置，配额类型超出已知范围，quotas=%s", quotas));
        }

        return val.getCode();
    }


    private Long convertBottomPrice(int bottomType, List<QuotaItem> quotas){
        Long quotaMoney = 0L;
        if (BottomTypeEnum.OverYuan.getValue() == bottomType
                || BottomTypeEnum.PerOverYuan.getValue() == bottomType
                || BottomTypeEnum.OverYuanAndCount.getValue() == bottomType) {
            quotaMoney = convertQuotaMoney(quotas);
        }
        return quotaMoney;
    }

    private Integer convertBottomCount(int bottomType, List<QuotaItem> quotas){
        Integer quotaCount = 0;
        if (BottomTypeEnum.OverCount.getValue() == bottomType
                || BottomTypeEnum.PerOverCount.getValue() == bottomType
                || BottomTypeEnum.OverYuanAndCount.getValue() == bottomType) {
            quotaCount = convertQuotaCount(quotas);
        }
        return quotaCount;
    }

    private Long convertPromotionValue(CouponConfigPo couponConfigPo, List<PolicyItem> policy){
        Long promotionValue = 0L;
        final int promotionType = couponConfigPo.getType();
        if (PromotionTypeEnum.ConditionReduce.getValue() == promotionType || PromotionTypeEnum.DirectReduce.getValue() == promotionType) {
            promotionValue = convertReduceMoney(policy.get(0).getRule());
        } else if (PromotionTypeEnum.ConditionDiscount.getValue() == promotionType) {
            promotionValue = convertReduceDiscount(policy.get(0).getRule());
        } else if (PromotionTypeEnum.NyuanBuy.getValue() == promotionType) {
            if(couponConfigPo.getDeductType() == 1) {
                promotionValue = 1L;
            } else {
                promotionValue = 0L;
            }
        } else {
            throw new BaseException(-1, String.format("非法的券配置，券的使用类型超出已知范围，promotionType=%s, policy=%s", promotionType, policy));
        }
        return promotionValue;
    }

    private Long convertMaxReduce(int promotionType, List<PolicyItem> policy){
        Long reduceMaxPrice=0L;
        if (PromotionTypeEnum.ConditionDiscount.getValue() == promotionType) {
            reduceMaxPrice = convertReduceMaxPrice(policy.get(0).getSuffix());
        }
        return reduceMaxPrice;
    }

    /**
     * convert all goods
     *
     * @param common BaseData
     * @return ConfigCacheGoods
     */
    private String convertAllGoodsInclude(BaseData common) {

        Set<Long> skuSet = new HashSet<>();
        Set<Long> gidSet = new HashSet<>();
        Set<Long> packageSet = new HashSet<>();

        for (GoodsPo it : common.getGoodsMap().values()) {
            skuSet.add(it.getSku());
            gidSet.add(it.getGoodsId());
        }

        for (PackagePo it : common.getPackageMap().values()) {
            packageSet.add(it.getPackageId());
        }

        GoodItemPO goodItemPO = new GoodItemPO();
        goodItemPO.setSku(Lists.newArrayList(skuSet));
        goodItemPO.setGoods(Lists.newArrayList(gidSet));
        goodItemPO.setPackages(Lists.newArrayList(packageSet));
        return GsonUtil.toJson(goodItemPO);
    }


    public String convertGoodsInclude(BaseData common, CouponConfigPo couponConfigPo, boolean isRefresh) {
        String goodsInclude = couponConfigPo.getGoodsInclude();
        Long modifyTime = couponConfigPo.getModifyTime();
        Long configId = couponConfigPo.getId();
        if (StringUtils.isEmpty(goodsInclude)) {
            log.error(" 可用券的商品列表为空, configId={}, goodsInclude={}", couponConfigPo.getId(), goodsInclude);
            return StringUtils.EMPTY;
        }

        //所有商品 是否还有 *
        if (CouponConstant.DB_FIELD_ALL.equals(goodsInclude)) {
            return convertAllGoodsInclude(common);
        }

        Map<String, List<Long>> goodIncludeMap = new HashMap<>();

        Set<Long> skuSet = new HashSet<>();
        Set<Long> gidSet = new HashSet<>();
        Set<Long> packageSet = new HashSet<>();

        //指定商品
        List<GoodsIncludeItem> goodsListJson = GsonUtil.fromListJson(goodsInclude, GoodsIncludeItem.class);

        if (goodsListJson == null) {
            log.error("task.couponConfig.cache, 可用券的商品列表无法正常解析, configId={}, goodsInclude={}", couponConfigPo.getId(), goodsInclude);
            return GsonUtil.toJson(goodIncludeMap);
        }
        if (CollectionUtils.isEmpty(goodsListJson)) {
            log.error("task.couponConfig.cache, 可用券的商品列表解析后为空, configId={}, goodsInclude={}", couponConfigPo.getId(), goodsInclude);
            return GsonUtil.toJson(goodIncludeMap);
        }

        for (GoodsIncludeItem includeItem : goodsListJson) {
            if (GoodsLevelEnum.Goods.getValue().equals(includeItem.getLevel())) {
                //配置的是货品ID
                GoodsPo goodsInfo = common.getGoodsInfo(includeItem.getId());
                if (goodsInfo == null) {
                    continue;
                }
                skuSet.add(goodsInfo.getSku());
                gidSet.add(goodsInfo.getGoodsId());
            } else if (GoodsLevelEnum.Sku.getValue().equals(includeItem.getLevel())) {
                //配置的是SKU
                GoodsPo skuInfo = common.getSkuInfo(includeItem.getId());
                if (skuInfo == null) {
                    continue;
                }

                skuSet.add(skuInfo.getSku());
                gidSet.add(skuInfo.getGoodsId());
            } else if (GoodsLevelEnum.Package.getValue().equals(includeItem.getLevel())) {
                //配置的是套装ID
                if(!isRefresh){
                    PackagePo packageInfo = common.getPackageInfo(includeItem.getId());
                    if (packageInfo == null) {
                        continue;
                    }
                }
                packageSet.add(includeItem.getId());

            } else if (GoodsLevelEnum.Group.getValue().equals(includeItem.getLevel())) {
                //配置的是品类ID
                List<SkuGroupMapPo> list = common.getGroupInfo(includeItem.getId());
                if (list == null || list.size() == 0) {
                    continue;
                }
                for (SkuGroupMapPo groupInfo : list) {
                    if (!isRefresh && groupInfo.getAddTime() >= modifyTime) {
                        continue;
                    }

                    //sku
                    GoodsPo skuInfo = common.getSkuInfo(groupInfo.getSku());
                    if (skuInfo != null) {
                        skuSet.add(skuInfo.getSku());
                        gidSet.add(skuInfo.getGoodsId());
                    }

                    //package
                    PackagePo packageInfo = common.getPackageInfo(groupInfo.getSku());

                    if (packageInfo != null) {
                        packageSet.add(packageInfo.getPackageId());
                    }
                }
            } else if ("cat".equals(includeItem.getLevel())) {
                //分类不再支持
                continue;
            } else {
                log.warn("task.couponConfig.cache, 超出已知范围的商品品级, configId={}, id={}, level={}", configId, includeItem.getId(), includeItem.getLevel());
            }
        }
        if (skuSet.size() + gidSet.size() + packageSet.size() == 0) {
            log.error("task.couponConfig.cache, 未解析出任何可用商品, configId={}, goodsInclude={}", configId, goodsInclude);
        }

        GoodItemPO goodItemPO = new GoodItemPO();
        goodItemPO.setSku(Lists.newArrayList(skuSet));
        goodItemPO.setGoods(Lists.newArrayList(gidSet));
        goodItemPO.setPackages(Lists.newArrayList(packageSet));
        return GsonUtil.toJson(goodItemPO);
    }

    /**
     * convert
     *
     * @param common      BaseData
     * @param configId    Long
     * @param targetGoods List<TargetGoodsItem>
     * @return List<ConfigCacheTargetGoodsItem>
     */
    private String convertDeductTargetGoods(BaseData common, Long configId, List<TargetGoodsItem> targetGoods) {
        if (CollectionUtils.isEmpty(targetGoods)) {
            throw new BaseException(-1, String.format("非法的券配置，券的抵扣商品列表不能为空，targetGoods=%s", targetGoods));
        }

        Map<String, List<Long>> goodIncludeMap = new HashMap<>();
        Set<Long> skuSet = new HashSet<>();
        Set<Long> packageSet = new HashSet<>();

        for (TargetGoodsItem item : targetGoods) {
            //SKU
            if (GoodsLevelEnum.Sku.getValue().equals(item.getLevel())) {
                skuSet.add(item.getId());
                continue;
            }
            //货品
            if (GoodsLevelEnum.Goods.getValue().equals(item.getLevel())) {
                GoodsPo info = common.getGoodsInfo(item.getId());
                if (info == null) {
                    log.warn("task.couponConfig.cache, 券的抵扣商品列表里存在非上架的货品, configId={}, targetGoods.item={}", configId, item);
                    continue;
                }
                skuSet.add(info.getSku());
                continue;
            }
            //商品
            if (GoodsLevelEnum.Commodity.getValue().equals(item.getLevel())) {
                List<GoodsPo> infos = common.getGoodsInfoByCommodityId(item.getId());
                if (infos == null) {
                    log.warn("task.couponConfig.cache, 券的抵扣商品列表里存在非上架的商品, configId={}, targetGoods.item={}", configId, item);
                    continue;
                }
                for (GoodsPo info : infos) {
                    skuSet.add(info.getSku());
                }
                continue;
            }
            //套装
            if (GoodsLevelEnum.Package.getValue().equals(item.getLevel())) {
                packageSet.add(item.getId());
                continue;
            }

            log.warn("task.couponConfig.cache, 券的抵扣商品列表的商品品级超出已知范围, configId={}, targetGoods={}", configId, item);
        }

        GoodItemPO goodItemPO = new GoodItemPO();
        goodItemPO.setSku(Lists.newArrayList(skuSet));
        goodItemPO.setPackages(Lists.newArrayList(packageSet));
        return GsonUtil.toJson(goodItemPO);
    }


    /**
     * convert
     *
     * @param suffixs List<SuffixItem>
     * @return Long
     */
    private Long convertReduceMaxPrice(List<SuffixItem> suffixs) {

        // 线上 todo 老数据是否有suffix为空的情况
        if (CollectionUtils.isEmpty(suffixs)) {
            return 0L;
        }

        for (SuffixItem suffix : suffixs) {
            String code = suffix.getCode();
            String value = suffix.getValue();
            if (!"max_price".equals(code)) {
                continue;
            }
            BigDecimal money = new BigDecimal(value);
            BigDecimal maxPrice = money.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_DOWN).stripTrailingZeros();
            if (maxPrice.compareTo(new BigDecimal(0)) < 0) {
                log.error(String.format("非法的券配置，折扣券的最大减免金额必须大于0，suffix=%s", suffix));
            }
            return maxPrice.longValue();
        }

        throw new BaseException(-1, String.format("非法的券配置，未配置折扣券的最大减免金额必须大于0，suffix=%s", suffixs));
    }

    /**
     * convert
     *
     * @param rule Rule
     * @return Long
     */
    private Long convertReduceDiscount(Rule rule) {
        if (rule == null) {
            throw new BaseException(-1, "非法的券配置，政策里的规则不能为空");
        }

        String disStr = rule.getReduceDiscount();
        BigDecimal money = new BigDecimal(disStr);
        BigDecimal discount = money.multiply(new BigDecimal(1000)).setScale(2, RoundingMode.UP).stripTrailingZeros();

        if (discount.compareTo(new BigDecimal(0)) < 0) {
            throw new BaseException(-1, String.format("非法的券配置，折扣券的折扣值必须大于0，rule=%s", rule));
        }

        return discount.longValue();
    }

    /**
     * convert
     *
     * @param rule Rule
     * @return Long
     */
    private Long convertReduceMoney(Rule rule) {
        if (rule == null) {
            throw new BaseException(-1, "非法的券配置，政策里的规则不能为空");
        }

        String moneyStr = rule.getReduceMoney();
        BigDecimal money = new BigDecimal(moneyStr);
        BigDecimal moneyCent = money.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_DOWN).stripTrailingZeros();

        if (moneyCent.compareTo(new BigDecimal(0)) < 0) {
            throw new BaseException(-1, String.format("非法的券配置，满减券的可减金额必须大于0，rule=%s", rule));
        }

        return moneyCent.longValue();
    }

    /**
     * parse
     *
     * @param policy String
     * @return List<PolicyItem>
     */
    private List<PolicyItem> parsePolicy(String policy) {
        if (StringUtils.isEmpty(policy)) {
            throw new BaseException(-1, String.format("非法的券配置，政策不能为空，policy=%s", policy));
        }

        List<PolicyItem> policyJson = GsonUtil.fromListJson(policy, PolicyItem.class);
        if (CollectionUtils.isEmpty(policyJson)) {
            throw new BaseException(-1, String.format("非法的券配置，政策无法正常解析，policy=%s", policy));
        }

        return policyJson;
    }

    /**
     * convert
     *
     * @param quotas List<QuotaItem>
     * @return Long
     */
    private Integer convertQuotaCount(List<QuotaItem> quotas) {
        if (quotas.get(0) == null) {
            throw new BaseException(-1, String.format("非法的券配置，配额里存在空记录，quotas=%s", quotas));
        }
        int count;
        try {
            String countStr = quotas.get(0).getValue();
            count = Integer.parseInt(countStr);
        } catch (NumberFormatException e) {
            throw new BaseException(-1, String.format("非法的券配置，配额的件数转成Long时出错，quotas=%s", quotas));
        }
        return count;
    }

    /**
     * convert
     *
     * @param quotas List<QuotaItem>
     * @return Long
     */
    private Long convertQuotaMoney(List<QuotaItem> quotas) {
        if (quotas.get(0) == null) {
            throw new BaseException(-1, String.format("非法的券配置，配额里存在空记录，quotas=%s", quotas));
        }

        try {
            String moneyStr = quotas.get(0).getValue();
            BigDecimal money = new BigDecimal(moneyStr);
            BigDecimal moneyCent = money.multiply(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros();

            if (moneyCent.compareTo(new BigDecimal(0)) < 0) {
                log.error(String.format("非法的券配置，配额的金额必须大于0，quotas=%s", quotas));
            }

            return moneyCent.longValue();
        } catch (NumberFormatException e) {
            throw new BaseException(-1, String.format("非法的券配置，配额的金额转成Long时出错，quotas=%s", quotas));
        }
    }

    private void addMoreMissionCoupon(StringBuilder insertSql, CouponConfigPO couponConfigPO) {
        String sql = "update nr_coupon_config set use_time_type =*,start_use_time=*, end_use_time=*, use_duration=*, status = 1 where id = "+ couponConfigPO.getId() + ";";
        insertSql.append(sql);
    }

    private void addNoSceneCoupon(StringBuilder insertSql, CouponConfigPO couponConfigPO) {
        String sql = "update nr_coupon_config set send_scene =" + couponConfigPO.getSendScene() + "  where id = " + couponConfigPO.getId() + ";";
        insertSql.append(sql);
    }

    /**
     * 获取券配置写入缓存过期时间
     * @param couponConfigPO
     * @return
     */
    private long getExpireTimeStamp(CouponConfigPO couponConfigPO) {
        long endTime = Math.max(couponConfigPO.getEndUseTime(), couponConfigPO.getEndFetchTime());
        return endTime - TimeUtil.getNowUnixSecond() + CommonConstant.YEAR_TIMESTAMP_LENGTH;
    }


    /**
     * 获取有效券id
     * @param ids
     * @return
     */
    public List<Long> getOldRefreshCouponIds(Set<Long> ids){
       return oldCouponConfigMapper.getRefreshCouponIds(ids);
    }
}
