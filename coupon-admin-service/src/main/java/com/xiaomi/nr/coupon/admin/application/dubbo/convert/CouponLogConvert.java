package com.xiaomi.nr.coupon.admin.application.dubbo.convert;

import com.google.gson.reflect.TypeToken;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.UseChannelVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.UseTermVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.CouponBriefVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.CouponLogListVO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.optlog.po.CouponLogPO;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Description: 优惠券日志
 * @Date: 2022.03.11 20:57
 */
@Component
public class CouponLogConvert {


    /**
     * 将PO转为券简要信息VO
     *
     * @param couponConfigPO
     * @return
     */
    public CouponBriefVO convertToBriefVO(CouponConfigPO couponConfigPO) throws Exception{
        CouponBriefVO couponBriefVO = new CouponBriefVO();
        couponBriefVO.setCouponType(couponConfigPO.getCouponType());
        couponBriefVO.setShipmentId(couponConfigPO.getShipmentId());
        couponBriefVO.setName(couponConfigPO.getName());
        couponBriefVO.setStatus(CouponConfigConvert.transferToTimeStatus(couponConfigPO.getStartFetchTime(),
                couponConfigPO.getEndFetchTime(), couponConfigPO.getStatus()));
        couponBriefVO.setSendScene(couponConfigPO.getSendScene());
        couponBriefVO.setSendPurpose(couponConfigPO.getSendPurpose());
        couponBriefVO.setStartFetchTime(new Date(couponConfigPO.getStartFetchTime() * 1000));
        couponBriefVO.setEndFetchTime(new Date(couponConfigPO.getEndFetchTime() * 1000));
        UseTermVO useTermVO = new UseTermVO();
        useTermVO.setUseTimeType(couponConfigPO.getUseTimeType());
        useTermVO.setStartUseTime(new Date(couponConfigPO.getStartUseTime() * 1000));
        useTermVO.setEndUseTime(new Date(couponConfigPO.getEndUseTime() * 1000));
        useTermVO.setUseDuration(couponConfigPO.getUseDuration());
        useTermVO.setTimeGranularity(couponConfigPO.getTimeGranularity());
        couponBriefVO.setUseTermVO(useTermVO);
        couponBriefVO.setUseChannel(convertToChannelVO(couponConfigPO.getUsePlatform(), couponConfigPO.getUseStore()));
        couponBriefVO.setSendTotal(couponConfigPO.getApplyCount());
        return couponBriefVO;
    }

    /**
     * 封装前端使用渠道VO
     * @param usePlatform
     * @param useStore
     * @return
     */
    private Map<Integer, UseChannelVO> convertToChannelVO(String usePlatform, String useStore) {
        Map<Integer, UseChannelVO> useChannelMap = new HashMap<>();
       if(StringUtils.isNotBlank(usePlatform)){
           Map<Integer, UseChannelVO> usePlatformMap = GsonUtil.fromJson(usePlatform, new TypeToken<Map<Integer, UseChannelVO>>() {}.getType());
           useChannelMap.putAll(usePlatformMap);
       }
        if(StringUtils.isNotBlank(useStore)){
            Map<Integer, UseChannelVO> useStoreMap = GsonUtil.fromJson(useStore, new TypeToken<Map<Integer, UseChannelVO>>() {}.getType());
            useChannelMap.putAll(useStoreMap);
        }
        return useChannelMap;
    }

    public List<CouponLogListVO> convertToVO(List<CouponLogPO> couponLogPOList) {
        List<CouponLogListVO> couponLogListVOList = new ArrayList<>();
        for (CouponLogPO couponLogPO : couponLogPOList) {
            CouponLogListVO couponLogListVO = new CouponLogListVO();
            couponLogListVO.setLogId(couponLogPO.getId());
            couponLogListVO.setOperationType(couponLogPO.getOperationType());
            couponLogListVO.setOperator(couponLogPO.getOperator());
            couponLogListVO.setCreateTime(couponLogPO.getCreateTime());
            couponLogListVOList.add(couponLogListVO);
        }
        return couponLogListVOList;
    }
}
