package com.xiaomi.nr.coupon.admin.application.dubbo.convert;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponCodeTaskListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillTaskListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponCodeTaskListVO;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.enums.task.CouponTaskTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.task.TaskStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.CodeTaskSearchParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.FillCouponTaskPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.Param;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.SearchTaskParam;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CouponTaskConvert {

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    /**
     * 券码列表可展示的状态
     */
    private static final Set<Integer> codeTaskStatus = new HashSet<>(Arrays.asList(TaskStatusEnum.RUNNING.code
    , TaskStatusEnum.FAIL.code, TaskStatusEnum.SUCCESS.code));


    /**
     * 查询参数转换
     * @param req 查询入参
     * @return 搜索参数
     */
    public SearchTaskParam transferToSearchParameter(CouponFillTaskListRequest req){
        SearchTaskParam searchParam = new SearchTaskParam();
        BeanUtils.copyProperties(req, searchParam);
        searchParam.setOrderBy(req.getOrderByMap().containsKey(req.getOrderBy())?req.getOrderByMap().get(req.getOrderBy()) : req.getOrderBy());
        searchParam.setType(CouponTaskTypeEnum.COUPON_FILL.getCode());
        searchParam.setCouponType(req.getCouponType());
        searchParam.setConfigIds(Objects.isNull(req.getConfigId()) ? null : new ArrayList<>(Collections.singletonList(req.getConfigId())));
        searchParam.setUseChannel(CollectionUtils.isEmpty(req.getUseChannel()) ? null : StringUtils.join(req.getUseChannel(), ","));
        searchParam.setStartTime(Objects.isNull(req.getStartTime()) ? null : TimeUtil.convertDateToLong(req.getStartTime()));
        searchParam.setEndTime(Objects.isNull(req.getEndTime()) ? null : TimeUtil.convertDateToLong(req.getEndTime()));
        if(!Objects.isNull(req.getStatus())){
            switch (Objects.requireNonNull(TaskStatusEnum.findByCode(req.getStatus()))){
                case RUNNING:
                    searchParam.setStatusList(Arrays.asList(TaskStatusEnum.RUNNING.getCode(), TaskStatusEnum.READY.getCode(), TaskStatusEnum.AWAIT.getCode(), TaskStatusEnum.PRE_AWAIT.getCode()));
                    break;
                case SUCCESS:
                    searchParam.setStatusList(Collections.singletonList(TaskStatusEnum.SUCCESS.getCode()));
                    break;
                case FAIL:
                    searchParam.setStatusList(Arrays.asList(TaskStatusEnum.FAIL.getCode(), TaskStatusEnum.ERROR.getCode()));
                    break;
                default:
                   log.warn("灌券任务状态不合法status:{}", req.getStatus());
                   break;
            }
        }

        Integer bizType = Optional.ofNullable(req.getBizType()).orElse(BizPlatformEnum.RETAIL.getCode());

        if(BizPlatformEnum.RETAIL.getCode() == bizType) {
            List<Integer> bizTypeList = Lists.newArrayList(bizType, BizPlatformEnum.CAR.getCode(), BizPlatformEnum.CAR_SHOP.getCode());
            searchParam.setBizType(StringUtils.join(bizTypeList, ","));
        } else {
            searchParam.setBizType(bizType.toString());
        }

        return searchParam;
    }

    public void convertToSearchTaskParam(CouponCodeTaskListRequest request, SearchTaskParam searchTaskParam) {
        searchTaskParam.setType(CouponTaskTypeEnum.COUPON_CODE.code);
        if (request.getConfigId() != null && request.getConfigId() > 0) {
            searchTaskParam.setConfigIds(Arrays.asList(request.getConfigId()));
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(request.getCouponName())) {
            searchTaskParam.setCouponName(request.getCouponName());
        }
        if (request.getStatus() != null && request.getStatus() > 0) {
            //searchTaskParam.setStatus(request.getStatus());
        }
        searchTaskParam.setPageNo(request.getPageNo());
        searchTaskParam.setPageSize(request.getPageSize());
        searchTaskParam.setOrderBy("id");
        searchTaskParam.setOrderDirection(request.getOrderDirection());
    }

    public void convertTOSearchParam(CouponCodeTaskListRequest request, CodeTaskSearchParam codeTaskSearchParam) {
        BeanMapper.copy(request, codeTaskSearchParam);
        if ("startTime".equals(request.getOrderBy())) {
            codeTaskSearchParam.setOrderBy("start_time");
        } else {
            codeTaskSearchParam.setOrderBy("config_id");
        }
        //兼容前端状态
        if (request.getStatus() != null && codeTaskStatus.contains(request.getStatus())) {
            List<Integer> statusList = null;
            String statusStr = "";
            if (request.getStatus() == TaskStatusEnum.RUNNING.code) {
                statusList = Arrays.asList(TaskStatusEnum.AWAIT.code, TaskStatusEnum.READY.code, TaskStatusEnum.RUNNING.code);
            }
            if (request.getStatus() == TaskStatusEnum.FAIL.code) {
                statusList = Arrays.asList(TaskStatusEnum.FAIL.code, TaskStatusEnum.ERROR.code);
            }
            if (request.getStatus() == TaskStatusEnum.SUCCESS.code) {
                statusList = Arrays.asList(TaskStatusEnum.SUCCESS.code);
            }
            codeTaskSearchParam.setStatus(statusList);
        }
        codeTaskSearchParam.setType(CouponTaskTypeEnum.COUPON_CODE.code);
    }

    public List<CouponCodeTaskListVO> convertToCodeTaskList(List<FillCouponTaskPO> couponTaskPOList) {
        //获取券配置信息
        List<CouponCodeTaskListVO> couponCodeTaskListVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(couponTaskPOList)) {
            return couponCodeTaskListVOList;
        }
        List<Long> configIds = couponTaskPOList.stream().map(FillCouponTaskPO::getConfigId).collect(Collectors.toList());
        List<CouponConfigPO> couponConfigs = couponConfigRepository.searchCouponListById(configIds);
        Map<Long, CouponConfigPO> idToCouponConfig = couponConfigs.stream().collect(Collectors.toMap(CouponConfigPO::getId, a -> a, (k1, k2) -> k1));
        for (FillCouponTaskPO couponTaskPO : couponTaskPOList) {
            Long configId = couponTaskPO.getConfigId();
            CouponCodeTaskListVO couponCodeTaskListVO = new CouponCodeTaskListVO();
            couponCodeTaskListVO.setCouponId(configId);
            couponCodeTaskListVO.setCouponName(idToCouponConfig.get(configId).getName());
            couponCodeTaskListVO.setProcessRate(couponTaskPO.getProcessRate());
            if (couponTaskPO.getStatus() == TaskStatusEnum.AWAIT.code || couponTaskPO.getStatus() == TaskStatusEnum.READY.code
            || couponTaskPO.getStatus() == TaskStatusEnum.RUNNING.code) {
                couponCodeTaskListVO.setStatus(TaskStatusEnum.RUNNING.code);
            }
            if (couponTaskPO.getStatus() == TaskStatusEnum.FAIL.code || couponTaskPO.getStatus() == TaskStatusEnum.ERROR.code) {
                couponCodeTaskListVO.setStatus(TaskStatusEnum.FAIL.code);
            }
            if (couponTaskPO.getStatus() == TaskStatusEnum.SUCCESS.code) {
                couponCodeTaskListVO.setStatus(couponTaskPO.getStatus());
            }
            if(couponTaskPO.getStartTime()>0){
                couponCodeTaskListVO.setStartTime(new Date(couponTaskPO.getStartTime() * 1000));
            }
            if(couponTaskPO.getFinishTime()>0){
                couponCodeTaskListVO.setEndTime(new Date(couponTaskPO.getFinishTime() * 1000));
            }

            Param param = GsonUtil.fromJson(couponTaskPO.getParams(), Param.class);
            couponCodeTaskListVO.setNum((int) param.getCount());
            couponCodeTaskListVO.setCreator(couponTaskPO.getCreator());
            couponCodeTaskListVOList.add(couponCodeTaskListVO);
        }
        return couponCodeTaskListVOList;
    }

}
