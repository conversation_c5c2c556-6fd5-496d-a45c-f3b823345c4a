package com.xiaomi.nr.coupon.admin.application.dubbo.convert;

import cn.hutool.core.date.DateUtil;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponCodeInfo;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sync.request.SyncXiguaMarketCouponReceiveRecordRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.UserCouponCodeVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.request.UserCouponCodeListRequest;
import com.xiaomi.nr.coupon.admin.enums.couponcode.AssignTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponcode.CodeStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.couponcode.UseModeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponcode.UserCodeStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.DownLoadCouponCodePO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.CodeCouponReceiveRecordPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.coupon.po.CouponCodePO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.coupon.po.SearchCodeListParam;
import com.xiaomi.nr.coupon.admin.util.AesUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 优惠码实体转换
 */
@Component
public class CouponCodeConvert {


    // 优惠码加解密key
    private static final String AES_PASSWORD = "M6EED09BCC6539AD";

    /**
     * 转换生成券码下载Excel实体
     *
     * @param codePOList 有码券
     * @return List<>
     */
    public List<DownLoadCouponCodePO> convertToCouponCodeExcel(List<CouponCodePO> codePOList) throws Exception {

        if (CollectionUtils.isEmpty(codePOList)) {
            return null;
        }

        long nowTime = TimeUtil.getNowUnixSecond();
        List<DownLoadCouponCodePO> result = Lists.newLinkedList();

        for (CouponCodePO codePO : codePOList) {
            result.add(new DownLoadCouponCodePO(AesUtil.decrypt(AES_PASSWORD, codePO.getCouponCode()), convertCouponCodeStat(codePO.getStat(), codePO.getEndTime(), nowTime)));
        }

        return result;
    }


    /**
     * 用户优惠码信息查询参数转换
     *
     * @param request request
     * @return SearchCodeListParam
     */
    public SearchCodeListParam convertToSearchParam(UserCouponCodeListRequest request) {

        SearchCodeListParam param = new SearchCodeListParam();
        param.setCouponIndex(StringUtils.isEmpty(request.getCouponCode()) ? null : DigestUtils.md5Hex(request.getCouponCode()));
        param.setUseMode(request.getUseMode());
        param.setUserId(request.getUserId());
        param.setStat(UserCodeStatusEnum.findValueByCode(request.getStatus()));

        return param;
    }


    /**
     * 用户优惠码信息返回值转换
     *
     * @param couponCodePOList
     * @return
     * @throws Exception
     */
    public List<UserCouponCodeVO> convertUserCouponCodeVO(List<CouponCodePO> couponCodePOList) throws Exception {

        if (CollectionUtils.isEmpty(couponCodePOList)) {
            return null;
        }

        long nowTime = TimeUtil.getNowUnixSecond();
        UserCouponCodeVO vo;
        List<UserCouponCodeVO> result = new ArrayList<>(couponCodePOList.size());

        for (CouponCodePO codePo : couponCodePOList) {

            vo = new UserCouponCodeVO();
            try {
                vo.setCouponCode(AesUtil.decrypt(AES_PASSWORD, codePo.getCouponCode()));
            } catch (Exception e) {
                vo.setCouponCode("老明码券(" + codePo.getCouponCode() + ")");
            }

            vo.setStatusDesc(convertCouponCodeStat(codePo.getStat(), codePo.getEndTime(), nowTime));
            vo.setConfigId(codePo.getTypeId());

            // 未使用|已过期,以下属性不赋默认值
            if (!StringUtils.equals(UserCodeStatusEnum.USED.getValue(), codePo.getStat())) {
                result.add(vo);
                continue;
            }

            vo.setUseTime(TimeUtil.convertLongToDate(codePo.getUseTime()));
            vo.setUseMode(codePo.getUseMode());
            vo.setUserId(codePo.getUserId());

            // 使用方式为明码的只赋值订单号
            if (UseModeEnum.CODE == UseModeEnum.findEnumByCode(codePo.getUseMode())) {
                vo.setOrderId(codePo.getOrderId());
                result.add(vo);
                continue;
            }

            // 兑换方式的只赋值兑换后的用户券id
            vo.setCouponId(codePo.getCouponId());
            result.add(vo);
        }

        return result;
    }


    /**
     * 优惠码状态适配
     *
     * @param stat    状态
     * @param endTime 结束时间
     * @param nowTime 当前时间
     * @return String
     */
    private String convertCouponCodeStat(String stat, long endTime, long nowTime) {

        // 已使用
        if (Objects.equal(UserCodeStatusEnum.USED.getValue(), stat)) {
            return CodeStatusEnum.USED.getDesc();
        }

        // 已过期
        if (endTime < nowTime) {
            return CodeStatusEnum.EXPIRED.getDesc();
        }

        // 未使用
        return CodeStatusEnum.UNUSED.getDesc();
    }


    /**
     * 构建CodeCouponReceiveRecordPo
     *
     * @param request request
     * @return CodeCouponReceiveRecordPo
     */
    public CodeCouponReceiveRecordPo buildCodeCouponReceiveRecordPo(SyncXiguaMarketCouponReceiveRecordRequest request) throws Exception {
        if (request == null) {
            return null;
        }

        CodeCouponReceiveRecordPo po = new CodeCouponReceiveRecordPo();
        // 加密code
        po.setCouponCode(AesUtil.encrypt(AES_PASSWORD, request.getVoucherCode()));
        // 索引：使用md5对couponCode加密
        po.setCouponIndex(org.springframework.util.DigestUtils.md5DigestAsHex(request.getVoucherCode().getBytes()));
        // 券批次id
        po.setTypeId(Integer.valueOf(request.getTemplateCode()));
        // 密用户手机号 AES加密
        String mobileNo = Optional.ofNullable(request.getMobileNo()).orElse(StringUtils.EMPTY);
        if (StringUtils.isNotBlank(mobileNo)) {
            mobileNo = AesUtil.encrypt(AES_PASSWORD, mobileNo);
        }
        po.setMobileNo(mobileNo);
        // 外部用户id
        po.setExternalUserId(Optional.ofNullable(request.getUserId()).orElse(StringUtils.EMPTY));
        // 投放方式：西瓜投放
        po.setAssignType(AssignTypeEnum.XIGUA_MARKET.getCode());
        // 券领取时间
        po.setAssignTime(Math.toIntExact(DateUtil.parse(request.getGrantTime()).getTime() / 1000));
        // 领取品牌
        po.setAssignMarket(Optional.ofNullable(request.getOrganizationName()).orElse(StringUtils.EMPTY));

        return po;
    }

    /**
     * 将CouponCodePO列表转换为CouponCodeInfo列表
     *
     * @param couponCodePoList CouponCodePO对象的列表
     * @return CouponCodeInfo对象的列表
     */
    public List<CouponCodeInfo> buildCouponCodeInfo(List<CouponCodePO> couponCodePoList) {
        List<CouponCodeInfo> couponCodeInfoList = Lists.newArrayList();
        for (CouponCodePO couponCodePO : couponCodePoList) {
            CouponCodeInfo couponCodeInfo = new CouponCodeInfo();
            couponCodeInfo.setId(couponCodePO.getId());
            couponCodeInfo.setCouponCode(couponCodePO.getCouponCode());
            couponCodeInfo.setCouponIndex(couponCodePO.getCouponIndex());
            couponCodeInfo.setTypeId(couponCodePO.getTypeId());
            couponCodeInfo.setBatchId(couponCodePO.getBatchId());
            couponCodeInfo.setStat(couponCodePO.getStat());
            couponCodeInfo.setUserId(couponCodePO.getUserId());
            couponCodeInfo.setCouponId(couponCodePO.getCouponId());
            couponCodeInfo.setUseMode(couponCodePO.getUseMode());
            couponCodeInfo.setUseTime(couponCodePO.getUseTime());
            couponCodeInfo.setOrderId(couponCodePO.getOrderId());
            couponCodeInfo.setReplaceMoney(couponCodePO.getReplaceMoney());
            couponCodeInfo.setReduceExpress(couponCodePO.getReduceExpress());
            couponCodeInfo.setSendType(couponCodePO.getSendType());
            couponCodeInfo.setOffline(couponCodePO.getOffline());
            couponCodeInfo.setAddTime(couponCodePO.getAddTime());
            couponCodeInfo.setFromOrderId(couponCodePO.getFromOrderId());

            couponCodeInfoList.add(couponCodeInfo);
        }

        return couponCodeInfoList;
    }

}
