package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigGoodsCachePo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigInfoCachePo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.NewConfigGoodsCachePo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;
import java.util.Map;

/**
 * 新缓存
 *
 * <AUTHOR>
 */
public interface CouponConfigRedisDao {

    /**
     * 缓存券信息
     */
    void setConfigInfoCache(ConfigInfoCachePo configInfoCachePo, long expireTime);


    /**
     * 缓存商品信息
     */
    void setCouponGoodsCache(ConfigGoodsCachePo couponGoodsCachePo, long expireTime);


    /**
     * 批量获取券发放总量
     */
    Map<Long,Long> batchGetCouponSendCount(List<Long> configIds) throws BizError;

    /**
     * 获取单个券信息
     * @param configId
     * @return
     */
    ConfigInfoCachePo getConfigInfoCache(Long configId);

    /**
     * 缓存商品信息(去除pid版本)
     */
    void setCouponGoodsCacheV2(NewConfigGoodsCachePo couponGoodsCachePo, long expireTime);

    /**
     * 批量获取券信息
     * @param configIds
     * @return
     */
    Map<Long, ConfigInfoCachePo> batchGetConfigInfoCache(List<Long> configIds) throws BizError;

}

