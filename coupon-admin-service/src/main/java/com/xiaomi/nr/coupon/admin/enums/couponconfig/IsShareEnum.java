package com.xiaomi.nr.coupon.admin.enums.couponconfig;

/**
 * 是否可分享 枚举
 *
 * <AUTHOR>
 */
public enum IsShareEnum {

    /**
     * 是
     */
    Yes(true, "1", "是"),

    /**
     * 否
     */
    No(false, "2", "否");

    private final Boolean redisValue;
    private final String mysqlValue;
    private final String name;

    IsShareEnum(Boolean redisValue, String mysqlValue, String name) {
        this.redisValue = redisValue;
        this.mysqlValue = mysqlValue;
        this.name = name;
    }

    public Boolean getRedisValue() {
        return this.redisValue;
    }

    public String getMysqlValue() {
        return this.mysqlValue;
    }

    public String getName() {
        return name;
    }

    public static String findNameByRedisValue(Boolean value) {
        IsShareEnum[] values = IsShareEnum.values();
        for (IsShareEnum item : values) {
            if (item.getRedisValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }

    public static Boolean findRedisValueByMysqlValue(String value) {
        IsShareEnum[] values = IsShareEnum.values();
        for (IsShareEnum item : values) {
            if (item.getMysqlValue().equals(value)) {
                return item.getRedisValue();
            }
        }
        return null;
    }
}

