package com.xiaomi.nr.coupon.admin.domain.coupon.couponactivity.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 领券活动接口返回信息
 *
 * <AUTHOR>
 */
@Data
public class EventApiResponse implements Serializable {

    private static final long serialVersionUID = 3100004047111138445L;

    /**
     * 返回码
     */
    @SerializedName("code")
    private Integer code;

    /**
     * 描述
     */
    @SerializedName("msg")
    private String msg;

    /**
     * 具体的数据
     */
    @SerializedName("data")
    private List<EventApiDataItem> data;
}
