package com.xiaomi.nr.coupon.admin.enums.goods;

import lombok.Getter;

@Getter
public enum GoodsDepartmentEnum{

    SALE_ONE(1, "销售运营一部"),
    SALE_TWO(2, "销售运营二部"),
    SALE_THREE(3, "销售运营三部"),
    INSURANCE(4, "保险产品线"),
    INCREMENT(5, "增值服务产品线"),
    GIFT(6, "赠品产品线"),
    RI_CHANG_YUAN_SU(7, "日常元素产品线"),
    CAR_SALES_OPERATION(8, "汽车销售运营部"),

    ;


    private final int value;
    private final String name;

    GoodsDepartmentEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static GoodsDepartmentEnum getByValue(int value) {
        GoodsDepartmentEnum[] values = GoodsDepartmentEnum.values();
        for (GoodsDepartmentEnum item : values) {
            if (item.getValue()==value) {
                return item;
            }
        }
        return null;
    }



}
