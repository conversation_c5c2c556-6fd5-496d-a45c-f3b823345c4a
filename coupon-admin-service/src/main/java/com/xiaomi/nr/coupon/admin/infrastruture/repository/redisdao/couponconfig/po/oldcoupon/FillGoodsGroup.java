package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.oldcoupon;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 包含商品
 *
 * <AUTHOR>
 * @date 2021/5/14
 */
@Data
public class FillGoodsGroup implements Serializable {
    private static final long serialVersionUID = 6503881396977744832L;

    /**
     * 包含商品
     */
    @SerializedName("join_goods")
    private CompareItem joinGoods;

    /**
     * 需要满足的条件
     */
    private QuotaEle quota;
}
