package com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批次上下线
 *
 * <AUTHOR>
 * @date 2023/12/8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PointBatchConfigUpdateStatusEvent extends PointBaseEvent<CarPointsBatchConfigPo> {

    /**
     * 修改前po
     */
    private CarPointsBatchConfigPo oldPo;

    /**
     * 操作人邮箱前缀
     */
    private String operator;

}
