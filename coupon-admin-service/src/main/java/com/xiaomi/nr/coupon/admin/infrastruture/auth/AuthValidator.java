package com.xiaomi.nr.coupon.admin.infrastruture.auth;

import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.localcache.LocalCacheCommon;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.auth.po.AppAuthInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 权限认证
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AuthValidator {

    private static LocalCacheCommon localCacheStatic;

    @Autowired
    private LocalCacheCommon localCache;

    @Autowired
    private void set() {
        localCacheStatic = localCache;
    }

    /**
     * 鉴权
     *
     * @param serviceName 接口名
     * @param methodName  方法名
     * @param params      所有参数
     * @param paramNames  有参数名
     */
    public static void auth(String serviceName, String methodName, Map<String, String> params, List<String> paramNames) {
        //参数空检查
        if (params.isEmpty() || paramNames.isEmpty()) {
            log.error("未传任何参数, serviceName={}, methodName={}, params={}", serviceName, methodName, params);
            throw new BaseException(-1, "未传任何参数，请核实");
        }

        String appId = params.get("appId");

        if (serviceName == null || "".equals(serviceName)) {
            log.error("serviceName为空, appId={}, serviceName={}, methodName={}, params={}", appId, serviceName, methodName, params);
            throw new BaseException(-1, "参数serviceName为空，请核实");
        }

        if (methodName == null || "".equals(methodName)) {
            log.error("methodName为空, appId={}, serviceName={}, methodName={}, params={}", appId, serviceName, methodName, params);
            throw new BaseException(-1, "参数methodName为空，请核实");
        }

        //不需要验证token和appId的服务，背景是权限上线前接入的服务没传这些字段
        if (isNotCheckAuthService(serviceName,methodName)) {
            return;
        }

        if (appId == null || "".equals(appId)) {
            log.info("参数appId为空, appId={}, serviceName={}, methodName={}, params={}", appId, serviceName, methodName, params);
            throw new BaseException(-1, "参数appId为空，请核实");
        }

        //有没有访问权限
        /* 在没有后台配置权限之前，这里就不进行验证接口权限了
        if (!isAccessRights(appId, serviceName, methodName)) {
            log.info("没有此接口的访问权限, appId={}, serviceName={}, methodName={}, params={}", appId, serviceName, methodName, params);
            throw new BaseException(-1, "没有访问权限，请核实");
        }
        */

        //密钥
        String secret = getSecret(appId);
        if (secret == null) {
            log.info("没有权限, appId={}, serviceName={}, methodName={}, params={}", appId, serviceName, methodName, params);
            throw new BaseException(-1, "并没有访问权限，请核实");
        }

        //token
        String token = params.get("token");
        if (token == null || "".equals(token)) {
            log.info("参数token为空, appId={}, serviceName={}, methodName={}, params={}", appId, serviceName, methodName, params);
            throw new BaseException(-1, "参数token为空，请核实");
        }

        String md5Token = genToken(appId, secret, params, paramNames, false);

        //校验token
        if (!md5Token.equals(token)) {
            if(!StringUtils.equals(token, genToken(appId, secret, params, paramNames, true))){
                log.info("token验证失败, appId={}, serviceName={}, methodName={}, params={}", appId, serviceName, methodName, params);
                throw new BaseException(-1, "token验证失败，请核实参数");
            }
        }
    }

    /**
     * 是否为不需要验证token和appId的服务方法
     * 背景是权限上线前接入的服务没传这些字段
     *
     * @param serviceName String
     * @return Boolean
     */
    private static Boolean isNotCheckAuthService(String serviceName, String methodName) {
        if (serviceName == null || methodName == null) {
            return false;
        }
        List<String> list = AppConfigs.getNotCheckAuthServices();
        if (list.isEmpty()) {
            return false;
        }
        return list.contains(serviceName+"."+methodName);
    }

    /**
     * 返回appId的secret
     *
     * @param appId String
     * @return String
     */
    private static String getSecret(String appId) {
        if (appId == null || "".equals(appId)) {
            return null;
        }

        AppAuthInfo appAuthInfo = localCacheStatic.getSingleAppAuth(appId);
        if(Objects.isNull(appAuthInfo)){
            return null;
        }
        /*Map<String, AppAuthInfo> configMap = localCacheStatic.getAppAuth();
        if(configMap.containsKey(appId)){
            return configMap.get(appId).getSecret();
        }*/

        return appAuthInfo.getSecret();
    }

    /**
     * 方法是否有访问权限
     *
     * @param appId       String
     * @param serviceName String
     * @param methodName  String
     * @return boolean
     */
    private static boolean isAccessRights(String appId, String serviceName, String methodName) {
        if (appId == null || "".equals(appId)) {
            return false;
        }

        Map<String, Map<String, List<String>>> permissions = AppConfigs.getPermissions();

        if (permissions == null) {
            return false;
        }

        if (permissions.get(appId) == null || permissions.get(appId).isEmpty()) {
            return false;
        }

        if (permissions.get(appId).get(serviceName) == null || permissions.get(appId).get(serviceName).isEmpty()) {
            return false;
        }

        return permissions.get(appId).get(serviceName).contains(methodName);
    }

    /**
     * 生成token
     *
     * @param appId      String
     * @param secret     String
     * @param params     Map<String, String>
     * @param paramNames List<String>
     * @param checkFlag  重复校验标记位
     * @return String
     */
    private static String genToken(String appId, String secret, Map<String, String> params, List<String> paramNames, boolean checkFlag) {
        //按首字母排序
        Collections.sort(paramNames);

        //拼接生成所有token的字符串
        StringBuilder str = new StringBuilder();
        str.append("appId").append("=").append(appId).append("&");

        for (String key : paramNames) {
            if ("appId".equals(key) || "token".equals(key)) {
                continue;
            }

            String value = params.get(key);
            if(checkFlag && (StringUtils.isEmpty(value) || StringUtils.equals("null", value))){
                continue;
            }
            str.append(key).append("=").append(value).append("&");
        }

        log.info("auth.token, appId={}, str={}secret={}***", appId, str, secret.substring(0,8));

        str.append("secret=").append(secret);

        //md5加密生成验证token
        return DigestUtils.md5Hex(str.toString());
    }
}