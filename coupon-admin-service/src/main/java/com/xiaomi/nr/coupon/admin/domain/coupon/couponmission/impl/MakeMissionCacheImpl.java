package com.xiaomi.nr.coupon.admin.domain.coupon.couponmission.impl;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponmission.MakeMissionCache;
import com.xiaomi.nr.coupon.admin.enums.couponmission.MissionTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponmission.MissionVersionEnum;
import com.xiaomi.nr.coupon.admin.enums.couponmission.TimeTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.coupon.MissionConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.nacosconfig.NacosSwitchConfig;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.CouponConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.mission.CouponMissionMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponmission.MissionMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponmission.po.MissionPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.CouponConfigOldRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigCacheItemPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponmission.MissionCacheRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponmission.po.MissionCacheItemPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponmission.po.MissionMapType;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 构建券发放任务缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MakeMissionCacheImpl implements MakeMissionCache {

    @Autowired
    private MissionMapper missionMapper;

    @Autowired
    private CouponMissionMapper couponMissionMapper;

    @Autowired
    private MissionCacheRedisDao missionCacheRedisDao;

    @Autowired
    private CouponConfigOldRedisDao couponConfigOldRedisDao;

    @Autowired
    private CouponConfigMapper couponConfigMapper;

    @Autowired
    private NacosSwitchConfig nacosSwitchConfig;

    /**
     * 构建券发放任务缓存(定时任务跑)
     */
    @Override
    public void runMissionCache() {

        long startTime = TimeUtil.getNowUnixMillis();
        long splitTime = TimeUtil.getNowUnixSecond() - 12 * 3600;
        try {

            if(nacosSwitchConfig.isRunWriteMissionData()) {
                //获取原始券发放任务数据源
                List<MissionPo> missionPoList = getMissionPoList(splitTime);
                log.info("task.couponMission.cache, 券发放任务基础数据获取完成 runTime={}", TimeUtil.getNowUnixMillis() - startTime);

                //取出券配置ID列表
                List<Long> configIdList = getConfigIdList(missionPoList).stream().distinct().collect(Collectors.toList());
                log.info("task.couponMission.cache, 券配置id列表获取完成 runTime={}", TimeUtil.getNowUnixMillis() - startTime);

                //批量从redis取券配置ID关联的券配置信息
                List<ConfigCacheItemPo> configCacheList = getCouponConfigById(configIdList);
                log.info("task.couponMission.cache, 批量从redis获取券配置信息完成 runTime={}", TimeUtil.getNowUnixMillis() - startTime);

                //构建发放任务缓存
                List<MissionCacheItemPo> missionCacheList = makeMissionCacheList(missionPoList, configCacheList);
                log.info("task.couponMission.cache, 构建券发放任务信息完成 runTime={}", TimeUtil.getNowUnixMillis() - startTime);

                //发放任务redis
                writeMissionCache(missionCacheList);
                log.info("task.couponMission.cache, 券发放任务信息写入redis完成 runTime={}", TimeUtil.getNowUnixMillis() - startTime);

                //构建券发放任务id列表
                List<MissionMapType> missionMapTypeList = getValidMissionIdList(missionCacheList);
                log.info("task.couponMission.cache, 构建券发放任务id列表完成 runTime={}", TimeUtil.getNowUnixMillis() - startTime);

                //发放任务列表redis
                writeMissionIds(missionMapTypeList);
                log.info("task.couponMission.cache, 券发放任务列表写入redis完成 runTime={}", TimeUtil.getNowUnixMillis() - startTime);
            }
            writeMissionCacheV2();

        } catch (BizError e) {
            log.warn("task.couponMission.cache, BizError, errMessage={}",e.getMessage(), e);
        } catch (Exception e) {
            log.warn("task.couponMission.cache, Exception", e);
        }
    }

    /**
     * 乾坤后台建券补偿任务刷入
     */
    private void writeMissionCacheV2() {
        long startTime = TimeUtil.getNowUnixMillis();
        long splitTime = TimeUtil.getNowUnixSecond() - 3600;
        List<Long> configIds = couponConfigMapper.getValidConfigIdByUseTime(splitTime);
        if (CollectionUtils.isEmpty(configIds)) {
            return;
        }
        int count = 0;
        int loopTime = getSplitCount(configIds.size(), 50);
        for (int i = 0; i < loopTime; i++) {
            List<MissionCacheItemPo> missionCacheList = null;
            List<Long> typeIds = null;
            try {
                List<CouponConfigPO> couponConfigPOs = couponConfigMapper.getConfigByIds(configIds.subList(i * 50, Math.min((i + 1) * 50, configIds.size())));
                typeIds = couponConfigPOs.stream().map(CouponConfigPO::getId).collect(Collectors.toList());
                List<MissionPo> missionPos = couponMissionMapper.getMissionByConfigIds(typeIds);
                //构建发放任务缓存
                missionCacheList = makeMissionCacheListV2(missionPos, couponConfigPOs);
                //发放任务redis
                writeMissionCache(missionCacheList);
                //构建券发放任务id列表
                List<MissionMapType> missionMapTypeList = getValidMissionIdList(missionCacheList);
                //发放任务列表redis
                missionCacheRedisDao.setMissionIdListV2(missionMapTypeList);

            } catch (Exception e) {
                log.error("task.couponMission.cache, 构建优惠券发放任务失败, typeIds:{}", typeIds, e);
            }

            count += missionCacheList.size();
        }
        log.info("task.couponMission.cache, 优惠券发放任务写入talos完成, listSize={}, runTime={}毫秒", count, TimeUtil.sinceMillis(startTime));
    }

    private int getSplitCount(int totalSize, int pageSize) {
        return totalSize / pageSize + (totalSize % pageSize == 0 ? 0 : 1);
    }

    /**
     * 从数据库中取出满足条件的券发放任务信息
     *
     * @param splitTime 当前时间向前推12个小时的时间戳
     * @return List<>   券发放任务信息列表
     */
    private List<MissionPo> getMissionPoList(long splitTime) {

        //分页获取数据
        int startsize = 0;
        int pageSize = 1000;
        List<MissionPo> initMissionPoList = new ArrayList<>();

        while (true) {
            List<MissionPo> list = missionMapper.getMissionAll(splitTime, startsize, pageSize);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }

            initMissionPoList.addAll(list);
            if (list.size() < pageSize) {
                break;
            }
            startsize += pageSize;
        }

        if (CollectionUtils.isEmpty(initMissionPoList)) {
            log.info("getMissionPoList,未获取到符合条件的券发放任务数据源");
            return Collections.emptyList();
        }

        //过滤后得到状态为history、approved、send状态的发放任务
        return getValidMission(initMissionPoList);
    }


    /**
     * 获取券发放任务列表中的券配置id列表
     *
     * @param missionPoList 券发放任务信息列表
     * @return List<Long>   券配置ID
     */
    private List<Long> getConfigIdList(List<MissionPo> missionPoList) {
        return missionPoList.stream().map(MissionPo::getTypeId).collect(Collectors.toList());
    }


    /**
     * 根据券配置id获取券配置缓存信息
     *
     * @param configIds 券配置ID列表
     * @return List<>   券配置缓存信息列表
     */
    private List<ConfigCacheItemPo> getCouponConfigById(List<Long> configIds) throws BizError {
        //获取券发放任务对应的券配置缓存信息
        List<ConfigCacheItemPo> configCacheList = couponConfigOldRedisDao.get(configIds);
        if (CollectionUtils.isEmpty(configCacheList)) {
            log.warn("getCouponConfigById,未从缓存中根据券配置id获取到有效的券配置缓存信息,configIdList={}", configIds);
            throw ExceptionHelper.create(ErrCode.COUPON, "根据券配置ID,获取券配置缓存信息失败");
        }
        return configCacheList;
    }


    /**
     * 生成券任务缓存信息列表
     *
     * @param missionPoList   券发放任务信息列表(DB)
     * @param configCacheList 券配置信息列表(缓存)
     * @return List<>         券发放任务缓存信息列表(缓存)
     */
    private List<MissionCacheItemPo> makeMissionCacheList(List<MissionPo> missionPoList, List<ConfigCacheItemPo> configCacheList) {

        Map<Long, List<MissionPo>> missionMap = getMissionMap(missionPoList);
        List<MissionCacheItemPo> missionCacheList = new ArrayList<>();

        for (ConfigCacheItemPo configCache : configCacheList) {
            long couponTypeId = configCache.getId();
            if (!missionMap.containsKey(couponTypeId)) {
                continue;
            }

            List<MissionPo> missionList = missionMap.get(couponTypeId);
            long globalCouponEndTime = configCache.getGlobalEndTime();

            for (MissionPo missionPo : missionList) {
                long missionEndTime = missionPo.getCouponEndTime();

                if (missionPo.getCouponDays() > 0) {

                    missionEndTime = TimeUtil.getNowUnixSecond() + missionPo.getCouponDays() * 24 * 3600;
                }

                if (missionEndTime > globalCouponEndTime) {
                    log.info("makeMissionCacheList, 发放任务的有效期不在券配置最大有效期内, couponConfigId={}, missionId={}",couponTypeId,missionPo.getId());
                    continue;
                }

                //初始化券发放任务缓存信息
                missionCacheList.add(makeMissionCache(missionPo, MissionVersionEnum.OLD_MISSION.getCode()));
            }
        }

        return missionCacheList;
    }

    /**
     * 生成券任务缓存信息列表
     *
     * @param missionPoList   券发放任务信息列表(DB)
     * @param couponConfigPOS 券配置信息列表(DB)
     * @return List<>         券发放任务缓存信息列表(缓存)
     */
    private List<MissionCacheItemPo> makeMissionCacheListV2(List<MissionPo> missionPoList, List<CouponConfigPO> couponConfigPOS) {

        Map<Long, List<MissionPo>> missionMap = getMissionMap(missionPoList);
        List<MissionCacheItemPo> missionCacheList = new ArrayList<>();

        for (CouponConfigPO couponConfigPO : couponConfigPOS) {
            long couponTypeId = couponConfigPO.getId();
            if (!missionMap.containsKey(couponTypeId)) {
                continue;
            }
            long globalCouponEndTime = Math.max(couponConfigPO.getEndFetchTime() + couponConfigPO.getUseDuration() * 3600, couponConfigPO.getEndUseTime());
            List<MissionPo> missionList = missionMap.get(couponTypeId);
            for (MissionPo missionPo : missionList) {
                long missionEndTime = missionPo.getCouponEndTime();
                if (missionPo.getCouponDays() > 0) {
                    missionEndTime = TimeUtil.getNowUnixSecond() + missionPo.getCouponDays() * 3600;
                }

                if (missionEndTime > globalCouponEndTime) {
                    log.info("makeMissionCacheList, 发放任务的有效期不在券配置最大有效期内, couponConfigId={}, missionId={}",couponTypeId,missionPo.getId());
                    continue;
                }

                //初始化券发放任务缓存信息
                missionCacheList.add(makeMissionCache(missionPo, MissionVersionEnum.NEW_MISSION.getCode()));
            }
        }

        return missionCacheList;
    }


    /**
     * 构建券配置id和券发放任务映射
     *
     * @param missionPoList 券发放任务列表
     * @return Map<>        券配置id和券发放任务的对应关系列表
     */
    private Map<Long, List<MissionPo>> getMissionMap(List<MissionPo> missionPoList) {


        Map<Long, List<MissionPo>> resultMap = new HashMap<>();
        for (MissionPo missionPo : missionPoList) {
            long configId = missionPo.getTypeId();
            if (resultMap.containsKey(configId)) {
                resultMap.get(configId).add(missionPo);
                continue;
            }
            List<MissionPo> list = new LinkedList<>();
            list.add(missionPo);
            resultMap.put(configId, list);
        }

        return resultMap;
    }


    /**
     * 构建券发放任务id列表
     *
     * @param missionCacheList 券发放任务缓存列表
     * @return List<>          任务ID、券配置ID间映射关系
     */
    private List<MissionMapType> getValidMissionIdList(List<MissionCacheItemPo> missionCacheList) {
        List<MissionMapType> missionMapTypeList = new ArrayList<>();
        for (MissionCacheItemPo missionCache : missionCacheList) {
            missionMapTypeList.add(new MissionMapType(missionCache.getId(), missionCache.getCouponConfigId()));
        }

        return missionMapTypeList;
    }


    /**
     * 过滤出有效的券发放任务列表(history、approved、send)
     *
     * @param missionPoList 初始券发放任务信息列表(DB)
     * @return List<>       有效的券发放任务信息列表
     */
    private List<MissionPo> getValidMission(List<MissionPo> missionPoList) {
        List<MissionPo> resultMissionPoList = new ArrayList<>();

        //获取状态为history、approved、send状态的券发放任务
        for (MissionPo missionPo : missionPoList) {
            switch (missionPo.getStat()) {
                case MissionConstant.HISTORY:
                case MissionConstant.APPROVED:
                case MissionConstant.SEND:
                    resultMissionPoList.add(missionPo);
                    break;
                default:
                    break;
            }
        }
        return resultMissionPoList;
    }


    /**
     * 将构建的发券任务信息写入缓存
     *
     * @param cacheList 券发放任务缓存信息列表
     */
    private void writeMissionCache(List<MissionCacheItemPo> cacheList) {
        missionCacheRedisDao.setMissionDesc(cacheList);
    }


    /**
     * 将构建的发券任务id列表写入缓存
     *
     * @param missionMapTypeList 券发放任务ID、券配置ID间映射关系列表
     */
    private void writeMissionIds(List<MissionMapType> missionMapTypeList) {
        missionCacheRedisDao.setMissionIdList(missionMapTypeList);
        missionCacheRedisDao.setMissionIdListV2(missionMapTypeList);
    }


    /**
     * 生成单个发放任务信息缓存 po -> missionCache
     *
     * @param missionPo           券发放任务初始信息(DB)
     * @return MissionCacheItemPo 券发放任务缓存信息(redis)
     */
    private MissionCacheItemPo makeMissionCache(MissionPo missionPo, Integer version) {

        if (missionPo == null) {
            return new MissionCacheItemPo();
        }

        Integer days = missionPo.getCouponDays();
        String timeType = days == 0 ? TimeTypeEnum.SECTION.getRedisValue() : TimeTypeEnum.DAYS.getRedisValue();
        Long id = missionPo.getId();
        String name = missionPo.getName();
        String status = missionPo.getStat();
        String missionType = MissionTypeEnum.findRedisValueBymysqlValue(missionPo.getMissionType());
        Long couponStartTime = missionPo.getCouponStartTime();
        Long couponEndTime = missionPo.getCouponEndTime();
        Long sendStartTime = missionPo.getSendTime();
        Long sendEndTime = missionPo.getSendEndTime();
        Long maxSendNum = StringUtils.isEmpty(missionPo.getGroupIds()) ? missionPo.getMaxNum() : missionPo.getSendNum();
        Long groupSendNum = missionPo.getSendNum();
        List<String> groupIds = Lists.newArrayList();
        if(StringUtils.isNotEmpty(missionPo.getGroupIds())){
            groupIds = Arrays.asList(missionPo.getGroupIds().split(","));
        }
        String department = missionPo.getDepartment();
        String departmentId = "0";
        if(StringUtils.isNotBlank(department)) {
            departmentId = department.substring(0, department.indexOf(":"));
        }
        Long addTime = missionPo.getAddTime();
        Long approvedTime = missionPo.getApprovedTime();
        Long couponConfigId = missionPo.getTypeId();
        Long adminId = missionPo.getAdminId();
        String adminName = missionPo.getAdminName();
        Long cacheCreateTime = TimeUtil.getNowUnixSecond();


        MissionCacheItemPo missionCache = new MissionCacheItemPo();
        missionCache.setId(id);
        missionCache.setName(name);
        missionCache.setStatus(status);
        missionCache.setMissionType(missionType);
        missionCache.setTimeType(timeType);
        missionCache.setCouponStartTime(couponStartTime);
        missionCache.setCouponEndTime(couponEndTime);

        if(version.equals(MissionVersionEnum.NEW_MISSION.getCode())) {
            missionCache.setDays(days / 24);
            missionCache.setHours(days % 24);
        } else {
            missionCache.setDays(days);
            missionCache.setHours(0);
        }

        missionCache.setSendStartTime(sendStartTime);
        missionCache.setSendEndTime(sendEndTime);
        missionCache.setMaxSendNum(maxSendNum);
        missionCache.setGroupSendNum(groupSendNum);
        missionCache.setGroupIds(groupIds);
        missionCache.setDepartmentId(departmentId);
        missionCache.setAddTime(addTime);
        missionCache.setApprovedTime(approvedTime);
        missionCache.setCouponConfigId(couponConfigId);
        missionCache.setAdminId(adminId);
        missionCache.setAdminName(adminName);
        missionCache.setCacheCreateTime(cacheCreateTime);
        missionCache.setVersion(version);
        return missionCache;
    }
}
