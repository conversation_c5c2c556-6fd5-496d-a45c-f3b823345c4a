package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig;

import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.coupon.CouponConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.nacosconfig.NacosSwitchConfig;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig.po.*;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.goods.po.GoodsPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.CouponConfigOldRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigCacheGoods;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigCacheItemPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigCacheTargetGoodsItem;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigIdListCachePo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 构建券配置缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Deprecated
public class MakeConfigCache {

    @Resource
    private MakeCacheBaseData makeData;

    @Autowired
    private CouponConfigOldRedisDao couponConfigOldRedisDao;

    @Autowired
    private NacosSwitchConfig nacosSwitchConfig;

    /**
     * 构建券配置缓存-主方法
     */
    public void run(String timeStart) {
        long runAllStartTime = TimeUtil.getNowUnixMillis();
        try {

            if (nacosSwitchConfig.isStopWriteCouponData()) {
                return;
            }

            //获取-原始基础数据
            long runStartTime = TimeUtil.getNowUnixMillis();
            BaseData common = makeData.get(timeStart);
            log.info("task.couponConfig.cache, 获取券配置缓存基础数据完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));


            //构建-券配置信息缓存
            runStartTime = TimeUtil.getNowUnixMillis();
            List<ConfigCacheItemPo> configCaches = makeCache(common);
            log.info("task.couponConfig.cache, 构建券配置信息缓存完成, size={}, cacheSize={}, runTime={}毫秒", common.getConfigList().size(), configCaches.size(), TimeUtil.sinceMillis(runStartTime));


            //构建-有效无码券配置ID列表
            runStartTime = TimeUtil.getNowUnixMillis();
            ConfigIdListCachePo validConfigIds = getValidNoCodeConfigIds(configCaches);
            log.info("task.couponConfig.cache, 构建有效无码券配置ID列表完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));

            //写redis-券配置信息
            runStartTime = TimeUtil.getNowUnixMillis();
            couponConfigOldRedisDao.set(configCaches);
            log.info("task.couponConfig.cache, 券配置信息写redis完成, size={}, cacheSize={}, runTime={}毫秒", common.getConfigList().size(), configCaches.size(), TimeUtil.sinceMillis(runStartTime));


            //写redis-有效券配置ID列表
            runStartTime = TimeUtil.getNowUnixMillis();
            couponConfigOldRedisDao.setValidNoCodeConfigIdList(validConfigIds);
            log.info("task.couponConfig.cache, 有效券配置ID列表写redis完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));


            log.info("task.couponConfig.cache, 生成所有券缓存的总计耗时, size={}, cacheSize={}, runTime={}毫秒", common.getConfigList().size(), configCaches.size(), TimeUtil.sinceMillis(runAllStartTime));
        } catch (Exception e) {
            log.error("task.couponConfig.cache, 生成所有券缓存过程中遇到异常退出，configId={}, runTime={}毫秒", TimeUtil.sinceMillis(runAllStartTime), e);
        } catch (Throwable e) {
            log.error("task.couponConfig.cache, 生成所有券缓存过程中遇到错误退出，configId={}, runTime={}毫秒", TimeUtil.sinceMillis(runAllStartTime), e);
        }
    }

    /**
     * 获取有效无码券配置ID
     *
     * @param list List<ConfigCacheItemPo>
     * @return ConfigIdListCachePo
     */
    private ConfigIdListCachePo getValidNoCodeConfigIds(List<ConfigCacheItemPo> list) {
        ConfigIdListCachePo result = new ConfigIdListCachePo();
        long nowTime = TimeUtil.getNowUnixSecond();
        List<Long> ids = new ArrayList<>();
        for (ConfigCacheItemPo item : list) {
            if (!ModeTypeEnum.NoCode.getRedisValue().equals(item.getModeType())) {
                continue;
            }
            if (item.getGlobalEndTime() > nowTime && StatusEnum.Approved.getRedisValue().equals(item.getStatus())) {
                ids.add(item.getId());
            }
        }
        result.setConfigIds(ids);
        result.setCacheCreateTime(TimeUtil.getNowDateTime());
        return result;
    }

    /**
     * 构建券配置信息缓存
     */
    private List<ConfigCacheItemPo> makeCache(BaseData common) {
        List<ConfigCacheItemPo> result = new ArrayList<>();

        if (common.getConfigList() == null || common.getConfigList().isEmpty()) {
            log.info("task.couponConfig.cache, 券配置基础信息列表为空，不需要进行构建下一步的构建");
            return result;
        }

        for (CouponConfigPo config : common.getConfigList()) {
            long runStartTime = TimeUtil.getNowUnixMillis();
            try {
                //券配置对应sku或套装列表
                ConfigCacheItemPo item = makeSingleCache(common, config);
                if (item.getId() <= 0) {
                    log.info("task.couponConfig.cache, 单个券配置缓存构建失败，ID必须为正整数, configId={}, runTime={}毫秒", item.getId(), TimeUtil.sinceMillis(runStartTime));
                    continue;
                }
                result.add(item);
            } catch (BaseException e) {
                log.error("task.couponConfig.cache, 单个券配置缓存构建失败，configId={}, runTime={}毫秒, err={}", config.getId(), TimeUtil.sinceMillis(runStartTime), e.getMessage());
            } catch (Exception e) {
                log.error("task.couponConfig.cache, 单个券配置缓存构建异常，configId={}, runTime={}毫秒", config.getId(), TimeUtil.sinceMillis(runStartTime), e);
            } catch (Throwable e) {
                log.error("task.couponConfig.cache, 单个券配置缓存构建出错，configId={}, runTime={}毫秒", config.getId(), TimeUtil.sinceMillis(runStartTime), e);
            }
        }
        return result;
    }

    /**
     * 构建单个券配置缓存
     *
     * @return ConfigCacheItemPo
     */
    private ConfigCacheItemPo makeSingleCache(BaseData common, CouponConfigPo config) {
        String modeType = convertModeType(config.getIsCode());
        String useType = convertUseType(config.getType());
        Long globalStartTime = convertGlobalStartTime(config.getGlobalStartTime());
        Long globalEndTime = convertGlobalEndTime(config.getGlobalEndTime());
        if (globalStartTime >= globalEndTime) {
            throw new BaseException(-1, String.format("非法的券配置，开始时间必须要小于结束时间，startTime=%s, endTime=%s", globalStartTime, globalEndTime));
        }

        ExtProp extProp = parseExtProp(config.getExtProp());
        List<QuotaItem> quotas = parseQuota(config.getQuota());

        String quotaType = convertQuotaType(quotas);
        Long quotaCount = 0L;
        Long quotaMoney = 0L;

        if (QuotaTypeEnum.Money.getRedisValue().equals(quotaType) || QuotaTypeEnum.EveMoney.getRedisValue().equals(quotaType)) {
            quotaMoney = convertQuotaMoney(quotas);
        } else if (QuotaTypeEnum.Count.getRedisValue().equals(quotaType) || QuotaTypeEnum.EveCount.getRedisValue().equals(quotaType)) {
            quotaCount = convertQuotaCount(quotas);
        } else if (QuotaTypeEnum.MoneyCount.getRedisValue().equals(quotaType)) {
            quotaMoney = convertQuotaMoney(quotas);
            quotaCount = convertQuotaCount(quotas);
        } else {
            throw new BaseException(-1, String.format("非法的券配置，券的配额类型超出已知范围，quotaType=%s, quotas=%s", quotaType, config.getQuota()));
        }

        if (quotaMoney <= 0L && quotaCount <= 0L) {
            throw new BaseException(-1, String.format("非法的券配置，满件配额与满件配额不能同时为空，quotaMoney=%s, quotaCount=%s", quotaMoney, quotaCount));
        }

        List<PolicyItem> policy = parsePolicy(config.getPolicy());
        if (policy.get(0) == null || policy.get(0).getRule() == null) {
            throw new BaseException(-1, String.format("非法的券配置，政策里存在空记录，policy=%s", config.getPolicy()));
        }

        Long reduceMoney = 0L;
        Long reduceDiscount = 0L;
        Long reduceMaxPrice = 0L;
        List<ConfigCacheTargetGoodsItem> deductTargetGoods = new ArrayList<>();

        if (UseTypeEnum.Cash.getRedisValue().equals(useType)) {
            reduceMoney = convertReduceMoney(policy.get(0).getRule());
        } else if (UseTypeEnum.Discount.getRedisValue().equals(useType)) {
            reduceDiscount = convertReduceDiscount(policy.get(0).getRule());
            reduceMaxPrice = convertReduceMaxPrice(policy.get(0).getSuffix());
        } else if (UseTypeEnum.Deduction.getRedisValue().equals(useType)) {
            deductTargetGoods = convertDeductTargetGoods(common, config.getId(), policy.get(0).getRule().getTargetGoods());
        } else {
            throw new BaseException(-1, String.format("非法的券配置，券的使用类型超出已知范围，userType=%s, quotas=%s", useType, config.getPolicy()));
        }

        ConfigCacheItemPo result = new ConfigCacheItemPo();
        result.setId(convertId(config.getId()));
        result.setName(convertName(config.getName()));
        result.setGlobalStartTime(globalStartTime);
        result.setGlobalEndTime(globalEndTime);
        result.setStatus(convertStatus(config.getStat()));
        result.setModeType(modeType);
        result.setUseType(useType);
        result.setIsShare(convertIsShare(extProp.getIsShare()));
        result.setIsPostFree(convertIsPostFree(extProp.getIsPostFree()));
        result.setIsCheckPrice(convertIsCheckPrice(config.getCheckPrice()));
        result.setIsCheckPackage(convertIsCheckPackage(config.getCheckPackage()));
        result.setRangeDesc(convertRangeDesc(config.getRangeDesc()));
        result.setSendChannel(config.getSendChannel());
        result.setAddTime(convertAddTime(config.getAddTime()));
        result.setModifyTime(convertModifyTime(config.getModifyTime()));
        result.setQuotaType(quotaType);
        result.setQuotaCount(quotaCount);
        result.setQuotaMoney(quotaMoney);
        result.setReduceMoney(reduceMoney);
        result.setReduceDiscount(reduceDiscount);
        result.setReduceMaxPrice(reduceMaxPrice);
        result.setDeductTargetGoods(deductTargetGoods);
        result.setDeductType(convertDeductType(config.getDeductType()));
        result.setClients(convertClients(config.getClient()));
        result.setUseChannel(convertUseChannel(common, config.getUseChannel(), result.getClients()));
        result.setUseChannelType(convertUseChannelType(result.getUseChannel()));

        //构建可用商品列表
        result.setGoodsInclude(convertGoodsIncludeV2(common, config.getId(), config.getModifyTime(), config.getGoodsInclude()));
        result.setCacheCreateTime(TimeUtil.getNowDateTime());
        return result;
    }

    /**
     * convert use channel
     *
     * @param common BaseData
     * @param useChannel String
     * @param clientIds List<Long>
     * @return List<String>
     */
    private List<String> convertUseChannel(BaseData common, String useChannel, List<String> clientIds) {
        List<String> result = new ArrayList<>();

        if(useChannel != null && !useChannel.isEmpty()) {
            String[] list = StringUtils.split(useChannel, ",");
            if(list == null || list.length == 0) {
                return result;
            }
            result.addAll(Arrays.asList(list));
            return result;
        }

        //老券配置兼容处理：凡是发现原券配置里的client存在现使用渠道对应的client_id列表里，则代码选择了此使用渠道。产品的 @镜谕 确定的
        for(Map.Entry<String, UseChannelClientRelationDo> item : common.getUseChannelClientRelation().entrySet()) {
            if(item.getValue() == null) {
                throw new BaseException(-1, "使用渠道基本配置存在null的情况");
            }
            Set<Long> ids = item.getValue().getClientIds();
            for (String clientId : clientIds) {
                if(ids.contains(clientId)){
                    result.add(item.getValue().getChannel());
                    break;
                }
            }
        }
        return result;
    }


    private ConfigCacheGoods convertGoodsIncludeV2(BaseData common, Long configId, Long modifyTime, String goodsInclude) {
        ConfigCacheGoods result = new ConfigCacheGoods();
        result.setSku(new HashMap<>());
        result.setGoods(new HashMap<>());
        result.setPackages(new HashMap<>());
        result.setGroups(new HashMap<>());
        return result;
    }

    /**
     * convert
     *
     * @param common      BaseData
     * @param configId    Long
     * @param targetGoods List<TargetGoodsItem>
     * @return List<ConfigCacheTargetGoodsItem>
     */
    private List<ConfigCacheTargetGoodsItem> convertDeductTargetGoods(BaseData common, Long configId, List<TargetGoodsItem> targetGoods) {
        if (targetGoods == null || targetGoods.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，券的抵扣商品列表不能为空，targetGoods=%s", targetGoods));
        }

        List<ConfigCacheTargetGoodsItem> result = new ArrayList<>();

        for (TargetGoodsItem item : targetGoods) {
            //SKU
            if (GoodsLevelEnum.Sku.getValue().equals(item.getLevel())) {
                ConfigCacheTargetGoodsItem newItem = new ConfigCacheTargetGoodsItem();
                newItem.setId(item.getId());
                newItem.setLevel(item.getLevel());
                result.add(newItem);
                continue;
            }

            //货品
            if (GoodsLevelEnum.Goods.getValue().equals(item.getLevel())) {
                GoodsPo info = common.getGoodsInfo(item.getId());
                if (info == null) {
                    log.warn("task.couponConfig.cache, 券的抵扣商品列表里存在非上架的货品, configId={}, targetGoods.item={}", configId, item);
                    continue;
                }
                ConfigCacheTargetGoodsItem newItem = new ConfigCacheTargetGoodsItem();
                newItem.setId(info.getSku());
                newItem.setLevel(GoodsLevelEnum.Sku.getValue());
                result.add(newItem);
                continue;
            }

            //商品
            if (GoodsLevelEnum.Commodity.getValue().equals(item.getLevel())) {
                List<GoodsPo> infos = common.getGoodsInfoByCommodityId(item.getId());
                if (infos == null) {
                    log.warn("task.couponConfig.cache, 券的抵扣商品列表里存在非上架的商品, configId={}, targetGoods.item={}", configId, item);
                    continue;
                }
                for (GoodsPo info : infos) {
                    ConfigCacheTargetGoodsItem newItem = new ConfigCacheTargetGoodsItem();
                    newItem.setId(info.getSku());
                    newItem.setLevel(GoodsLevelEnum.Sku.getValue());
                    result.add(newItem);
                }
                continue;
            }

            //套装
            if (GoodsLevelEnum.Package.getValue().equals(item.getLevel())) {
                ConfigCacheTargetGoodsItem newItem = new ConfigCacheTargetGoodsItem();
                newItem.setId(item.getId());
                newItem.setLevel(GoodsLevelEnum.Package.getValue());
                result.add(newItem);
                continue;
            }

            log.warn("task.couponConfig.cache, 券的抵扣商品列表的商品品级超出已知范围, configId={}, targetGoods={}", configId, item);
        }
        return result;
    }

    /**
     * convert
     *
     * @param clients String
     * @return List<String>
     */
    private List<String> convertClients(String clients) {
        if (clients == null || clients.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，可用券的client列表不能为空，clients=%s", clients));
        }

        if(CouponConstant.DB_FIELD_ALL.equals(clients)) {
            return new ArrayList<>();
        }

        List<String> clientJson = GsonUtil.fromListJson(clients, String.class);
        if (clientJson == null || clientJson.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，可用券的client列表无法正常解析，clients=%s", clients));
        }
        return clientJson;
    }

    /**
     * convert
     *
     * @param modifyTime Long
     * @return Long
     */
    private Long convertModifyTime(Long modifyTime) {
        if (modifyTime == null || modifyTime <= 0) {
            throw new BaseException(-1, String.format("非法的券配置，变更时间不能为空，modifyTime=%s", modifyTime));
        }

        if (!modifyTime.equals(TimeUtil.parseDateTime(TimeUtil.formatSecond(modifyTime)))) {
            throw new BaseException(-1, String.format("非法的券配置，创建时间的格式不符合要求，modifyTime=%s", modifyTime));
        }
        return modifyTime;
    }

    /**
     * convert
     *
     * @param addTime Long
     * @return Long
     */
    private Long convertAddTime(Long addTime) {
        if (addTime == null || addTime <= 0) {
            throw new BaseException(-1, String.format("非法的券配置，创建时间不能为空，addTime=%s", addTime));
        }

        if (!addTime.equals(TimeUtil.parseDateTime(TimeUtil.formatSecond(addTime)))) {
            throw new BaseException(-1, String.format("非法的券配置，创建时间的格式不符合要求，addTime=%s", addTime));
        }
        return addTime;
    }

    /**
     * convert
     *
     * @param desc String
     * @return String
     */
    private String convertRangeDesc(String desc) {
        return desc == null ? "" : desc;
    }

    /**
     * convert
     *
     * @param useChannel List<String>
     * @return String
     */
    private String convertUseChannelType(List<String> useChannel) {
        if (useChannel == null || useChannel.isEmpty()) {
            return UseChannelType.OnlineOffline.getRedisValue();
        }

        //只有一种情况：线上可用　或　线下可用
        if(useChannel.size() == 1) {
            if(UseChannelEnum.MiShop.getValue().equals(useChannel.get(0))) {
                return UseChannelType.Online.getRedisValue();
            }else if(UseChannelEnum.MiHome.getValue().equals(useChannel.get(0))) {
                return UseChannelType.Offline.getRedisValue();
            }else if(UseChannelEnum.MiAuthorized.getValue().equals(useChannel.get(0))) {
                return UseChannelType.Offline.getRedisValue();
            }
        }

        //线上线下均可使用（注：如果以后线上或线下需要再分渠道组合的话，这个地方需要增加相应的逻辑处理）
        return UseChannelType.OnlineOffline.getRedisValue();
    }

    /**
     * convert
     *
     * @param isCheckPackage String
     * @return Boolean
     */
    private Boolean convertIsCheckPackage(String isCheckPackage) {
        if (isCheckPackage == null || isCheckPackage.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，是否套装部分商品可用券不能为空，isCheckPackage=%s", isCheckPackage));
        }

        Boolean val = IsCheckPackageEnum.findRedisValueByMysqlValue(isCheckPackage);
        if (val == null) {
            throw new BaseException(-1, String.format("非法的券配置，是否套装部分商品可用券超出已知范围，isCheckPackage=%s", isCheckPackage));
        }

        return val;
    }

    /**
     * convert
     *
     * @param isCheckPrice String
     * @return Boolean
     */
    private Boolean convertIsCheckPrice(String isCheckPrice) {
        if (isCheckPrice == null || isCheckPrice.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，是否调价商品可用不能为空，isCheckPrice=%s", isCheckPrice));
        }

        Boolean val = IsCheckPriceEnum.findRedisValueByMysqlValue(isCheckPrice);
        if (val == null) {
            throw new BaseException(-1, String.format("非法的券配置，是否调价商品可用超出已知范围，isCheckPrice=%s", isCheckPrice));
        }

        return val;
    }

    /**
     * convert
     *
     * @param isPostFree String
     * @return Boolean
     */
    private Boolean convertIsPostFree(String isPostFree) {
        if (isPostFree == null || isPostFree.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，是否包邮不能为空，isPostFree=%s", isPostFree));
        }

        Boolean val = IsPostFreeEnum.findRedisValueByMysqlValue(isPostFree);
        if (val == null) {
            throw new BaseException(-1, String.format("非法的券配置，是否包邮超出已知范围，isPostFree=%s", isPostFree));
        }

        return val;
    }

    /**
     * convert
     *
     * @param isShare String
     * @return Boolean
     */
    private Boolean convertIsShare(String isShare) {
        if (isShare == null || isShare.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，是否可分享不能为空，isShare=%s", isShare));
        }

        Boolean val = IsShareEnum.findRedisValueByMysqlValue(isShare);
        if (val == null) {
            throw new BaseException(-1, String.format("非法的券配置，是否可分享超出已知范围，isShare=%s", isShare));
        }

        return val;
    }

    /**
     * convert
     *
     * @param status String
     * @return String
     */
    private String convertStatus(String status) {
        if (status == null || status.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，券状态不能为空，status=%s", status));
        }

        String val = StatusEnum.findRedisValueByMysqlValue(status);
        if (val == null) {
            throw new BaseException(-1, String.format("非法的券配置，券状态超出已知范围，status=%s", status));
        }

        return val;
    }

    /**
     * convert
     *
     * @param suffixs List<SuffixItem>
     * @return Long
     */
    private Long convertReduceMaxPrice(List<SuffixItem> suffixs) {
        if (suffixs == null || suffixs.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，政策规则不能为空，suffix=%s", suffixs));
        }

        for (SuffixItem suffix : suffixs) {
            String code = suffix.getCode();
            String value = suffix.getValue();
            if (!"max_price".equals(code)) {
                continue;
            }
            BigDecimal money = new BigDecimal(value);
            BigDecimal maxPrice = money.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_DOWN).stripTrailingZeros();
            if (maxPrice.compareTo(new BigDecimal(0)) < 0) {
                throw new BaseException(-1, String.format("非法的券配置，折扣券的最大减免金额必须大于0，suffix=%s", suffix));
            }
            return maxPrice.longValue();
        }

        throw new BaseException(-1, String.format("非法的券配置，未配置折扣券的最大减免金额必须大于0，suffix=%s", suffixs));
    }

    /**
     * convert
     *
     * @param rule Rule
     * @return Long
     */
    private Long convertReduceDiscount(Rule rule) {
        if (rule == null) {
            throw new BaseException(-1, "非法的券配置，政策里的规则不能为空");
        }

        String disStr = rule.getReduceDiscount();
        BigDecimal money = new BigDecimal(disStr);
        BigDecimal discount = money.multiply(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros();

        if (discount.compareTo(new BigDecimal(0)) < 0) {
            throw new BaseException(-1, String.format("非法的券配置，折扣券的折扣值必须大于0，rule=%s", rule));
        }

        return discount.longValue();
    }

    /**
     * convert
     *
     * @param rule Rule
     * @return Long
     */
    private Long convertReduceMoney(Rule rule) {
        if (rule == null) {
            throw new BaseException(-1, "非法的券配置，政策里的规则不能为空");
        }

        String moneyStr = rule.getReduceMoney();
        BigDecimal money = new BigDecimal(moneyStr);
        BigDecimal moneyCent = money.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_DOWN).stripTrailingZeros();

        if (moneyCent.compareTo(new BigDecimal(0)) < 0) {
            throw new BaseException(-1, String.format("非法的券配置，满减券的可减金额必须大于0，rule=%s", rule));
        }

        return moneyCent.longValue();
    }

    /**
     * parse
     *
     * @param policy String
     * @return List<PolicyItem>
     */
    private List<PolicyItem> parsePolicy(String policy) {
        if (policy == null || policy.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，政策不能为空，policy=%s", policy));
        }

        List<PolicyItem> policyJson = GsonUtil.fromListJson(policy, PolicyItem.class);
        if (policyJson == null || policyJson.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，政策无法正常解析，policy=%s", policy));
        }

        return policyJson;
    }

    /**
     * convert
     *
     * @param quotas List<QuotaItem>
     * @return Long
     */
    private Long convertQuotaCount(List<QuotaItem> quotas) {
        if (quotas == null || quotas.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，配额不能为空，quotas=%s", quotas));
        }

        if (quotas.get(0) == null) {
            throw new BaseException(-1, String.format("非法的券配置，配额里存在空记录，quotas=%s", quotas));
        }

        long count;
        try {
            String countStr = quotas.get(0).getValue();
            count = Long.parseLong(countStr);
        } catch (NumberFormatException e) {
            throw new BaseException(-1, String.format("非法的券配置，配额的件数转成Long时出错，quotas=%s", quotas));
        }

        if (count <= 0) {
            throw new BaseException(-1, String.format("非法的券配置，配额的件数必须大于0，quotas=%s", quotas));
        }

        return count;
    }

    /**
     * convert
     *
     * @param quotas List<QuotaItem>
     * @return Long
     */
    private Long convertQuotaMoney(List<QuotaItem> quotas) {
        if (quotas == null || quotas.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，配额不能为空，quotas=%s", quotas));
        }

        if (quotas.get(0) == null) {
            throw new BaseException(-1, String.format("非法的券配置，配额里存在空记录，quotas=%s", quotas));
        }

        try {
            String moneyStr = quotas.get(0).getValue();
            BigDecimal money = new BigDecimal(moneyStr);
            BigDecimal moneyCent = money.multiply(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros();

            if (moneyCent.compareTo(new BigDecimal(0)) < 0) {
                throw new BaseException(-1, String.format("非法的券配置，配额的金额必须大于0，quotas=%s", quotas));
            }

            return moneyCent.longValue();
        } catch (NumberFormatException e) {
            throw new BaseException(-1, String.format("非法的券配置，配额的金额转成Long时出错，quotas=%s", quotas));
        }
    }


    /**
     * convert
     *
     * @param quotas List<QuotaItem>
     * @return String
     */
    private String convertQuotaType(List<QuotaItem> quotas) {
        if (quotas == null || quotas.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，配额不能为空，quotas=%s", quotas));
        }

        if (quotas.get(0) == null) {
            throw new BaseException(-1, String.format("非法的券配置，配额里存在空记录，quotas=%s", quotas));
        }

        String val = QuotaTypeEnum.findRedisValueByMysqlValue(quotas.get(0).getCode());
        if (val == null) {
            throw new BaseException(-1, String.format("非法的券配置，配额类型超出已知范围，quotas=%s", quotas));
        }

        return val;
    }


    /**
     * parse
     *
     * @param quota String
     * @return List<QuotaItem>
     */
    private List<QuotaItem> parseQuota(String quota) {
        if (quota == null || quota.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，配额不能为空，quota=%s", quota));
        }

        List<QuotaItem> quotaJson = GsonUtil.fromListJson(quota, QuotaItem.class);
        if (quotaJson == null || quotaJson.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，配额无法正常解析，quota=%s", quota));
        }

        return quotaJson;
    }

    /**
     * parse
     *
     * @param extProp String
     * @return ExtProp
     */
    private ExtProp parseExtProp(String extProp) {
        if (extProp == null || extProp.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，附加属性不能为空，extProp=%s", extProp));
        }

        ExtProp extPropJson = GsonUtil.fromJson(extProp, ExtProp.class);
        if (extPropJson == null) {
            throw new BaseException(-1, String.format("非法的券配置，附加属性无法正常解析，extProp=%s", extProp));
        }

        return extPropJson;
    }

    /**
     * convert
     *
     * @param endTime Long
     * @return Long
     */
    private Long convertGlobalEndTime(Long endTime) {
        if (endTime == null || endTime <= 0) {
            throw new BaseException(-1, String.format("非法的券配置，结束时间不能为空，endTime=%s", endTime));
        }

        if (!endTime.equals(TimeUtil.parseDateTime(TimeUtil.formatSecond(endTime)))) {
            throw new BaseException(-1, String.format("非法的券配置，格式不符合要求，endTime=%s", endTime));
        }

        return endTime;
    }

    /**
     * convert
     *
     * @param startTime Long
     * @return Long
     */
    private Long convertGlobalStartTime(Long startTime) {
        if (startTime == null || startTime <= 0) {
            throw new BaseException(-1, String.format("非法的券配置，结束时间不能为空，startTime=%s", startTime));
        }

        if (!startTime.equals(TimeUtil.parseDateTime(TimeUtil.formatSecond(startTime)))) {
            throw new BaseException(-1, String.format("非法的券配置，格式不符合要求，startTime=%s", startTime));
        }

        return startTime;
    }

    /**
     * convert
     *
     * @param id Long
     * @return Long
     */
    private Long convertId(Long id) {
        if (id == null || id <= 0) {
            throw new BaseException(-1, String.format("非法的券配置，ID不能为空，id=%d", id));
        }

        return id;
    }

    /**
     * convert
     *
     * @param name String
     * @return String
     */
    private String convertName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，名称不能为空，name=%s", name));
        }

        return name;
    }

    /**
     * convert
     *
     * @param isCode String
     * @return String
     */
    private String convertModeType(String isCode) {
        if (isCode == null || isCode.isEmpty()) {
            throw new BaseException(-1, String.format("非法的券配置，模式类型不能为空，isCode=%s", isCode));
        }

        String val = ModeTypeEnum.findRedisValueByMysqlValue(isCode);
        if (val == null) {
            throw new BaseException(-1, String.format("非法的券配置，模式类型超出已知范围，isCode=%s", isCode));
        }

        return val;
    }

    /**
     * convert
     *
     * @param type Integer
     * @return String
     */
    private String convertUseType(Integer type) {
        if (type == null || type <= 0) {
            throw new BaseException(-1, String.format("非法的券配置，使用类型不能为空，type=%d", type));
        }

        String val = UseTypeEnum.findRedisValueByMysqlValue(type);
        if (val == null) {
            throw new BaseException(-1, String.format("非法的券配置，使用类型超出已知范围，type=%d", type));
        }

        return val;
    }

    /**
     * convert
     *
     * @param deductType Integer
     * @return String
     */
    private String convertDeductType(Integer deductType) {
        if (deductType == null || deductType < 0) {
            throw new BaseException(-1, String.format("非法的券配置，抵扣类型不能为空，deductType=%d", deductType));
        }

        String val = DeductTypeEnum.findRedisValueByMysqlValue(deductType);
        if (val == null) {
            throw new BaseException(-1, String.format("非法的券配置，抵扣类型超出已知范围，deductType=%d", deductType));
        }

        return val;
    }

}
