package com.xiaomi.nr.coupon.admin.util;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;


/**
 * 加解密工具
 */
@Slf4j
public class AesUtil {

    // 加密模式
    private static final String TRANS_FORMATION = "AES/ECB/PKCS5Padding";
    // 加密算法
    private static final String ALGORITHM = "AES";
    private static Map<String, Cipher> encryptCache = new ConcurrentHashMap<>();
    private static Map<String,Cipher> decryptCache = new ConcurrentHashMap<>();
    private static final Lock enLock = new ReentrantLock();
    private static final Lock deLock = new ReentrantLock();

    private AesUtil() {
    }

    private static Cipher getEncryptCipher(String password) throws Exception {
        Cipher encrypt = encryptCache.get(password);
        if(encrypt == null){
            enLock.lock();
            try {
                encrypt = encryptCache.get(password);
                if(encrypt == null){
                    SecretKeySpec aesKey = new SecretKeySpec(password.getBytes(StandardCharsets.UTF_8), ALGORITHM);
                    encrypt = Cipher.getInstance(TRANS_FORMATION);
                    encrypt.init(Cipher.ENCRYPT_MODE, aesKey);
                    encryptCache.put(password,encrypt);
                }
            }finally {
                enLock.unlock();
            }
        }
        return encrypt;
    }

    private static Cipher getDecryptCipher(String password) throws Exception {
        Cipher decrypt = decryptCache.get(password);
        if(decrypt == null){
            deLock.lock();
            try {
                decrypt = decryptCache.get(password);
                if(decrypt == null){
                    SecretKeySpec aesKey = new SecretKeySpec(password.getBytes(StandardCharsets.UTF_8), ALGORITHM);
                    decrypt = Cipher.getInstance(TRANS_FORMATION);
                    decrypt.init(Cipher.DECRYPT_MODE, aesKey);
                    decryptCache.put(password,decrypt);
                }
            }finally {
                deLock.unlock();
            }
        }
        return decrypt;
    }

    /**
     * AES 加密
     *
     * @param value 原始字符串
     * @return base64编码后的加密字符串
     * @throws Exception 编码异常
     */
    public static String encrypt(String password, String value) throws Exception {
        Cipher cipher = getEncryptCipher(password);
        byte[] encrypted = cipher.doFinal(value.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encrypted);
    }

    /**
     * AES 解密
     *
     * @param value base64编码后的加密字符串
     * @return base64解码后的原始字符串
     * @throws Exception 解码异常
     */
    public static String decrypt(String password, String value) throws Exception {
        byte[] decode = Base64.getDecoder().decode(value.getBytes(StandardCharsets.UTF_8));
        Cipher cipher = getDecryptCipher(password);
        return new String(cipher.doFinal(decode));
    }

}
