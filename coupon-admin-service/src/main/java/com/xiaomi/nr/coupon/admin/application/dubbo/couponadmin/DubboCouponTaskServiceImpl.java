package com.xiaomi.nr.coupon.admin.application.dubbo.couponadmin;

import com.google.common.base.Stopwatch;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigDescVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponTaskListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.FillCouponDetailVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponCodeDownloadRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponCodeTaskListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponConfigDescRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillTaskDetailRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillTaskListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CreateFillCouponTaskRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.DownLoadFillTaskDetailRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.DownLoadUserRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.ReStartTaskRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.UserTagDescRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponCodeTaskListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponConfigDescResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponFillTaskDetailResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CreateTaskResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.DownLoadUserResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.UserTagDescResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponTaskService;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponCodeConvert;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponTaskConvert;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponcode.CouponCodeService;
import com.xiaomi.nr.coupon.admin.domain.coupon.fillcoupontask.DownLoadHelper;
import com.xiaomi.nr.coupon.admin.domain.coupon.fillcoupontask.FillCouponService;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneAssignModeEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.task.DownloadTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.infrastruture.notify.robotmessage.RobotSendMessageUtil;
import com.xiaomi.nr.coupon.admin.infrastruture.notify.robotmessage.po.Card;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponCodeRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponTaskRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.DownLoadCouponCodePO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.CodeTaskSearchParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.FillCouponTaskPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.SearchTaskParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.coupon.po.CouponCodePO;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.http.UserApiService;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.http.entrty.UserBatchData;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
@Component
@Slf4j
public class DubboCouponTaskServiceImpl implements DubboCouponTaskService {

    @Autowired
    private CouponTaskConvert fillCouponTaskConvert;

    @Autowired
    private FillCouponService fillCouponService;

    @Autowired
    private CouponCodeRepository couponCodeRepository;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Autowired
    private CouponTaskRepository fillCouponTaskRepository;

    @Autowired
    private UserApiService userApi;

    @Autowired
    private DownLoadHelper downLoadHelper;

    @Autowired
    private CouponCodeConvert couponCodeConvert;

    @Autowired
    private CouponCodeService couponCodeService;

    @Autowired
    private RobotSendMessageUtil robotMessageUtil;

    @Autowired
    private ThreadPoolTaskExecutor asyncExecutor;


    /**
     * 灌券任务列表
     *
     * @param request request
     * @return
     */
    @Override
    public Result<BasePageResponse<CouponTaskListVO>> couponFillTaskList(CouponFillTaskListRequest request) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        BasePageResponse<CouponTaskListVO> response;

        try {
            // 封装查询参数
            SearchTaskParam searchParam = fillCouponTaskConvert.transferToSearchParameter(request);
            response = new BasePageResponse<>(searchParam.getPageNo(), searchParam.getPageSize());

            // 具体查询逻辑
            List<CouponTaskListVO> fillCouponList = fillCouponService.searchFillCoupon(searchParam, response);
            response.setList(fillCouponList);

            log.info("DubboFillCouponService.couponFillTaskList execute success, runTime={}ms, request={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboFillCouponService.couponFillTaskList request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }


    /**
     * 创建灌券任务
     *
     * @param request request
     * @return
     */
    @Override
    public Result<CreateTaskResponse> couponFillTaskCreate(CreateFillCouponTaskRequest request) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("DubboFillCouponService.createTask request, request:{}", request);
        try {
            long taskId = fillCouponService.createFillCouponTask(request);
            log.info("DubboFillCouponService.createTask response, runTime:{}ms, request:{}, taskId:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS), request, taskId);
            return Result.success(new CreateTaskResponse(taskId));
        } catch (Exception e) {
            log.error("DubboFillCouponService.createTask request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 查看灌券任务详情
     *
     * @param request request
     * @return
     */
    @Override
    public Result<CouponFillTaskDetailResponse> taskDetail(CouponFillTaskDetailRequest request) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        Long taskId = request.getTaskId();
        if (Objects.isNull(taskId)) {
            Result.fail(GeneralCodes.ParamError, "券配置ID为NULL");
        }
        CouponFillTaskDetailResponse response = new CouponFillTaskDetailResponse();
        try {
            response.setCouponTaskVO(fillCouponService.taskDetail(taskId));
            log.info("DubboFillCouponService.taskDetail execute success, runTime={}ms, request={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboFillCouponService.taskDetail request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 重试灌券任务
     *
     * @param request request
     * @return
     */
    @Override
    public Result<Void> taskRetry(ReStartTaskRequest request) {

        // 参数校验
        Stopwatch stopwatch = Stopwatch.createStarted();
        if (Objects.isNull(request.getTaskId()) || request.getTaskId() <= CommonConstant.ZERO_LONG) {
            Result.fail(GeneralCodes.ParamError, "灌券任务ID不合法");
        }

        try {
            String account = UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix();

            fillCouponService.retryFillCouponTask(request, account);
            log.info("DubboFillCouponService.taskRetry execute success, runTime={}ms, request={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), request);
            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboFillCouponService.taskRetry request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }


    /**
     * 下载灌券详情
     *
     * @param request request
     * @return
     */
    @Override
    public Result<List<FillCouponDetailVO>> detailDownload(DownLoadFillTaskDetailRequest request) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        Long taskId = request.getTaskId();
        if (Objects.isNull(taskId) || taskId < CommonConstant.ZERO_LONG) {
            Result.fail(GeneralCodes.ParamError, "灌券任务ID不合法");
        }

        try {
            List<FillCouponDetailVO> resultStr = downLoadHelper.downloadFillCouponDetail(taskId, DownloadTypeEnum.TASK_DETAIL);
            log.info("DubboFillCouponService.detailDownload execute success, runTime={}ms, request={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), request);
            return Result.success(resultStr);
        } catch (Exception e) {
            log.error("DubboFillCouponService.detailDownload request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }


    /**
     * 下载灌券失败名单
     *
     * @param request request
     * @return
     */
    @Override
    public Result<List<FillCouponDetailVO>> failUidDownload(DownLoadFillTaskDetailRequest request) {

        Stopwatch stopwatch = Stopwatch.createStarted();
        Long taskId = request.getTaskId();
        if (Objects.isNull(taskId) || taskId < CommonConstant.ZERO_LONG) {
            Result.fail(GeneralCodes.ParamError, "灌券任务ID不合法");
        }

        try {
            List<FillCouponDetailVO> resultStr = downLoadHelper.getFailUserDetail(taskId, DownloadTypeEnum.FAIL_UID);
            for (FillCouponDetailVO fillCouponDetailVO : resultStr) {
                fillCouponDetailVO.setMid(Long.parseLong(fillCouponDetailVO.getEntity()));
            }
            log.info("DubboFillCouponService.failUidDownload execute success, runTime={}ms, request={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), request);
            return Result.success(resultStr);
        } catch (Exception e) {
            log.error("DubboFillCouponService.failUidDownload request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 券码列表
     *
     * @param request
     * @return
     */
    @Override
    public Result<BasePageResponse<CouponCodeTaskListVO>> couponCodeTaskList(CouponCodeTaskListRequest request) {
        try {
            log.info("DubboCouponTaskService.couponCodeTaskList begin request:{}", request);
            CodeTaskSearchParam codeTaskSearchParam = new CodeTaskSearchParam();
            fillCouponTaskConvert.convertTOSearchParam(request, codeTaskSearchParam);
            int pageNo = request.getPageNo();
            int pageSize = request.getPageSize();
            int count = fillCouponTaskRepository.searchTaskCount(codeTaskSearchParam);
            int totalPage = count / pageSize + (count % pageSize == 0 ? 0 : 1);
            List<FillCouponTaskPO> fillCouponTaskPOList = fillCouponTaskRepository.searchTaskPageList(codeTaskSearchParam,
                    (pageNo - 1) * pageSize, pageSize);
            List<CouponCodeTaskListVO> couponCodeTaskListVOList = fillCouponTaskConvert.convertToCodeTaskList(fillCouponTaskPOList);
            log.info("DubboCouponTaskService.couponCodeTaskList end request:{} res:{}", request, couponCodeTaskListVOList);
            return Result.success(new BasePageResponse<>(pageNo, pageSize, count, totalPage, couponCodeTaskListVOList));
        } catch (Exception e) {
            log.error("DubboCouponTaskService.couponCodeTaskList error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 优惠码下载
     *
     * @param request request
     * @return String
     */
    @Override
    public Result<String> downloadCode(CouponCodeDownloadRequest request) {

        try {

            // 权限校验同步执行
            String account = UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix();
            CouponConfigPO couponPO = couponConfigRepository.getCreatorInfoByConfigId(request.getConfigId());

            if (!checkLoadUser(request.getIsAdmin(), couponPO.getCreator(), account)) {
                return Result.fail(GeneralCodes.NotAuthorized, "仅支持创建人获取优惠码, 若有特殊情况请联系系统管理员");
            }

            log.info("DubboCouponTaskService.downloadCode async execute start. request:{}", request);

            // 下载逻辑异步执行
            asyncExecutor.submit(() -> {
                try {

                    List<CouponCodePO> codePOList = couponCodeRepository.getCouponCodeByConfigId(request.getConfigId());

                    List<DownLoadCouponCodePO> excelData = couponCodeConvert.convertToCouponCodeExcel(codePOList);

                    String fdsUrl = couponCodeService.uploadCouponCode(excelData, request.getConfigId());

                    robotMessageUtil.sendPrivateCard(Card.getCouponCodeCard(request.getConfigId(), couponPO.getName(), fdsUrl), account);

                    log.info("DubboCouponTaskService.downloadCode async execute success. request:{}", request);

                } catch (Exception e) {
                    log.error("DubboCouponTaskService.downloadCode async execute error. request:{}", request);
                }
            });

            return Result.success("下载成功, 注意查收飞书消息下载链接!");
        } catch (Exception e) {
            log.error("DubboCouponTaskService.downloadCode error. request:{}", request);
            return Result.fromException(e);
        }
    }

    /**
     * 查询人群包信息服务
     *
     * @param request UserTagRequest
     * @return
     */
    @Override
    public Result<UserTagDescResponse> queryUserTagDesc(UserTagDescRequest request) {
        if (Objects.isNull(request.getId())) {
            Result.fail(GeneralCodes.ParamError, "ID不合法");
        }

        try {
            UserBatchData userData = userApi.userBatchData(request.getId());
            if (Objects.isNull(userData)) {
                return Result.success(null);
            }
            return Result.success(new UserTagDescResponse(userData.getBatch_id(), userData.getBatch_name(), userData.getHdfs_addr(), userData.getTotal()));

        } catch (Exception e) {
            log.error("DubboFillCouponService.queryUserTagDesc request={}, error: ", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 查询券信息
     *
     * @param request request
     * @return
     */
    @Override
    public Result<CouponConfigDescResponse> queryCouponConfigDesc(CouponConfigDescRequest request) {
        long id = request.getId();
        if (id <= 0) {
            Result.fail(GeneralCodes.ParamError, "ID不合法");
        }
        try {
            List<CouponScenePO> scenes = couponSceneRepository.selectSceneByAssignMode(SceneAssignModeEnum.Coupon_Schedule.getCode(), SceneStatusEnum.ONLINE.getCode());
            if (Objects.isNull(scenes)) {
                return Result.success(null);
            }

            List<String> sceneCodeList = scenes.stream().map(CouponScenePO::getSceneCode).collect(Collectors.toList());
            // 获取券信息
            List<CouponConfigPO> couponPO = couponConfigRepository.getCouponNameById(id, sceneCodeList, System.currentTimeMillis() / 1000);
            if (Objects.isNull(couponPO)) {
                return Result.success(null);
            }

            // 获取券库存信息
            Map<Long, Long> countMap = couponConfigRepository.getCouponSendCount(couponPO.stream().map(CouponConfigPO::getId).collect(Collectors.toList()));

            List<CouponConfigDescVO> couponConfigDescVOS = couponPO.stream().map(x -> {
                CouponConfigDescVO couponConfigDescVO = new CouponConfigDescVO();
                couponConfigDescVO.setId(x.getId());
                couponConfigDescVO.setNameDesc(x.getId() + "-" + x.getName());
                couponConfigDescVO.setAvailableCount(x.getApplyCount() - Optional.ofNullable(countMap.get(id)).orElse(0L));
                return couponConfigDescVO;
            }).collect(Collectors.toList());

            CouponConfigDescResponse response = new CouponConfigDescResponse();
            response.setCouponConfigDescVOS(couponConfigDescVOS);
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboFillCouponService.queryCouponConfigDesc request={}, error: ", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 下载灌券用户集
     *
     * @param request
     * @return
     */
    @Override
    public Result<DownLoadUserResponse> downloadUserList(DownLoadUserRequest request) {
        if (StringUtils.isEmpty(request.getHdfsPath())) {
            Result.fail(GeneralCodes.ParamError, "下载地址为空");
        }

        try {
            List<Long> uidData = downLoadHelper.downloadEntityList(request.getHdfsPath())
                    .stream()
                    .filter(StringUtils::isNumeric)
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            return Result.success(new DownLoadUserResponse(uidData));

        } catch (Exception e) {
            log.error("DubboFillCouponService.downloadUserList request={}, error: ", request, e);
            return Result.fromException(e);
        }
    }

    private boolean checkLoadUser(int isAdmin, String creator, String account) {

        if (CommonConstant.ONE_INT == isAdmin) {
            return true;
        }


        return StringUtils.equals(account, creator);
    }

}
