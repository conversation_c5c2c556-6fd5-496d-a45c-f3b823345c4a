package com.xiaomi.nr.coupon.admin.infrastruture.notify.rocketmq.impl;

import com.xiaomi.nr.coupon.admin.infrastruture.notify.rocketmq.PostMessageService;
import com.xiaomi.nr.coupon.admin.infrastruture.notify.rocketmq.model.PostOfficeMsg;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

/**
 * 邮件短信飞书提醒操作类
 */
@Slf4j
@Service
public class PostMessageServiceImpl implements PostMessageService {

    @Value("${postoffice.topic}")
    private String postOfficeTopic;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Override
    public void sendMesaage(PostOfficeMsg postOfficeMsg, String msgKey) throws Exception{
        Message message = MessageBuilder.withPayload(GsonUtil.toJson(postOfficeMsg)).setHeader(RocketMQHeaders.KEYS, msgKey).build();
        SendResult sendResult = rocketMQTemplate.syncSend(postOfficeTopic, message);
        if (SendStatus.SEND_OK != sendResult.getSendStatus()) {
            log.error("PostMessageService sendMesaage error postOfficeMsg:{},sendResult:{}",postOfficeMsg,sendResult);
            throw ExceptionHelper.create(ErrCode.COUPON, "发送邮局消息失败");
        }
    }
}
