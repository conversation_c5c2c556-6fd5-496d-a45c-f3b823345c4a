package com.xiaomi.nr.coupon.admin.infrastruture.notify.zk;

import com.xiaomi.miliao.zookeeper.ZKClient;
import com.xiaomi.miliao.zookeeper.ZKFacade;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.coupon.ZkPathConstant;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.net.InetAddress;

@Slf4j
@Service
public class ZkNotifyService {



    private final ZKClient zkClient = ZKFacade.getAbsolutePathClient();


    //TODO check version right
    public synchronized void updateConfigZkVersion(String version, long id) {
       log.info("ZkNotifyService updateConfigZkVersion version:{}, id:{}", version, id);
        if (StringUtils.isBlank(version)) {
            throw new IllegalArgumentException("版本(" + version + ")不合法");
        }


        boolean exists = zkClient.exists(ZkPathConstant.COUPON_ZK_PATH);
        String host = "";
        try {
            host = InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            log.error("ZkNotifyService updateConfigZkVersion Exception, version:{}, id:{}, e:", version, id, e);
        }
        String latestInfo = String.format("version:%s|time:%s|host:%s",
                version,
                TimeUtil.getNowDateTime(),
                host);
        if (exists) {
            zkClient.updatePersistent(ZkPathConstant.COUPON_ZK_PATH, latestInfo);
        } else {
            zkClient.createPersistent(ZkPathConstant.COUPON_ZK_PATH, latestInfo);
        }
    }

}
