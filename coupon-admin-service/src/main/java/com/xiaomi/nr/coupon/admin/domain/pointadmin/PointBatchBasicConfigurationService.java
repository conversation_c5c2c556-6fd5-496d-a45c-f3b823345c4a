package com.xiaomi.nr.coupon.admin.domain.pointadmin;

import cn.hutool.core.util.PageUtil;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoDTO;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.SsuBlacklistDto;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.SsuBlacklistRequest;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.SsuBlacklistDeleteStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CarPointsBaseConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CarPointsBlackSsuRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.param.CarPointsBlackSsuParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBaseConfigPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBlackSsuPo;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.goods.GisProxyService;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.goods.GmsProxyService;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/7 17:08
 */
@Service
@Slf4j
public class PointBatchBasicConfigurationService {
    @Autowired
    private CarPointsBlackSsuRepository carPointsBlackSsuRepository;

    @Autowired
    private GmsProxyService gmsProxyService;

    @Autowired
    private GisProxyService gisProxyService;

    @Autowired
    private CarPointsBaseConfigRepository carPointsBaseConfigRepository;

    public BasePageResponse<SsuBlacklistDto> ssuBlacklist(SsuBlacklistRequest request) throws Exception {
        log.info("PointBatchBasicConfigurationService.ssuBlacklist begin, request = {}", request);

        CarPointsBlackSsuParam param = buildCarPointsBlackSsuParam(request);
        Integer totalCount = carPointsBlackSsuRepository.selectTotal(param);
        List<SsuBlacklistDto> ssuBlacklistDtoList = Lists.newArrayList();

        BasePageResponse<SsuBlacklistDto> response = new BasePageResponse<>();
        response.setList(ssuBlacklistDtoList);
        response.setTotalCount(totalCount);
        response.setPageNo(request.getPageNo());
        response.setPageSize(request.getPageSize());
        response.setTotalPage(PageUtil.totalPage(totalCount, request.getPageSize()));

        if (totalCount <= 0) {
            log.info("PointBatchBasicConfigurationService.ssuBlacklist totalCount = {}", totalCount);
            return response;
        }

        List<CarPointsBlackSsuPo> carPointsBlackSsuPoList = carPointsBlackSsuRepository.findByParam(param);
        // 根据添加时间降序排列
        carPointsBlackSsuPoList = carPointsBlackSsuPoList.stream()
                .sorted(Comparator.comparing(CarPointsBlackSsuPo::getCreateTime).reversed())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(carPointsBlackSsuPoList)) {
            log.info("PointBatchBasicConfigurationService.ssuBlacklist carPointsBlackSsuPoList为空, request = {}", request);
            return response;
        }

        //  List<CarPointsBlackSsuPo>  -> List<ssuId>
        List<Long> ssuIdList = carPointsBlackSsuPoList.stream().map(CarPointsBlackSsuPo::getSsu).collect(Collectors.toList());

        // 调用gis接口，查询ssu信息
        Map<Long, GoodsMultiInfoDTO> ssuMap = gisProxyService.queryGoodsInfoBySsuIds(ssuIdList);

        for (CarPointsBlackSsuPo carPointsBlackSsuPo : carPointsBlackSsuPoList) {
            Long ssuId = carPointsBlackSsuPo.getSsu();

            GoodsMultiInfoDTO goodsMultiInfoDTO = ssuMap.getOrDefault(ssuId, null);
            SsuBlacklistDto ssuBlacklistDto = buildSsuBlacklistDto(carPointsBlackSsuPo, goodsMultiInfoDTO);
            ssuBlacklistDtoList.add(ssuBlacklistDto);

        }

        return response;
    }

    private SsuBlacklistDto buildSsuBlacklistDto(CarPointsBlackSsuPo carPointsBlackSsuPo, GoodsMultiInfoDTO goodsMultiInfoDTO) {
        SsuBlacklistDto ssuBlacklistDto = new SsuBlacklistDto();
        BeanUtils.copyProperties(carPointsBlackSsuPo, ssuBlacklistDto);
        ssuBlacklistDto.setItem(carPointsBlackSsuPo.getSsu());
        ssuBlacklistDto.setName(Optional.ofNullable(goodsMultiInfoDTO).map(GoodsMultiInfoDTO::getGoodsName).orElse("-"));

        return ssuBlacklistDto;
    }

    private CarPointsBlackSsuParam buildCarPointsBlackSsuParam(SsuBlacklistRequest request) {
        CarPointsBlackSsuParam param = new CarPointsBlackSsuParam();

        if (Objects.nonNull(request.getSsu())) {
            param.setSsu(request.getSsu());
        }

        param.setDeleteStatus(SsuBlacklistDeleteStatusEnum.NO_DELETE.getCode());

        Integer pageSize = request.getPageSize();

        param.setLimit(pageSize);
        Integer offset = (request.getPageNo() - 1) * pageSize;
        param.setOffset(offset);

        return param;
    }

    public void deleteSsuBlacklist(Long ssuId) throws BizError {
        log.info("PointBatchBasicConfigurationService.deleteSsuBlacklist begin, ssuId = {}", ssuId);

        CarPointsBlackSsuParam param = new CarPointsBlackSsuParam();
        param.setSsu(ssuId);
        param.setDeleteStatus(SsuBlacklistDeleteStatusEnum.NO_DELETE.getCode());

        // 查询表中是否存在未删除的ssu记录
        List<CarPointsBlackSsuPo> carPointsBlackSsuPoList = carPointsBlackSsuRepository.findByParam(param);

        if (CollectionUtils.isNotEmpty(carPointsBlackSsuPoList)) {
            // 表中存在未删除的ssu记录，删除ssu记录
            carPointsBlackSsuRepository.deleteBySsuId(ssuId);
        }

        // 获取黑名单中所有有效的ssuId
        param.setSsu(null);

        List<CarPointsBlackSsuPo> ssuPoList = carPointsBlackSsuRepository.findByParam(param);
        List<Long> validSsuIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ssuPoList)) {
            validSsuIdList = ssuPoList.stream().map(CarPointsBlackSsuPo::getSsu).collect(Collectors.toList());
        }

        // TODO: 更新积分通用配置

        // 更新积分通用配置cache
        // 获取最后一个有效的通用配置
        CarPointsBaseConfigPo baseConfigPo = carPointsBaseConfigRepository.findLastValid();
        if (Objects.isNull(baseConfigPo)) {
            log.error("PointBatchBasicConfigurationService.deleteSsuBlacklist 积分通用配置不存在");
            throw ExceptionHelper.create(ErrCode.POINT, "积分通用配置不存在");
        }

        // 设置积分通用配置缓存
        carPointsBaseConfigRepository.setPointBaseBatchConfigCache(baseConfigPo, validSsuIdList);

        log.info("PointBatchBasicConfigurationService.deleteSsuBlacklist finished, ssuId = {}", ssuId);
    }

    public void addSsuBlacklist(List<Long> ssuIdList, String createUser) throws Exception {
        log.info("PointBatchBasicConfigurationService.addSsuBlacklist begin, ssuIdList = {}, createUser = {}", ssuIdList, createUser);

        // 调用gis接口，查询ssu信息
        Map<Long, GoodsMultiInfoDTO> goodsMap = gisProxyService.queryGoodsInfoBySsuIds(ssuIdList);

        if (goodsMap.size() != ssuIdList.size()) {
            // 查询到的ssu数量不等于ssuIdList数量
            log.error("PointBatchBasicConfigurationService.addSsuBlacklist 查询到的ssu数量不等于ssuIdList数量, ssuIdList = {}, goodsMap = {}", ssuIdList, goodsMap);
            throw ExceptionHelper.create(ErrCode.POINT, "查询到的ssu数量不等于ssuIdList数量");
        }

        List<CarPointsBlackSsuPo> carPointsBlackSsuPoList = carPointsBlackSsuRepository.findBySsu(ssuIdList);

        // 需要新增的ssuId
        List<Long> needAddSsuIdList = ssuIdList;

        if (CollectionUtils.isNotEmpty(carPointsBlackSsuPoList)) {
            List<Long> existSsuIdList = carPointsBlackSsuPoList.stream().map(CarPointsBlackSsuPo::getSsu).collect(Collectors.toList());

            // 需要新增的ssuId集合 = ssuIdList 和 existSsuIdList 差集
            needAddSsuIdList = ListUtils.subtract(ssuIdList, existSsuIdList);

            // 数据库中已存在该SSU，修改deleteStatus
            Map<Integer, List<Long>> blackSsuMap = carPointsBlackSsuPoList.stream()
                    .collect(Collectors.groupingBy(CarPointsBlackSsuPo::getDeleteStatus,
                            Collectors.mapping(CarPointsBlackSsuPo::getSsu, Collectors.toList())));
            for (Map.Entry<Integer, List<Long>> entry : blackSsuMap.entrySet()) {
                Integer deleteStatus = entry.getKey();
                if (SsuBlacklistDeleteStatusEnum.IS_DELETE.getCode().equals(deleteStatus)) {
                    List<Long> ssuIds = entry.getValue();
                    log.info("PointBatchBasicConfigurationService.addSsuBlacklist ssuIds = {}", ssuIds);

                    // 数据库中已删除的ssu，修改为 未删除
                    carPointsBlackSsuRepository.updateDeleteStatus(ssuIds, SsuBlacklistDeleteStatusEnum.NO_DELETE.getCode());

                }
            }

        }

        if (CollectionUtils.isNotEmpty(needAddSsuIdList)) {
            log.info("PointBatchBasicConfigurationService.addSsuBlacklist needAddSsuIdList = {}", needAddSsuIdList);

            List<CarPointsBlackSsuPo> carPointsBlackSsuPos = Lists.newArrayList();
            needAddSsuIdList.forEach(ssuId -> {
                CarPointsBlackSsuPo carPointsBlackSsuPo = new CarPointsBlackSsuPo();

                carPointsBlackSsuPo.setSsu(ssuId);
                carPointsBlackSsuPo.setCreateUser(createUser);
                carPointsBlackSsuPo.setDeleteStatus(SsuBlacklistDeleteStatusEnum.NO_DELETE.getCode());

                carPointsBlackSsuPos.add(carPointsBlackSsuPo);
            });

            // 批量插入ssu
            carPointsBlackSsuRepository.batchInsert(carPointsBlackSsuPos);

        }

        // 获取黑名单中所有有效的ssuId
        CarPointsBlackSsuParam param = new CarPointsBlackSsuParam();
        param.setDeleteStatus(SsuBlacklistDeleteStatusEnum.NO_DELETE.getCode());

        List<CarPointsBlackSsuPo> ssuPoList = carPointsBlackSsuRepository.findByParam(param);
        List<Long> validSsuIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ssuPoList)) {
            validSsuIdList = ssuPoList.stream().map(CarPointsBlackSsuPo::getSsu).collect(Collectors.toList());
        }

        // TODO: 更新积分通用配置

        // 更新积分通用配置cache
        // 获取最后一个有效的通用配置
        CarPointsBaseConfigPo baseConfigPo = carPointsBaseConfigRepository.findLastValid();
        if (Objects.isNull(baseConfigPo)) {
            log.error("PointBatchBasicConfigurationService.deleteSsuBlacklist 积分通用配置不存在");
            throw ExceptionHelper.create(ErrCode.POINT, "积分通用配置不存在");
        }

        // 设置积分通用配置缓存
        carPointsBaseConfigRepository.setPointBaseBatchConfigCache(baseConfigPo, validSsuIdList);

        log.info("PointBatchBasicConfigurationService.addSsuBlacklist finished, ssuIdList = {}, createUser = {}", ssuIdList, createUser);
    }
}
