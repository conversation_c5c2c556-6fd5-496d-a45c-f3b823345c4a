package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.oldcoupon.OldCouponInfo;

import java.util.List;

/**
 * @description: 老缓存新key
 */
public interface OldCouponRedisDao {


    void setOldCouponInfo(OldCouponInfo oldCouponCachePo);


    void setValidConfigIds(List<Long> validConfigIds);


    void deleteOldCouponInfo(List<Long> configIds);

}
