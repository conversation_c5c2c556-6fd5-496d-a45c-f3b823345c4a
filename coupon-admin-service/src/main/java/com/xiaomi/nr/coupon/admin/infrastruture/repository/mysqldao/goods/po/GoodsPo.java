package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.goods.po;


import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 货品
 *
 * <AUTHOR>
 */
@Data
public class GoodsPo implements Serializable {

    private static final long serialVersionUID = -6253972232570608375L;

    /**
     * 货品ID
     */
    @SerializedName("goods_id")
    private Long goodsId;

    /**
     * SKU
     */
    @SerializedName("sku")
    private Long sku;

    /**
     * 商品ID
     */
    @SerializedName("commodity_id")
    private Long commodityId;

    /**
     * 产品ID
     */
    @SerializedName("product_id")
    private Long productId;

    /**
     * 创建时间
     */
    @SerializedName("add_time")
    private Long addTime;
}

