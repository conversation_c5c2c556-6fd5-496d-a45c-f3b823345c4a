package com.xiaomi.nr.coupon.admin.enums.couponcode;

/**
 * 优惠码使用方式
 */
public enum UseModeEnum {

    /**
     * 明码
     */
    CODE(1,"明码"),

    /**
     * 兑换
     */
    EXCHANGE(2, "兑换");


    private final int code;
    private final String desc;

    UseModeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static String findDescByCode(int code) {
        UseModeEnum[] codes = UseModeEnum.values();
        for (UseModeEnum item : codes) {
            if (code == item.getCode()) {
                return item.getDesc();
            }
        }
        return null;
    }

    public static UseModeEnum findEnumByCode(int code) {
        UseModeEnum[] codes = UseModeEnum.values();
        for (UseModeEnum item : codes) {
            if (code == item.getCode()) {
                return item;
            }
        }
        return null;
    }

}
