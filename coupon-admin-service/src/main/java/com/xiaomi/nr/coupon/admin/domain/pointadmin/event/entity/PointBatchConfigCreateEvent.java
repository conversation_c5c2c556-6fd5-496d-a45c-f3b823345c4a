package com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/12/6 10:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PointBatchConfigCreateEvent extends PointBaseEvent<CarPointsBatchConfigPo> {

    private static final long serialVersionUID = 2073014476092640534L;


}
