package com.xiaomi.nr.coupon.admin.domain.coupon.couponscene;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.request.CreateOrUpdateSendSceneRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.response.CreateOrUpdateSendSceneResponse;

/**
 * @Description: 优惠券场景
 * @Date: 2022.03.03 18:54
 */
public interface CouponSceneDomainService {
    /**
     * 创建或修改优惠券场景
     * @param request
     * @return
     */
    String createOrUpdateSendScene(CreateOrUpdateSendSceneRequest request) throws Exception;
}
