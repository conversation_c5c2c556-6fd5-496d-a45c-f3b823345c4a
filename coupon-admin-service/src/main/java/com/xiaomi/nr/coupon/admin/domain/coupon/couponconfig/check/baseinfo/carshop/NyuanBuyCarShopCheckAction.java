package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.carshop;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.CouponConfigCheckService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.CouponCheckFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.NyuanBuyCheckAction;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： Pancras
 * @date： 2024/9/11
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Service
@Slf4j
public class NyuanBuyCarShopCheckAction extends NyuanBuyCheckAction {

    @Autowired
    private CouponConfigCheckService couponCheckService;

    @Override
    public void init() {
        CouponCheckFactory.register(getBizPlatformEnum(), this);
    }

    @Override
    public void createSpecialCheck(CouponBaseInfo info) throws BizError {
        if (info.getPromotionValue() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠值必须大于0");
        }
    }

    @Override
    public void updateSpecialCheck(CouponBaseInfo info) throws BizError {
        if (info.getPromotionValue() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠值必须大于0");
        }
    }

    @Override
    public void goodsCheck(CouponConfigItem configItem) throws Exception {
        couponCheckService.getGoodsHandler(configItem.getCouponGoodsInfo().getScopeType()).handleGoods(configItem);
    }

    @Override
    public BizPlatformEnum getBizPlatformEnum() {
        return BizPlatformEnum.CAR_SHOP;
    }


}
