package com.xiaomi.nr.coupon.admin.application.dubbo.pointadmin;

import com.xiaomi.nr.coupon.admin.api.service.pointadmin.DubboPointScheduleService;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.PointBatchScheduleService;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/12
 */
@Component
@Slf4j
@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
public class DubboPointScheduleServiceImpl implements DubboPointScheduleService {

    @Resource
    private PointBatchScheduleService pointBatchScheduleService;

    /**
     * 积分批次定时任务
     * 涉及：风险预警、预算释放
     *
     * @return void
     */
    @Override
    public Result<Void> pointBatchSchedule() {

        log.info("DubboPointScheduleService.pointBatchSchedule started");

        // 1、风险预警
        try {
            pointBatchScheduleService.riskWarning();
        } catch (Exception e) {
            log.info("DubboPointScheduleService pointBatchSchedule error. e ", e);
        }

        // 2、预算释放
        try {
            pointBatchScheduleService.budgetRelease();
        } catch (Exception e) {
            log.info("DubboPointScheduleService budgetRelease error. e ", e);
        }

        log.info("DubboPointScheduleService.pointBatchSchedule finished");
        return Result.success(null);
    }

    /**
     * 积分批次定时任务
     * 涉及：预算释放
     *
     * @return void
     */
    @Override
    public Result<Void> budgetRelease() {

        log.info("DubboPointScheduleService.budgetRelease started");

        try {
            pointBatchScheduleService.budgetRelease();
        } catch (Exception e) {
            log.info("DubboPointScheduleService budgetRelease error. e ", e);
        }
        log.info("DubboPointScheduleService.budgetRelease finished");
        return Result.success(null);
    }
}
