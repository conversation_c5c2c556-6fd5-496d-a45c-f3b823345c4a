package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/10 09:59
 */
public abstract class ServiceCouponCheckTools {
    /**
     * 工时&配件ssu校验
     *
     * @param labourHourSsu 工时ssu
     * @param partsSsu 配件ssu
     */
    public abstract void goodsCheck(Map<Long, Integer> labourHourSsu, Map<Long, Integer> partsSsu) throws BizError;

    /**
     * 券创建校验
     *
     * @param info 券配置基础信息
     */
    public abstract void createSpecialCheck(CouponBaseInfo info) throws BizError;

    /**
     * 券修改校验
     *
     * @param info 券配置基础信息
     */
    public abstract void updateSpecialCheck(CouponBaseInfo info) throws BizError;
}
