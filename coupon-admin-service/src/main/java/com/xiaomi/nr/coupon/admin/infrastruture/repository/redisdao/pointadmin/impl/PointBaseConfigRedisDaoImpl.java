package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.pointadmin.impl;

import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.pointadmin.PointBaseConfigRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.pointadmin.po.PointBaseConfigCachePo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/12/13 19:07
 */
@Component
@Slf4j
public class PointBaseConfigRedisDaoImpl implements PointBaseConfigRedisDao {
    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringPointAdminRedisTemplate")
    private StringRedisTemplate redisStringTemplate;

    /**
     * 汽车积分通用配置缓存key
     */
    private static final String KEY_POINT_BASE_CONFIG_CACHE = "nr:car:point:config:base";

    /**
     * 设置积分通用配置缓存
     *
     * @param cachePo cachePo
     */
    @Override
    public void setPointBaseBatchConfigCache(PointBaseConfigCachePo cachePo) throws BizError {
        String configStr = GsonUtil.toJson(cachePo);
        ValueOperations<String, String> operations = redisStringTemplate.opsForValue();

        int numRetries = 0;
        int maxRetries = 1;
        boolean success = false;

        while (!success && numRetries <= maxRetries) {
            try {
                operations.set(KEY_POINT_BASE_CONFIG_CACHE, configStr);
                success = true;
            } catch (Exception e) {
                numRetries++;
                if (numRetries <= maxRetries) {
                    log.warn("PointBaseConfigRedisDaoImpl.setPointBaseBatchConfigCache 写入redis积分通用配置缓存失败，重试一次，numRetries = {}, err = ", numRetries, e);
                    // 重试一次
                    operations.set(KEY_POINT_BASE_CONFIG_CACHE, configStr);
                } else {
                    // 失败次数 > 最大重试次数 抛异常
                    log.error("PointBatchConfigRedisDaoImpl.setPointBatchConfigCache 写入redis积分通用配置缓存失败，err = ", e);
                    throw ExceptionHelper.create(ErrCode.POINT, "写入redis积分通用配置缓存失败");
                }
            }
        }
    }

    /**
     * 获取积分通用配置缓存
     *
     * @return PointBaseConfigCachePo
     */
    @Override
    public PointBaseConfigCachePo getPointBaseBatchConfigCache() {
        ValueOperations<String, String> operations = redisStringTemplate.opsForValue();

        String configStr = operations.get(KEY_POINT_BASE_CONFIG_CACHE);
        if (StringUtils.isBlank(configStr)){
            return null;
        }

        return GsonUtil.fromJson(configStr, PointBaseConfigCachePo.class);
    }
}
