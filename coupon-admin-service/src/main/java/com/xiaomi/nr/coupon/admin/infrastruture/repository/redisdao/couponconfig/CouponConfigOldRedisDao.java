package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigCacheItemPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigIdListCachePo;

import java.util.List;

/**
 * 原缓存-原key
 *
 * <AUTHOR>
 */
@Deprecated
public interface CouponConfigOldRedisDao {


    /**
     * 批量设置券配置缓存
     *
     * @param list List<SingleConfigCache>
     */
    void set(List<ConfigCacheItemPo> list);

    /**
     * 批量获取券配置缓存
     *
     * @param configIds List<Long>
     * @return List<SingleConfigCache>
     */
    List<ConfigCacheItemPo> get(List<Long> configIds);

    /**
     * 获取单个券配置信息缓存
     * @param configId long
     * @return ConfigCacheItemPo
     */
    ConfigCacheItemPo get(long configId);

    /**
     * 所有－有效的券配置ID列表写入redis
     *
     * @param list NoCodeConfigIdListCachePo
     */
    void setValidNoCodeConfigIdList(ConfigIdListCachePo list);

    /**
     * 所有－有效的券配置ID列表读redis
     *
     * @return List<Long>
     */
    ConfigIdListCachePo getNoCodeValidConfigIdList();

}

