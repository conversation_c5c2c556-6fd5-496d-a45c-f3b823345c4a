package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/25 09:45
 */
@Component
public class CouponCheckFactory {
    /**
     * 立减校验Action 工厂
     * Map<BizPlatformEnum, DirectReduceCheckAction>
     */
    private static final Map<BizPlatformEnum, DirectReduceCheckAction> DIRECT_REDUCE_CHECK_ACTION_MAP = new HashMap<>();

    /**
     * n元券校验Action 工厂
     * Map<BizPlatformEnum, NyuanBuyCheckAction>
     */
    private static final Map<BizPlatformEnum, NyuanBuyCheckAction> NYUAN_BUY_CHECK_ACTION_MAP = new HashMap<>();
    /**
     * n元券校验Action 工厂
     * Map<BizPlatformEnum, NyuanBuyCheckAction>
     */
    private static final Map<BizPlatformEnum, GiftBuyCheckAction> GIFT_BUY_CHECK_ACTION_MAP = new HashMap<>();

    /**
     * 满减校验Action 工厂
     * Map<BizPlatformEnum, ConditionReduceCheckAction>
     */
    private static final Map<BizPlatformEnum, ConditionReduceCheckAction> CONDITION_REDUCE_CHECK_ACTION_MAP = new HashMap<>();

    /**
     * 满折校验Action 工厂
     * Map<BizPlatformEnum, ConditionReduceCheckAction>
     */
    private static final Map<BizPlatformEnum, ConditionDiscountCheckAction> CONDITION_DISCOUNT_CHECK_ACTION_MAP = new HashMap<>();

    public static void register(BizPlatformEnum platformEnum, CouponConfigBaseCheck configBaseCheck) {
        if (Objects.isNull(platformEnum) || Objects.isNull(configBaseCheck)) {
            return;
        }

        // 直降
        if (configBaseCheck instanceof DirectReduceCheckAction) {
            DIRECT_REDUCE_CHECK_ACTION_MAP.put(platformEnum, (DirectReduceCheckAction) configBaseCheck);
        }

        // n元券
        if (configBaseCheck instanceof NyuanBuyCheckAction) {
            NYUAN_BUY_CHECK_ACTION_MAP.put(platformEnum, (NyuanBuyCheckAction) configBaseCheck);
        }

        // 满减
        if (configBaseCheck instanceof ConditionReduceCheckAction) {
            CONDITION_REDUCE_CHECK_ACTION_MAP.put(platformEnum, (ConditionReduceCheckAction) configBaseCheck);
        }

        // 满折
        if (configBaseCheck instanceof ConditionDiscountCheckAction) {
            CONDITION_DISCOUNT_CHECK_ACTION_MAP.put(platformEnum, (ConditionDiscountCheckAction) configBaseCheck);
        }
        // 礼品券
        if (configBaseCheck instanceof GiftBuyCheckAction) {
            GIFT_BUY_CHECK_ACTION_MAP.put(platformEnum, (GiftBuyCheckAction) configBaseCheck);
        }
    }

    public CouponConfigBaseCheck getCouponConfigBaseCheck(BizPlatformEnum platformEnum, PromotionTypeEnum promotionTypeEnum) {
        if (Objects.isNull(platformEnum) || Objects.isNull(promotionTypeEnum)) {
            return null;
        }

        // 直降
        if (Objects.equals(PromotionTypeEnum.DirectReduce, promotionTypeEnum)) {
            return DIRECT_REDUCE_CHECK_ACTION_MAP.get(platformEnum);
        }

        // 礼品券
        if (Objects.equals(PromotionTypeEnum.NyuanBuy, promotionTypeEnum)) {
            return NYUAN_BUY_CHECK_ACTION_MAP.get(platformEnum);
        }

        // 满减
        if (Objects.equals(PromotionTypeEnum.ConditionReduce, promotionTypeEnum)) {
            return CONDITION_REDUCE_CHECK_ACTION_MAP.get(platformEnum);
        }

        // 满折
        if (Objects.equals(PromotionTypeEnum.ConditionDiscount, promotionTypeEnum)) {
            return CONDITION_DISCOUNT_CHECK_ACTION_MAP.get(platformEnum);
        }

        // n元券
        if (Objects.equals(PromotionTypeEnum.GIFT, promotionTypeEnum)) {
            return GIFT_BUY_CHECK_ACTION_MAP.get(platformEnum);
        }

        return null;
    }
}
