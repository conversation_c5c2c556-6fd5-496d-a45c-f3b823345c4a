package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.warning.handler;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.warning.WarnCheckResult;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.WarningType;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @description: 低库存校验
 * @author: hejiapeng
 * @Date 2022/7/11 9:04 下午
 * @Version: 1.0
 **/
@Component
public class LowStockWarningCheckHandler extends AbstractWarningCheckHandler {

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Override
    public WarnCheckResult doWarningCheck(CouponConfigPO couponConfigPO) throws BizError {

        WarnCheckResult warnCheckResult;
        AbstractWarningCheckHandler next = super.next();
        if (next != null) {
            warnCheckResult = next.doWarningCheck(couponConfigPO);
        } else {
            warnCheckResult = new WarnCheckResult();
        }

        LowStockEnum lowStockEnum = LowStockEnum.getLowStockEnumByCount(couponConfigPO.getApplyCount());

        if (lowStockEnum == null) {
            return warnCheckResult;
        }

        Map<Long,Long> stockMap = couponConfigRepository.getCouponSendCount(Lists.newArrayList(couponConfigPO.getId()));

        double stockRatio = (double)(couponConfigPO.getApplyCount() - stockMap.get(couponConfigPO.getId())) / couponConfigPO.getApplyCount();

        if (stockRatio < lowStockEnum.ratio) {
            warnCheckResult.getCodes().add(WarningType.LOW_STOCK.getCode());
        }
        return warnCheckResult;
    }

    private enum LowStockEnum{

        TEN_PERCENT(20, 0.1),
        FIVE_PERCENT(500, 0.05),
        ONE_PERCENT(10000, 0.01);

        private int thresholdNum;
        private double ratio;

        LowStockEnum(int thresholdNum, double ratio) {
            this.thresholdNum = thresholdNum;
            this.ratio = ratio;
        }

        private static  LowStockEnum getLowStockEnumByCount(Integer applyCount) {
            if (TEN_PERCENT.thresholdNum > applyCount) {
                return null;
            }
            if (FIVE_PERCENT.thresholdNum > applyCount) {
                return TEN_PERCENT;
            }
            if (ONE_PERCENT.thresholdNum > applyCount) {
                return FIVE_PERCENT;
            }
            return ONE_PERCENT;
        }

    }
}
