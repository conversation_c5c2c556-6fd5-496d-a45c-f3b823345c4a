package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.adsTidb.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 优惠券统计分析数据
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/5/25 4:18 下午
 * @Version: 1.0
 **/
@Data
public class CouponStatisticPo implements Serializable {

    /**
     * 券id
     */
    private Integer couponId;

    /**
     * 活动编号
     */
    private String actId;

    /**
     * 发放日期
     */
    private Date applyTime;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 折扣类型ID
     */
    private Integer couponTypeId;


    private String couponTypeName;

    private String usageCondition;

    private String sendScene;

    private String sendMode;

    private Integer applyCnt;

    private Integer fetchCnt;

    private Integer usedCnt;

    private String usedChannel;

    private BigDecimal reduceAmt;

    private BigDecimal ordAmt;
}
