package com.xiaomi.nr.coupon.admin.enums.couponconfig;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum CouponTypeEnum {
    /**
     * 1: 商品券
     */
    GOODS(1, "商品券"),

    /**
     * 2: 运费券
     */
    POSTFREE(2, "运费券"),

    /**
     * 3: 超级补贴券
     */
    SUBSIDY(3, "超级补贴券"),

    /**
     * 11: 抵扣券
     */
    DEDUCTION(11, "抵扣券"),

    /**
     * 12：不限次服务卡
     */
    UNLIMITED_SERVICE_CARD(12, "不限次服务卡"),
    ;

    private final Integer value;
    private final String name;

    CouponTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static CouponTypeEnum getByValue(int value) {
        CouponTypeEnum[] values = CouponTypeEnum.values();
        for (CouponTypeEnum item : values) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }


    public static String getNameByValue(int value) {
        CouponTypeEnum[] values = CouponTypeEnum.values();
        for (CouponTypeEnum item : values) {
            if (item.getValue().equals(value)) {
                return item.getName();
            }
        }
        return StringUtils.EMPTY;
    }

}
