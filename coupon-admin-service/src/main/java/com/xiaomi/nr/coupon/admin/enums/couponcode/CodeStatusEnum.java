package com.xiaomi.nr.coupon.admin.enums.couponcode;

/**
 * 优惠码状态枚举
 */
public enum CodeStatusEnum {

    /**
     * 未使用
     */
    UNUSED(1,"未使用"),

    /**
     * 已使用
     */
    USED(2, "已使用"),

    /**
     * 已过期
     */
    EXPIRED(5,"已过期");



    private final int value;
    private final String desc;

    CodeStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return this.value;
    }

    public String getDesc() {
        return this.desc;
    }

    public static String findDescByValue(int value) {
        CodeStatusEnum[] values = CodeStatusEnum.values();
        for (CodeStatusEnum item : values) {
            if (value == item.getValue()) {
                return item.getDesc();
            }
        }
        return null;
    }


    public static CodeStatusEnum findEnumByValue(int value) {
        CodeStatusEnum[] values = CodeStatusEnum.values();
        for (CodeStatusEnum item : values) {
            if (value == item.getValue()) {
                return item;
            }
        }
        return null;
    }

}
