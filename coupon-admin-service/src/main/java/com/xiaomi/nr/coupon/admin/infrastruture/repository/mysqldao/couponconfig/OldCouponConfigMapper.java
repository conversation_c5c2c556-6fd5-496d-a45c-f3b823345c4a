package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig.po.CouponConfigPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 优惠券配置mapper
 *
 * <AUTHOR>
 */
@Mapper
@Component
@Deprecated
public interface OldCouponConfigMapper {

    /**
     * 券配置表的所有字段，不能随便动
     */
    String configAllColumn = "id, name, name_desc, range_desc, value_desc, type, deduct_type, area_id, gallery_url, offline," +
            "is_code, check_price, check_package, client, goods_include, goods_inexclude, goods_exclude, ext_prop," +
            "quota, policy, cond, department_id, costcenter_id, agent_departments, send_limit, nowcount," +
            "global_start_time, global_end_time, sku_list, add_time, stat, range_long_desc, modify_time, send_channel, last_update_time," +
            "use_channel";

    /**
     * 获取有效券配置表的部分字段
     */
    String configSelectColumn = "id, name, range_desc, type, deduct_type, offline, " +
            "is_code, check_price, check_package, client, goods_include, goods_inexclude, ext_prop, quota, policy, department_id, send_limit, " +
            "global_start_time, global_end_time, add_time, stat, modify_time, send_channel, last_update_time, " +
            "use_channel";


    /**
     * 获取所有未结束的优惠券配置列表（部分信息）
     *
     * @param nowTime long
     * @return ArrayList<CouponConfig>
     */
    @Select("select id, name, goods_include, add_time, modify_time" +
            " from v3_coupon_type" +
            " where global_end_time>#{nowTime,jdbcType=BIGINT} and stat='approved' and area_id=2" +
            " order by id asc")
    ArrayList<CouponConfigPo> getNotEndApprovedList(@Param("nowTime") long nowTime);

    /**
     * 获取指定优惠券配置列表（部分信息）
     *
     * @param ids List<Long>
     * @return ArrayList<CouponConfigPo>
     */
    @Select("<script>select id, name, goods_include, add_time, modify_time" +
            " from v3_coupon_type" +
            " where id in <foreach collection='ids' item='id' index='index' open='(' close=')' separator=','>#{id}</foreach></script>")
    ArrayList<CouponConfigPo> getAssignConfig(@Param("ids") List<Long> ids);


    /**
     * 获取需要生成缓存的优惠券配置列表（过期不超过24小时）
     *
     * @param timeStart long
     * @param startSize long
     * @param pageSize long
     * @return ArrayList<CouponConfigPo>
     */
    @Select("select " + configSelectColumn +
            " from v3_coupon_type" +
            " where global_end_time>#{timeStart,jdbcType=BIGINT} and stat='approved' and area_id=2" +
            " order by id asc" +
            " limit #{startSize,jdbcType=BIGINT},#{pageSize,jdbcType=BIGINT}")
    ArrayList<CouponConfigPo> getCacheConfigListForValidityPeriod(@Param("timeStart") long timeStart, @Param("startSize") long startSize, @Param("pageSize") long pageSize);



    /**
     * 获取需要生成缓存的优惠券配置列表（24小时内发生过变更的）
     *
     * @param timeStart String 可以定时间时间节点进行全量刷新
     * @param startSize long
     * @param pageSize long
     * @return ArrayList<CouponConfigPo>
     */
    @Select("select " + configSelectColumn +
            " from v3_coupon_type" +
            " where last_update_time>#{timeStart,jdbcType=TIMESTAMP} and stat in('approved', 'cancel') and area_id=2" +
            " order by id asc" +
            " limit #{startSize,jdbcType=BIGINT},#{pageSize,jdbcType=BIGINT}")
    ArrayList<CouponConfigPo> getCacheConfigListForUpdateTime(@Param("timeStart") String timeStart, @Param("startSize") long startSize, @Param("pageSize") long pageSize);


    /**
     * 有效包括过期近90天以内的券都取出来放本地缓存
     *
     * @param nearlyDaysTime
     * @return
     */
    @Select("select id from v3_coupon_type where area_id=2 and  global_end_time > #{nearlyDaysTime} and global_end_time < #{nowTime}  and stat in('approved', 'cancel')")
    ArrayList<Long> getConfigIds(@Param("nearlyDaysTime") long nearlyDaysTime, @Param("nowTime") long nowTime);


    @Select("select id from v3_coupon_type where area_id=2 and stat in('approved', 'cancel') and last_update_time> #{updateTime} and is_code=1")
    List<Long> getConfigIdByUpdateTime(@Param("updateTime") String updateTime);

    @Select("<script>select * from v3_coupon_type" +
            " where id in <foreach collection='ids' item='id' index='index' open='(' close=')' separator=','>#{id}</foreach></script>")
    List<CouponConfigPo> getConfigByIds(@Param("ids") List<Long> ids);


    @Select("select distinct m.type_id from v3_coupon_type t  join v3_coupon_mission m on t.id = m.type_id " +
            "where  t.global_end_time > #{nowTime} group by m.type_id having count(1) > 1;")
    List<Long> getMoreMissionCoupon(@Param("nowTime") long nowTime);

    @Select("<script>" +
            "select id,goods_include from v3_coupon_type " +
            " where area_id=2 " +
            " and id in <foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>#{id}</foreach>" +
            "</script>")
    List<CouponConfigPo> getRefreshCoupons(@Param("ids") Set<Long> ids);

    /**
     * 获取有效券id
     * @param ids
     * @return
     */
    @Select("<script>" +
            "select id from v3_coupon_type " +
            " where area_id=2 " +
            " and id in <foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>#{id}</foreach>" +
            "</script>")
    List<Long> getRefreshCouponIds(@Param("ids") Set<Long> ids);


}
