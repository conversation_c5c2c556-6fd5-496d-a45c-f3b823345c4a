package com.xiaomi.nr.coupon.admin.enums;


import java.util.HashMap;

public enum Env {

    LOCAL("local", "本地开发环境"),
    DEV("dev", "联调开发环境"),
    TEST("test", "测试环境"),
    PRE("pre", "预上线环境"),
    SMALL("small", "小流量环境"),
    PRO("pro", "生产环境");

    private static final HashMap<String, Env> MAPPING = new HashMap();
    private String code;
    private String name;

    private Env(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    static {
       Env[] var0 = values();
        int var1 = var0.length;

        for(int var2 = 0; var2 < var1; ++var2) {
            Env env = var0[var2];
            MAPPING.put(env.getCode(), env);
        }

    }
}
