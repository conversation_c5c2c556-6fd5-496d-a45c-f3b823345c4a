package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.userredpacket.po;

import lombok.Data;

import java.io.Serializable;

@Data
public class SearchUserRedpacketListParam implements Serializable {
    private static final long serialVersionUID = 8741304516698345964L;

    /**
     * uid
     */
    private Long userId;

    /**
     * 红包类型id
     */
    private Long typeId;

    /**
     * 用户红包ID
     */
    private Long redpacketId;

    /**
     * 红包状态
     */
    private String status;

    /**
     * 当前页码
     */
    private int offset;
    /**
     * 页面条数
     */
    private int limit;

    /**
     * 排序字段
     */
    private String orderBy = "type_id";

    /**
     * 排序顺序  desc倒序   asc顺序
     */
    private String orderDirection = "desc";




}
