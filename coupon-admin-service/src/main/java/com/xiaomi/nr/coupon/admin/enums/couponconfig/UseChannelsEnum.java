package com.xiaomi.nr.coupon.admin.enums.couponconfig;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import lombok.Getter;

@Getter
public enum UseChannelsEnum {
    XIAOMI_SHOP(1, "shangcheng","mishop","小米商城", BizPlatformEnum.RETAIL),
    DIRECTSALE_STORE(2,"zhiying","zy","直营店", BizPlatformEnum.RETAIL),
    EXCLUSIVE_SHOP(3,"zhuanmai", "zm","专卖店", BizPlatformEnum.RETAIL),
    AUTHORIZED_STORE(4,"shouquan", "sq","授权店", BizPlatformEnum.RETAIL),
    FORTRESS_STORE(5,"baolei", "bl","堡垒店", BizPlatformEnum.RETAIL),
    CAR_SHOP(6, "carshop",  "cs", "车商城", BizPlatformEnum.CAR_SHOP),
    ;

    private final int value;
    private final String bpmValue;
    private final String adsValue;
    private final String name;
    private final BizPlatformEnum bizPlatformEnum;

    UseChannelsEnum(int value,String bpmValue, String adsValue, String name, BizPlatformEnum bizPlatformEnum) {
        this.value = value;
        this.bpmValue = bpmValue;
        this.adsValue = adsValue;
        this.name = name;
        this.bizPlatformEnum = bizPlatformEnum;
    }

    public static UseChannelsEnum getByValue(int value) {
        UseChannelsEnum[] values = UseChannelsEnum.values();
        for (UseChannelsEnum item : values) {
            if (item.getValue()==value) {
                return item;
            }
        }
        return null;
    }

    public static String getNameByValue(int value) {
        UseChannelsEnum[] values = UseChannelsEnum.values();
        for (UseChannelsEnum item : values) {
            if (item.getValue()==value) {
                return item.getName();
            }
        }
        return null;
    }

    public static UseChannelsEnum getByAdsValue(String value) {
        UseChannelsEnum[] values = UseChannelsEnum.values();
        for (UseChannelsEnum item : values) {
            if (item.getAdsValue().equals(value)) {
                return item;
            }
        }
        return null;
    }

    public static BizPlatformEnum getBizByChannel(int code) {
        UseChannelsEnum[] values = UseChannelsEnum.values();
        for (UseChannelsEnum item : values) {
            if (item.getValue() == code) {
                return item.getBizPlatformEnum();
            }
        }
        return null;
    }
}
