package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.car;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.CouponCheckFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.DirectReduceCheckAction;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2023/12/25 10:21
 */
@Component
public class DirectReduceCarCheckAction extends DirectReduceCheckAction {

    /**
     * 初始化（用来工厂和策略模式的注册）
     */
    @PostConstruct
    @Override
    public void init() {
        CouponCheckFactory.register(BizPlatformEnum.CAR, this);
    }

    /**
     * 券创建特殊校验
     */
    @Override
    public void createSpecialCheck(CouponBaseInfo info) throws BizError {
        if (info.getPromotionValue() < 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠值必须大于0");
        }
    }

    /**
     * 券修改特殊校验
     */
    @Override
    public void updateSpecialCheck(CouponBaseInfo info) throws BizError {
        if (info.getPromotionValue() < 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠值必须大于0");
        }
    }

    /**
     * 商品校验
     */
    @Override
    public void goodsCheck(CouponConfigItem configItem) {

    }

    /**
     * 业务平台类型
     */
    @Override
    public BizPlatformEnum getBizPlatformEnum() {
        return BizPlatformEnum.CAR;
    }
}
