package com.xiaomi.nr.coupon.admin.util.beancopy;

import ma.glasnost.orika.MappingContext;
import ma.glasnost.orika.converter.BidirectionalConverter;
import ma.glasnost.orika.metadata.Type;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class ListLong2StringCustomerConverter extends BidirectionalConverter<List<Long>,String> {

    @Override
    public String convertTo(List<Long> list, Type<String> type, MappingContext mappingContext) {
        return StringUtils.join(list,",");
    }

    @Override
    public List<Long> convertFrom(String s, Type<List<Long>> type, MappingContext mappingContext) {
        if(StringUtils.isBlank(s)){
            return new ArrayList<>();
        }
        return Stream.of(s.split(",")).map(Long::parseLong).collect(Collectors.toList());
    }
}
