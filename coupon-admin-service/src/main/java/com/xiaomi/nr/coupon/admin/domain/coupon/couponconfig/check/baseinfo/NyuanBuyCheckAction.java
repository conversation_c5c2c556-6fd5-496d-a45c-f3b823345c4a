package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2023/12/25 10:27
 */
public abstract class NyuanBuyCheckAction extends CouponConfigBaseCheck {
    @Autowired
    private CouponConfigRepository couponConfigRepository;

    /**
     * 券创建基础校验
     */
    @Override
    public void createCommonCheck(CouponBaseInfo info) throws BizError {
        // 券创建、修改公共校验
        commonCheck(info);

        // 商品券类型校验
        couponTypeCheck(info);

        if (info.getId() > 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券id必须为空");
        }

        if (info.getPromotionValue() > 9999999999L) {
            throw ExceptionHelper.create(ErrCode.COUPON, "n元券最大为99999999.99元");
        }
    }

    /**
     * 券优惠类型
     */
    @Override
    public PromotionTypeEnum getPromotionType() {
        return PromotionTypeEnum.NyuanBuy;
    }

}
