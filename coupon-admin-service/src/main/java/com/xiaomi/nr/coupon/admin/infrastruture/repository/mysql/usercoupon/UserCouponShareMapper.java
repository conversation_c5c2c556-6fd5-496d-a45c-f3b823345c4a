package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.UserCouponSharePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * 用户优惠券mapper
 *
 * <AUTHOR>
 */
@Mapper
@Component
public interface UserCouponShareMapper {


    @Select("<script> select * from tb_coupon_gift where user_id=#{uid} and coupon_id = #{couponId} and status =#{status}</script>")
    UserCouponSharePO selectByUidAndStatus(@Param("uid") long uid,@Param("couponId") long couponId, @Param("status") int status);

    @Select("<script> select user_id,recipient_uid,giving_time,recipient_time,new_coupon_id from tb_coupon_gift where coupon_id = #{couponId} and user_id=#{uid}</script>")
    List<UserCouponSharePO> selectByUid(@Param("uid") long uid, @Param("couponId") long couponId);



}
