package com.xiaomi.nr.coupon.admin.enums.ecard;

/**
 * 礼品卡类型 枚举
 *
 * <AUTHOR>
 */
public enum EcardVirtualEnum {
    /**
     * 虚拟卡
     */
    isVirtual(1, "虚拟卡"),

    /**
     * 实物卡
     */
    noVirtual(0, "实物卡");


    private final Integer value;
    private final String name;

    EcardVirtualEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static EcardVirtualEnum findByValue(Integer value) {
        EcardVirtualEnum[] values = EcardVirtualEnum.values();
        for (EcardVirtualEnum ecardVirtualEnum : values) {
            if (value.equals(ecardVirtualEnum.value)) {
                return ecardVirtualEnum;
            }
        }
        return null;
    }
}
