package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale.tools;

import com.xiaomi.nr.coupon.admin.api.enums.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale.ServiceCouponCheckTools;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale.ServiceCouponCheckToolsFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.TimesLimitEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2025/1/20 15:18
 */
@Component
public class GlassRepairCouponCheckTools extends ServiceCouponCheckTools {

    @PostConstruct
    public void init() {
        ServiceCouponCheckToolsFactory.register(CouponServiceTypeEnum.GLASS_REPAIR, this);
    }


    @Override
    public void goodsCheck(Map<Long, Integer> labourHourSsu, Map<Long, Integer> partsSsu) throws BizError {
        // 服务类型为漆面修复
        if (MapUtils.isEmpty(labourHourSsu)) {
            // 工时ssu不能为空
            throw ExceptionHelper.create(ErrCode.COUPON, "工时ssu不能为空");
        }

    }

    @Override
    public void createSpecialCheck(CouponBaseInfo info) throws BizError {
        // 券类型校验
        couponTypeCheck(info);

        // 使用次数限制
        timesLimitCheck(info);
    }

    @Override
    public void updateSpecialCheck(CouponBaseInfo info) throws BizError {
        // 券类型校验
        couponTypeCheck(info);

        // 使用次数限制
        timesLimitCheck(info);
    }


    /**
     * 券类型校验
     *
     * @param info 券配置基础信息
     */
    public void couponTypeCheck(CouponBaseInfo info) throws BizError {
        // 券类型校验
        CouponTypeEnum couponTypeEnum = CouponTypeEnum.getByValue(info.getCouponType());

        if (null == couponTypeEnum) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券类型不存在");
        }

        // 玻璃无忧券，券类型必须为不限次服务卡
        if (!Objects.equals(couponTypeEnum, CouponTypeEnum.DEDUCTION)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券类型必须为抵扣券");
        }
    }

    /**
     * 使用次数校验
     *
     * @param info 券配置基础信息
     */
    public void timesLimitCheck(CouponBaseInfo info) throws BizError {
        TimesLimitEnum timesLimitEnum = TimesLimitEnum.valueOf(info.getTimesLimit());

        if (null == timesLimitEnum) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用次数限制参数异常");
        }

        // 玻璃无忧券，使用次数限制为不限次
        if (!Objects.equals(timesLimitEnum, TimesLimitEnum.LIMIT)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用次数限制必须为有限制");
        }
    }
}
