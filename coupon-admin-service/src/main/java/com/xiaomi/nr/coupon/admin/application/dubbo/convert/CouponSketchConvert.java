package com.xiaomi.nr.coupon.admin.application.dubbo.convert;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponSketchListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request.CouponCreateSketchRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request.CouponSketchListRequest;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.enums.sketch.CouponSketchOptType;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.sketch.po.CouponSketchListParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.sketch.po.CouponSketchPO;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @Description: 优惠券草稿
 * @Date: 2022.03.12 13:52
 */
@Slf4j
@Component
public class CouponSketchConvert {

    public List<CouponSketchListVO> convertToVOList(List<CouponSketchPO> couponSketchPOList) {
        List<CouponSketchListVO> couponSketchListVOList = new ArrayList<>();
        for (CouponSketchPO couponSketchPO : couponSketchPOList) {
            CouponSketchListVO couponSketchListVO = new CouponSketchListVO();
            couponSketchListVO.setSketchId(couponSketchPO.getId());
            couponSketchListVO.setCouponName(couponSketchPO.getCouponName());
            try {
                CouponConfigVO couponConfigVO = GsonUtil.fromJson(CompressUtil.decompress(couponSketchPO.getConfigCompress()),CouponConfigVO.class);
                couponSketchListVO.setPromotionType(couponConfigVO.getPromotionRuleVO().getPromotionType());
                couponSketchListVO.setBottomCount(couponConfigVO.getPromotionRuleVO().getBottomCount());
            } catch (Exception e) {
                log.error("CouponSketchConvert convertToVOList couponSketchPO:{}",couponSketchPO,e);
            }
            couponSketchListVO.setPromotionValue(couponSketchPO.getPromotionValue());
            couponSketchListVO.setBottomType(couponSketchPO.getBottomType());
            couponSketchListVO.setBottomPrice(couponSketchPO.getBottomPrice());
            couponSketchListVO.setStartFetchTime(new Date(couponSketchPO.getStartFetchTime() * 1000));
            couponSketchListVO.setEndFetchTime(new Date(couponSketchPO.getEndFetchTime() * 1000));
            couponSketchListVO.setCreator(couponSketchPO.getCreator());
            couponSketchListVO.setCreateTime(couponSketchPO.getCreateTime());
            couponSketchListVOList.add(couponSketchListVO);
        }
        return couponSketchListVOList;
    }

    public CouponSketchListParam convertToParam(CouponSketchListRequest request) {
        CouponSketchListParam couponSketchListParam = new CouponSketchListParam();
        couponSketchListParam.setSketchId(request.getSketchId());
        couponSketchListParam.setCouponType(request.getCouponType());
        couponSketchListParam.setCouponName(request.getCouponName());
        if (request.getStartFetchTime() != null) {
            couponSketchListParam.setStartFetchTime(request.getStartFetchTime().getTime() / 1000);
        }
        if (request.getEndFetchTime() != null) {
            couponSketchListParam.setEndFetchTime(request.getEndFetchTime().getTime() / 1000);
        }
        couponSketchListParam.setCreator(request.getCreator());
        couponSketchListParam.setStartCreateTime(request.getStartCreateTime());
        couponSketchListParam.setEndCreateTime(request.getEndCreateTime());
        couponSketchListParam.setOrderBy(request.getOrderByMap().containsKey(request.getOrderBy())?request.getOrderByMap().get(request.getOrderBy()):request.getOrderBy());
        couponSketchListParam.setOrderDirection(request.getOrderDirection());
        couponSketchListParam.setOffset((request.getPageNo() - 1) * request.getPageSize());
        couponSketchListParam.setLimit(request.getPageSize());
        return couponSketchListParam;
    }

    public CouponSketchPO convertTOPO(CouponCreateSketchRequest request) throws Exception {
        CouponSketchPO couponSketchPO = new CouponSketchPO();
        CouponConfigVO couponConfigVO = request.getCouponConfigVO();
        if (request.getType() == CouponSketchOptType.UPDATE.getCode()) {
            couponSketchPO.setId(request.getSketchId());
        }
        couponSketchPO.setCouponType(couponConfigVO.getCouponType());
        couponSketchPO.setCouponName(couponConfigVO.getName());
        couponSketchPO.setBizPlatform(request.getBizType());
        couponSketchPO.setStartFetchTime(couponConfigVO.getStartFetchTime().getTime() / 1000);
        couponSketchPO.setEndFetchTime(couponConfigVO.getEndFetchTime().getTime() / 1000);
        couponSketchPO.setPromotionValue(couponConfigVO.getPromotionRuleVO().getPromotionValue());
        couponSketchPO.setBottomType(couponConfigVO.getPromotionRuleVO().getBottomType());
        couponSketchPO.setBottomPrice(couponConfigVO.getPromotionRuleVO().getBottomPrice());
        couponSketchPO.setApplyCount(couponConfigVO.getDistributionRuleVO().getApplyCount());
        couponSketchPO.setConfigCompress(CompressUtil.compress(GsonUtil.toJson(couponConfigVO)));
        couponSketchPO.setApplyAttachment(GsonUtil.toJson(request.getApplyAttachment()));
        couponSketchPO.setDepartmentId(couponConfigVO.getDepartmentId());
        couponSketchPO.setCreator(couponConfigVO.getCreator());

        return couponSketchPO;
    }
}
