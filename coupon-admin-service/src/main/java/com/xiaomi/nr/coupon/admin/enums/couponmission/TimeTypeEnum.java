package com.xiaomi.nr.coupon.admin.enums.couponmission;

/**
 * 券的真实有效期类别 枚举
 *
 * <AUTHOR>
 */
public enum TimeTypeEnum {

    /**
     * 时间区间内有效
     */
    SECTION("section", "时间区间"),

    /**
     * 发放后N天内有效
     */
    DAYS("days", "发放后N天");

    private final String redisValue;
    private final String name;

    TimeTypeEnum(String redisValue, String name) {
        this.redisValue = redisValue;
        this.name = name;
    }

    public String getRedisValue() {
        return this.redisValue;
    }

    public String getName() {
        return name;
    }

    public static String findNameByRedisValue(String value) {
        TimeTypeEnum[] values = TimeTypeEnum.values();
        for (TimeTypeEnum item : values) {
            if (item.getRedisValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }
}

