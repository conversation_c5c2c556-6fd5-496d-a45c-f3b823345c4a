package com.xiaomi.nr.coupon.admin.enums.scene;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.kerby.config.IniConfigLoader;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 优惠券场景枚举
 * @Date: 2022.03.04 15:25
 */
@Getter
public enum SceneEnum {
    // 3C场景
    Official_Marking(1, "官方营销活动", BizPlatformEnum.RETAIL.getCode()),
    Enterprise_Cooperation(2, "外部异业合作", BizPlatformEnum.RETAIL.getCode()),
    After_Sale_Service(3, "售后服务", BizPlatformEnum.RETAIL.getCode()),
    Other(100, "其它", BizPlatformEnum.RETAIL.getCode()),

    // 汽车场景
    Car(101, "汽车", BizPlatformEnum.CAR.getCode()),

    // 汽车售后
    Car_Service(102, "汽车售后服务", BizPlatformEnum.CAR_AFTER_SALE.getCode()),

    // 车商城营销活动
    Car_Shop_Marketing(103, "车商城营销活动", BizPlatformEnum.CAR_SHOP.getCode()),

    // 购车/车主/会员/车商城 权益
    Car_Member_Benefits(104, "购车/车主/会员/车商城 权益", BizPlatformEnum.CAR_SHOP.getCode()),
    ;


    /**
     * 编码
     */
    private final int code;
    /**
     * 描述
     */
    private final String desc;
    /**
     * 业务平台code
     */
    private final int bizPlatform;

    SceneEnum(int code, String desc, int bizPlatform) {
        this.code = code;
        this.desc = desc;
        this.bizPlatform = bizPlatform;
    }

    public static SceneEnum getByCode(int code) {
        SceneEnum[] values = SceneEnum.values();
        for (SceneEnum item : values) {
            if (item.getCode() == code) {
                return item;
            }
        }
        return null;
    }

    private static final Map<Integer, List<SceneEnum>> BIZ_PLATFORM_MAPPING = Arrays.stream(SceneEnum.values())
            .collect(Collectors.groupingBy(SceneEnum::getBizPlatform));

    public static List<SceneEnum> getSceneList(Integer bizPlatform) {
        return getSceneList(Lists.newArrayList(bizPlatform));
    }

    public static List<SceneEnum> getSceneList(List<Integer> bizPlatformList) {
        List<SceneEnum> sceneEnumList = Lists.newArrayList();
        for (Integer biz : bizPlatformList) {
            List<SceneEnum> scl = BIZ_PLATFORM_MAPPING.get(biz);
            if (CollectionUtils.isNotEmpty(scl)) {
                sceneEnumList.addAll(scl);
            }
        }
        return sceneEnumList;
    }

    public static void main(String[] args) {
        System.out.println(BIZ_PLATFORM_MAPPING);
    }

}
