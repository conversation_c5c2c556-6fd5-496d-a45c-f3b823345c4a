package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponactivity.impl;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponactivity.CouponActivityRedisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

/**
 * 领券活动配置redis缓存操作对象
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CouponActivityRedisDaoImpl implements CouponActivityRedisDao {
    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringMiscRedisTemplate")
    private StringRedisTemplate redisTemplate;

    private static final String KEY_COUPON_ACTIVITY_CONFIG_CACHE = "pulse_getcoupon_event_new_v2";


    @Override
    public void set(String data) {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        operations.set(KEY_COUPON_ACTIVITY_CONFIG_CACHE, data);
    }

}