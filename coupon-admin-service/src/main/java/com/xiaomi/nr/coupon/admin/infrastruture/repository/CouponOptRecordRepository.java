package com.xiaomi.nr.coupon.admin.infrastruture.repository;

import com.github.pagehelper.PageHelper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.optrecord.CouponOptRecordMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.optrecord.po.CouponOptRecordPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

/**
 * 补偿表
 */
@Component
public class CouponOptRecordRepository {

    @Autowired
    private CouponOptRecordMapper couponOptRecordMapper;


    /**
     * 新增
     * @param record
     * @return
     */
    public long insert(CouponOptRecordPO record){
        couponOptRecordMapper.insert(record);
        return record.getId();
    }

    /**
     * 根据id查询记录
     * @param id
     * @return
     */
    public CouponOptRecordPO selectById(Long id){
        return couponOptRecordMapper.selectById(id);
    }

    /**
     * 根据状态查询记录
     * @return
     */
    public List<CouponOptRecordPO> selectByStatus(Timestamp createTime, int pageNo, int pageSize){
        PageHelper.startPage(pageNo, pageSize);
        return couponOptRecordMapper.selectByStatus(createTime);
    }


    /**
     * 根据id修改记录
     * @param id
     * @return
     */
    public Integer updateStatusById(Long id,int status){
       return couponOptRecordMapper.updateStatusById(id,status);
    }


    /**
     * 修改
     * @param record
     * @return
     */
    public void updateById(CouponOptRecordPO record){
        couponOptRecordMapper.updateById(record);
    }




}
