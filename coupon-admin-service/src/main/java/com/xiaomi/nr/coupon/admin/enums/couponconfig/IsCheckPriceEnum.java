package com.xiaomi.nr.coupon.admin.enums.couponconfig;

/**
 * 是否调价商品可用 枚举
 *
 * <AUTHOR>
 */
public enum IsCheckPriceEnum {

    /**
     * 是
     */
    Yes(true, "2", "是"),

    /**
     * 否
     */
    No(false, "1", "否");

    private final Boolean redisValue;
    private final String mysqlValue;
    private final String name;

    IsCheckPriceEnum(Boolean redisValue, String mysqlValue, String name) {
        this.redisValue = redisValue;
        this.mysqlValue = mysqlValue;
        this.name = name;
    }

    public Boolean getRedisValue() {
        return this.redisValue;
    }

    public String getMysqlValue() {
        return this.mysqlValue;
    }

    public String getName() {
        return name;
    }

    public static String findNameByRedisValue(Boolean value) {
        IsCheckPriceEnum[] values = IsCheckPriceEnum.values();
        for (IsCheckPriceEnum item : values) {
            if (item.getRedisValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }

    public static Boolean findRedisValueByMysqlValue(String value) {
        IsCheckPriceEnum[] values = IsCheckPriceEnum.values();
        for (IsCheckPriceEnum item : values) {
            if (item.getMysqlValue().equals(value)) {
                return item.getRedisValue();
            }
        }
        return null;
    }
}

