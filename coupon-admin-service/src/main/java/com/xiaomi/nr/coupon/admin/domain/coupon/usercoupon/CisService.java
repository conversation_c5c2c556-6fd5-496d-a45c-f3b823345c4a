package com.xiaomi.nr.coupon.admin.domain.coupon.usercoupon;

import com.google.common.collect.Lists;
import com.xiaomi.nr.cis.api.dto.GetVinRelationMapRequest;
import com.xiaomi.nr.cis.api.dto.GetVinRelationMapResponse;
import com.xiaomi.nr.cis.api.dto.RelationItem;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.CisProxy;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * @description 整车中心服务
 * <AUTHOR>
 * @date 2024-12-25 21:01
*/
@Slf4j
@Component
public class CisService {

    CisProxy cisProxy;

    @Autowired
    public CisService(CisProxy cisProxy) {
        this.cisProxy = cisProxy;
    }

    /**
     * 根据vin查询vid
     *
     * @param vin vin
     * @return vid
     */
    public String getSingleVidByVin(String vin) throws BizError {
        if (StringUtils.isBlank(vin)) {
            return StringUtils.EMPTY;
        }
        GetVinRelationMapRequest request = GetVinRelationMapRequest.builder().vins(Lists.newArrayList(vin)).build();
        GetVinRelationMapResponse response = cisProxy.getVinRelationMap(request);
        if (Objects.isNull(response) || Objects.isNull(response.getVinRelationMap())) {
            log.error("CisService.getSingleVidByVin error, vin: {}, response: {}", vin, response);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "根据VIN查询VID失败");
        }
        RelationItem relationItem = response.getVinRelationMap().getOrDefault(vin, null);
        return Optional.ofNullable(relationItem).map(RelationItem::getVid).orElse(StringUtils.EMPTY);
    }
}
