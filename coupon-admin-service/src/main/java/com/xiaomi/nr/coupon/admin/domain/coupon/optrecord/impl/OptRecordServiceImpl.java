package com.xiaomi.nr.coupon.admin.domain.coupon.optrecord.impl;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.optrecord.OptRecordService;
import com.xiaomi.nr.coupon.admin.enums.optrecord.OptRecordTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponOptRecordRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.optrecord.po.CouponOptRecordPO;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 补偿操作
 */
@Slf4j
@Service
public class OptRecordServiceImpl implements OptRecordService {

    @Autowired
    private CouponOptRecordRepository couponOptRecordRepository;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;


    /**
     * 执行补偿
     */
    @Override
    public void compensateOptRecord() {

        List<CouponOptRecordPO> recordPOS = couponOptRecordRepository.selectByStatus(Timestamp.valueOf(LocalDateTime.now().minusMinutes(10)), 0, 100);
        if (CollectionUtils.isNotEmpty(recordPOS)) {
            for (CouponOptRecordPO couponOptRecordPO : recordPOS) {
                switch (OptRecordTypeEnum.findByValue(couponOptRecordPO.getOptType())) {
                    case ADD_COUPON:
                        try {
                            CouponCreateEvent couponCreateEvent =
                                    GsonUtil.fromJson(CompressUtil.decompress(couponOptRecordPO.getOptInfo()), CouponCreateEvent.class);
                            couponCreateEvent.setCompensateFlag(true);
                            couponCreateEvent.setRecordId(couponOptRecordPO.getId());
                            applicationEventPublisher.publishEvent(couponCreateEvent);
                        } catch (Exception e) {
                            log.error("compensateOptRecord couponOptRecordPO:{}", couponOptRecordPO, e);
                            return;
                        }
                        break;
                    case UPDATE_COUPON:
                        try {
                            CouponUpdateEvent couponUpdateEvent =
                                    GsonUtil.fromJson(CompressUtil.decompress(couponOptRecordPO.getOptInfo()), CouponUpdateEvent.class);
                            couponUpdateEvent.setCompensateFlag(true);
                            couponUpdateEvent.setRecordId(couponOptRecordPO.getId());
                            applicationEventPublisher.publishEvent(couponUpdateEvent);
                        } catch (Exception e) {
                            log.error("compensateOptRecord couponOptRecordPO:{}", couponOptRecordPO, e);
                            return;
                        }
                        break;
                    case UPDATE_COUPON_STATUS:
                        try {
                            CouponUpdateStatusEvent couponUpdateStatusEvent =
                                    GsonUtil.fromJson(CompressUtil.decompress(couponOptRecordPO.getOptInfo()), CouponUpdateStatusEvent.class);
                            couponUpdateStatusEvent.setCompensateFlag(true);
                            couponUpdateStatusEvent.setRecordId(couponOptRecordPO.getId());
                            applicationEventPublisher.publishEvent(couponUpdateStatusEvent);
                        } catch (Exception e) {
                            log.error("compensateOptRecord couponOptRecordPO:{}", couponOptRecordPO, e);
                            return;
                        }
                        break;
                    default:

                }
            }
        }
    }


}
