package com.xiaomi.nr.coupon.admin.enums.couponconfig;

import lombok.Getter;

@Getter
public enum SendPurposeEnum {
    /**
     * 0: 无
     */
    Default(0, "无"),

    /**
     * 1: 用户拉新
     */
    PullNew(1, "用户拉新"),

    /**
     * 2: 用户促活
     */
    Active(2, "用户促活"),

    /**
     * 3: 用户转化
     */
    Conversion(3, "用户转化"),

    /**
     * 4: 用户安抚
     */
    Pacify(4, "用户安抚"),

    /**
     * 5: 尾货清仓
     */
    Clearance(5, "尾货清仓"),

    /**
     * 6: 拉齐政策
     */
    EvenUp(6, "拉齐政策"),

    /**
     * 7: 员工福利
     */
    Welfare(7, "员工福利"),

    /**
     * 8: 功能测试
     */
    Test(8, "功能测试"),

    /**
     * 9: 汽车专用（不展示）
     */
    CAR(9, "汽车专用")
    ;

    private final int value;
    private final String name;

    SendPurposeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static SendPurposeEnum getByValue(int value) {
        SendPurposeEnum[] values = SendPurposeEnum.values();
        for (SendPurposeEnum item : values) {
            if (item.getValue()==value) {
                return item;
            }
        }
        return null;
    }
}
