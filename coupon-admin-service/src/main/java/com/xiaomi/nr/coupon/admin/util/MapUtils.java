package com.xiaomi.nr.coupon.admin.util;

import com.google.common.collect.Maps;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public class MapUtils {

    /**
     * 对象转map
     */
    public static Map<String, Object> objectToMap(Object object) throws Exception {
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = object.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            if(field.get(object) != null){
                map.put(field.getName(), field.get(object));
            }
        }
        return map;
    }


    public static List<Map<String, Object>> splitMap(String key, String value, Map<String, Object> map){

        List<Map<String, Object>> list = new LinkedList<>();
        if(org.apache.commons.collections.MapUtils.isEmpty(map)){
            return list;
        }

        Map<String, Object> tmpMap;
        for(Map.Entry<String, Object> item : map.entrySet()){
            tmpMap=Maps.newHashMap();
            tmpMap.put(key, item.getKey());
            tmpMap.put(value, item.getValue());
            list.add(tmpMap);
        }

        return list;
    }


}
