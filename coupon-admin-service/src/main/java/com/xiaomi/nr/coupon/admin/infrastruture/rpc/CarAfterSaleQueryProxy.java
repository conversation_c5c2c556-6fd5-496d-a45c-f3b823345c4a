package com.xiaomi.nr.coupon.admin.infrastruture.rpc;

import com.google.common.collect.Lists;
import com.xiaomi.goods.gis.api.PriceInfoService;
import com.xiaomi.nr.goods.tob.api.car.CarAfterSaleQueryService;
import com.xiaomi.nr.goods.tob.dto.common.PageDTO;
import com.xiaomi.nr.goods.tob.dto.common.PageResponse;
import com.xiaomi.nr.goods.tob.dto.request.aftersale.PageCarMaintenanceSsuRequest;
import com.xiaomi.nr.goods.tob.dto.response.ssu.car.aftersale.CarMaintenanceSsuDTO;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper.create;

/**
 * <AUTHOR>
 * @date 2023/12/26 18:52
 */
@Slf4j
@Service
public class CarAfterSaleQueryProxy {
    @Reference(check = false, interfaceClass = CarAfterSaleQueryService.class, group = "${dubbo.group}", version = "1.0", timeout = 5000)
    private CarAfterSaleQueryService carAfterSaleQueryService;

    /**
     * 售后ssu（工时&配件）信息查询
     *
     * @param ssuIdList ssuIdList
     * @return 售后ssu（工时&配件）信息
     */
    public List<CarMaintenanceSsuDTO> pageCarMaintenanceSsu(List<Long> ssuIdList) throws BizError {
        log.info("CarAfterSaleQueryService.pageCarMaintenanceSsu begin, ssuIdList = {}", ssuIdList);
        long startTime = System.currentTimeMillis();

        PageCarMaintenanceSsuRequest request = new PageCarMaintenanceSsuRequest();
        List<CarMaintenanceSsuDTO> result = Lists.newArrayList();
        try {

            for (List<Long> ssuIds : Lists.partition(ssuIdList, 100)) {
                PageDTO pageDTO = new PageDTO();
                pageDTO.setPageNum(1);
                pageDTO.setPageSize(ssuIds.size());
                pageDTO.setTotal(ssuIds.size());

                request.setSsuIdList(ssuIds);
                request.setPage(pageDTO);

                Result<PageResponse<CarMaintenanceSsuDTO>> resp = carAfterSaleQueryService.pageCarMaintenanceSsu(request);

                if (Objects.nonNull(resp) && Objects.nonNull(resp.getData())) {
                    result.addAll(resp.getData().getList());
                }
            }

            log.info("CarAfterSaleQueryProxy.pageCarMaintenanceSsu finished, ssuIdList = {}, result = {}, costTime = {}ms", ssuIdList, result, System.currentTimeMillis() - startTime);

            return result;
        } catch (Exception e) {
            log.error("CarAfterSaleQueryService.pageCarMaintenanceSsu error request:{}", request, e);
            throw create(GeneralCodes.InternalError, "售后ssu（工时&配件）信息查询异常");
        }
    }
}
