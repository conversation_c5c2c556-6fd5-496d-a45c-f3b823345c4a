package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 优惠券配置实体
 */
@Data
public class CouponConfigPO implements Serializable {
    private static final long serialVersionUID = -7278527488012115290L;

    /**
     * 优惠券编号
     */
    private long id;

    /**
     * 优惠券类型名称
     */
    private String name;

    /**
     * 状态 (1:上线, 2:下线, 3:终止)
     */
    private Integer status;

    /**
     * 优惠券描述信息，包含使用范围描述，金额描述等
     */
    private String couponDesc;

    /**
     * 预算申请单号
     */
    private String budgetApplyNo;

    /**
     * 行号
     */
    private Long lineNum;

    /**
     * 预算单创建时间
     */
    private String budgetCreateTime;

    /**
     * br申请单号（唯一标识）
     */
    private String brApplyNo;

    /**
     * 优惠券类型  1: 商品券 2: 运费券 3:超级补贴券
     */
    private Integer couponType;

    /**
     * 履约方式 -1: 所有方式 139: 门店闪送 (这个跟交易中台统一定义)
     */
    private Integer shipmentId;

    /**
     * 优惠类型 (1:满减, 2:满折, 3:N元券, 4:立减)
     */
    private Integer promotionType;

    /**
     * 投放场景
     */
    private String sendScene;

    /**
     * 投放目的
     */
    private Integer sendPurpose;

    /**
     * 开始领取时间
     */
    private Long startFetchTime;

    /**
     * 结束领取时间
     */
    private Long endFetchTime;

    /**
     * 使用有效期类型 (1:固定有效, 2:相对有效期, 3:自定义有效期)
     */
    private Integer useTimeType;

    /**
     * 开始使用时间
     */
    private Long startUseTime;

    /**
     * 结束使用时间
     */
    private Long endUseTime;
    /**
     * 相对时间粒度，1-小时，2=天
     */
    private Integer timeGranularity;
    /**
     * 有效时长(单位小时)
     */
    private Integer useDuration;

    /**
     * 使用渠道 (1:小米商城 2:直营店 3:专卖店 4:授权店 5:堡垒店)
     * 数据库格式 "1,2,3,4,5,6"
     */
    private String useChannel;

    /**
     * 使用平台
     * 数据库格式
     * key为使用渠道id {"2":{"all":true,"limitIds":[]},"3":{"all":false,"limitIds":["s1","s2","s3"]}}
     */
    private String usePlatform;

    /**
     * 使用门店
     * json  key为使用渠道id {"2":{"all":true,"limitIds":[]},"3":{"all":false,"limitIds":["s1","s2","s3"]}}
     */
    private String useStore;

    /**
     * 门槛类型 (1:满元, 2:满件, 3:每满元, 4:每满件, 5:满元且满件)
     */
    private Integer bottomType;

    /**
     * 满元门槛值 (单位分)
     */
    private Long bottomPrice;

    /**
     * 满件门槛值 (单位个)
     */
    private Integer bottomCount;

    /**
     * 优惠值 (单位分/折)
     */
    private Long promotionValue;

    /**
     * 最大减免金额 (单位分)
     */
    private Long maxReduce;

    /**
     * 商品范围类型 (1:商品券, 2:品类券)
     */
    private Integer scopeType;

    /**
     * 券可用商品
     * json {"sku":"s1,s2","package":"p1,p2"}
     */
    private String goodsInclude;

    /**
     * 商品部门  结构 1,2,3
     */
    private String goodsDepartments;

    /**
     * 排除商品
     * json  {"sku":"s1,s2","package":"p1,p2"}
     */
    private String goodsExclude;

    /**
     * 类目Id列表
     * "c1,c2,c3"
     */
    private String categoryIds;

    /**
     * 可发放总量
     */
    private Integer applyCount;

    /**
     * 释放总量
     */
    private Long releaseCount;

    /**
     * 每人领取限制
     */
    private Integer fetchLimit;

    /**
     * 附加属性 postFree可包邮  share可转增 area指定地区  proMember会员  specialStore专店专用
     * 1-是  2-否
     * json  {"postFree":2,"share":2,"area":2,"proMember":2,"specialStore":2}
     */
    private String extProp;

    /**
     * 可使用地区,到市级
     * "1,2,3,4"
     */
    private String areaIds;

    /**
     * 成本分摊
     */
    private String costShare;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 最后更新时间
     */
    private Timestamp updateTime;

    /**
     * 创建部门
     */
    private Integer departmentId;

    /**
     * 是否有码券
     */
    private Integer code;

    /**
     * 优惠券投放渠道，store_manager：店长券，store_order_gift：下单赠券，空：其他渠道
     */
    private String sendChannel;

    /**
     * 自动更新新品
     */
    private Integer autoUpdateGoods;

    /**
     * 业务渠道  0:3c业务 3:汽车业务 4:汽车服务
     */
    private Integer bizPlatform;

    /**
     * 来源 1 老券迁移 2 乾坤创建
     */
    private Integer source;

    /**
     * 服务券类型
     */
    private Integer serviceType;

    /**
     * 限领类型 1限领 2不限领
     */
    private Integer fetchLimitType;

    /**
     * 使用次数限制，1-限制，2-不限制
     */
    private Integer timesLimit;

    /**
     * 是否公开推广：1-是; 2-否
     */
    private Integer publicPromotion;
}
