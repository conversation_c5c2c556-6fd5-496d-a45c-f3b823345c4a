package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponOnlineStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.GoodsCouponRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.es.po.CouponEsPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.CouponConfigPoConvert;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.GoodsItemPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @description: 刷新商品可用券service
 * @author: hejiapeng
 * @Date 2022/4/1 2:22 下午
 * @Version: 1.0
 **/
@Service
@Slf4j
public class GoodsCouponRefreshService {

    private final static Object UPDATE_LOCK = new Object();

    @Autowired
    private GoodsCouponRepository goodsCouponRepository;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private CouponConfigPoConvert couponConfigPoConvert;

    /**
     * 更新商品和券关系缓存
     *
     * @param fullLoad
     * @param lastUpdateTime
     * @return
     * @throws Exception
     */
    public Timestamp updateRedisGoodsCouponRel(boolean fullLoad, String lastUpdateTime) throws Exception {
        try {
            synchronized (UPDATE_LOCK) {
                List<CouponConfigPO> changedCouponConfigs = goodsCouponRepository.getChangedCouponConfig(lastUpdateTime);
                if (CollectionUtils.isEmpty(changedCouponConfigs)) {
                    return Timestamp.valueOf(lastUpdateTime);
                }
                if (fullLoad) {
                    fullUpdateRedisInverted(changedCouponConfigs);
                } else {
                    incrUpdateRedisInverted(changedCouponConfigs);
                }
                //获取当前最大版本
                Timestamp maxUpdateTime = changedCouponConfigs.stream().map(CouponConfigPO::getUpdateTime).max(Timestamp::compareTo).get();
                return maxUpdateTime;
            }
        } catch (Exception e) {
            log.info("GoodsCouponRepository.updateRedisGoodsCouponRel error", e);
            throw e;
        }
    }

    /**
     * 全量更新redis中的倒排索引
     *
     * @param couponConfigs
     * @throws IOException
     * @throws InterruptedException
     */
    private void fullUpdateRedisInverted(List<CouponConfigPO> couponConfigs) throws Exception {

        // 获取pid集合
        Set<Long> skus = new HashSet<>();
        Set<Long> packageIds = new HashSet<>();
        Set<Long> ssuIds = new HashSet<>();

        for (CouponConfigPO couponConfig : couponConfigs) {
            // 过滤不需要更新倒排的券配置 只对上线的券去更新 如果券手动下线 依靠下一次pid倒排变动更新
            if (couponConfig.getStatus() != CouponOnlineStatusEnum.ONLINE.getCode()) {
                continue;
            }
            try {
                GoodsItemPo goodsItem = couponConfigPoConvert.serializeGoodsItemPo(couponConfig);
                skus.addAll(goodsItem.getSkuList());
                packageIds.addAll(goodsItem.getPackageList());
                ssuIds.addAll(Stream.concat(Optional.ofNullable(goodsItem.getSsuList()).orElse(Lists.newArrayList()).stream(), Optional.ofNullable(goodsItem.getSuitList()).orElse(Lists.newArrayList()).stream()).collect(Collectors.toSet()));
            } catch (Exception e) {
                log.error("fullUpdateRedisInverted goods convert failed configId:{}", couponConfig.getId());
            }
        }
        if (CollectionUtils.isNotEmpty(skus)) {
            goodsCouponRepository.updateGoodCouponRelRedis(skus, CouponEsPO.SKU_ID);
        }
        if (CollectionUtils.isNotEmpty(packageIds)) {
            goodsCouponRepository.updateGoodCouponRelRedis(packageIds, CouponEsPO.PACKAGE_ID);
        }
        if(CollectionUtils.isNotEmpty(ssuIds)){
            goodsCouponRepository.updateGoodCouponRelRedis(ssuIds, CouponEsPO.SSU_ID);
        }
    }

    /**
     * 增量更新redis中的倒排索引
     *
     * @param couponConfigs
     * @throws IOException
     * @throws InterruptedException
     */
    private void incrUpdateRedisInverted(List<CouponConfigPO> couponConfigs) throws Exception {
        // 获取pid集合
        Set<Long> skus = new HashSet<>();
        Set<Long> packageIds = new HashSet<>();
        Set<Long> ssuIds = new HashSet<>();
        Map<Long, List<Long>> skuConfigIdMap = new HashMap<>();
        Map<Long, List<Long>> packageIdConfigIdMap = new HashMap<>();
        Map<Long, List<Long>> ssuConfigIdMap = new HashMap<>();

        for (CouponConfigPO couponConfig : couponConfigs) {
            try {
                // 补偿ES写入
                GoodsItemPo goodsItemPo = couponConfigPoConvert.serializeGoodsItemPo(couponConfig);
                couponConfigRepository.updateCouponConfigEsInfo(couponConfig, goodsItemPo);

                // 过滤不需要更新倒排的券配置 只对上线的券去更新 如果券手动下线 依靠下一次pid倒排变动更新
                if (couponConfig.getStatus() != CouponOnlineStatusEnum.ONLINE.getCode()) {
                    continue;
                }
                List<Long> ssuAndSuitList = Stream.concat(Optional.ofNullable(goodsItemPo.getSsuList()).orElse(Lists.newArrayList()).stream(), Optional.ofNullable(goodsItemPo.getSuitList()).orElse(Lists.newArrayList()).stream()).collect(Collectors.toList());
                supplementGoodCoupons(skuConfigIdMap, couponConfig, goodsItemPo.getSkuList());
                supplementGoodCoupons(packageIdConfigIdMap, couponConfig, goodsItemPo.getPackageList());
                supplementGoodCoupons(ssuConfigIdMap, couponConfig, ssuAndSuitList);

                skus.addAll(goodsItemPo.getSkuList());
                packageIds.addAll(goodsItemPo.getPackageList());
                ssuIds.addAll(ssuAndSuitList);
            } catch (Exception e) {
                log.error("incrUpdateRedisInverted goods convert failed configId:{}", couponConfig.getId());
            }
        }
        // 更新sku倒排
        goodsCouponRepository.updateGoodCouponRelRedis(skus, skuConfigIdMap, CouponEsPO.SKU_ID);
        // 更新package倒排
        goodsCouponRepository.updateGoodCouponRelRedis(packageIds, packageIdConfigIdMap, CouponEsPO.PACKAGE_ID);
        // 更新ssu倒排
        goodsCouponRepository.updateGoodCouponRelRedis(ssuIds, ssuConfigIdMap, CouponEsPO.SSU_ID);
    }

    /**
     * 补充商品可用券列表
     *
     * @param goodIdConfigIdMap
     * @param couponConfig
     * @param goodList
     */
    private void supplementGoodCoupons(Map<Long, List<Long>> goodIdConfigIdMap, CouponConfigPO couponConfig, List<Long> goodList) {
        for (Long good : goodList) {
            if (!goodIdConfigIdMap.containsKey(good)) {
                goodIdConfigIdMap.put(good, Lists.newArrayList());
            }
            goodIdConfigIdMap.get(good).add(couponConfig.getId());
        }
    }
}
