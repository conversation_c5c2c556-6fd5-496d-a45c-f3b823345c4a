package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale;

import com.google.common.collect.Maps;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.CouponCheckFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.NyuanBuyCheckAction;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponGoodsInfo;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/25 10:47
 */
@Component
@Slf4j
public class NyuanBuyServiceCouponCheckAction extends NyuanBuyCheckAction {
    @Autowired
    private ServiceCouponCheckToolsFactory serviceCouponCheckToolsFactory;

    /**
     * 初始化（用来工厂和策略模式的注册）
     */
    @PostConstruct
    @Override
    public void init() {
        CouponCheckFactory.register(BizPlatformEnum.CAR_AFTER_SALE, this);
    }

    /**
     * 券创建特殊校验
     */
    @Override
    public void createSpecialCheck(CouponBaseInfo info) throws BizError {
        if (info.getPromotionValue() < 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠值必须大于0");
        }

        // 服务类型
        CouponServiceTypeEnum serviceTypeEnum = CouponServiceTypeEnum.valueOf(info.getServiceType());

        if (null == serviceTypeEnum) {
            throw ExceptionHelper.create(ErrCode.COUPON, "汽车售后券服务类型不存在");
        }

        ServiceCouponCheckTools serviceCouponCheckTools = serviceCouponCheckToolsFactory.getServiceCouponCheckTools(serviceTypeEnum);

        // 券创建校验
        serviceCouponCheckTools.createSpecialCheck(info);
    }

    /**
     * 券修改特殊校验
     */
    @Override
    public void updateSpecialCheck(CouponBaseInfo info) throws BizError {
        if (info.getPromotionValue() < 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠值必须大于0");
        }

        // 服务类型
        CouponServiceTypeEnum serviceTypeEnum = CouponServiceTypeEnum.valueOf(info.getServiceType());

        if (null == serviceTypeEnum) {
            throw ExceptionHelper.create(ErrCode.COUPON, "汽车售后券服务类型不存在");
        }

        ServiceCouponCheckTools serviceCouponCheckTools = serviceCouponCheckToolsFactory.getServiceCouponCheckTools(serviceTypeEnum);

        // 券修改校验
        serviceCouponCheckTools.updateSpecialCheck(info);
    }

    /**
     * 商品校验
     */
    @Override
    public void goodsCheck(CouponConfigItem configItem) throws BizError {
        CouponGoodsInfo couponGoodsInfo = configItem.getCouponGoodsInfo();
        if (couponGoodsInfo == null) {
            throw ExceptionHelper.create(ErrCode.COUPON, "商品信息不能为空");
        }

        // 服务类型
        CouponServiceTypeEnum serviceTypeEnum = CouponServiceTypeEnum.valueOf(configItem.getCouponBaseInfo().getServiceType());

        if (null == serviceTypeEnum) {
            throw ExceptionHelper.create(ErrCode.COUPON, "汽车售后券服务类型不存在");
        }

        ServiceCouponCheckTools serviceCouponCheckTools = serviceCouponCheckToolsFactory.getServiceCouponCheckTools(serviceTypeEnum);

        // 工时ssu
        Map<Long, Integer> labourHourSsu = org.apache.commons.collections4.MapUtils.isNotEmpty(couponGoodsInfo.getLabourHourSsu()) ? couponGoodsInfo.getLabourHourSsu() : Maps.newHashMap();

        // 配件ssu
        Map<Long, Integer> partsSsu = MapUtils.isNotEmpty(couponGoodsInfo.getPartsSsu()) ? couponGoodsInfo.getPartsSsu() : Maps.newHashMap();

        // 汽车售后服务券，工时&配件ssu校验
        serviceCouponCheckTools.goodsCheck(labourHourSsu, partsSsu);
    }

    /**
     * 业务平台类型
     */
    @Override
    public BizPlatformEnum getBizPlatformEnum() {
        return BizPlatformEnum.CAR_AFTER_SALE;
    }
}
