package com.xiaomi.nr.coupon.admin.infrastruture.error;

import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.Scopes;

/**
 * 错误常量
 * <p>
 * 错误码值分三部分 400{scope}{internalCode},
 * 400:          固定值
 * scope:        定义级别 {@link Scopes}
 * internalCode：错误级别的确切问题
 *
 * </p>
 *
 * <AUTHOR>
 */
public class ErrInfo {
    public static final BizError ERR_COUPON_CONFIG_ID = ExceptionHelper.create(ErrCode.COUPON, "优惠券配置ID不符合要求");
    public static final BizError ERR_COUPON_CONFIG_ID_EMPTY = ExceptionHelper.create(ErrCode.COUPON, "优惠券配置ID列表不能为空");
    public static final BizError ERR_COUPON_CONFIG_ACTION = ExceptionHelper.create(ErrCode.COUPON, "优惠券配置操作超出已知范围");
    public static final BizError ERR_COUPON_CONFIG_CHANGE_NOTIFY = ExceptionHelper.create(ErrCode.COUPON, "优惠券配置变更通知失败");
    public static final BizError ERR_COUPON_CONFIG_SKU_PACKAGE_ID = ExceptionHelper.create(ErrCode.COUPON, "SKU或套装ID必须为正整数");
    public static final BizError ERR_COUPON_CONFIG_LEVEL = ExceptionHelper.create(ErrCode.COUPON, "品级超出已知范围");
    public static final BizError ERR_COUPON_CONFIG_CACHE_NOT_FIND = ExceptionHelper.create(ErrCode.COUPON, "优惠券配置信息未找到");
    public static final BizError ERR_COUPON_CONFIG_GOODS_CACHE_NOT_FIND = ExceptionHelper.create(ErrCode.COUPON, "商品对应优惠券配置列表信息未找到");
    public static final BizError ERR_COUPON_CONFIG_INTERFACE_BUZY = ExceptionHelper.create(ErrCode.COUPON, "访问该接口频率过快");
    public static final BizError ERR_ECARD_STAT_FAIL = ExceptionHelper.create(ErrCode.ECARD, "礼品卡ID对应礼品卡状态列表未找到");
    public static final BizError ERR_ECARD_DESC_FAIL = ExceptionHelper.create(ErrCode.ECARD, "礼品卡ID对应礼品卡信息列表未找到");
    public static final BizError ERR_ECARD_ID_LENGTH = ExceptionHelper.create(ErrCode.ECARD, "一次查询的礼品卡ID不能超过100个");
    public static final BizError ERR_ECARD_LIST_FAIL = ExceptionHelper.create(ErrCode.ECARD, "礼品卡ID对应礼品卡列表未找到");

}
