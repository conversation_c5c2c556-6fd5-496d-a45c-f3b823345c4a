package com.xiaomi.nr.coupon.admin.infrastruture.repository;

import com.xiaomi.nr.coupon.admin.enums.usercoupon.UserCouponStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.carcoupon.CarCouponMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.UserCouponPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class CarCouponRepository {

    @Autowired
    private CarCouponMapper carCouponMapper;

    /**
     * 根据id修改状态
     *
     * @return
     */
    public long destroyCoupon(String vid, long couponId, UserCouponStatusEnum couponStatus) {
        return carCouponMapper.destroyCoupon(vid, couponId, couponStatus.getValue(), new Date().getTime() / 1000);
    }

    /**
     * 根据id查询
     * @return
     */
    public UserCouponPO selectByCouponId(String vid, long couponId) {
        return carCouponMapper.selectByCouponId(vid, couponId);
    }

}
