package com.xiaomi.nr.coupon.admin.infrastruture.repository;


import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.mapper.CarPointsParentScenePoMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsParentScenePo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2025-06-11
 */
@Component
public class CarPointParentSceneRepository {

    @Autowired
    private CarPointsParentScenePoMapper carPointParentSceneMapper;

    /**
     * 插入场景
     * @param carPointsParentScenePo
     * @return
     */
    public Integer insert(CarPointsParentScenePo carPointsParentScenePo) {
        return carPointParentSceneMapper.insert(carPointsParentScenePo);
    }

    /**
     * 获取所有有效的场景编码
     * @return
     */
    public List<CarPointsParentScenePo> getAllParentSceneList(){
        return carPointParentSceneMapper.searchAllParentScene();
    }

    /**
     * 根据名称查询一级场景
     * @param name
     * @return
     */
    public CarPointsParentScenePo searchParentSceneByName(String name){
        return carPointParentSceneMapper.searchParentSceneByName(name);
    }
}
