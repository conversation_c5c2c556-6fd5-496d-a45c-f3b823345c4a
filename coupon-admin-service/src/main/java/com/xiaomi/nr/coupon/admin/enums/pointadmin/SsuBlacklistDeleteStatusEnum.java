package com.xiaomi.nr.coupon.admin.enums.pointadmin;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2023/12/8 10:17
 */
@Getter
@AllArgsConstructor
public enum SsuBlacklistDeleteStatusEnum {
    /**
     * 0: 未删除
     */
    NO_DELETE(0, "未删除"),

    /**
     * 1: 已删除
     */
    IS_DELETE(1, "已删除"),
    ;

    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String desc;

    private static final HashMap<Integer, SsuBlacklistDeleteStatusEnum> MAPPING = new HashMap<>();

    static {
        for (SsuBlacklistDeleteStatusEnum e : SsuBlacklistDeleteStatusEnum.values()) {
            MAPPING.put(e.getCode(), e);
        }
    }

    public static SsuBlacklistDeleteStatusEnum valueOf(Integer code) {
        return MAPPING.get(code);
    }
}
