package com.xiaomi.nr.coupon.admin.util;


import com.xiaomi.nr.coupon.admin.api.dto.coupon.AssignCouponTmpRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.AssignRequestItem;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量发券Excel数据转换参数
 */
public class MakeRequest {

    private XSSFSheet sheet;

    /**
     * 构造函数，初始化excel数据
     *
     * @param filePath  excel路径
     * @param sheetName sheet表名
     */
    MakeRequest(String filePath, String sheetName) {
        FileInputStream fileInputStream = null;
        try {
            fileInputStream = new FileInputStream(filePath);
            XSSFWorkbook sheets = new XSSFWorkbook(fileInputStream);
            //获取sheet
            sheet = sheets.getSheet(sheetName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据行和列的索引获取单元格的数据
     *
     * @param row
     * @param column
     * @return
     */
    public String getExcelDateByIndex(int row, int column) {
        XSSFRow row1 = sheet.getRow(row);
        String cell = String.valueOf(row1.getCell(column));
        return cell;
    }

    /**
     * 根据某一列值为“******”的这一行，来获取该行第x列的值
     *
     * @param caseName
     * @param currentColumn 当前单元格列的索引
     * @param targetColumn  目标单元格列的索引
     * @return
     */
    public String getCellByCaseName(String caseName, int currentColumn, int targetColumn) {
        String operateSteps = "";
        //获取行数
        int rows = sheet.getPhysicalNumberOfRows();
        for (int i = 0; i < rows; i++) {
            XSSFRow row = sheet.getRow(i);
            String cell = row.getCell(currentColumn).toString();
            if (cell.equals(caseName)) {
                operateSteps = row.getCell(targetColumn).toString();
                break;
            }
        }
        return operateSteps;
    }


    /**
     * 读取excel数据并生成请求参数
     */
    public void readExcelData() {
        //批量发券请求参数
        AssignCouponTmpRequest request = new AssignCouponTmpRequest();
        List<AssignRequestItem> itemList = new ArrayList<>();
        request.setAppId("XM2106");

        //获取行数
        int rows = sheet.getPhysicalNumberOfRows();
        for (int i = 1; i < rows; i++) {

            //获取列数
            XSSFRow row = sheet.getRow(i);
            int columns = row.getPhysicalNumberOfCells();
            AssignRequestItem item = new AssignRequestItem();
            for (int j = 0; j < columns; j++) {
                DecimalFormat df = new DecimalFormat("0");
                String cell = String.valueOf(df.format(row.getCell(j).getNumericCellValue()));

                switch (j){
                    case 0:
                        item.setMissionId(Long.parseLong(cell));
                        break;
                    case 1:
                        item.setUserId(Long.parseLong(cell));
                        break;
                    default:
                        break;
                }

            }
            itemList.add(item);
        }

        request.setAssignRequestItemList(itemList);
        String token = StringUtil.getToken(request,request.getAppId(),"");
        request.setToken(token);
        System.out.println("["+ GsonUtil.toJson(request)+"]");
    }



    /**
     * 主函数-生成请求参数
     */
    public static void main(String[] args) {
        MakeRequest sheet1 = new MakeRequest("/home/<USER>/文档/request.xlsx", "assign");
        sheet1.readExcelData();
    }

}

