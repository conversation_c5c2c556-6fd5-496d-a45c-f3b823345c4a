package com.xiaomi.nr.coupon.admin.enums.couponconfig;

public enum CouponTimeStatusEnum {

    NOT_START("未开始",1),
    IN_PROGRESS("进行中",2),
    ENDED("已结束",3),
    STOP_FETCHING( "已终止",4);


    private final String name;
    private final int code;


    CouponTimeStatusEnum(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return this.code;
    }
    public String getName() {
        return name;
    }

    public static CouponTimeStatusEnum findByCode(int value) {
        CouponTimeStatusEnum[] values = CouponTimeStatusEnum.values();
        for (CouponTimeStatusEnum item : values) {
            if (item.getCode() == value) {
                return item;
            }
        }
        return null;
    }


}
