package com.xiaomi.nr.coupon.admin.infrastruture.repository;

import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.mapper.CarPointsBlackSsuMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.param.CarPointsBlackSsuParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBlackSsuPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/6
 */
@Component
@Slf4j
public class CarPointsBlackSsuRepository {
    @Autowired
    private CarPointsBlackSsuMapper carPointsBlackSsuMapper;

    public List<CarPointsBlackSsuPo> findByParam(CarPointsBlackSsuParam param) {
        return carPointsBlackSsuMapper.findByParam(param);
    }

    public Integer selectTotal(CarPointsBlackSsuParam param) {
        return carPointsBlackSsuMapper.selectTotal(param);
    }

    public void deleteBySsuId(Long ssuId) throws BizError {
        log.info("CarPointsBlackSsuRepository.deleteBySsuId begin, ssuId = {}", ssuId);

        long effectRows = carPointsBlackSsuMapper.deleteBySsuId(ssuId);

        if (effectRows <= 0) {
            log.info("CarPointsBlackSsuRepository.deleteBySsuId failed, ssuId = {}", ssuId);
            throw ExceptionHelper.create(ErrCode.POINT, "根据ssu删除通用黑名单商品失败");
        }

        log.info("CarPointsBlackSsuRepository.deleteBySsuId finished, ssuId = {}", ssuId);
    }

    public List<CarPointsBlackSsuPo> findBySsu(List<Long> ssuIdList) {
        return carPointsBlackSsuMapper.findBySsu(ssuIdList);
    }

    public void updateDeleteStatus(List<Long> ssuIdList, Integer deleteStatus) throws BizError {
        log.info("CarPointsBlackSsuRepository.updateDeleteStatus begin, ssuIdList = {}, deleteStatus = {}", ssuIdList, deleteStatus);

        int effectRows =  carPointsBlackSsuMapper.updateDeleteStatus(ssuIdList, deleteStatus);

        if (effectRows != ssuIdList.size()) {
            log.error("CarPointsBlackSsuRepository.updateDeleteStatus 修改数据库里已删除的ssu改为未删除失败，ssuIdList = {}", ssuIdList);
            throw ExceptionHelper.create(ErrCode.POINT, "修改数据库里已删除的ssu改为未删除失败");
        }

        log.info("CarPointsBlackSsuRepository.updateDeleteStatus finished, ssuIdList = {}, deleteStatus = {}", ssuIdList, deleteStatus);
    }


    public void batchInsert(List<CarPointsBlackSsuPo> carPointsBlackSsuPos) throws BizError {
        log.info("CarPointsBlackSsuRepository.batchInsert begin, carPointsBlackSsuPos = {}", carPointsBlackSsuPos);

        int effectRows = carPointsBlackSsuMapper.batchInsert(carPointsBlackSsuPos);

        if (effectRows != carPointsBlackSsuPos.size()) {
            log.error("arPointsBlackSsuRepository.addSsuBlacklist 批量插入ssu失败, carPointsBlackSsuPos = {}", carPointsBlackSsuPos);
            throw ExceptionHelper.create(ErrCode.POINT, "批量插入ssu失败");
        }

        log.info("CarPointsBlackSsuRepository.batchInsert finished, carPointsBlackSsuPos = {}", carPointsBlackSsuPos);
    }
}
