package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2023/12/22 17:05
 */
@Slf4j
public abstract class DirectReduceCheckAction extends CouponConfigBaseCheck {

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Override
    public PromotionTypeEnum getPromotionType() {
        return PromotionTypeEnum.DirectReduce;
    }

    /**
     * 券创建基础校验
     */
    @Override
    public void createCommonCheck(CouponBaseInfo info) throws BizError {
        // 券创建、修改公共校验
        commonCheck(info);

        // 商品券类型校验
        couponTypeCheck(info);

        if (info.getId() > 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券id必须为空");
        }
    }

    /**
     * 券修改基础校验
     */
    public void updateCommonCheck(CouponBaseInfo info) throws BizError {
        // 券创建、修改公共校验
        commonCheck(info);

        // 商品券类型校验
        couponTypeCheck(info);

        if (info.getId() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券id不能为空");
        }

        CouponConfigPO couponConfigPO = couponConfigRepository.searchCouponById(info.getId());
        if (couponConfigPO == null) {
            throw ExceptionHelper.create(ErrCode.COUPON, "未找到券信息");
        }
        if (!couponConfigPO.getSendScene().equals(info.getSendScene())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "投放场景不可修改");
        }
        if (couponConfigPO.getUseTimeType() != info.getUseTimeType()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用时间类型不可修改");
        }
        if (couponConfigPO.getPromotionType() != info.getPromotionType()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠类型不可修改");
        }
        if (couponConfigPO.getBottomType() != info.getBottomType()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "门槛类型不可修改");
        }
        if (info.getApplyCount() < couponConfigPO.getApplyCount()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "发放数量只能增加");
        }
    }

}
