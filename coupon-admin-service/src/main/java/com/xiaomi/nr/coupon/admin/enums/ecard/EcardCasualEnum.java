package com.xiaomi.nr.coupon.admin.enums.ecard;

/**
 * 礼品卡临时卡　枚举
 *
 * <AUTHOR>
 */

public enum EcardCasualEnum {
    /**
     * 非临时卡
     */
    isCasual(0, "非临时卡"),

    /**
     * 临时卡
     */
    noCasual(1, "临时卡");


    private final Integer value;
    private final String name;

    EcardCasualEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static EcardCasualEnum findByValue(Integer value) {
        EcardCasualEnum[] values = EcardCasualEnum.values();
        for (EcardCasualEnum ecardCasualEnum : values) {
            if (value.equals(ecardCasualEnum.value)) {
                return ecardCasualEnum;
            }
        }
        return null;
    }
}
