package com.xiaomi.nr.coupon.admin.infrastruture.repository;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.EventContext;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.StatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.bdmall.ActivityMarketCouponMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.bdmall.MarketCouponApplyMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.bdmall.po.ActivityMarketCouponPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.bdmall.po.BdMallCouponConvert;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.bdmall.po.MarketCouponApplyPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.mission.CouponMissionMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.mission.po.CouponMissionPO;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * @description: 优惠券配置资源库
 * @author: hejiapeng
 * @Date 2022/3/1 10:42 上午
 * @Version: 1.0
 **/
@Component
@Slf4j
public class BdCouponSyncRepository {

    @Autowired
    private ActivityMarketCouponMapper mallActivityMarketCouponMapper;

    @Autowired
    private MarketCouponApplyMapper mallMarketCouponApplyMapper;

    @Autowired
    private CouponMissionMapper couponMissionMapper;

    @Autowired
    private BdMallCouponConvert bdMallCouponConvert;


    /**
     * 更新b.d数据库数据
     *
     * @param couponConfigPO
     * @throws Exception
     */
    @Transactional(transactionManager = "xmBdMallTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void insertMallMarketCoupon(CouponConfigPO couponConfigPO, EventContext eventContext) {
        ActivityMarketCouponPO activityMarketCouponPO = bdMallCouponConvert.serializeActivityMarketCouponPO(couponConfigPO, eventContext.getSkuInfoDtos(), eventContext.getBatchedInfoDtos());

        List<CouponMissionPO> couponMissionPOS = couponMissionMapper.getMissionByConfigId(couponConfigPO.getId());
        MarketCouponApplyPO marketCouponApplyPO = bdMallCouponConvert.serializeMarketCouponApplyPO(couponMissionPOS.get(0), couponConfigPO);

        mallActivityMarketCouponMapper.insertMarketCoupon(activityMarketCouponPO);
        mallMarketCouponApplyMapper.insertCouponApply(marketCouponApplyPO);
    }


    /**
     * 更新b.d数据库数据
     *
     * @param couponConfigPO
     * @throws Exception
     */
    @Transactional(transactionManager = "xmBdMallTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void updateMallMarketCoupon(CouponConfigPO couponConfigPO, EventContext eventContext) {
        ActivityMarketCouponPO activityMarketCouponPO = bdMallCouponConvert.serializeActivityMarketCouponPO(couponConfigPO, eventContext.getSkuInfoDtos(), eventContext.getBatchedInfoDtos());

        List<CouponMissionPO> couponMissionPOS = couponMissionMapper.getMissionByConfigId(couponConfigPO.getId());
        MarketCouponApplyPO marketCouponApplyPO = bdMallCouponConvert.serializeMarketCouponApplyPO(couponMissionPOS.get(0), couponConfigPO);

        mallActivityMarketCouponMapper.updateMarketCoupon(activityMarketCouponPO);
        mallMarketCouponApplyMapper.updateCouponApply(marketCouponApplyPO);

    }

    /**
     * 更新b.d数据库数据
     *
     * @param couponConfigPO
     * @throws Exception
     */
    @Transactional(transactionManager = "xmBdMallTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void updateMallMarketCouponStatus(CouponConfigPO couponConfigPO) {
        StatusEnum statusEnum = StatusEnum.Cancel;
        if (couponConfigPO.getStatus() == CouponConfigStatusEnum.ONLINE.code) {
            statusEnum = StatusEnum.Approved;
        }

        mallActivityMarketCouponMapper.updateMarketCouponStatus(statusEnum.getMysqlValue(), couponConfigPO.getId());
    }

    /**
     * 判断b.d中是否已经存在改券
     * @param configId
     * @return
     */
    public boolean checkMallMarketCoupon(Long configId){
        Long marketCouponId = mallActivityMarketCouponMapper.getMarketCouponIdByConfigId(configId);
        return Optional.ofNullable(marketCouponId).orElse(0L) > 0;
    }

}
