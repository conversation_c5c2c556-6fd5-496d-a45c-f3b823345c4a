package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po;

import lombok.Data;

@Data
public class ExtPropPO {

    /**
     * 是否包邮 1-是 2-否
     */
    private int postFree;

    /**
     * 可分享 1-是 2-否
     */
    private int share;

    /**
     * 限制地区 1-是 2-否
     */
    private int area;

    /**
     * 套装是否允许部分商品参加活动(套装是否拆分) 1允许 2不允许
     */
    private int checkPackage;

    /**
     * 特价商品是否参与  1表示不参与，2表示参与
     */
    private int checkPrice;

    /**
     * 是否pro会员券  1表示是，2表示否
     */
    private int proMember;

    /**
     * 是否专店专用 1-是， 2-否
     */
    private int specialStore;


}
