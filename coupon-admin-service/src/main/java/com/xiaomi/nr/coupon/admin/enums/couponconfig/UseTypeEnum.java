package com.xiaomi.nr.coupon.admin.enums.couponconfig;

/**
 * 使用类型 枚举
 *
 * <AUTHOR>
 */
public enum UseTypeEnum {

    /**
     * 现金券
     */
    Cash("cash", 1, "现金券"),

    /**
     * 拆扣券
     */
    Discount("discount", 2, "折扣券"),

    /**
     * 抵扣券
     */
    Deduction("deductible", 3, "抵扣券"),

    /**
     * 现金券
     */
    Reduce("cash", 4, "立减券");

    private final String redisValue;
    private final Integer mysqlValue;
    private final String name;

    UseTypeEnum(String redisValue, Integer mysqlValue, String name) {
        this.redisValue = redisValue;
        this.mysqlValue = mysqlValue;
        this.name = name;
    }

    public String getRedisValue() {
        return this.redisValue;
    }

    public Integer getMysqlValue() {
        return this.mysqlValue;
    }

    public String getName() {
        return name;
    }

    public static String findNameByRedisValue(String value) {
        UseTypeEnum[] values = UseTypeEnum.values();
        for (UseTypeEnum item : values) {
            if (item.getRedisValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }

    public static String findRedisValueByMysqlValue(Integer value) {
        UseTypeEnum[] values = UseTypeEnum.values();
        for (UseTypeEnum item : values) {
            if (item.getMysqlValue().equals(value)) {
                return item.getRedisValue();
            }
        }
        return null;
    }
}

