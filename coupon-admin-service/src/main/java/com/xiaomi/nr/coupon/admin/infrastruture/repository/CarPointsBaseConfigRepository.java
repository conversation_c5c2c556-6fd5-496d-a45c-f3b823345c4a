package com.xiaomi.nr.coupon.admin.infrastruture.repository;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.mapper.CarPointsBaseConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBaseConfigPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.pointadmin.PointBaseConfigRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.pointadmin.po.PointBaseConfigCachePo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/6
 */
@Component
@Slf4j
public class CarPointsBaseConfigRepository {
    @Autowired
    private CarPointsBaseConfigMapper carPointsBaseConfigMapper;

    @Autowired
    private PointBaseConfigRedisDao pointBaseConfigRedisDao;

    /**
     * 设置积分通用配置缓存
     *
     * @param baseConfigPo baseConfigPo
     * @param ssuIdList    ssuIdList
     */
    public void setPointBaseBatchConfigCache(CarPointsBaseConfigPo baseConfigPo, List<Long> ssuIdList) throws BizError {
        PointBaseConfigCachePo cachePo = new PointBaseConfigCachePo();
        BeanUtils.copyProperties(baseConfigPo, cachePo);
        // 黑名单ssuId列表
        String blacklistSsu = ssuIdList.stream().map(Object::toString).collect(Collectors.joining(","));
        cachePo.setBlacklistSsu(blacklistSsu);

        pointBaseConfigRedisDao.setPointBaseBatchConfigCache(cachePo);
    }

    /**
     * 获取积分通用配置缓存
     *
     * @return PointBaseConfigCachePo
     */
    public PointBaseConfigCachePo getPointBaseBatchConfigCache() {
        return pointBaseConfigRedisDao.getPointBaseBatchConfigCache();
    }

    /**
     * 获取最后一个有效的通用配置
     *
     * @return CarPointsBaseConfigPo
     */
    public CarPointsBaseConfigPo findLastValid() {
        return carPointsBaseConfigMapper.findLastValid();
    }
}
