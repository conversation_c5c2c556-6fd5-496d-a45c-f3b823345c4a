package com.xiaomi.nr.coupon.admin.domain.coupon.couponoptlog;

import com.google.gson.reflect.TypeToken;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.ExtPropVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.ModifyContentVO;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponoptlog.model.CouponModifyDimension;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponLogRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.GoodItemPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.UseChannelPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.optlog.po.CouponLogPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.util.*;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 优惠券日志
 * @Date: 2022.02.28 14:16
 */
@Service
@Slf4j
public class CouponLogDomainServiceImpl implements CouponLogDomainService {

    @Autowired
    private CouponLogRepository couponLogRepository;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    /**
     * ssu 数量对
     */
    private static final String SSU_NUM_PAIR_TEMP = "[%d: %d]";

    /**
     * ssu 数量对
     */
    private static final String SSU_NUM_CHANGE_TEMP = "ssu(%d)数量由%d改为%d";

    /**
     * 查询日志详情
     *
     * @param logId
     * @return
     */
    @Override
    public List<ModifyContentVO> queryLogDetail(long logId) throws Exception {
        CouponLogPO couponLogPO = couponLogRepository.getLogById(logId);
        return buildModifyInfo(couponLogPO.getOperationContent());
    }

    /**
     * 构建修改对比结果信息
     * @param operationContent
     * @return
     * @throws Exception
     */
    private List<ModifyContentVO> buildModifyInfo(String operationContent) throws Exception {
        List<ModifyContentVO> modifyContentVOList = new ArrayList<>();
        Map<Object, byte[]> configPOMap = GsonUtil.get().fromJson(operationContent, new TypeToken<Map<String, byte[]>>() {
        }.getType());
        CouponConfigPO originCouponPO = GsonUtil.get().fromJson(CompressUtil.decompress(configPOMap.get("old")), CouponConfigPO.class);
        CouponConfigPO couponPO = GsonUtil.get().fromJson(CompressUtil.decompress(configPOMap.get("new")), CouponConfigPO.class);
        CouponModifyDimension originCoupon = convertPOToCouponDimension(originCouponPO);
        CouponModifyDimension coupon = convertPOToCouponDimension(couponPO);
        modifyContentVOList.addAll(getSpecialDimensionDiff(originCoupon, coupon));
        return modifyContentVOList;
    }

    /**
     * 生成特殊字段diff信息
     *
     * @param originCoupon
     * @param coupon
     * @return
     */
    private List<ModifyContentVO> getSpecialDimensionDiff(CouponModifyDimension originCoupon, CouponModifyDimension coupon) throws BizError {
        List<ModifyContentVO> modifyContentVOList = new ArrayList<>();
        if (!originCoupon.getName().equals(coupon.getName())) {
            String type = "优惠券名称";
            modifyContentVOList.add(new ModifyContentVO(type, generateEditDesc(originCoupon.getName(), coupon.getName())));
        }
        if (!originCoupon.getCouponDesc().equals(coupon.getCouponDesc())) {
            String type = "优惠券描述";
            modifyContentVOList.add(new ModifyContentVO(type, generateEditDesc(originCoupon.getCouponDesc(), coupon.getCouponDesc())));
        }
        ///////////////////////// 优惠类型//////////////////////
        //折扣类型不允许被修改
        //门槛类型
        if (!originCoupon.getBottomType().equals(coupon.getBottomType()) || !originCoupon.getBottomPrice().
                equals(coupon.getBottomPrice()) || !originCoupon.getBottomCount().equals(coupon.getBottomCount())) {
            String type = "门槛类型";
            String origin =BottomTypeEnum.getDesc(BottomTypeEnum.getByValue(originCoupon.getBottomType()), originCoupon.getBottomCount(), originCoupon.getBottomPrice());
            String now = BottomTypeEnum.getDesc(BottomTypeEnum.getByValue(coupon.getBottomType()), coupon.getBottomCount(), coupon.getBottomPrice());
            modifyContentVOList.add(new ModifyContentVO(type, generateEditDesc(origin, now)));
        }
        if (!originCoupon.getMaxReduce().equals(coupon.getMaxReduce())) {
            String type = "最大减免金额";
            String origin = originCoupon.getMaxReduce() / 100 + "元";
            String now = coupon.getMaxReduce() / 100 + "元";
            modifyContentVOList.add(new ModifyContentVO(type, generateEditDesc(origin, now)));
        }
        //投放场景
        if (!originCoupon.getSendScene().equals(coupon.getSendScene())) {
            String type = "投放场景";
            CouponScenePO originScene = couponSceneRepository.selectBySceneCode(originCoupon.getSendScene());
            CouponScenePO scene = couponSceneRepository.selectBySceneCode(coupon.getSendScene());
            String origin = SceneEnum.getByCode(originScene.getRelationSceneId()).getDesc() + "-" + originScene.getName();
            String now = SceneEnum.getByCode(scene.getRelationSceneId()).getDesc() + "-" + scene.getName();
            modifyContentVOList.add(new ModifyContentVO(type, generateEditDesc(origin, now)));
        }
        //投放目的
        if (!originCoupon.getSendPurpose().equals(coupon.getSendPurpose())) {
            String type = "投放目的";
            String origin = SendPurposeEnum.getByValue(originCoupon.getSendPurpose()).getName();
            String now = SendPurposeEnum.getByValue(coupon.getSendPurpose()).getName();
            modifyContentVOList.add(new ModifyContentVO(type, generateEditDesc(origin, now)));
        }
        //领取时间
        if (!originCoupon.getStartFetchTime().equals(coupon.getStartFetchTime()) ||
                !originCoupon.getEndFetchTime().equals(coupon.getEndFetchTime())) {
            String type = "领取时间";
            String origin = TimeUtil.formatDate(new Date(originCoupon.getStartFetchTime() * 1000)) + " 至 " + TimeUtil.formatDate(new Date(originCoupon.getEndFetchTime() * 1000));
            String now = TimeUtil.formatDate(new Date(coupon.getStartFetchTime() * 1000)) + " 至 " + TimeUtil.formatDate(new Date(coupon.getEndFetchTime() * 1000));
            modifyContentVOList.add(new ModifyContentVO(type, generateEditDesc(origin, now)));
        }
        //使用时间类型
        if (originCoupon.getUseTimeType().equals(UseTimeTypeEnum.ABSOLUTE.getValue())) {
            if (!originCoupon.getStartUseTime().equals(coupon.getStartUseTime()) || !originCoupon.getEndUseTime().equals(coupon.getEndUseTime())) {
                String type = "使用时间";
                String origin = TimeUtil.formatDate(new Date(originCoupon.getStartUseTime() * 1000)) + " 至 " + TimeUtil.formatDate(new Date(originCoupon.getEndUseTime() * 1000));
                String now = TimeUtil.formatDate(new Date(coupon.getStartUseTime() * 1000)) + " 至 " + TimeUtil.formatDate(new Date(coupon.getEndUseTime() * 1000));
                modifyContentVOList.add(new ModifyContentVO(type, generateEditDesc(origin, now)));
            }
        } else {
            if (!originCoupon.getUseDuration().equals(coupon.getUseDuration())) {
                String type = "使用有效时长";
                String origin = getUseDurationContent(originCoupon.getUseDuration(), originCoupon.getTimeGranularity());
                String now = getUseDurationContent(coupon.getUseDuration(), coupon.getTimeGranularity());
                modifyContentVOList.add(new ModifyContentVO(type, generateEditDesc(origin, now)));
            }
        }
        ////////////////////////使用渠道、平台、门店///////////////
        //产品沟通结果：只有全部和指定两个维度



        StringBuilder channelModifyContent = new StringBuilder();
        boolean useChannelModified = false;
        Map<Integer, UseChannelPO> originUseStore = originCoupon.getUseStore();
        Map<Integer, UseChannelPO> useStore = coupon.getUseStore();

        for (UseChannelsEnum useChannelsEnum :UseChannelsEnum.values()){
            if(originCoupon.getUseChannel().contains(useChannelsEnum.getValue()) != coupon.getUseChannel().contains(useChannelsEnum.getValue())){
                if(UseChannelsEnum.XIAOMI_SHOP.equals(useChannelsEnum)){
                    if(originCoupon.getUseChannel().contains(useChannelsEnum.getValue())){
                        channelModifyContent.append("删除").append("“").append(useChannelsEnum.getName()).append("”。");
                    }else{
                        channelModifyContent.append("新增").append("“").append(useChannelsEnum.getName()).append("”。");
                    }
                }else{
                    getChannelContent(channelModifyContent, originUseStore.get(useChannelsEnum.getValue()),useStore.get(useChannelsEnum.getValue()), useChannelsEnum);
                }
                useChannelModified = true;
            }else if(originCoupon.getUseChannel().contains(useChannelsEnum.getValue()) && coupon.getUseChannel().contains(useChannelsEnum.getValue())){
                if(!UseChannelsEnum.XIAOMI_SHOP.equals(useChannelsEnum)){
                    useChannelModified = getChannelContent(channelModifyContent, originUseStore.get(useChannelsEnum.getValue()),useStore.get(useChannelsEnum.getValue()), useChannelsEnum);
                }
            }
        }

        if (useChannelModified) {
            String type = "使用渠道";
            modifyContentVOList.add(new ModifyContentVO(type, channelModifyContent.toString()));
        }
        ///////////////////////////////商品//////////////////////
        //商品范围类型不允许被修改

        // 汽车售后业务比对工时、配件map
        if (BizPlatformEnum.CAR_AFTER_SALE.getCode().equals(coupon.getBizPlatform())) {
            Map<Long, Integer> originLabourHourSsu = originCoupon.getLabourHourSsu();
            Map<Long, Integer> labourHourSsu = coupon.getLabourHourSsu();
            if (!Objects.equals(originLabourHourSsu, labourHourSsu)) {
                modifyContentVOList.add(getLabourHourPartsModifyContent("工时ssu", originLabourHourSsu, labourHourSsu));
            }
            Map<Long, Integer> originPartsSsu = originCoupon.getPartsSsu();
            Map<Long, Integer> partsSsu = coupon.getPartsSsu();
            if (!Objects.equals(originPartsSsu, partsSsu)) {
                modifyContentVOList.add(getLabourHourPartsModifyContent("配件ssu", originPartsSsu, partsSsu));
            }
        }
        // 非汽车售后业务比对商品列表
        else {
            String goodContent;
            if (coupon.getScopeType() == CouponScopeTypeEnum.Goods.getValue()) {
                goodContent = generateProductDiffDesc(originCoupon.getGoodsInclude(), coupon.getGoodsInclude(), "商品");
            } else {
                goodContent = generateProductDiffDesc(originCoupon.getGoodsInclude(), coupon.getGoodsInclude(), "商品");
                String excludeGood = generateProductDiffDesc(originCoupon.getGoodsExclude(), coupon.getGoodsExclude(), "黑名单");
                if (StringUtils.isNotBlank(excludeGood)) {
                    goodContent += (StringUtils.isEmpty(goodContent) ? "" : "/") + excludeGood;
                }
                Set<Integer> originCategoryIds = originCoupon.getCategoryIds();
                Set<Integer> categoryIds = coupon.getCategoryIds();
                Set<Integer> addCategory = diffSet(categoryIds, originCategoryIds);
                Set<Integer> deleteCategory = diffSet(originCategoryIds, categoryIds);
                if (CollectionUtils.isNotEmpty(addCategory)) {
                    goodContent += (StringUtils.isEmpty(goodContent) ? "" : "/") + "新增品类" + StringUtils.join(addCategory, ",");
                }
                if (CollectionUtils.isNotEmpty(deleteCategory)) {
                    goodContent += (StringUtils.isEmpty(goodContent) ? "" : "/") + "删除品类" + StringUtils.join(deleteCategory, ",");
                }
            }
            if (StringUtils.isNotBlank(goodContent)) {
                String type = "适用商品";
                modifyContentVOList.add(new ModifyContentVO(type, goodContent));
            }
        }

        //发放数量、可领数量
        if (!originCoupon.getApplyCount().equals(coupon.getApplyCount())) {
            String type = "发放总量";
            String origin = originCoupon.getApplyCount() + "张";
            String now = coupon.getApplyCount() + "张";
            modifyContentVOList.add(new ModifyContentVO(type, generateEditDesc(origin, now)));
        }
        if (!originCoupon.getFetchLimit().equals(coupon.getFetchLimit())) {
            String type = "每人限领";
            String origin = originCoupon.getFetchLimit() + "张";
            String now = coupon.getFetchLimit() + "张";
            modifyContentVOList.add(new ModifyContentVO(type, generateEditDesc(origin, now)));
        }
        //特殊规则
        ExtPropVO originExtProp = originCoupon.getExtProp();
        ExtPropVO extProp = coupon.getExtProp();

        Map<String, Integer> extPropOld = GsonUtil.fromMapJson(GsonUtil.toJson(originExtProp),new TypeToken<Map<String, Integer>>() {}.getType());
        Map<String, Integer> extPropNew = GsonUtil.fromMapJson(GsonUtil.toJson(extProp),new TypeToken<Map<String, Integer>>() {}.getType());

        Set<String> addField = new HashSet<>();
        Set<String> deleteFiled = new HashSet<>();
        extPropOld.forEach((k, v) -> {
            if (extPropNew.get(k) != v) {
                if(v == YesNoEnum.Yes.getMysqlValue()){
                    deleteFiled.add(ExtPropEnum.getByField(k).getName());
                }else{
                    addField.add(ExtPropEnum.getByField(k).getName());
                }
            }
        });

        if (CollectionUtils.isNotEmpty(addField) || CollectionUtils.isNotEmpty(deleteFiled)) {
            boolean modified = false;
            String type = "特殊规则";
            StringBuilder detail = new StringBuilder();
            if (CollectionUtils.isNotEmpty(addField)) {
                detail.append("新增").append("“").append(String.join(",", addField)).append("”");
                modified = true;
            }
            if (CollectionUtils.isNotEmpty(deleteFiled)) {
                detail.append(modified ? "/" : "").append("删除").append("“").append(String.join(",", deleteFiled)).append("”");
            }
            modifyContentVOList.add(new ModifyContentVO(type, detail.toString()));
        }
        //成本分摊
        if (!isMapSame(originCoupon.getCostShare(), coupon.getCostShare())) {
            String type = "成本分摊";
            modifyContentVOList.add(new ModifyContentVO(type, generateEditDesc(generateCostDesc(originCoupon.getCostShare()), generateCostDesc(coupon.getCostShare()))));
        }
        return modifyContentVOList;
    }

    /**
     * 获取使用时长文案
     *
     * @param useDuration       使用时长（小时）
     * @param timeGranularity   单位
     * @return                  使用时长文案
     * @throws BizError         bizError
     */
    private String getUseDurationContent(int useDuration, int timeGranularity) throws BizError {

        UseTimeGranularityEnum timeGranularityEnum = UseTimeGranularityEnum.getByValue(timeGranularity);
        if (Objects.isNull(timeGranularityEnum)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "非法使用时间粒度枚举");
        }

        if (UseTimeGranularityEnum.DAY.equals(timeGranularityEnum)) {
            useDuration = useDuration / 24;
        }
        return useDuration + timeGranularityEnum.getName();
    }

    /**
     * 比对工时ssu、配件ssu修改内容
     *
     * @param type  工时ssu or 配件ssu
     * @param originMap 修改前map
     * @param map       修改后map
     * @return  修改文案
     */
    private ModifyContentVO getLabourHourPartsModifyContent(String type, Map<Long, Integer> originMap, Map<Long, Integer> map) {
        Set<Long> originSsuSet = originMap.keySet();
        Set<Long> ssuSet = map.keySet();

        StringBuilder sb = new StringBuilder();

        // 新增
        SetUtils.SetView<Long> addSsuSet = SetUtils.difference(ssuSet, originSsuSet);
        if (CollectionUtils.isNotEmpty(addSsuSet)) {
            sb.append("新增: ");
            sb.append(addSsuSet.stream().map(ssu -> String.format(SSU_NUM_PAIR_TEMP, ssu, map.get(ssu))).collect(Collectors.joining(";")));
        }

        // 删除
        SetUtils.SetView<Long> deleteSsuSet = SetUtils.difference(originSsuSet, ssuSet);
        if (CollectionUtils.isNotEmpty(deleteSsuSet)) {
            sb.append(sb.length() > 0 ? "/" : "");
            sb.append("删除: ");
            sb.append(deleteSsuSet.stream().map(ssu -> String.format(SSU_NUM_PAIR_TEMP, ssu, originMap.get(ssu))).collect(Collectors.joining("; ")));
        }

        // 修改
        List<Long> changeSsuList = SetUtils.intersection(originSsuSet, ssuSet).stream()
                .filter(ssu -> !Objects.equals(originMap.get(ssu), map.get(ssu)))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(changeSsuList)) {
            sb.append(sb.length() > 0 ? "/" : "");
            sb.append("修改: ");
            sb.append(changeSsuList.stream().map(ssu -> String.format(SSU_NUM_CHANGE_TEMP, ssu, originMap.get(ssu), map.get(ssu))).collect(Collectors.joining(";")));
        }

        return new ModifyContentVO(type, sb.toString());
    }

    private boolean getChannelContent(StringBuilder channelModifyContent,UseChannelPO originUseChannelPO, UseChannelPO useChannelPO, UseChannelsEnum useChannelsEnum) {
        boolean result = false;
        if(useChannelsEnum.getValue() != UseChannelsEnum.XIAOMI_SHOP.getValue()){
            if(originUseChannelPO ==null && useChannelPO !=null){
                channelModifyContent.append("新增").append("“").append(useChannelsEnum.getName()).append("”");
                channelModifyContent.append(",").append(useChannelPO.isAll() ? "全部":"指定门店");
                if(CollectionUtils.isNotEmpty(useChannelPO.getLimitIds())){
                    channelModifyContent.append("[").append(StringUtils.join(useChannelPO.getLimitIds(),",")).append("]");
                }
                channelModifyContent.append("。");
                result = true;
            }if(originUseChannelPO !=null && useChannelPO ==null){
                channelModifyContent.append("删除").append("“").append(useChannelsEnum.getName()).append("”");
                channelModifyContent.append(",").append(originUseChannelPO.isAll() ? "全部":"指定门店");
                if(CollectionUtils.isNotEmpty(originUseChannelPO.getLimitIds())){
                    channelModifyContent.append("[").append(StringUtils.join(originUseChannelPO.getLimitIds(),",")).append("]");
                }
                channelModifyContent.append("。");
                result = true;
            }if(originUseChannelPO !=null && useChannelPO !=null){
                if(originUseChannelPO.isAll() != useChannelPO.isAll() ){
                    channelModifyContent.append("修改").append(useChannelsEnum.getName()).append(",由").append(originUseChannelPO.isAll() ? "全部":"指定门店");
                    channelModifyContent.append("改为").append(useChannelPO.isAll() ? "全部":"指定门店").append("。");
                    result = true;
                }
                if(!CouponCollectionUtil.equalsStringList(originUseChannelPO.getLimitIds(),useChannelPO.getLimitIds())){
                    List<String> delete=CouponCollectionUtil.removeAll(originUseChannelPO.getLimitIds(),useChannelPO.getLimitIds());
                    List<String> add=CouponCollectionUtil.removeAll(useChannelPO.getLimitIds(),originUseChannelPO.getLimitIds());
                    if(CollectionUtils.isNotEmpty(delete)){
                        channelModifyContent.append("指定门店删除：[").append(StringUtils.join(delete,",")).append("]");
                    }
                    if(CollectionUtils.isNotEmpty(add)){
                        channelModifyContent.append("指定门店增加：[").append(StringUtils.join(add,",")).append("]");
                    }
                    result = true;
                }
            }
        }
        return result;
    }

    /**
     * 生成商品diff描述
     * @param originGoodsInclude
     * @param goodsInclude
     * @param desc
     * @return
     */
    private String generateProductDiffDesc(Map<String, Set<Long>> originGoodsInclude, Map<String, Set<Long>> goodsInclude, String desc) {
        Set<Long> addSku = diffSet(goodsInclude.get(GoodsLevelEnum.Sku.getValue()), originGoodsInclude.get(GoodsLevelEnum.Sku.getValue()));
        Set<Long> deleteSku = diffSet(originGoodsInclude.get(GoodsLevelEnum.Sku.getValue()), goodsInclude.get(GoodsLevelEnum.Sku.getValue()));
        Set<Long> addPackage = diffSet(goodsInclude.get(GoodsLevelEnum.Package.getValue()), originGoodsInclude.get(GoodsLevelEnum.Package.getValue()));
        Set<Long> deletePackage = diffSet(originGoodsInclude.get(GoodsLevelEnum.Package.getValue()), goodsInclude.get(GoodsLevelEnum.Package.getValue()));
        Set<Long> addSsu = diffSet(goodsInclude.get(GoodsLevelEnum.Ssu.getValue()), originGoodsInclude.get(GoodsLevelEnum.Ssu.getValue()));
        Set<Long> deleteSsu = diffSet(originGoodsInclude.get(GoodsLevelEnum.Ssu.getValue()), goodsInclude.get(GoodsLevelEnum.Ssu.getValue()));
        Set<Long> addSuit = diffSet(goodsInclude.get(GoodsLevelEnum.Suit.getValue()), originGoodsInclude.get(GoodsLevelEnum.Suit.getValue()));
        Set<Long> deleteSuit = diffSet(originGoodsInclude.get(GoodsLevelEnum.Suit.getValue()), goodsInclude.get(GoodsLevelEnum.Suit.getValue()));
        StringBuilder content = new StringBuilder();
        //新增
        boolean added = false;
        StringBuilder addProduct = new StringBuilder();
        if (CollectionUtils.isNotEmpty(addSku)) {
            addProduct.append("sku:").append(StringUtils.join(addSku, ","));
            added = true;
        }
        if (CollectionUtils.isNotEmpty(addPackage)) {
            addProduct.append(added ? "|" : "").append("套装ID:").append(StringUtils.join(addPackage, ","));
            added = true;
        }
        if (CollectionUtils.isNotEmpty(addSuit)) {
            addProduct.append(added ? "|" : "").append("新套装ID:").append(StringUtils.join(addSuit, ","));
            added = true;
        }
        if (CollectionUtils.isNotEmpty(addSsu)) {
            addProduct.append(added ? "|" : "").append("ssu:").append(StringUtils.join(addSsu, ","));
            added = true;
        }
        if (added) {
            content.append("新增").append(desc).append(":").append(addProduct);
        }
        //删除
        boolean deleted = false;
        StringBuilder deleteProduct = new StringBuilder();
        if (CollectionUtils.isNotEmpty(deleteSku)) {
            deleteProduct.append("sku:").append(StringUtils.join(deleteSku, ","));
            deleted = true;
        }
        if (CollectionUtils.isNotEmpty(deletePackage)) {
            deleteProduct.append(deleted ? "|" : "").append("套装ID:").append(StringUtils.join(deletePackage, ","));
        }
        if (CollectionUtils.isNotEmpty(deleteSsu)) {
            deleteProduct.append(deleted ? "|" : "").append("ssu:").append(StringUtils.join(deleteSsu, ","));
        }
        if (CollectionUtils.isNotEmpty(deleteSuit)) {
            deleteProduct.append(deleted ? "|" : "").append("新套装ID:").append(StringUtils.join(deleteSuit, ","));
            deleted = true;
        }
        if (deleted) {
            content.append(added ? "/" : "").append("删除").append(desc).append(":").append(deleteProduct);
        }
        return content.toString();
    }

    /**
     * 生成类似 由...修改为...的描述
     *
     * @param origin
     * @param now
     * @param <T>
     * @param <K>
     * @return
     */
    private <T, K> String generateEditDesc(T origin, K now) {
        return "由" + "“" + origin + "”" + "修改为" + "“" + now + "”";
    }

    /**
     * 生成分摊信息描述
     *
     * @param costMap
     * @return
     */
    private String generateCostDesc(Map<Integer, Integer> costMap) {
        if (MapUtils.isEmpty(costMap)) {
            return "无分摊";
        }
        StringBuilder content = new StringBuilder();
        boolean first = true;
        for (int department : costMap.keySet()) {
            content.append(first ? "" : ";").append(CostShareEnum.getByValue(department).getName()).append("承担").append(costMap.get(department)).append("%");
            first = false;
        }
        return content.toString();
    }

    /**
     * 判断给定map是否一样
     *
     * @param m1
     * @param m2
     * @param <T>
     * @param <K>
     * @return
     */
    private <T, K> boolean isMapSame(Map<T, K> m1, Map<T, K> m2) {
        if (MapUtils.isEmpty(m1) && MapUtils.isEmpty(m2)) {
            return true;
        }
        if ((MapUtils.isEmpty(m1) && MapUtils.isNotEmpty(m2)) || (MapUtils.isEmpty(m2) && MapUtils.isNotEmpty(m1))) {
            return false;
        }
        if (m1.size() != m2.size()) {
            return false;
        }
        for (T t : m1.keySet()) {
            if (!m2.containsKey(t) || !m1.get(t).equals(m2.get(t))) {
                return false;
            }
        }
        for (T t : m2.keySet()) {
            if (!m1.containsKey(t) || !m1.get(t).equals(m2.get(t))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 将po转为对比对象
     *
     * @param couponConfigPO
     * @return
     */
    private CouponModifyDimension convertPOToCouponDimension(CouponConfigPO couponConfigPO) {
        CouponModifyDimension couponModifyDimension = new CouponModifyDimension();
        couponModifyDimension.setName(couponConfigPO.getName());
        couponModifyDimension.setStatus(couponConfigPO.getStatus());
        couponModifyDimension.setCouponDesc(couponConfigPO.getCouponDesc());
        couponModifyDimension.setPromotionType(couponConfigPO.getPromotionType());
        couponModifyDimension.setBottomType(couponConfigPO.getBottomType());
        couponModifyDimension.setBottomPrice(couponConfigPO.getBottomPrice());
        couponModifyDimension.setBottomCount(couponConfigPO.getBottomCount());
        couponModifyDimension.setPromotionValue(couponConfigPO.getPromotionValue());
        couponModifyDimension.setMaxReduce(couponConfigPO.getMaxReduce());
        couponModifyDimension.setSendScene(couponConfigPO.getSendScene());
        couponModifyDimension.setSendPurpose(couponConfigPO.getSendPurpose());
        couponModifyDimension.setStartFetchTime(couponConfigPO.getStartFetchTime());
        couponModifyDimension.setEndFetchTime(couponConfigPO.getEndFetchTime());
        couponModifyDimension.setUseTimeType(couponConfigPO.getUseTimeType());
        couponModifyDimension.setStartUseTime(couponConfigPO.getStartUseTime());
        couponModifyDimension.setEndUseTime(couponConfigPO.getEndUseTime());
        couponModifyDimension.setUseDuration(couponConfigPO.getUseDuration());
        couponModifyDimension.setTimeGranularity(couponConfigPO.getTimeGranularity());
        //如果为空统一转成空对象，省去后续一堆判空逻辑
        couponModifyDimension.setUseChannel(StringUtils.isEmpty(couponConfigPO.getUseChannel()) ? new HashSet<>() :
                StringUtil.convertToIntegerSet(couponConfigPO.getUseChannel()));
        couponModifyDimension.setUsePlatform(StringUtils.isEmpty(couponConfigPO.getUsePlatform()) ? new HashMap<>() :
                GsonUtil.get().fromJson(couponConfigPO.getUsePlatform(), new TypeToken<Map<Integer, UseChannelPO>>() {
        }.getType()));
        couponModifyDimension.setUseStore(StringUtils.isEmpty(couponConfigPO.getUseStore()) ? new HashMap<>() :
                GsonUtil.get().fromJson(couponConfigPO.getUseStore(), new TypeToken<Map<Integer, UseChannelPO>>() {
        }.getType()));
        couponModifyDimension.setScopeType(couponConfigPO.getScopeType());
        Integer bizPlatform = couponConfigPO.getBizPlatform();
        // 汽车售后业务构造工时、配件map
        if (BizPlatformEnum.CAR_AFTER_SALE.getCode().equals(bizPlatform)) {
            couponModifyDimension.setGoodsInclude(Collections.emptyMap());
            couponModifyDimension.setGoodsExclude(Collections.emptyMap());

            GoodItemPO goodItemPO = GsonUtil.fromJson(couponConfigPO.getGoodsInclude(), GoodItemPO.class);
            couponModifyDimension.setLabourHourSsu(Optional.ofNullable(goodItemPO).map(GoodItemPO::getLabourHourSsu).orElse(Collections.emptyMap()));
            couponModifyDimension.setPartsSsu(Optional.ofNullable(goodItemPO).map(GoodItemPO::getPartsSsu).orElse(Collections.emptyMap()));
        }
        // 非汽车售后业务
        else {
            couponModifyDimension.setGoodsInclude(StringUtils.isEmpty(couponConfigPO.getGoodsInclude()) ? new HashMap<>() :
                    GsonUtil.get().fromJson(couponConfigPO.getGoodsInclude(), new TypeToken<Map<String, Set<Long>>>() {
                    }.getType()));
            couponModifyDimension.setGoodsExclude(StringUtils.isEmpty(couponConfigPO.getGoodsExclude()) ? new HashMap<>() :
                    GsonUtil.get().fromJson(couponConfigPO.getGoodsExclude(), new TypeToken<Map<String, Set<Long>>>() {
                    }.getType()));

            couponModifyDimension.setLabourHourSsu(Collections.emptyMap());
            couponModifyDimension.setPartsSsu(Collections.emptyMap());
        }
        couponModifyDimension.setCategoryIds(StringUtils.isEmpty(couponConfigPO.getCategoryIds()) ? new HashSet<>() :
                StringUtil.convertToIntegerSet(couponConfigPO.getCategoryIds()));
        couponModifyDimension.setApplyCount(couponConfigPO.getApplyCount());
        couponModifyDimension.setFetchLimit(couponConfigPO.getFetchLimit());
        couponModifyDimension.setExtProp(GsonUtil.fromJson(couponConfigPO.getExtProp(), ExtPropVO.class));
        couponModifyDimension.setAreaIds(StringUtils.isEmpty(couponConfigPO.getAreaIds()) ? new HashSet<>() :
                StringUtil.convertToIntegerSet(couponConfigPO.getAreaIds()));
        couponModifyDimension.setCostShare(StringUtils.isEmpty(couponConfigPO.getCostShare()) ? new HashMap<>() :
                GsonUtil.get().fromJson(couponConfigPO.getCostShare(), new TypeToken<Map<Integer, Integer>>() {
        }.getType()));
        couponModifyDimension.setBizPlatform(bizPlatform);
        return couponModifyDimension;

    }

    /**
     * 生成渠道修改描述
     *
     * @param channelSet 新旧渠道交集
     * @param originStore 原门店
     * @param store 现门店
     * @return
     */
    private String generateChannelDesc(Set<Integer> channelSet, Map<Integer, UseChannelPO> originStore, Map<Integer, UseChannelPO> store) {
        StringBuilder content = new StringBuilder();
        boolean modified = false;
        for (int channel : channelSet) {
            if (channel == UseChannelsEnum.XIAOMI_SHOP.getValue() || channel == UseChannelsEnum.FORTRESS_STORE.getValue()) {
                continue;
            }
            UseChannelPO originUseChannelPO = originStore.get(channel);
            UseChannelPO useChannelPO = store.get(channel);
            if (originUseChannelPO.isAll() != useChannelPO.isAll()) {
                String origin = UseChannelsEnum.getByValue(channel).getName() + (originUseChannelPO.isAll() ? " - 全部门店" : " - 指定门店");
                String now = UseChannelsEnum.getByValue(channel).getName() + (useChannelPO.isAll() ? " - 全部门店" : " - 指定门店");
                content.append(modified ? "/" : "").append(generateEditDesc(origin, now));
                modified = true;
            } else {
                Set<String> addStore = diffSet(new HashSet<>(useChannelPO.getLimitIds()), new HashSet<>(originUseChannelPO.getLimitIds()));
                Set<String> deleteStore = diffSet(new HashSet<>(originUseChannelPO.getLimitIds()), new HashSet<>(useChannelPO.getLimitIds()));
                if (CollectionUtils.isNotEmpty(addStore) || CollectionUtils.isNotEmpty(deleteStore)) {
                    content.append(modified ? "/" : "").append("修改").append(UseChannelsEnum.getByValue(channel).getName()).append(" - 指定门店");
                }
            }
        }
        return content.toString();
    }

    /**
     * 求s1对s2的差集
     *
     * @param s1
     * @param s2
     * @param <T>
     * @return
     */
    private <T> Set<T> diffSet(Set<T> s1, Set<T> s2) {
        if (s1 == null) {
            return new HashSet<>();
        }
        if (CollectionUtils.isEmpty(s1) || CollectionUtils.isEmpty(s2)) {
            return s1;
        }
        Set<T> res = new HashSet<>();
        s1.forEach(item -> {
            if (!s2.contains(item)) {
                res.add(item);
            }
        });
        return res;
    }


}
