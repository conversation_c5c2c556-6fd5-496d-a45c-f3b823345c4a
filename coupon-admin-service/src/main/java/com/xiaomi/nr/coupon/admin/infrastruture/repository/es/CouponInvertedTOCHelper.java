package com.xiaomi.nr.coupon.admin.infrastruture.repository.es;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponOnlineStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.es.po.CouponEsPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.SearchConfigParam;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: hejiapeng
 * @Date 2022/2/28 5:58 下午
 * @Version: 1.0
 **/
@Component
public class CouponInvertedTOCHelper  extends CouponConfigESHelper {

    /**
     * 在线每次获取的最大优惠券数量
     */
    private final static int OFFLINE_CONFIGID_PAGE_SIZE = 1000;

    /**
     * 构造ToC倒排查询条件
     *
     * @param level
     * @param item
     * @param timeNow
     * @return
     */
    @Override
    public SearchSourceBuilder buildSourceBuilder(SearchConfigParam searchConfigParam, String level, long item, long timeNow) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        boolQuery.filter(QueryBuilders.termsQuery(level, Lists.newArrayList(item)));
        boolQuery.filter(QueryBuilders.termQuery(CouponEsPO.STATUS, CouponOnlineStatusEnum.ONLINE.getCode()));
        boolQuery.filter(QueryBuilders.rangeQuery(CouponEsPO.END_FETCH_TIME).gt(timeNow));

        sourceBuilder.query(boolQuery);
        sourceBuilder.size(OFFLINE_CONFIGID_PAGE_SIZE);

        return sourceBuilder;
    }

    @Override
    public SearchSourceBuilder buildSourceBuilder(SearchConfigParam searchConfigParam, long nowTime) {
        return null;
    }

    @Override
    public SearchSourceBuilder buildSourceBuilder(SearchConfigParam searchConfigParam, boolean isRelativeTime, long itemId) {
        return null;
    }

}
