package com.xiaomi.nr.coupon.admin.enums.coupondata;

public enum CouponDataTypeEnum {

    /**
     * 所有发放数据
     */
    EXTERNAL_COUPON_DATA("external","外部系统发放"),

    /**
     * 灌券发放数据
     */
    FILL_COUPON_DATA("marketing","灌券系统发放");

    private final String code;
    private final String name;

    CouponDataTypeEnum(String code, String name) {
        this.code =code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public static String findNameByCode(String code) {
        CouponDataTypeEnum[] values = CouponDataTypeEnum.values();
        for (CouponDataTypeEnum item : values) {
            if(item.getCode().equals(code)) {
                return item.name;
            }
        }
        return "全部";
    }
}
