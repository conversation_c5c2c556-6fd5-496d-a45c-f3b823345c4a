package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.goods;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponGoodsInfo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponScopeTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

/**
 * 分类券
 * pids = 分类下pid-黑名单pid
 * exceptPids = 黑名单pid
 */
@Slf4j
@Component
public class ThirdCategoriesHandler extends CouponGoodsBaseHandler {

    @Override
    public void handleGoods(CouponConfigItem couponConfigItem) throws Exception {
        CouponGoodsInfo couponGoodsInfo = couponConfigItem.getCouponGoodsInfo();
        if (CollectionUtils.isEmpty(couponGoodsInfo.getCategoryIds())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "通过选择品类建券，分类信息不能为空");
        }
        if (MapUtils.isEmpty(couponGoodsInfo.getGoodsInclude())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "通过选择品类建券，商品信息不能为空");
        }
        this.checkNyuan(couponConfigItem);
    }

    @Override
    public CouponScopeTypeEnum getScopeType() {
        return CouponScopeTypeEnum.Categories;
    }
}
