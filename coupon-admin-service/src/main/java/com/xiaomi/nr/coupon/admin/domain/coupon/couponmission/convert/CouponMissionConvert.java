package com.xiaomi.nr.coupon.admin.domain.coupon.couponmission.convert;

import com.xiaomi.nr.coupon.admin.api.dto.mission.MissionDto;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.coupon.MissionConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig.po.PolicyItem;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponmission.po.MissionJoinConfigPo;
import com.xiaomi.nr.coupon.admin.enums.MissionStatEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponmission.TimeTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class CouponMissionConvert {

    /**
     * 根据券发放任务信息和券配置信息组装返回信息 DB -> DTO
     *
     * @param missionJoinConfigPo 券发放任务和券配置信息(DB)
     * @return MissionDto         券发放任务DTO信息
     */
    public MissionDto makeMissionDto(MissionJoinConfigPo missionJoinConfigPo) throws BizError {
        if(Objects.isNull(missionJoinConfigPo)){
            return null;
        }

        Long missionId = missionJoinConfigPo.getMissionId();
        String missionName = missionJoinConfigPo.getMissionName();
        String status = missionJoinConfigPo.getMissionStatus();
        String statusDesc = MissionStatEnum.findByValue(status);
        String missionType = missionJoinConfigPo.getMissionType().toString();
        Long sendNumList = formatSendNum(missionJoinConfigPo);
        Integer couponDays = missionJoinConfigPo.getSource() == 1 ? missionJoinConfigPo.getCouponDays() : missionJoinConfigPo.getCouponDays() / 24;
        Integer couponHours = missionJoinConfigPo.getSource() == 1 ? 0 : missionJoinConfigPo.getCouponDays() % 24;
        String timeType = missionJoinConfigPo.getCouponDays() == 0 ? TimeTypeEnum.SECTION.getRedisValue() : TimeTypeEnum.DAYS.getRedisValue();
        String timeTypeDesc = formatTimeTypeDesc(timeType);
        Long couponStartTime = missionJoinConfigPo.getCouponStartTime();
        Long couponEndTime = missionJoinConfigPo.getCouponEndTime();
        Long addTime = missionJoinConfigPo.getAddTime();
        Long couponConfigId = missionJoinConfigPo.getCouponConfigId();
        String couponConfigName = missionJoinConfigPo.getCouponName();
        String useType = UseTypeEnum.findRedisValueByMysqlValue(missionJoinConfigPo.getCouponTypeCode());
        String useTypeDesc = UseTypeEnum.findNameByRedisValue(useType);
        //PolicyItem policy = getPolicy(missionJoinConfigPo);
        //Map<String, String> showMap = getShowList(useType, policy);
        Map<String, String> showMap = getShowList(missionJoinConfigPo.getCouponTypeCode(), missionJoinConfigPo.getPromotionValue());
        String showValue = showMap.get(MissionConstant.SHOW_VALUE);
        String showUnit = showMap.get(MissionConstant.SHOW_UNIT);
        String couponSendChannel = missionJoinConfigPo.getSendChannel();
        Long globalCouponStartTime = missionJoinConfigPo.getGlobalCouponStartTime();
        Long globalCouponEndTime = missionJoinConfigPo.getGlobalCouponEndTime();

        MissionDto missionDto = new MissionDto();
        missionDto.setMissionId(missionId);
        missionDto.setMissionName(missionName);
        missionDto.setStatus(status);
        missionDto.setStatusDesc(statusDesc);
        missionDto.setMissionType(missionType);
        missionDto.setSendNumLimit(sendNumList);
        missionDto.setTimeType(timeType);
        missionDto.setTimeTypeDesc(timeTypeDesc);
        missionDto.setCouponStartTime(couponStartTime);
        missionDto.setCouponEndTime(couponEndTime);
        missionDto.setCouponDays(couponDays);
        missionDto.setCouponHours(couponHours);
        missionDto.setAddTime(addTime);
        missionDto.setCouponConfigId(couponConfigId);
        missionDto.setCouponConfigName(couponConfigName);
        missionDto.setCouponTypeCode(useType);
        missionDto.setCouponTypeCodeDesc(useTypeDesc);
        missionDto.setUseType(useType);
        missionDto.setUseTypeDesc(useTypeDesc);
        missionDto.setShowValue(showValue);
        missionDto.setShowUnit(showUnit);
        missionDto.setCouponSendChannel(couponSendChannel);
        missionDto.setGlobalCouponStartTime(globalCouponStartTime);
        missionDto.setGlobalCouponEndTime(globalCouponEndTime);
        missionDto.setCouponType(missionJoinConfigPo.getCouponType());
        missionDto.setShipmentId(missionJoinConfigPo.getShipmentId());
        return missionDto;
    }


    /**
     * 获取券配置中policy信息
     *
     * @param missionJoinConfigPo 数据库原始券发放任务和券配置信息
     * @return PolicyItem         券政策信息
     * @throws BizError           业务异常
     */
    private PolicyItem getPolicy(MissionJoinConfigPo missionJoinConfigPo) throws BizError {
        List<PolicyItem> policy = GsonUtil.fromListJson(missionJoinConfigPo.getPolicy(), PolicyItem.class);
        if(Objects.isNull(policy)){
            log.info("getShowList,数据库获取券配置policy数据为空!");
            throw ExceptionHelper.create(ErrCode.COUPON, "数据库获取券配置policy数据为空!");
        }
        return policy.get(0);
    }

    /**
     * 格式化券发放任务的最大发放数量
     *
     * @param missionJoinConfigPo 券发放任务&券配置信息(DB)
     * @return Long               券发放任务的最大发放数量
     */
    private Long formatSendNum(MissionJoinConfigPo missionJoinConfigPo) {
        long sendNumList;
        if (StringUtils.equals("", missionJoinConfigPo.getGroupIds())) {
            sendNumList = missionJoinConfigPo.getSendNum();
            return sendNumList;
        }

        sendNumList = missionJoinConfigPo.getMaxNum();
        return sendNumList;
    }


    /**
     * Value相关字段展示转换
     *
     * @param useType  现金券/折扣券/抵扣券
     * @param policy    券政策信息
     * @return Map<>    showValue、showUnit映射
     */
    private Map<String, String> getShowList(String useType, PolicyItem policy) {

        Map<String, String> showMap = new HashMap<>();

        //现金券
        if (StringUtils.equals(UseTypeEnum.Cash.getRedisValue(), useType)) {
            String showValue =  new BigDecimal(policy.getRule().getReduceMoney()).stripTrailingZeros().toPlainString();
            showMap.put(MissionConstant.SHOW_VALUE, showValue);
            showMap.put(MissionConstant.SHOW_UNIT, MissionConstant.COUPON_CASH_UNIT);
            return showMap;
        }

        //折扣券
        if (StringUtils.equals(UseTypeEnum.Discount.getRedisValue(), useType)) {
            BigDecimal reduceValue = new BigDecimal(policy.getRule().getReduceDiscount());
            showMap.put(MissionConstant.SHOW_VALUE, reduceValue.multiply(new BigDecimal("10")).setScale(2, RoundingMode.UP).stripTrailingZeros().toPlainString());
            showMap.put(MissionConstant.SHOW_UNIT, MissionConstant.COUPON_DISCOUNT_UNIT);
            return showMap;
        }

        //抵扣券
        if (StringUtils.equals(UseTypeEnum.Deduction.getRedisValue(), useType)) {
            showMap.put(MissionConstant.SHOW_VALUE, MissionConstant.SHOW_EMPTY);
            showMap.put(MissionConstant.SHOW_UNIT, MissionConstant.SHOW_EMPTY);
            return showMap;
        }
        return showMap;
    }

    /**
     * Value相关字段展示转换
     *
     * @param useType  现金券/折扣券/抵扣券
     * @param promotionValue    券政策信息
     * @return Map<>    showValue、showUnit映射
     */
    private Map<String, String> getShowList(int useType, Long promotionValue) {

        Map<String, String> showMap = new HashMap<>();

        //现金券
        if (UseTypeEnum.Cash.getMysqlValue() == useType || UseTypeEnum.Reduce.getMysqlValue() == useType){
            String showValue = new BigDecimal(promotionValue).divide(new BigDecimal(100)).setScale(2).stripTrailingZeros().toPlainString();
            showMap.put(MissionConstant.SHOW_VALUE, showValue);
            showMap.put(MissionConstant.SHOW_UNIT, MissionConstant.COUPON_CASH_UNIT);
            return showMap;
        }

        //折扣券
        if (UseTypeEnum.Discount.getMysqlValue() == useType) {
            BigDecimal reduceValue = new BigDecimal(promotionValue);
            showMap.put(MissionConstant.SHOW_VALUE, reduceValue.divide(new BigDecimal(10)).setScale(2, RoundingMode.UP).stripTrailingZeros().toPlainString());
            showMap.put(MissionConstant.SHOW_UNIT, MissionConstant.COUPON_DISCOUNT_UNIT);
            return showMap;
        }

        //抵扣券
        if (UseTypeEnum.Deduction.getMysqlValue() == useType) {
            showMap.put(MissionConstant.SHOW_VALUE, MissionConstant.SHOW_EMPTY);
            showMap.put(MissionConstant.SHOW_UNIT, MissionConstant.SHOW_EMPTY);
            return showMap;
        }
        return showMap;
    }

    /**
     * 券的真实有效期类别描述
     *
     * @param timeType 券有效期时间类型
     * @return String  券有效期时间类型描述
     */
    public String formatTimeTypeDesc(String timeType) {

        if (StringUtils.equals(TimeTypeEnum.SECTION.getRedisValue(), timeType)) {
            return TimeTypeEnum.SECTION.getName();
        }

        if (StringUtils.equals(TimeTypeEnum.DAYS.getRedisValue(), timeType)) {
            return TimeTypeEnum.DAYS.getName();
        }

        return "";
    }

}
