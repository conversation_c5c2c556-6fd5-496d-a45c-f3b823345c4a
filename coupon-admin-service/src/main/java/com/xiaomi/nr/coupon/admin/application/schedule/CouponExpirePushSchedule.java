package com.xiaomi.nr.coupon.admin.application.schedule;

import com.xiaomi.nr.coupon.admin.application.schedule.constant.ScheduleConstant;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponpush.CouponExpirePushService;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.nr.infra.aop.clustertask.ClusterTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 优惠券即将过期push定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CouponExpirePushSchedule {

    @Resource
    private CouponExpirePushService couponExpirePushService;

    /**
     * 即将过期提醒
     * 《云店社群营销活动》《营销互动平台》投放场景的券
     * 每1小时执行一次
     */
    @Scheduled(cron = "0 0 */1 * * ?")
    @ClusterTask(value = "", zkLockPath = ScheduleConstant.COUPON_POSTFEE_EXPIRE_PUSH_LOCK)
    public void postFeeExpirePushScheduleTask(){
        long runStartTime = TimeUtil.getNowUnixMillis();
        int timeMark = CouponExpirePushService.TIME_MARK_12;
        try {
            log.info("postFeeExpirePushScheduleTask, start, timeMark={}", timeMark);
            long nowTime = TimeUtil.getNowUnixSecond();
            //12小时内即将过期的
            long filterEndTime = nowTime + timeMark*3600;
            couponExpirePushService.postFeeExpiringSoon(Integer.toString(timeMark), filterEndTime);
            log.info("postFeeExpirePushScheduleTask, execute success, runTime={}ms, timeMark={}", TimeUtil.sinceMillis(runStartTime), timeMark);
        } catch (Exception e) {
            log.error("postFeeExpirePushScheduleTask, execute fail, runTime={}ms, timeMark={}, {}", TimeUtil.sinceMillis(runStartTime), timeMark, e);
        }
    }







}
