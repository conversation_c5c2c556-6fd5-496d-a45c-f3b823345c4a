package com.xiaomi.nr.coupon.admin.application.dubbo.pointadmin.convert;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.response.CouponSceneTypeVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.response.SceneCatVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.response.SceneTypeVO;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointCategoryScene;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.SceneCategoryDto;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.PointSceneEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CarPointParentSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsParentScenePo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsScenePo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.modelmapper.TypeToken;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 场景
 * @Date: 2022.03.03 17:33
 */
@Component
public class PointSceneConvert {

    /**
     * 转换场景类型列表
     * @param carPointsScenePos,carPointsParentScenePos
     * @return
     */
    public List<PointCategoryScene> convertCouponSceneTypeVOS(List<CarPointsScenePo> carPointsScenePos, List<CarPointsParentScenePo> carPointsParentScenePos) {
        List<PointCategoryScene> pointChannelTypeVOList = new ArrayList<>();
        Map<Integer, List<CarPointsScenePo>> couponScenePOMap = carPointsScenePos.stream()
                .collect(Collectors.groupingBy(CarPointsScenePo::getParentId));

        // 按照一级场景类型排序
        carPointsParentScenePos.sort(Comparator.comparing(CarPointsParentScenePo::getId));
        for (CarPointsParentScenePo parentScene : carPointsParentScenePos) {

            int type = Math.toIntExact(parentScene.getId());
            PointCategoryScene pointCategoryScene = new PointCategoryScene(type, parentScene.getName(), convertTOCatVO(couponScenePOMap.get(type)));
            pointChannelTypeVOList.add(pointCategoryScene);
        }
        return pointChannelTypeVOList;
    }

    public List<SceneTypeVO> convertSceneTypeVOS(List<CarPointsScenePo> carPointsScenePos, List<CarPointsParentScenePo> carPointsParentScenePos) {
        List<SceneTypeVO> sceneTypeVOList = new ArrayList<>();
        Map<Integer, CarPointsParentScenePo> couponParentScenePOMap = carPointsParentScenePos.stream()
                .collect(Collectors.toMap(po -> Math.toIntExact(po.getId()), po -> po));

        for (CarPointsScenePo carPointsScenePo : carPointsScenePos) {
            SceneTypeVO sceneTypeVO = new SceneTypeVO();
            sceneTypeVO.setType(carPointsScenePo.getParentId());
            sceneTypeVO.setName(couponParentScenePOMap.get(carPointsScenePo.getParentId()).getName());
            sceneTypeVO.setSceneCategory(convertTOCatDTO(carPointsScenePo));
            sceneTypeVOList.add(sceneTypeVO);
        }
        return sceneTypeVOList;
    }


    public List<SceneCategoryDto> convertTOCatVO(List<CarPointsScenePo> pointsScenePoList) {
        List<SceneCategoryDto> sceneCatVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(pointsScenePoList)) {
            sceneCatVOList = pointsScenePoList.stream().map(this::convertTOCatDTO).collect(Collectors.toList());
        }
        return sceneCatVOList;
    }

    private SceneCategoryDto convertTOCatDTO(CarPointsScenePo carPointsScenePo) {
        SceneCategoryDto sceneCategoryDto = new SceneCategoryDto();
        sceneCategoryDto.setSceneCode(carPointsScenePo.getSceneCode());
        sceneCategoryDto.setSceneName(carPointsScenePo.getName());
        sceneCategoryDto.setCreateTime(carPointsScenePo.getCreateTime());
        sceneCategoryDto.setCreateUser(carPointsScenePo.getCreator());

        Map<String, String> contentMap = GsonUtil.fromMapJson(carPointsScenePo.getContent(), new TypeToken<Map<String, String>>() {
        }.getType());
        // 检查 contentMap 是否为 null，如果为 null，则创建一个空的 Map
        if (contentMap == null) {
            contentMap = new HashMap<>();
        }
        sceneCategoryDto.setAppName(contentMap.getOrDefault("appName", ""));
        sceneCategoryDto.setAssignRecordDesc(contentMap.getOrDefault("assignRecordDesc", ""));
        sceneCategoryDto.setExpireRecordDesc(contentMap.getOrDefault("expireRecordDesc", ""));
        sceneCategoryDto.setDelayRecordDesc(contentMap.getOrDefault("delayRecordDesc", ""));

        return sceneCategoryDto;
    }


}
