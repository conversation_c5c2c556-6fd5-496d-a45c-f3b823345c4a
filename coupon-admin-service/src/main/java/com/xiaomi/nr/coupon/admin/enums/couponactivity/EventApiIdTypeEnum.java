package com.xiaomi.nr.coupon.admin.enums.couponactivity;

/**
 * 优惠券领券活动接口返回ID类型 枚举
 *
 * <AUTHOR>
 */
public enum EventApiIdTypeEnum {
    /**
     * 发放任务ID
     */
    Mission("1", "发放任务ID"),

    /**
     * 配置ID
     */
    Config("23", "配置ID");


    private final String value;
    private final String name;

    public String getValue() {
        return value;
    }
    public String getName() {
        return name;
    }

    EventApiIdTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static EventApiIdTypeEnum getByValue(String code) {
        EventApiIdTypeEnum[] values = EventApiIdTypeEnum.values();
        for (EventApiIdTypeEnum item : values) {
            if (item.getValue().equals(code)) {
                return item;
            }
        }
        return null;
    }


}
