package com.xiaomi.nr.coupon.admin.infrastruture.repository;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.mapper.CarPointsBatchConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.mapper.CarPointsScenePoMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsScenePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/6
 */
@Component
@Slf4j
public class CarPointsSceneRepository {

    @Resource
    private CarPointsScenePoMapper carPointsScenePoMapper;

    /**
     * 批量查询场景信息
     *
     * @param sceneCodeList List
     * @return Map
     */
    public Map<String, CarPointsScenePo> batchGetSceneInfo(List<String> sceneCodeList) {
        List<CarPointsScenePo> r = carPointsScenePoMapper.batchGetSceneInfo(sceneCodeList);
        return r.stream().collect(Collectors.toMap(CarPointsScenePo::getSceneCode, e -> e, (k1, k2) -> k2));
    }

}
