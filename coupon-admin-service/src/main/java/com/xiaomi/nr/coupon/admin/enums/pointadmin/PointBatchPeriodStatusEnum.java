package com.xiaomi.nr.coupon.admin.enums.pointadmin;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 周期状态类型
 */
@AllArgsConstructor
@Getter
public enum PointBatchPeriodStatusEnum {

    /**
     * 1: 进行中
     */
    IN_PROGRESS(1, "进行中"),

    /**
     * 2: 未开始
     */
    NOT_STARTED(2, "未开始"),

    /**
     * 3: 已结束
     */
    COMPLETED(3, "已结束"),
    ;

    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String desc;

}
