package com.xiaomi.nr.coupon.admin.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.text.StringSubstitutor;
import org.apache.commons.text.lookup.StringLookup;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 字符串操作对象
 *
 * <AUTHOR>
 */
@Slf4j
public class StringUtil {
    /**
     * 占位符前缀
     */
    private static final String KEY_PREFIX = "{";
    /**
     * 占位符后缀
     */
    private static final String KEY_SUFFIX = "}";
    /**
     * 转义符
     */
    private static final char KEY_ESCAPE = '0';

    /**
     * 文本内容格式化
     * <code>
     * String content = "my name is {name}, and age is {age}";
     * Map<String, String> valuesMap = new HashMap<>();
     * valuesMap.put("name", "Tom");
     * valuesMap.put("age", "8");
     * String result = StringUtil.formatContent(content, valuesMap);
     * </code>
     * <p>
     * result is: my name is Tom, and age is 8
     * </p>
     *
     * @param content   包含占位符的字符串
     * @param valuesMap 数据值
     * @return 格式化好的结果
     */
    public static String formatContent(String content, Map<String, String> valuesMap) {
        StringSubstitutor sub = initStringSubstitutor();
        sub.setVariableResolver(new MapLookup(content, valuesMap));
        return sub.replace(content);
    }

    /**
     * 文本内容格式化
     * <code>
     * String content = "my name is {name}, and age is {age}";
     * String result = StringUtil.formatContent(content, "Tom", "8);
     * </code>
     * <p>
     * result is: my name is Tom, and age is 8
     * </p>
     *
     * @param content 包含占位符的字符串
     * @param values  数据值
     * @return 格式化好的结果
     */
    public static String formatContent(String content, String... values) {
        StringSubstitutor sub = initStringSubstitutor();
        sub.setVariableResolver(new ArrayLookup(content, values));
        return sub.replace(content);
    }

    /**
     * 提供默认的构造实现
     *
     * @return StringSubstitutor
     */
    private static StringSubstitutor initStringSubstitutor() {
        StringSubstitutor sub = new StringSubstitutor();
        sub.setVariablePrefix(KEY_PREFIX);
        sub.setVariableSuffix(KEY_SUFFIX);
        sub.setEscapeChar(KEY_ESCAPE);
        return sub;
    }

    /**
     * 根据数组元素来进行查找
     */
    private static class ArrayLookup implements StringLookup {
        private final String content;
        private final String[] values;
        private int index = 0;

        public ArrayLookup(String content, String[] values) {
            this.content = content;
            this.values = values;
        }

        @Override
        public String lookup(String var) {
            if (this.values == null) {
                throw new IllegalArgumentException(this.content + " parameters must not be empty");
            }
            if (this.values.length <= this.index) {
                throw new IllegalArgumentException(this.content + " incorrect number of parameters");
            }
            String value = this.values[this.index];
            if (value == null) {
                throw new IllegalArgumentException(this.content + " parameter value is incorrect");
            } else {
                ++this.index;
                return value;
            }
        }
    }

    /**
     * 根据Map元素来进行查找
     */
    private static class MapLookup implements StringLookup {
        private final String content;
        private final Map<String, String> valuesMap;

        public MapLookup(String content, Map<String, String> valuesMap) {
            this.content = content;
            this.valuesMap = valuesMap;
        }

        @Override
        public String lookup(String var) {
            if (this.valuesMap == null) {
                throw new IllegalArgumentException(this.content + " parameters must not be empty");
            }
            String value = this.valuesMap.get(var);
            if (value == null) {
                throw new IllegalArgumentException(this.content + " parameter value is incorrect");
            } else {
                return value;
            }
        }
    }

    /**
     * 生成接口权限验证        token
     * @param appId         appId
     * @param secret        appKey
     * @param paramsMap     拼接token参数列表
     * @param paramNameList 属性名
     * @return String       token
     */
    public static String makeToken(String appId, String secret, Map<String, String> paramsMap, List<String> paramNameList) {
        //按首字母排序
        Collections.sort(paramNameList);

        //拼接生成所有token的字符串
        StringBuilder str = new StringBuilder();
        str.append("appId").append("=").append(appId).append("&");

        for (String key : paramNameList) {
            if ("appId".equals(key) || "token".equals(key) ) {
                continue;
            }
            str.append(key).append("=").append(paramsMap.get(key)).append("&");
        }

        str.append("secret=").append(secret);

        //md5加密生成验证token
        return DigestUtils.md5Hex(str.toString());
    }


    /**
     * 生成权限验证用的token
     *
     * @param cls        参数对象
     * @param appId      appId
     * @param secret     appKey
     * @return String    token
     * @throws Exception 反射异常
     */
    public static String getToken(Object cls, String appId, String secret){
        Field[] fields = cls.getClass().getDeclaredFields();
        List<String> paramNameList = new ArrayList<>();
        for(int i=0; i< fields.length; i++){
            String fieldName = fields[i].getName();
            if(StringUtils.equals("serialVersionUID",fieldName) || StringUtils.equals("token",fieldName)){
                continue;
            }
            paramNameList.add(fieldName);
        }

        Map<String,String> paramMap  = new HashMap<>();
        try{
            for (String fieldName : paramNameList) {
                String firstLetter = fieldName.substring(0, 1).toUpperCase();
                String getter = "get" + firstLetter + fieldName.substring(1);
                Method method = cls.getClass().getMethod(getter);
                Object value = method.invoke(cls);
                paramMap.put(fieldName, String.valueOf(value));
            }
        }catch (Exception e){
            log.error("getToken Exception reason={}",e.getMessage(),e);
            return null;
        }

        return makeToken(appId,secret,paramMap,paramNameList);
    }

    /**
     * 把逗号分割的长整型数字列表字符串恢复成长整型List
     *
     * @param str
     * @return
     */
    public static List<Long> convertToLongList(String str) {
        List<Long> list = new ArrayList<>();
        if (StringUtils.isEmpty(str)) {
            return list;
        } else {
            list = Arrays.stream(str.split(","))
                    .map(String::trim)
                    .filter(NumberUtils::isParsable)
                    .map(s -> Long.parseLong(s.trim()))
                    .collect(Collectors.toList());
        }
        return list;
    }

    public static Set<Long> convertToLongSet(String str) {
        if (StringUtils.isEmpty(str)) {
            return Collections.EMPTY_SET;
        } else {
            return Arrays.stream(str.split(","))
                    .map(String::trim)
                    .filter(NumberUtils::isParsable)
                    .map(s -> Long.parseLong(s.trim()))
                    .collect(Collectors.toSet());
        }
    }



    /**
     * 把逗号分割的整型数字列表字符串恢复成整型List
     *
     * @param str
     * @return
     */
    public static List<Integer> convertToIntegerList(String str) {
        List<Integer> list = new ArrayList<>();
        if (StringUtils.isEmpty(str)) {
            return list;
        } else {
            list = Arrays.stream(str.split(","))
                    .map(String::trim)
                    .filter(NumberUtils::isParsable)
                    .map(s -> Integer.parseInt(s.trim()))
                    .collect(Collectors.toList());
        }
        return list;
    }

    /**
     * list转String且添加分隔符
     * @param delimiter
     * @param list
     * @param <T>
     * @return
     */
    public static <T> String join(String delimiter, List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        Objects.requireNonNull(delimiter);
        StringJoiner joiner = new StringJoiner(delimiter);
        for (T ele : list) {
            joiner.add(String.valueOf(ele));
        }
        return joiner.toString();
    }



    /**
     * 把逗号分割的长整型数字列表字符串恢复成长整型Set
     *
     * @param str
     * @return
     */
    public static Set<Integer> convertToIntegerSet(String str) {
        Set<Integer> set = new HashSet<>();
        if (str == null || "".equals(str)) {
            return set;
        } else {
            set = Arrays.stream(str.split(","))
                    .map(String::trim)
                    .filter(NumberUtils::isParsable)
                    .map(s -> Integer.parseInt(s.trim()))
                    .collect(Collectors.toSet());
        }
        return set;
    }


    /**
     * 把逗号分割的整型数字列表字符串恢复成整型List
     *
     * @param str
     * @return
     */
    public static List<String> convertToStringList(String str) {
        List<String> list = new ArrayList<>();
        if (StringUtils.isEmpty(str)) {
            return list;
        } else {
            list = Arrays.stream(str.split(","))
                    .map(String::trim).collect(Collectors.toList());
        }
        return list;
    }


}

