package com.xiaomi.nr.coupon.admin.application.dubbo.couponadmin;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.UserCouponCodeVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.request.UserCouponCodeListRequest;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponCodeService;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponCodeConvert;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponCodeRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.coupon.po.CouponCodePO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.coupon.po.SearchCodeListParam;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
public class DubboCouponCodeServiceImpl implements DubboCouponCodeService {

    @Autowired
    private CouponCodeConvert couponCodeConvert;

    @Autowired
    private CouponCodeRepository couponCodeRepository;


    @Override
    public Result<BasePageResponse<UserCouponCodeVO>> userCouponCodeList(UserCouponCodeListRequest request) {

        try {

            //封装查询参数
            SearchCodeListParam searchParam = couponCodeConvert.convertToSearchParam(request);
            PageHelper.startPage(request.getPageNo(), request.getPageSize());

            //具体查询逻辑
            List<CouponCodePO> couponCodePOList = couponCodeRepository.getUserCouponCode(searchParam);

            //封装返回数据
            List<UserCouponCodeVO> data = couponCodeConvert.convertUserCouponCodeVO(couponCodePOList);

            //分页返回
            BasePageResponse<UserCouponCodeVO> response = new BasePageResponse<>(request.getPageNo(), request.getPageSize());
            PageInfo<CouponCodePO> pageInfo = new PageInfo<>(couponCodePOList);
            response.setTotalCount(pageInfo.getTotal());
            response.setTotalPage(pageInfo.getPages());
            response.setList(data);

            log.info("DubboCouponCodeService.userCouponCodeList execute success, request={}", request);
            return Result.success(response);
        }catch (Exception e){
            log.error("DubboCouponCodeService.userCouponCodeList request={}, error: ",request , e);
            return Result.fromException(e);
        }
    }

}
