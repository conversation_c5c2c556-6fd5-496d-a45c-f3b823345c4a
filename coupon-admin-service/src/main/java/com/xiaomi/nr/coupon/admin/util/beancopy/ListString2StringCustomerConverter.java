package com.xiaomi.nr.coupon.admin.util.beancopy;

import ma.glasnost.orika.MappingContext;
import ma.glasnost.orika.converter.BidirectionalConverter;
import ma.glasnost.orika.metadata.Type;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class ListString2StringCustomerConverter extends BidirectionalConverter<List<String>,String> {

    @Override
    public String convertTo(List<String> strings, Type<String> type, MappingContext mappingContext) {
        return StringUtils.join(strings,",");
    }

    @Override
    public List<String> convertFrom(String s, Type<List<String>> type, MappingContext mappingContext) {
        if(StringUtils.isBlank(s)){
            return new ArrayList<>();
        }
        return Stream.of(s.split(",")).collect(Collectors.toList());
    }
}
