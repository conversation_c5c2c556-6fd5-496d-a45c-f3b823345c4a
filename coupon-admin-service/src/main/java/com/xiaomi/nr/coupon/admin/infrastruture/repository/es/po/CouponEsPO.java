package com.xiaomi.nr.coupon.admin.infrastruture.repository.es.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * @description: 优惠券ES索引PO
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/2/28 5:29 下午
 * @Version: 1.0
 **/
@Data
public class CouponEsPO {

    public final static String CONFIG_ID = "id";
    public final static String NAME = "name";
    public final static String STATUS = "status";
    public final static String SCENE = "scene";
    public final static String GID = "gid";
    public final static String SKU_ID = "sku";
    public final static String PACKAGE_ID = "package";
    public final static String SSU_ID = "ssu";
    public final static String START_FETCH_TIME = "startFetchTime";
    public final static String END_FETCH_TIME = "endFetchTime";
    public final static String START_USE_TIME = "startUseTime";
    public final static String END_USE_TIME = "endUseTime";
    public final static String USE_CHANNEL = "useChannel";
    public final static String STORE_ID = "storeId";
    public final static String PROMOTION_TYPE = "promotionType";
    public final static String BIZ_PLATFORM = "bizPlatform";
    public final static String CREATOR = "creator";

    public final static String SERVICE_TYPE = "serviceType";

    public final static String FETCH_LIMIT_TYPE = "fetchLimitType";

    public final static String TIMES_LIMIT_TYPE = "timesLimit";

    public final static String PUBLIC_PROMOTION = "publicPromotion";

    public final static String USE_TIME_TYPE = "useTimeType";

    @SerializedName(value = CONFIG_ID)
    private Long id;

    @SerializedName(value = NAME)
    private String name;

    @SerializedName(value = STATUS)
    private Integer status;

    @SerializedName(value = SCENE)
    private String scene;

    @SerializedName(value = GID)
    private List<Long> gid;

    @SerializedName(value = SKU_ID)
    private List<Long> skuId;

    @SerializedName(value = PACKAGE_ID)
    private List<Long> packageId;

    @SerializedName(value = SSU_ID)
    private List<Long> ssuId;

    @SerializedName(value = START_FETCH_TIME)
    private Long startFetchTime;

    @SerializedName(value = END_FETCH_TIME)
    private Long endFetchTime;

    @SerializedName(value = START_USE_TIME)
    private Long startUseTime;

    @SerializedName(value = END_USE_TIME)
    private Long endUseTime;

    @SerializedName(value = USE_CHANNEL)
    private List<Integer> useChannel;

    @SerializedName(value = STORE_ID)
    private String storeId;

    @SerializedName(value = PROMOTION_TYPE)
    private Integer promotionType;

    @SerializedName(value = BIZ_PLATFORM)
    private Integer bizPlatform;

    @SerializedName(value = CREATOR)
    private String creator;

    @SerializedName(value = SERVICE_TYPE)
    private String serviceType;

    @SerializedName(value = FETCH_LIMIT_TYPE)
    private Integer fetchLimitType;

    @SerializedName(value = TIMES_LIMIT_TYPE)
    private Integer timesLimit;

    /**
     * 公开推广
     */
    @SerializedName(value = PUBLIC_PROMOTION)
    private Integer publicPromotion;

    /**
     * 使用有效期类型
     */
    @SerializedName(value = USE_TIME_TYPE)
    private Integer useTimeType;

}
