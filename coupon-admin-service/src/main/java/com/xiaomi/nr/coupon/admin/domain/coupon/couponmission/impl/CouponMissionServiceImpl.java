package com.xiaomi.nr.coupon.admin.domain.coupon.couponmission.impl;

import com.xiaomi.nr.coupon.admin.api.dto.mission.MissionDto;
import com.xiaomi.nr.coupon.admin.api.dto.mission.PageResponse;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.mission.CouponMissionMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponmission.po.MissionCountItem;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponmission.po.MissionJoinConfigPo;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponmission.CouponMissionService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponmission.convert.CouponMissionConvert;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


import java.util.*;


/**
 * 券发放任务相关服务
 */
@Slf4j
@Service
public class CouponMissionServiceImpl implements CouponMissionService {

    @Autowired
    private CouponMissionMapper couponMissionMapper;

    @Autowired
    private CouponMissionConvert couponMissionConvert;

    /**
     * 根据渠道/missionId 获取券发放任务相关信息
     *
     * @param sendChannel   券发放渠道
     * @param missionId     发放任务ID
     * @param lastMissionId 次分页结果最后一个发放任务ID
     * @param pageSize      分页页面大小
     * @return PageResponse  分页返回接口结果
     */
    @Override
    public PageResponse<MissionDto> getMissionList(String sendChannel, Long missionId, Long lastMissionId, Integer pageSize) throws BizError {

        //1.如果传的missionId不为空,按missionId去查券发放任务信息
        if (missionId  > 0L) {
            MissionJoinConfigPo missionJoinConfigPo = getMissionById(missionId);
            MissionDto missionDto = makeMissionDto(missionJoinConfigPo);
            return new PageResponse<>(0L, pageSize, 1, Collections.singletonList(missionDto));
        }

        //2.如果传的missionId为空,按sendChannel去查发放任务信息
        long nowTime = TimeUtil.getNowUnixSecond();

        //从数据库获取券发放任务列表
        List<MissionJoinConfigPo> missionJoinConfigPos = getMissionList(nowTime, sendChannel, lastMissionId, pageSize);

        //构建接口返回的DTO信息 DB->DTO
        List<MissionDto> missionDtoList = new ArrayList<>();
        for(MissionJoinConfigPo missionJoinConfigPo : missionJoinConfigPos){
            missionDtoList.add(makeMissionDto(missionJoinConfigPo));
        }

        //返回总数
        int total = getValidMissionTotal(nowTime, sendChannel);
        lastMissionId = missionJoinConfigPos.get(missionJoinConfigPos.size() - 1).getMissionId();
        return new PageResponse<>(lastMissionId, pageSize, total, missionDtoList);
    }

    /**
     * 根据券发放任务id查询任务信息
     *
     * @param missionId            发放任务ID
     * @return MissionJoinConfigPo 券配置和发放任务联合查询结果
     */
    private MissionJoinConfigPo getMissionById(long missionId) throws BizError {
        MissionJoinConfigPo missionJoinConfigPo = couponMissionMapper.getMissionByIdOne(missionId);
        if (Objects.isNull(missionJoinConfigPo)) {
            log.info("getMissionById result null,无效的发放任务id={}", missionId);
            throw ExceptionHelper.create(ErrCode.COUPON, "未检索到券发放任务信息, 无效的券发放任务id!");
        }
        return missionJoinConfigPo;
    }



    /**
     * 根据发放渠道从数据库获取有效券发放任务信息
     *
     * @param nowTime       当前时间
     * @param sendChannel   券发放渠道
     * @param lastMissionId 上页分页结果的最后一个发放任务ID
     * @param pageSize      页面大小
     * @return List<>       发放任务和券配置信息列表
     */
    private List<MissionJoinConfigPo> getMissionList(long nowTime, String sendChannel, long lastMissionId, int pageSize) throws BizError {
        List<MissionJoinConfigPo> missionJoinConfigPos = couponMissionMapper.getMissionByChannel(nowTime, sendChannel, lastMissionId, pageSize);
        if (CollectionUtils.isEmpty(missionJoinConfigPos)) {
            log.info("getMissionList,数据库获取券发放任务列表为空!");
            throw ExceptionHelper.create(ErrCode.COUPON, "根据渠道未检索到有效的券发放任务信息!");
        }
        return missionJoinConfigPos;
    }


    /**
     * 查询符合条件的券发放任务总数
     *
     * @param nowTime     当前时间
     * @param sendChannel 券发放渠道
     * @return Integer    总数据量(total)
     */
    private Integer getValidMissionTotal(long nowTime, String sendChannel) {
        List<MissionCountItem> itemList = couponMissionMapper.getMissionByChannelCount(nowTime, sendChannel);
        for(MissionCountItem missionCountItem : itemList){
            if(StringUtils.equals(sendChannel,missionCountItem.getSendChannel())){
                return missionCountItem.getTotal();
            }
        }
        return 0;
    }



    /**
     * 根据券发放任务信息和券配置信息组装返回信息 DB -> DTO
     *
     * @param missionJoinConfigPo 券发放任务和券配置信息(DB)
     * @return MissionDto         券发放任务DTO信息
     */
    private MissionDto makeMissionDto(MissionJoinConfigPo missionJoinConfigPo) throws BizError {
        return couponMissionConvert.makeMissionDto(missionJoinConfigPo);
    }

}
