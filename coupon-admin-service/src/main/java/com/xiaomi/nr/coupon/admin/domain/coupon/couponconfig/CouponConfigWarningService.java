package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.warning.WarnCheckResult;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.warning.WarningCheckHandlerConfig;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.warning.handler.AbstractWarningCheckHandler;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @description: 优惠券条件过滤领域服务
 * @author: hejiapeng
 * @Date 2022/7/9 4:49 下午
 * @Version: 1.0
 **/
@Slf4j
@Service
public class CouponConfigWarningService {

    @Resource
    private Map<String, AbstractWarningCheckHandler> handlerMap;

    /**
     * 预计内容获取
     * @param couponConfigPOList
     * @return
     */
    public Map<Integer, List<Long>> couponWarningCheck(List<CouponConfigPO> couponConfigPOList){

        Map<Integer, List<Long>> warningCheckMap = new HashMap<>(3);
        for (CouponConfigPO couponConfigPO : couponConfigPOList) {
            WarnCheckResult warnCheckResult = null;
            try {
                warnCheckResult = warningCheckChain(couponConfigPO);
            } catch (BizError bizError) {
                log.error("CouponConfigWarningService.warningCheckContent happend error configId:{}", couponConfigPO.getId());
            }
            if (CollectionUtils.isEmpty(warnCheckResult.getCodes())) {
                continue;
            }
            for (Integer code : warnCheckResult.getCodes()) {
                if (warningCheckMap.containsKey(code)) {
                    warningCheckMap.get(code).add(couponConfigPO.getId());
                } else {
                    warningCheckMap.put(code, Lists.newArrayList(couponConfigPO.getId()));
                }
            }
        }
        return warningCheckMap;
    }

    /**
     * 预警校验：链式校验
     * @param couponConfigPO
     * @return
     */
    private WarnCheckResult warningCheckChain(CouponConfigPO couponConfigPO) throws BizError {

        WarningCheckHandlerConfig handlerConfig = this.getHandlerConfig();

        AbstractWarningCheckHandler handler = this.getHandler(handlerConfig);

        WarnCheckResult executeChainResult = handler.doWarningCheck(couponConfigPO);

        return executeChainResult;
    }

    /**
     * 获取处理器配置
     * 可改为dayu配置中心配置，动态修改
     * @return
     */
    private WarningCheckHandlerConfig getHandlerConfig() {

        WarningCheckHandlerConfig lowStockWarningCheckHandler = new WarningCheckHandlerConfig();
        lowStockWarningCheckHandler.setHandler("lowStockWarningCheckHandler");

        WarningCheckHandlerConfig noGoodsWarningCheckHandler = new WarningCheckHandlerConfig();
        noGoodsWarningCheckHandler.setHandler("noGoodsWarningCheckHandler");
        noGoodsWarningCheckHandler.setNext(lowStockWarningCheckHandler);


        WarningCheckHandlerConfig handlerConfig = new WarningCheckHandlerConfig();
        handlerConfig.setHandler("expiringWarningCheckHandler");
        handlerConfig.setNext(noGoodsWarningCheckHandler);

        //转成Config对象
        return handlerConfig;
    }

    /**
     * 获取处理器
     * @param config
     * @return
     */
    private AbstractWarningCheckHandler getHandler(WarningCheckHandlerConfig config) {
        if (Objects.isNull(config) || StringUtils.isBlank(config.getHandler())) {
            return null;
        }

        AbstractWarningCheckHandler abstractCheckHandler = handlerMap.get(config.getHandler());
        if (Objects.isNull(abstractCheckHandler)) {
            return null;
        }

        abstractCheckHandler.setConfig(config);

        abstractCheckHandler.setNextHandler(this.getHandler(config.getNext()));

        return abstractCheckHandler;
    }

}
