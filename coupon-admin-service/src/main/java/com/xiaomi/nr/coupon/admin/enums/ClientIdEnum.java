package com.xiaomi.nr.coupon.admin.enums;

public enum ClientIdEnum {

    //米家线下渠道
    CLIENTID_MIJIA(180100041075L, "小米之家"),
    CLIENTID_MIJIA_ANTHORIZED(180100041157L,"小米授权店"),
    CLIENTID_MIJIA_ZG(180100041158L,"小米直供专营店"),

    //排除线上的clientId
    CLIENTID_STOREGO(180100041191L,"门店go小程序(直营)"),
    CLIENTID_STOREGOSQ(180100041195L,"门店GO小程序(授权店)"),
    CLIENTID_STOREGOZM(180100041196L,"门店GO小程序(专卖店)"),
    CLIENTID_SCANGO(180100041113L,"扫码购(微信小程序)"),
    CLIENTID_DKA(180100041113L,"门店站点 DKA"),
    CLIENTID_STORE_GO(180100041191L,"门店购小程序"),
    CLIENTID_TV_MALL(180100031056L,"电视商城"),
    CLIENTID_WECHAT_PROGRAM(180100041089L,"微信小程序"),

    //端上
    ClientID_ANDROID(180100031052L,"安卓"),
    ClientID_IOS(180100031055L,"ios"),

    //其他clientId 相关常量
    CLIENTID_PC(180100031024L,"PC"),
    CLIENTID_PC_LINK(180100041086L,"PC外链"),
    CLIENTID_YOUPIN_YOUYU(180100041184L,"有品有鱼,小米有品社交电商"),
    CLIENTID_YOUPIN_ZHINENGJIATING(180100031044L,"智能家庭m站渠道"),
    CLIENTID_YOUPIN_APP(180100041097L,"小米有品APP"),
    CLIENTID_YOUPIN_MIJIAPP(180100031050L,"小米有品（米家APP）");


    private final Long value;
    private final String name;

    ClientIdEnum(Long value, String name) {
        this.value = value;
        this.name = name;
    }

    public Long getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static ClientIdEnum findByValue(Long value) {
        ClientIdEnum[] values = ClientIdEnum.values();
        for (ClientIdEnum clientIdEnum : values) {
            if (value.equals(clientIdEnum.value)) {
                return clientIdEnum;
            }
        }
        return null;
    }
}
