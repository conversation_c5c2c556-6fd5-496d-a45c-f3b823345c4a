package com.xiaomi.nr.coupon.admin.application.schedule;

import com.xiaomi.miliao.zookeeper.ZKClient;
import com.xiaomi.miliao.zookeeper.ZKFacade;
import com.xiaomi.nr.coupon.admin.application.schedule.constant.ScheduleConstant;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.*;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.RefreshCouponGoodsContext;
import com.xiaomi.nr.coupon.admin.domain.coupon.optrecord.OptRecordService;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.nr.infra.aop.clustertask.ClusterTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CouponConfigSchedule {

    private final ZKClient zkClient = ZKFacade.getAbsolutePathClient();

    @Resource
    private MakeConfigCache makeConfigCache;

    @Resource
    private CouponConfigRefreshService couponConfigRefreshService;

    @Resource
    private GoodsCouponRefreshService goodsCouponRefreshService;

    @Resource
    private OptRecordService optRecordService;

    @Autowired
    private CouponConfigAdminService couponConfigAdminService;

    @Autowired
    private CouponGoodsRefreshService couponGoodsRefreshService;

    /**
     * 生成优惠券配置缓存
     * 启动30秒后执行，上一次执行完后隔5分钟再执行
     */
    @Scheduled(fixedDelay = 1000 * 60 * 30, initialDelay = 1000 * 30)
    @ClusterTask(value = "", zkLockPath = ScheduleConstant.COUPON_CONFIG_CACHE_GEN_PATH)
    public void genConfigRedisCacheScheduleTask() {
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            log.info("CouponConfigSchedule.genConfigRedisCacheScheduleTask, 生成优惠券配置缓存任务开始");
            //生成缓存
            makeConfigCache.run(null);
            log.info("CouponConfigSchedule.genConfigRedisCacheScheduleTask, 生成优惠券配置缓存任务结束, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));
        } catch (Exception e) {
            log.error("CouponConfigSchedule.genConfigRedisCacheScheduleTask, 生成优惠券配置缓存任务失败, runTime={}毫秒, {}", TimeUtil.sinceMillis(runStartTime), e);
        }
    }

    /**
     * 生成有效券配置ID列表
     * 启动20秒后执行，上一次执行完后隔5分钟再执行
     */
    @Scheduled(fixedDelay = 1000 * 60 * 2, initialDelay = 1000 * 20)
    @ClusterTask(value = "", zkLockPath = ScheduleConstant.COUPON_VALID_LIST_CACHE_LOCK)
    public void genConfigIdsRedisCacheScheduleTask() {
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            log.info("CouponConfigSchedule.genConfigIdsRedisCacheScheduleTask, 生成有效券Id列表缓存任务开始");
            //生成缓存
            couponConfigRefreshService.updateValidCouponIdToRedis();
            log.info("CouponConfigSchedule.genConfigIdsRedisCacheScheduleTask, 生成有效券Id列表缓存任务结束, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));
        } catch (Exception e) {
            log.error("CouponConfigSchedule.genConfigIdsRedisCacheScheduleTask, 生成有效券Id列表缓存任务失败, runTime={}毫秒, {}", TimeUtil.sinceMillis(runStartTime), e);
        }
    }

    /**
     * 增量写入商品和券关系到redis
     * 启动20秒后执行，上一次执行完后隔20秒再执行
     */
    @Scheduled(fixedDelay = 20000, initialDelay = 20000)
    @ClusterTask(value = "", zkLockPath = ScheduleConstant.GOODS_COUPON_RELATION_INCR_LOCK)
    public void incrLoadGoodsCouponRel() {
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            log.info("CouponConfigSchedule.incrLoadGoodsCouponRel, 增量生成商品可用优惠券缓存任务开始");
            String lastUpdateTime = zkClient.getData(String.class, ScheduleConstant.COUPON_CONFIG_CHANGE_RECORD_PATH);
            Timestamp maxUpdateTime = goodsCouponRefreshService.updateRedisGoodsCouponRel(false, lastUpdateTime);
            if (CommonConstant.ZERO_STR.equals(lastUpdateTime) || maxUpdateTime.after(Timestamp.valueOf(lastUpdateTime))) {
                zkClient.updatePersistent(ScheduleConstant.COUPON_CONFIG_CHANGE_RECORD_PATH, TimeUtil.formatDate(new Date(maxUpdateTime.getTime())));
            }
            log.info("CouponConfigSchedule.incrLoadGoodsCouponRel, 增量生成商品可用优惠券缓存任务结束, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));
        } catch (Exception e) {
            log.error("CouponConfigSchedule.incrLoadGoodsCouponRel, 增量生成商品可用优惠券缓存任务失败, runTime={}毫秒, {}", TimeUtil.sinceMillis(runStartTime), e);
        }
    }

    /**
     * 全量写入商品和券关系到redis
     * 每天凌晨4点执行
     */
    @Scheduled(cron = "0 0 4 * * ?")
    @ClusterTask(value = "", zkLockPath = ScheduleConstant.GOODS_COUPON_RELATION_FULL_LOCK)
    public void fullLoadGoodsCouponRel() {
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            log.info("CouponConfigSchedule.fullLoadGoodsCouponRel, 全量生成商品可用优惠券缓存任务开始");
            Timestamp currentSeqId = goodsCouponRefreshService.updateRedisGoodsCouponRel(true, CommonConstant.ZERO_STR);
            zkClient.updatePersistent(ScheduleConstant.COUPON_CONFIG_CHANGE_RECORD_PATH, currentSeqId.toString());
            log.info("CouponConfigSchedule.fullLoadGoodsCouponRel, 全量生成商品可用优惠券缓存任务结束, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));
        } catch (Exception e) {
            log.error("CouponConfigSchedule.fullLoadGoodsCouponRel, 全量生商品可用优惠券缓存任务失败, runTime={}毫秒, {}", TimeUtil.sinceMillis(runStartTime), e);
        }
    }


    /**
     * 券后置处理补偿 1分钟一次
     */
    @Scheduled(fixedDelay = 60000, initialDelay = 60000)
    @ClusterTask(value = "", zkLockPath = ScheduleConstant.COUPON_OPERATE_RECORD_LOCK)
    public void couponOptRecord() {
        optRecordService.compensateOptRecord();
    }

    /**
     * 券新品提醒 每天一次
     */
    @Scheduled(cron = "0 45 11 * * ?")
    @ClusterTask(value = "", zkLockPath = ScheduleConstant.COUPON_NEW_GOODS_NOTIFY_LOCK)
    public void notifyNewGoods() {
        couponConfigAdminService.notifyNewGoods();
        log.info("couponConfigAdminService.notifyNewGoods(), time:"+TimeUtil.getNowUnixSecond());
    }

    /**
     * 品类券新品更新每天0点执行一次
     */
    @Scheduled(cron = "0 0 0 * * ?")
    @ClusterTask(value = "", zkLockPath = ScheduleConstant.UPDATE_COUPON_GOODS_REFRESH_LOCK)
    public void updateCouponGoods() {

        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            log.info("CouponConfigSchedule.updateCouponGoods, refresh start");
            RefreshCouponGoodsContext request = couponGoodsRefreshService.buildSystemRefreshCouponGoodsContext();
            couponGoodsRefreshService.refreshCouponGoods(request);
            log.info("CouponConfigSchedule.updateCouponGoods executing request:" + GsonUtil.toJson(request));
            log.info("CouponConfigSchedule.updateCouponGoods, refresh execute success runTime={}ms, request={}", TimeUtil.sinceMillis(runStartTime), request);
        } catch (Exception e) {
            log.error("CouponConfigSchedule.fullLoadGoodsCouponRel, 全量更新品类商品任务失败, runTime={}毫秒, {}", TimeUtil.sinceMillis(runStartTime), e);
            e.printStackTrace();
        }
    }





}