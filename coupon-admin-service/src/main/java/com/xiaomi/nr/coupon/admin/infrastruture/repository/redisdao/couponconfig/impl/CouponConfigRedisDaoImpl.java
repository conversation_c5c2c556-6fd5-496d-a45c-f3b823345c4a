package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.impl;

import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.CouponConfigRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigGoodsCachePo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigInfoCachePo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.NewConfigGoodsCachePo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 新缓存-券配置的redis缓存操作对象
 */
@Component
@Slf4j
public class CouponConfigRedisDaoImpl implements CouponConfigRedisDao {


    /**
     * 券配置信息cache key
     */
    private static final String KEY_COUPON_INFO_CACHE = "nr:coupon:info:{configId}";

    /**
     * 券配置商品cache key
     */
    private static final String KEY_COUPON_GOODS_CACHE = "nr:coupon:goods:{configId}";

    /**
     * 券配置商品cache key
     */
    private static final String KEY_COUPON_SEND_COUNT = "nr:coupon:sendCount:{configId}";

    /**
     * 券配置商品cache key
     */
    private static final String KEY_COUPON_NEW_GOODS_CACHE = "nr:coupon:newGoods:{configId}";



    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisStringTemplate;


    @Autowired
    @Qualifier("numberNewCouponRedisTemplate")
    private RedisTemplate redisNumberTemplate;



    @Override
    public void setConfigInfoCache(ConfigInfoCachePo configInfoCachePo, long expireTime) {
        String configInfoStr = GsonUtil.toJson(configInfoCachePo);
        ValueOperations<String, String> operations = redisStringTemplate.opsForValue();
        String couponConfigKey = getConfigInfoKey(configInfoCachePo.getId());
        try {
            //优惠券过12个月后失效
            operations.set(couponConfigKey, configInfoStr, expireTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("CouponConfigRedisDao.setConfigInfoCache, 初次写入redis券信息缓存失败, key:{}, err:", couponConfigKey, e);
            operations.set(couponConfigKey, configInfoStr, expireTime, TimeUnit.SECONDS);
        }
    }


    @Override
    public void setCouponGoodsCache(ConfigGoodsCachePo couponGoodsCachePo, long expireTime) {
        String configGoodStr = GsonUtil.toJson(couponGoodsCachePo);
        ValueOperations<String, String> operations = redisStringTemplate.opsForValue();
        String couponGoodKey = getConfigGoodKey(couponGoodsCachePo.getConfigId());
        try {
            //优惠券12个月后失效
            operations.set(couponGoodKey, configGoodStr, expireTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("CouponConfigRedisDao.setCouponGoodsCache, 初次写入redis券商品缓存失败, key:{}, err:", couponGoodKey, e);
            operations.set(couponGoodKey, configGoodStr, expireTime, TimeUnit.SECONDS);
        }

    }

    @Override
    public Map<Long,Long> batchGetCouponSendCount(List<Long> configIds) throws BizError {
        try {
            int size = configIds.size();
            ValueOperations<String, Number> redisClusterValueOps = redisNumberTemplate.opsForValue();
            Map<Long, Long> sendCountMap = new HashMap<>(size);
            List<String> keys = configIds.stream().map(configId -> StringUtil.formatContent(KEY_COUPON_SEND_COUNT, String.valueOf(configId))).collect(Collectors.toList());
            List<Number> sendCounts = redisClusterValueOps.multiGet(keys);
            for (int i = 0; i < size; i++) {
                final int configId = i;
                final long inventory = Optional.ofNullable(sendCounts).map(inv -> inv.get(configId)).map(Number::longValue).orElse(0L);
                sendCountMap.put(configIds.get(configId), inventory);
            }
            return sendCountMap;
        } catch (Exception exp) {
            log.error("CouponConfigRedisDao.batchGetCouponSendCount configIds:{},err:{}", configIds, exp);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取已发放总量失败", exp);
        }
    }

    @Override
    public ConfigInfoCachePo getConfigInfoCache(Long configId) {

        ValueOperations<String, String> operations = redisStringTemplate.opsForValue();
        String couponConfigKey = getConfigInfoKey(configId.intValue());

        String configStr = operations.get(couponConfigKey);
        if(StringUtils.isNotEmpty(configStr)){
            return GsonUtil.fromJson(configStr, ConfigInfoCachePo.class);
        }

        return null;
    }

    @Override
    public void setCouponGoodsCacheV2(NewConfigGoodsCachePo couponGoodsCachePo, long expireTime) {
        String configGoodStr = GsonUtil.toJson(couponGoodsCachePo);
        ValueOperations<String, String> operations = redisStringTemplate.opsForValue();
        String couponGoodKey = getConfigGoodKeyV2(couponGoodsCachePo.getConfigId());
        try {
            //优惠券12个月后失效
            operations.set(couponGoodKey, configGoodStr, expireTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("CouponConfigRedisDao.setCouponGoodsCache, 初次写入redis券商品缓存失败, key:{}, err:", couponGoodKey, e);
            operations.set(couponGoodKey, configGoodStr, expireTime, TimeUnit.SECONDS);
        }
    }

    @Override
    public Map<Long, ConfigInfoCachePo> batchGetConfigInfoCache(List<Long> configIds) throws BizError {

        Map<Long, ConfigInfoCachePo> resultMap = new HashMap<>();

        try {
            if (CollectionUtils.isEmpty(configIds)) {
                return resultMap;
            }

            List<String> keyList = configIds.stream().map(id -> getConfigInfoKey(id.intValue())).collect(Collectors.toList());
            ValueOperations<String, String> redisOps = redisStringTemplate.opsForValue();

            List<String> strList = redisOps.multiGet(keyList);
            if(CollectionUtils.isEmpty(strList)){
                return resultMap;
            }

            ConfigInfoCachePo cachePo;
            for(String item : strList){

                cachePo = GsonUtil.fromJson(item, ConfigInfoCachePo.class);

                if(!Objects.equals(CouponTypeEnum.GOODS.getValue(), cachePo.getCouponType())){
                    continue;
                }

                if(Objects.equals(PromotionTypeEnum.NyuanBuy.getValue(), cachePo.getPromotionType())){
                    continue;
                }

                resultMap.put(cachePo.getId().longValue(), cachePo);
            }

            return resultMap;
        } catch (Exception e) {
            log.error("GoodCouponsRedisDao.batchGetConfigInfoCache error. configId:{}, err:{}", configIds, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "批量获取券配置信息失败");
        }
    }

    private String getConfigInfoKey(Integer configId){
        return StringUtil.formatContent(KEY_COUPON_INFO_CACHE, String.valueOf(configId));
    }

    private String getConfigGoodKey(Long configId){
        return StringUtil.formatContent(KEY_COUPON_GOODS_CACHE, String.valueOf(configId));
    }

    private String getConfigGoodKeyV2(Long configId){
        return StringUtil.formatContent(KEY_COUPON_NEW_GOODS_CACHE, String.valueOf(configId));
    }

}