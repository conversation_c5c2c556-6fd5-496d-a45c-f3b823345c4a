package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.goods;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponGoodsInfo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponScopeTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.goods.GmsProxyService;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * pid券
 * pids = 上传pid
 * exceptPids = null
 */
@Slf4j
@Component
public class GoodsHandler extends CouponGoodsBaseHandler {

    @Autowired
    private GmsProxyService gmsProxyService;

    @Override
    public void handleGoods(CouponConfigItem couponConfigItem) throws Exception {
        CouponGoodsInfo couponGoodsInfo = couponConfigItem.getCouponGoodsInfo();
        if (couponGoodsInfo == null || MapUtils.isEmpty(couponGoodsInfo.getGoodsInclude())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "通过选择商品建券，商品信息不能为空");
        }
        this.checkNyuan(couponConfigItem);
        this.checkGift(couponConfigItem);
    }

    @Override
    public CouponScopeTypeEnum getScopeType() {
        return CouponScopeTypeEnum.Goods;
    }
}
