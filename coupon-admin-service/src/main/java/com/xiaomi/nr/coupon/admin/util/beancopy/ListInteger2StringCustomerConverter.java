package com.xiaomi.nr.coupon.admin.util.beancopy;

import ma.glasnost.orika.MappingContext;
import ma.glasnost.orika.converter.BidirectionalConverter;
import ma.glasnost.orika.metadata.Type;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class ListInteger2StringCustomerConverter extends BidirectionalConverter<List<Integer>,String> {

    @Override
    public String convertTo(List<Integer> integers, Type<String> type, MappingContext mappingContext) {
        return StringUtils.join(integers,",");
    }

    @Override
    public List<Integer> convertFrom(String s, Type<List<Integer>> type, MappingContext mappingContext) {
        if(StringUtils.isBlank(s)){
            return new ArrayList<>();
        }
        return Stream.of(s.split(",")).map(Integer::parseInt).collect(Collectors.toList());
    }
}
