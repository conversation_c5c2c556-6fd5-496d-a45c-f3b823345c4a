package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecard;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecardconfig.po.EcardPo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Component
public interface EcardMapper {


    String COMMON_SQL = " card_id,user_id,type_id,start_time,end_time,stat,from_order_id,is_virtual ";

    /**
     * 根据user_id、card_id查询券信息
     *
     * @Param user_id(1)   <====>    card_id(1)
     */
    @Select("<script>" +
            "select * from tb_ecard " +
            "<if test=\"cardIds != null and cardIds.size >0\"> where card_id in " +
            "        <foreach item='card_id' index='index' collection='cardIds' open='(' separator=',' close=')'>" +
            "            #{card_id}" +
            "        </foreach> " +
            "</if>" +
            "order by add_time desc" +
            "</script>")
    ArrayList<EcardPo> listQueryByCardIds(@Param("cardIds") List<Long> cardIds);

    /**
     *
     * @param userId
     * @param cardId
     * @return
     */
    @Select("<script>" +
            "select"+ COMMON_SQL +"from tb_ecard where 1=1 " +
            "<if test=\"userId != null and userId>0\"> and user_id=#{userId} </if>" +
            "<if test=\"cardId != null and cardId>0\"> and card_id=#{cardId} </if>" +
            " order by add_time desc" +
            "</script>")
    List<EcardPo> queryByCardUserId(@Param("userId") Long userId, @Param("cardId") Long cardId);


    /**
     *
     * @param userId
     * @param cardId
     * @return
     */
    @Select("<script>" +
            "select card_id,user_id,stat,is_locked,delay_times,start_time,end_time,balance from tb_ecard " +
            " where card_id=#{cardId} " +
            "<if test=\"userId != null and userId>0\"> and user_id=#{userId} </if>" +
            "</script>")
    EcardPo queryByCardId(@Param("userId") Long userId, @Param("cardId") Long cardId);


    @Update("update tb_ecard set " +
            "end_time=#{delayTime}," +
            "delay_times=delay_times+1 " +
            "where card_id=#{cardId}")
    int updateEcardEndTime(@Param("cardId") Long cardId, @Param("delayTime") String delayTime);

    /**
     * 查用户礼品卡,自动化测试
     */
    @Select("<script>select * from tb_ecard where 1=1 " +
            "<if test='userId!=null and userId>0'> and user_id=#{userId}</if>" +
            "<if test='typeId!=null and typeId>0'> and type_id = #{typeId}</if>"+
            "<if test='cardId!=null and cardId>0'> and card_id=#{cardId}</if>" +
            "<if test='cardId==null'>and balance &gt; 0 and stat=5 and end_time &gt; unix_timestamp(now())</if>" +
            "</script>")
    List<EcardPo> selectUserEcard(@Param("userId")Long userId,@Param("cardId")Long cardId,@Param("typeId")Long typeId);

    /**
     * 删除用户礼品卡，自动化测试
     * @return
     */
    @Delete("<script>delete from tb_ecard where user_id=#{uid} and card_id =#{cardId}</script>")
    Long deleteEcard(@Param("uid") long uid, @Param("cardId") long cardId);



}
