package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.GoodsConfigRelationPo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商品可用券配置redis缓存操作对象
 *
 * <AUTHOR>
 */
public interface GoodsConfigRedisDao {

    /**
     * 批量-设置商品可用券配置列表缓存
     *
     * @param list List<GoodsConfigRelationPo>
     */
    @Deprecated
    void set(List<GoodsConfigRelationPo> list);

    /**
     * 批量-设置商品可用券配置列表缓存
     *
     * @param level String
     * @param goodCouponRelMap Map<Long, Set<Integer>>
     */
    void set(String level, Map<Long, Set<Long>> goodCouponRelMap);

    /**
     * 获取商品可用券配置列表缓存
     *
     * @param id    String
     * @param level String
     * @return GoodsConfigRelationPo
     */
    @Deprecated
    GoodsConfigRelationPo get(String id, String level);

    /**
     * 获取商品可用券配置列表缓存
     *
     * @param level
     * @param id
     */
    String getGoodsCouponRel(String level, Long id );
}

