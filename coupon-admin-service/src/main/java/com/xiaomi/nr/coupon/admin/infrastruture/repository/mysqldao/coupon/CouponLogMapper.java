package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.coupon;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Mapper
@Component
public interface CouponLogMapper {

    @Select("select max(coupon_id) as coupon_id from tb_coupon_log where add_time < #{addTime} and database() = #{dbName}")
    Long getMaxCoupinIdByAddtime(@Param("addTime") Integer addTime, @Param("dbName") String dbName);

    @Select("select min(coupon_id) as coupon_id from tb_coupon_log where add_time < #{addTime} and database() = #{dbName} ")
    Long getMinCoupinIdByAddtime(@Param("addTime") Integer addTime, @Param("dbName") String dbName);

    @Delete("delete from tb_coupon_log where coupon_id>= #{idStart} and coupon_id < #{idEnd} and add_time < #{addTime} and database() = #{dbName}")
    void deleteCouponLogByCouponId(@Param("idStart") Long idStart, @Param("idEnd") Long idEnd, @Param("addTime") Integer addTime, @Param("dbName") String dbName);
}
