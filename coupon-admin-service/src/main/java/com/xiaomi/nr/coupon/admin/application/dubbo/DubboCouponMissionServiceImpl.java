package com.xiaomi.nr.coupon.admin.application.dubbo;

import com.google.common.base.Stopwatch;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.mission.CouponMissionListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.mission.MissionDto;
import com.xiaomi.nr.coupon.admin.api.dto.mission.PageResponse;
import com.xiaomi.nr.coupon.admin.api.service.DubboMissionService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponmission.CouponMissionService;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;


/**
 * 优惠券发放任务服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
@ApiModule(value = "发放任务服务", apiInterface = DubboMissionService.class)
public class DubboCouponMissionServiceImpl implements DubboMissionService {

    @Autowired
    private CouponMissionService couponMissionService;

    /**
     * 根据发放渠道查询当前有效的发放任务信息列表接口
     *
     * @param request CouponMissionListRequest
     * @return Result<List < MissionDto>>
     */
    @Override
    @ApiDoc("根据发放渠道查询当前有效的发放任务信息列表接口")
    public Result<PageResponse<MissionDto>> getCouponMissionList(CouponMissionListRequest request) {
        String sendChannel = request.getSendChannel();
        Long missionId = request.getMissionId();
        Long lastMissionId = request.getLastMissionId();
        Integer pageSize = request.getPageSize();

        if (lastMissionId == null) {
            return Result.fail(ErrCode.COUPON, "所传参数lastMissionId不能为null!");
        }

        if (pageSize == null || pageSize < 0 || pageSize > 100) {
            return Result.fail(ErrCode.COUPON, "所传参数pageSize不能超过100个!");
        }

        if (missionId == null) {
            return Result.fail(ErrCode.COUPON, "所传参数missionId不能为null!");
        }

        if (StringUtils.isEmpty(sendChannel)) {
            return Result.fail(ErrCode.COUPON, "所传参数sendChannel不能为空!");
        }

        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            PageResponse<MissionDto> missionDtoList = couponMissionService.getMissionList(sendChannel, missionId, lastMissionId, pageSize);
            log.info("coupon-admin.DubboCouponMissionService.getCouponMissionList() execute success, request={}, runTime={}",request , stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return Result.success(missionDtoList);
        } catch (BizError e) {
            log.warn("BizError or BaseException on getCouponMissionList. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.warn("Exception on getCouponMissionList. request:{}", request, e);
            return Result.fromException(e, "系统异常请稍后再试");
        }
    }
}
