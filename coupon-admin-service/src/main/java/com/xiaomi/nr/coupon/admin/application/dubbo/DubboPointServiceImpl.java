package com.xiaomi.nr.coupon.admin.application.dubbo;

import com.google.common.base.Stopwatch;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.GetPointBatchListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.GetPointBatchListResponse;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointBatchDetailDto;
import com.xiaomi.nr.coupon.admin.api.service.DubboPointService;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CarPointSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.PointConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsScenePo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/6 15:20
 */
@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
@Component
@Slf4j
public class DubboPointServiceImpl implements DubboPointService {

    @Autowired
    private PointConfigRepository pointConfigRepository;

    @Autowired
    private CarPointSceneRepository pointSceneRepository;

    /**
     * 有效积分列表查询
     *
     * @param req request
     * @return 积分批次列表
     */
    @Override
    public Result<GetPointBatchListResponse> getPointBatchList(GetPointBatchListRequest req) {

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 1、查询条件构造
            List<Long> batchIdList = req.getBatchIdList();
            List<String> sceneCodeList = req.getSceneCodeList();
            if (CollectionUtils.isNotEmpty(sceneCodeList)) {
                sceneCodeList = sceneCodeList.stream().map(String::trim).collect(Collectors.toList());
            } else {
                sceneCodeList = StringUtils.isNotBlank(req.getSceneCode()) ? Lists.newArrayList(req.getSceneCode().trim()) : new ArrayList<>();
            }
            // 默认不返回积分余额
            Boolean withBalanceCnt = Optional.ofNullable(req.getWithBalanceCnt()).orElse(false);
            // 默认值返回有效批次
            Boolean onlyAvailable = Optional.ofNullable(req.getOnlyAvailable()).orElse(true);
            Long queryTime = System.currentTimeMillis() / 1000;

            // 2、查询数据
            List<CarPointsBatchConfigPo> pointsBatchConfigPoList = pointConfigRepository.selectAvailableBatch(batchIdList, sceneCodeList, onlyAvailable, queryTime);

            GetPointBatchListResponse resp = new GetPointBatchListResponse();
            List<PointBatchDetailDto> pointBatchDeatilList = Collections.emptyList();
            if (CollectionUtils.isNotEmpty(pointsBatchConfigPoList)) {

                // 3、查询场景
                List<CarPointsScenePo> sceneList = pointSceneRepository.getAllSceneList(false);
                Map<String, String> sceneMap = sceneList.stream().collect(Collectors.toMap(CarPointsScenePo::getSceneCode, CarPointsScenePo::getName));

                // 4、查询使用量
                List<Long> curBatchIdList = pointsBatchConfigPoList.stream().map(CarPointsBatchConfigPo::getId).distinct().collect(Collectors.toList());
                Map<Long, Long> sendCountMap = withBalanceCnt ? pointConfigRepository.getPointBatchDistributeCache(curBatchIdList) : new HashMap<>();

                // 5、结果转化
                pointBatchDeatilList = pointsBatchConfigPoList.stream().map(batchConfigPo -> toPointBatchDetailDto(batchConfigPo, sceneMap, sendCountMap, withBalanceCnt)).collect(Collectors.toList());

            }

            resp.setPointBatchDeatilList(pointBatchDeatilList);

            log.info("DubboPointAdminServiceImpl.getPointBatchList finished, req:{} resp:{} costTime = {}ms", GsonUtil.toJson(req), resp, stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return Result.success(resp);
        } catch (Exception e) {
            log.error("DubboPointAdminServiceImpl.getPointBatchList req:{}, error: ", GsonUtil.toJson(req), e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }


    private PointBatchDetailDto toPointBatchDetailDto(CarPointsBatchConfigPo batchConfigPo,
                                                      Map<String, String> sceneMap,
                                                      Map<Long, Long> sendCountMap,
                                                      Boolean withBalanceCnt) {

        PointBatchDetailDto batchDetailDto = new PointBatchDetailDto();
        BeanUtils.copyProperties(batchConfigPo, batchDetailDto);
        batchDetailDto.setSendCount(null);
        Long batchId = batchConfigPo.getId();
        batchDetailDto.setBatchId(batchId);
        batchDetailDto.setBatchName(batchConfigPo.getName());
        String sceneCode = batchConfigPo.getSendScene();
        batchDetailDto.setSceneCode(sceneCode);
        batchDetailDto.setSendScene(sceneMap.get(sceneCode));
        Long applyCount = batchConfigPo.getApplyCount();
        if (withBalanceCnt) {
            Long sendCount = sendCountMap.getOrDefault(batchId, batchConfigPo.getSendCount());
            batchDetailDto.setSendCount(sendCount);
            batchDetailDto.setBalanceCount(applyCount - sendCount);
            // 预计抵扣（积分和现金10:1）
            BigDecimal predictDeduction = BigDecimal.valueOf(sendCount / 10).setScale(1,  RoundingMode.DOWN);
            batchDetailDto.setPredictDeduction(predictDeduction);
        }

        return batchDetailDto;
    }


}
