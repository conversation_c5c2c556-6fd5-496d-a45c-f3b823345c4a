package com.xiaomi.nr.coupon.admin.enums.couponconfig;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

@Getter
public enum PromotionTypeEnum {

    /**
     * 满减
     */
    ConditionReduce(1, "满减", "CashCoupon"),
    /**
     * 满折
     */
    ConditionDiscount(2, "满折", "PercentOff"),
    /**
     * 兑换券
     */
    NyuanBuy(3, "N元券",""),

    /**
     * 立减
     */
    DirectReduce(4, "立减","CashCoupon"),
    /**
     * 礼品券
     */
    GIFT(5, "礼品券",""),

    ;

    private final int value;
    private final String name;
    private final String watermelonDesc;

    PromotionTypeEnum(int value, String name, String watermelonDesc) {
        this.value = value;
        this.name = name;
        this.watermelonDesc = watermelonDesc;
    }

    public static PromotionTypeEnum getByValue(int value) {
        PromotionTypeEnum[] values = PromotionTypeEnum.values();
        for (PromotionTypeEnum item : values) {
            if (item.getValue()==value) {
                return item;
            }
        }
        return null;
    }


    public static String getDesc(PromotionTypeEnum promotionTypeEnum,long promotionValue,BottomTypeEnum typeEnum,int bottomCount,long bottomPrice,long maxReduce){
        BigDecimal decimal = new BigDecimal(promotionValue).divide(BigDecimal.valueOf(100),2, BigDecimal.ROUND_HALF_UP);
        switch (promotionTypeEnum){
            case ConditionReduce:
                return  BottomTypeEnum.getDesc( typeEnum, bottomCount, bottomPrice)+"减 ¥"+decimal+" 元";
            case ConditionDiscount:
               return BottomTypeEnum.getDesc( typeEnum, bottomCount, bottomPrice)+"打 "+decimal+" 折,最大优惠"+BigDecimal.valueOf(maxReduce).divide(BigDecimal.valueOf(100),2, BigDecimal.ROUND_HALF_UP)+"元";
            case NyuanBuy:
                return "直减至 ¥"+decimal+" 元";
            case GIFT:
                return "直减至 ¥0元";
                case DirectReduce:
                    return "无门槛立减 ¥"+decimal+" 元";
        }
        return null;
    }


    public static String getPromotionValue(PromotionTypeEnum promotionTypeEnum,long promotionValue){
        BigDecimal decimal = new BigDecimal(promotionValue).divide(BigDecimal.valueOf(100),2, BigDecimal.ROUND_HALF_UP);
        switch (promotionTypeEnum){
            case ConditionReduce:
            case NyuanBuy:
            case DirectReduce:
                return decimal+"元";
            case GIFT:
                return "0元";
            case ConditionDiscount:
                return decimal+"折";

        }
        return null;
    }

    public static String getNameByValue(int value) {
        PromotionTypeEnum[] values = PromotionTypeEnum.values();
        for (PromotionTypeEnum item : values) {
            if (item.getValue() == value) {
                return item.getName();
            }
        }
        return StringUtils.EMPTY;
    }

    public static String getWatermelonDescByValue(int value) {
        PromotionTypeEnum[] values = PromotionTypeEnum.values();
        for (PromotionTypeEnum item : values) {
            if (item.getValue() == value) {
                return item.getWatermelonDesc();
            }
        }
        return StringUtils.EMPTY;
    }

}
