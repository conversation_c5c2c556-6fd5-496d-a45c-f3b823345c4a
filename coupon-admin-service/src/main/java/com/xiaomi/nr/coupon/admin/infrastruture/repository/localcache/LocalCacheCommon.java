package com.xiaomi.nr.coupon.admin.infrastruture.repository.localcache;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.auth.po.AppAuthInfo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigInfoCachePo;

import java.util.Map;

/**
 * 本地缓存通用操作接口
 */
public interface LocalCacheCommon {


    /**
     * 取单个PP配置信息
     *
     * @param appId appId
     * @return AppAuthInfo
     */
    AppAuthInfo getSingleAppAuth(String appId);


    /** 缓存中获取券信息
     * @param configId
     * @return
     */
    ConfigInfoCachePo getConfigInfoCachePo(Long configId);

}