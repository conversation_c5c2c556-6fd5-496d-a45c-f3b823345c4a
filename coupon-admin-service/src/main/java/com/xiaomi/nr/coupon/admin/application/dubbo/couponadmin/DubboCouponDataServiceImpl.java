package com.xiaomi.nr.coupon.admin.application.dubbo.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.statistic.request.CouponDataStatisticRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.statistic.response.CouponDataStatisticVO;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponDataService;
import com.xiaomi.nr.coupon.admin.domain.coupon.datastatistic.CouponDataStatisticService;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.fds.FileService;
import com.xiaomi.nr.coupon.admin.infrastruture.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.enums.coupondata.ExportDataTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponDataRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.adsTidb.entity.SearchCouponDataParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.adsTidb.entity.SearchCouponDataResult;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.adsTidb.po.CouponStatisticPo;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;

/**
 * @description:
 * @author: hejiapeng
 * @Version: 1.0
 **/
@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
@Component
@Slf4j
public class DubboCouponDataServiceImpl implements DubboCouponDataService {

    @Autowired
    private CouponDataRepository couponDataRepository;

    @Autowired
    private CouponDataStatisticService couponDataStatisticService;

    @Autowired
    private FileService fileService;

    @Override
    public Result<BasePageResponse<CouponDataStatisticVO>> getCouponDataStatistic(CouponDataStatisticRequest request) {
        BasePageResponse<CouponDataStatisticVO> response = new BasePageResponse<>(request.getPageNo(), request.getPageSize());
        try {

            log.info("DubboCouponDataService.getCouponDataStatistic request={}",request);

            SearchCouponDataParam searchCouponDataParam = SearchCouponDataParam.buildSearchCouponDataParam(request);

            SearchCouponDataResult searchCouponDataResult =  couponDataRepository.searchCouponDataList(searchCouponDataParam);

            List<CouponDataStatisticVO> dataStatisticVOList = couponDataStatisticService.calCouponAggreateData(searchCouponDataResult.getCouponStatisticPos(), searchCouponDataParam);

            couponDataStatisticService.sortCouponDataStatisticVOS(dataStatisticVOList, request);

            response.setList(dataStatisticVOList);
            response.setTotalCount(searchCouponDataResult.getTotalCnt());
            response.setTotalPage(searchCouponDataResult.getTotalPage());

            return Result.success(response);
        }catch (Exception e){
            log.error("DubboCouponDataService.getCouponDataStatistic request={}, error: ",request , e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<BasePageResponse<CouponDataStatisticVO>> getFillCouponDataStatistic(CouponDataStatisticRequest request) {
        BasePageResponse<CouponDataStatisticVO> response = new BasePageResponse<>(request.getPageNo(), request.getPageSize());
        try {
            log.info("DubboCouponDataService.getFillCouponDataStatistic request={}",request);

            SearchCouponDataParam searchCouponDataParam = SearchCouponDataParam.buildSearchCouponDataParam(request);

            SearchCouponDataResult searchCouponDataResult =  couponDataRepository.searchFillCouponDataList(searchCouponDataParam);

            List<CouponDataStatisticVO> dataStatisticVOList = couponDataStatisticService.calFillCouponAggreateData(searchCouponDataResult.getCouponStatisticPos(), searchCouponDataParam);

            couponDataStatisticService.sortCouponDataStatisticVOS(dataStatisticVOList, request);

            response.setList(dataStatisticVOList);
            response.setTotalCount(searchCouponDataResult.getTotalCnt());
            response.setTotalPage(searchCouponDataResult.getTotalPage());

            return Result.success(response);
        }catch (Exception e){
            log.error("DubboCouponDataService.getCouponDataStatistic request={}, error: ",request , e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<String> exportCouponDataStatistic(CouponDataStatisticRequest request) {
        try {
            log.info("DubboCouponDataService.getCouponDataStatistic request={}",request);

            SearchCouponDataParam searchCouponDataParam = SearchCouponDataParam.buildSearchCouponDataParam(request);

            couponDataRepository.setCouponIdByCreator(searchCouponDataParam);

            List<CouponDataStatisticVO> dataStatisticVOList;

            if(ExportDataTypeEnum.ALL_COUPON_DATA.getCode().equals(request.getExportData())) {
                List<CouponStatisticPo> couponStatisticPos =  couponDataRepository.exportCouponDataList(searchCouponDataParam);
                dataStatisticVOList = couponDataStatisticService.calCouponAggreateData(couponStatisticPos, searchCouponDataParam);
            } else {
                List<CouponStatisticPo> couponStatisticPos =  couponDataRepository.exportFillCouponDataList(searchCouponDataParam);
                dataStatisticVOList = couponDataStatisticService.calFillCouponAggreateData(couponStatisticPos, searchCouponDataParam);
            }

            couponDataStatisticService.sortCouponDataStatisticVOS(dataStatisticVOList, request);

            String fileName = ExportDataTypeEnum.findNameByCode(request.getExportData()) + UserInfoItemFactory.createUserInfoItem().getEmailPrefix() + TimeUtil.getNowUnixSecond();

            File file = couponDataStatisticService.getExportCouponDataFile(dataStatisticVOList, fileName);

            String downLoadUrl = fileService.uploadFileToFds("coupon/data/" + fileName + ".xlsx", file, true);

            return Result.success(downLoadUrl);
        }catch (Exception e){
            log.error("DubboCouponDataService.getCouponDataStatistic request={}, error: ",request , e);
            return Result.fromException(e);
        }
    }

}
