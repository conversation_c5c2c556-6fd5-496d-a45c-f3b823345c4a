package com.xiaomi.nr.coupon.admin.util;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.FileInfo;
import com.xiaomi.nr.coupon.admin.infrastruture.staticdata.PictureFileSuffeix;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Collection;
import java.util.List;

/**
 * 文件工具类
 */
public class FileUtils {

    /**
     * 拷贝文件
     * @param sourcePath
     * @param targetPath
     * @throws IOException
     */
    public static void copyResourceFile(String sourcePath, String targetPath) throws IOException {
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(sourcePath);
        File f = new File(targetPath);
        if (!f.exists()) {
            f.mkdirs();
        }
        Files.copy(inputStream, Paths.get(targetPath), StandardCopyOption.REPLACE_EXISTING);
    }


    /**
     * 解析excel文件数据
     */
    public static <T> List<T> getExcelList(InputStream inputStream, Class<T> tClass, int headRowNumber){
        List<T> list = EasyExcel.read(inputStream).head(tClass).headRowNumber(headRowNumber).sheet().doReadSync();
        return list;
    }


    /**
     * 生成Excel文件，同一文件写一个sheet
     * @return
     */
    public static <T>  void writeExcelFile(File file,String sheetName,Collection sourceList, Class<T> destinationClass){
        EasyExcel.write(file,destinationClass).sheet(sheetName).doWrite(sourceList);
    }


    /**
     * 同一个文件写多个sheet
     * @param file 要写入的文件
     * @param fileInfos 数据和sheet相关信息
     * @param <T> 写入数据的类型
     */
    public static <T>  void writeExcelFile(File file , List<FileInfo> fileInfos){
        ExcelWriter excelWriter = null;
        try {
            excelWriter  = EasyExcel.write(file).build();
            for (FileInfo f:fileInfos) {
                WriteSheet writeSheet = EasyExcel.writerSheet(f.getSheetName()).head(f.getDestinationClass()).build();
                excelWriter.write(f.getSourceList(), writeSheet);
            }
        } finally {
            // 千万别忘记finish 会帮忙关闭流
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 判断文件是否为图片
     * @param url
     * @return
     */
    public static boolean isPicture(String url) {
        if (StringUtils.isEmpty(url)) {
            return false;
        }
        String fileType = url.substring(url.lastIndexOf(".") + 1);

        if (PictureFileSuffeix.pictureSuffiex.contains(fileType.toUpperCase())) {
            return true;
        }
        return false;
    }

}
