package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig;

import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: 券数据刷新service
 * @author: hejiapeng
 * @Date 2022/3/29 6:00 下午
 * @Version: 1.0
 **/
@Service
@Slf4j
public class CouponConfigRefreshService {

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private MakeCacheBaseData makeData;

    public void updateAllCouponToRedis(List<Long> configIds, Boolean full) throws Exception {
        List<CouponConfigPO> couponConfigPOs;
        if(CollectionUtils.isNotEmpty(configIds)){
            couponConfigPOs = couponConfigRepository.searchCouponListById(configIds);
        } else {
            couponConfigPOs = couponConfigRepository.getAllCouponConfig();
        }
        BaseData common = makeData.getV2();
        long threlodTime = TimeUtil.getNowUnixSecond() - CommonConstant.WEEK_TIMESTAMP_LENGTH;
        for (CouponConfigPO couponConfigPO : couponConfigPOs) {
            if(!full && couponConfigPO.getEndFetchTime() < threlodTime && couponConfigPO.getEndUseTime() < threlodTime){
                continue;
            }
            try {
                // 更新券缓存和ES数据
                couponConfigRepository.updateCouponConfigCache(couponConfigPO, common);

                // 更新老券redis缓存
                couponConfigRepository.updateOldCouponInfoCache(couponConfigPO);
            } catch (Exception e) {
                log.error("CouponConfigRefreshService.updateAllCouponToRedis coupon cache err, configId:{}", couponConfigPO.getId(), e);
            }
        }
        // 执行完写入后 forceMerge
        couponConfigRepository.forceMergeIndex();
    }

    /**
     * 更新有效券配置Id列表
     * @throws Exception
     */
    public void updateValidCouponIdToRedis() {
        List<Long> configIds = couponConfigRepository.getAllValidConfigId();
        couponConfigRepository.updateOldConfigIdsCache(configIds);
    }
}
