package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class BaseDomainEvent<T> implements Serializable {
    /**
     * 领域事件数据
     */
    private T data;

    /**
     * 业务类型 0 3c 1 汽车
     */
    private Integer bizPlatform;

    /**
     * 幂等键
     */
    private String idempotentKey;

    /**
     * 补偿记录id
     */
    private long recordId;

    /**
     * 是否是补偿执行
     */
    private boolean compensateFlag;

    /**
     * 发生时间
     */
    private long time = System.currentTimeMillis();


}
