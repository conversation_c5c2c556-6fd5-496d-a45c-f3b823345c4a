package com.xiaomi.nr.coupon.admin.enums.couponconfig;

import lombok.Getter;

/**
 * 预警提醒校验类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum WarningType {

    LOW_STOCK(1, "低库存预警"),
    EXPIRING(2, "快过期预警"),
    NO_GOODS(3, "无可用商品预警");

    private Integer code;
    private String desc;

    WarningType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WarningType getByCode(int code) {
        WarningType[] values = WarningType.values();
        for (WarningType item : values) {
            if (item.getCode() == code) {
                return item;
            }
        }
        return null;
    }

}
