package com.xiaomi.nr.coupon.admin.enums.couponlog;

import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.Getter;

import java.util.Objects;

/**
 * @Description: 券操作类型
 * @Date: 2022.03.01 14:39
 */
@Getter
public enum CouponLogOptType {
    CREATE(1, "创建"),
    UPDATE(2, "修改"),
    ONLINE(3, "上线"),
    OFFLINE(4, "下线"),
    CANCEL(5, "终止领取"),
    ;
    private Integer code;
    private String desc;
    CouponLogOptType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 优惠券状态枚举转化为券操作类型
     *
     * @param couponConfigStatus    优惠券状态枚举
     * @return  券操作类型
     */
    public static CouponLogOptType convert2OptType(CouponConfigStatusEnum couponConfigStatus) throws BizError {

        if (Objects.isNull(couponConfigStatus)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券状态非法");
        }

        switch (couponConfigStatus) {
            case ONLINE:
                return CouponLogOptType.ONLINE;
            case OFFLINE:
                return CouponLogOptType.OFFLINE;
            case STOP_FETCHING:
                return CouponLogOptType.CANCEL;
            default:
                throw ExceptionHelper.create(ErrCode.COUPON, "错误的操作类型");
        }
    }
}
