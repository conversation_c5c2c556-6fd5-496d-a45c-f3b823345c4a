package com.xiaomi.nr.coupon.admin.enums.couponconfig;

/**
 * 模式类型 枚举
 *
 * <AUTHOR>
 */
public enum ModeTypeEnum {

    /**
     * 有码券
     */
    Code("code", "1", "有码券"),

    /**
     * 无码券
     */
    NoCode("no_code", "2", "无码券");

    private final String redisValue;
    private final String mysqlValue;
    private final String name;

    ModeTypeEnum(String redisValue, String mysqlValue, String name) {
        this.redisValue = redisValue;
        this.mysqlValue = mysqlValue;
        this.name = name;
    }

    public String getRedisValue() {
        return this.redisValue;
    }

    public String getMysqlValue() {
        return this.mysqlValue;
    }

    public String getName() {
        return name;
    }

    public static String findNameByRedisValue(String value) {
        ModeTypeEnum[] values = ModeTypeEnum.values();
        for (ModeTypeEnum item : values) {
            if (item.getRedisValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }

    public static String findRedisValueByMysqlValue(String value) {
        ModeTypeEnum[] values = ModeTypeEnum.values();
        for (ModeTypeEnum item : values) {
            if (item.getMysqlValue().equals(value)) {
                return item.getRedisValue();
            }
        }
        return null;
    }
}

