package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.posthandler;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponTaskRepository;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TaskUpdatePostHandler extends BaseCouponPostHandler{

    private final List<Integer> matchBizPlatform = Lists.newArrayList(
            BizPlatformEnum.RETAIL.getCode(),
            BizPlatformEnum.CAR_SHOP.getCode()
    );

    @Autowired
    private CouponTaskRepository couponTaskRepository;

    @Override
    public void createPost(CouponCreateEvent event) throws Exception {

    }

    @Override
    public void updatePost(CouponUpdateEvent event) {

    }

    @Override
    public void updateStatusPost(CouponUpdateStatusEvent event) {

        if(!(CouponConfigStatusEnum.ONLINE.getCode() == event.getData().getStatus())){
            return;
        }

        if(!couponTaskRepository.checkPreTask(event.getData().getId())){
            return;
        }

        couponTaskRepository.updateTaskStatusByCoupon(event.getData().getId());
    }

    @Override
    public int order() {
        return 7;
    }

    @Override
    public Boolean bizPlatformMatch(Integer bizPlatform) {
        return matchBizPlatform.contains(bizPlatform);
    }
}
