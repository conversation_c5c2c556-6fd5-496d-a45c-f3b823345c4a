package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.warning.handler;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.warning.WarnCheckResult;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.WarningType;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @description: 快过期预警
 * @author: hejiapeng
 * @Date 2022/7/11 9:04 下午
 * @Version: 1.0
 **/
@Component
public class ExpiringWarningCheckHandler extends AbstractWarningCheckHandler {

    @Override
    public WarnCheckResult doWarningCheck(CouponConfigPO couponConfigPO) throws BizError {
        WarnCheckResult warnCheckResult;
        AbstractWarningCheckHandler next = super.next();
        if (next != null) {
            warnCheckResult = next.doWarningCheck(couponConfigPO);
        } else {
            warnCheckResult = new WarnCheckResult();
        }

        Date startFetchTime = TimeUtil.convertLongToDate(couponConfigPO.getStartFetchTime());
        Date endFetchTime = TimeUtil.convertLongToDate(couponConfigPO.getEndFetchTime());

        if (TimeUtil.daysBetweenDate(startFetchTime, endFetchTime) <= CommonConstant.FIFTEEN_INT) {
            return warnCheckResult;
        }

        if (TimeUtil.daysBetweenDate(new Date(), endFetchTime) < CommonConstant.TWO_INT) {
            warnCheckResult.getCodes().add(WarningType.EXPIRING.getCode());
        }

        return warnCheckResult;
    }
}
