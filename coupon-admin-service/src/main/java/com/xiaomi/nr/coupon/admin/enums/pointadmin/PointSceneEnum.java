package com.xiaomi.nr.coupon.admin.enums.pointadmin;

import lombok.Getter;

/**
 * 积分场景枚举
 */
@Getter
public enum PointSceneEnum {

    USER_SERVICE(1, "用户服务"),
    DELIVERY_LOGISTICS(2, "交付物流"),
    APP_MARKET(3, "App运营"),
    SALE_OPERATIONS(4, "销售运营"),
    ;

    /**
     * 编码
     */
    private final int code;
    /**
     * 描述
     */
    private final String desc;


    PointSceneEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;

    }

    public static PointSceneEnum getByCode(int code) {
        PointSceneEnum[] values = PointSceneEnum.values();
        for (PointSceneEnum item : values) {
            if (item.getCode() == code) {
                return item;
            }
        }
        return null;
    }

}
