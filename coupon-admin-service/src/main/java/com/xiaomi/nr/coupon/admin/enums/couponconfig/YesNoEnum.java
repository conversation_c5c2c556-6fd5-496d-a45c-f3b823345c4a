package com.xiaomi.nr.coupon.admin.enums.couponconfig;

/**
 * 是否
 */
public enum YesNoEnum {

    /**
     * 是
     */
    Yes(true, 1, "是"),

    /**
     * 否
     */
    No(false, 2, "否");

    private final Boolean redisValue;
    private final int mysqlValue;
    private final String name;

    YesNoEnum(Boolean redisValue, int mysqlValue, String name) {
        this.redisValue = redisValue;
        this.mysqlValue = mysqlValue;
        this.name = name;
    }

    public Boolean getRedisValue() {
        return this.redisValue;
    }

    public int getMysqlValue() {
        return this.mysqlValue;
    }

    public String getName() {
        return name;
    }

    public static String findNameByRedisValue(Boolean value) {
        YesNoEnum[] values = YesNoEnum.values();
        for (YesNoEnum item : values) {
            if (item.getRedisValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }

    public static Boolean findRedisValueByMysqlValue(int value) {
        YesNoEnum[] values = YesNoEnum.values();
        for (YesNoEnum item : values) {
            if (item.getMysqlValue() == value) {
                return item.getRedisValue();
            }
        }
        return null;
    }
}

