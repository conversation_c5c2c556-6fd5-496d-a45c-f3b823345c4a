package com.xiaomi.nr.coupon.admin.domain.coupon.couponpush;

import api.producer.NormalProducer;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponpush.model.CouponConfigChangeMsg;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 券变更push
 *
 * <AUTHOR>
 * @date 2024/9/19
 */
@Slf4j
@Service
public class CouponConfigChangeService {

    @Resource
    @Qualifier("xmRocketNormalProducer")
    private NormalProducer normalProducer;

    @Value("${nr.rocketmq.producer.coupon.change.topic}")
    private String couponChangeTopic;

    /**
     * 发送券变更消息
     *
     * @param couponConfigChangeMsg 券变更消息
     * @param bizPlatform   业务领域
     */
    public void sendChangeMsg(CouponConfigChangeMsg couponConfigChangeMsg, Integer bizPlatform) throws InterruptedException {

        Message msg = new Message(couponChangeTopic, GsonUtil.toJson(couponConfigChangeMsg).getBytes(StandardCharsets.UTF_8));
        msg.setKeys(couponConfigChangeMsg.getConfigId().toString());
        msg.setTags(BizPlatformEnum.valueOf(bizPlatform).getValue());

        try {
            SendResult sendResult = normalProducer.send(msg);
            if (SendStatus.SEND_OK != sendResult.getSendStatus()) {
                log.warn("CouponConfigChangeService.sendChangeMsg failed. msg {}, sendStatus:{}",GsonUtil.toJson(couponConfigChangeMsg), sendResult.getSendStatus());
            } else {
                log.info("CouponConfigChangeService.sendChangeMsg success. msg {}",GsonUtil.toJson(couponConfigChangeMsg));
            }
        } catch (InterruptedException ie) {
            log.warn("CouponConfigChangeService.sendChangeMsg failed. msg {}, e ",GsonUtil.toJson(couponConfigChangeMsg), ie);
            Thread.currentThread().interrupt();
            throw ie;
        }
        catch (Exception e) {
            log.warn("CouponConfigChangeService.sendChangeMsg failed. msg {}, e ",GsonUtil.toJson(couponConfigChangeMsg), e);
            throw new RuntimeException(e);
        }
    }
}
