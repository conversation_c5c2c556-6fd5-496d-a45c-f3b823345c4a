package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.posthandler;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponpush.CouponConfigChangeService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponpush.model.CouponConfigChangeMsg;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.couponlog.CouponLogOptType;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 券批次修改RocketMQ广播消息
 *
 * <AUTHOR>
 * @date 2024/9/19
 */
@Slf4j
@Component
public class CouponChangeMQPostHandler extends BaseCouponPostHandler {

    private final List<Integer> matchBizPlatform= Lists.newArrayList(
            BizPlatformEnum.RETAIL.getCode(),
            BizPlatformEnum.CAR.getCode(),
            BizPlatformEnum.CAR_AFTER_SALE.getCode(),
            BizPlatformEnum.CAR_SHOP.getCode()
    );

    @Resource
    private CouponConfigChangeService couponConfigChangeService;

    @Override
    public void createPost(CouponCreateEvent event) throws Exception {
        CouponConfigChangeMsg couponConfigChangeMsg = convert2ChangeMsg(event.getData(), CouponLogOptType.CREATE);
        couponConfigChangeService.sendChangeMsg(couponConfigChangeMsg, event.getBizPlatform());
    }

    @Override
    public void updatePost(CouponUpdateEvent event) throws Exception {
        CouponConfigChangeMsg couponConfigChangeMsg = convert2ChangeMsg(event.getData(), CouponLogOptType.UPDATE);
        couponConfigChangeService.sendChangeMsg(couponConfigChangeMsg, event.getBizPlatform());
    }

    @Override
    public void updateStatusPost(CouponUpdateStatusEvent event) throws Exception {
        CouponConfigPO newPo = event.getData();

        CouponConfigStatusEnum couponConfigStatus = CouponConfigStatusEnum.findByCode(newPo.getStatus());
        CouponLogOptType optType = CouponLogOptType.convert2OptType(couponConfigStatus);

        CouponConfigChangeMsg couponConfigChangeMsg = convert2ChangeMsg(newPo, optType);
        couponConfigChangeService.sendChangeMsg(couponConfigChangeMsg, event.getBizPlatform());
    }

    /**
     * 执行器顺序
     *
     * @return
     */
    @Override
    public int order() {
        return 8;
    }

    @Override
    public Boolean bizPlatformMatch(Integer bizPlatform) {
        return matchBizPlatform.contains(bizPlatform);
    }

    private CouponConfigChangeMsg convert2ChangeMsg(CouponConfigPO couponConfigPO, CouponLogOptType couponLogOptType) {
        CouponConfigChangeMsg couponConfigChangeMsg = new CouponConfigChangeMsg();
        couponConfigChangeMsg.setConfigId(couponConfigPO.getId());
        couponConfigChangeMsg.setOptType(couponLogOptType.getCode());
        couponConfigChangeMsg.setStartFetchTime(couponConfigPO.getStartFetchTime());
        couponConfigChangeMsg.setEndFetchTime(couponConfigPO.getEndFetchTime());
        return couponConfigChangeMsg;
    }
}
