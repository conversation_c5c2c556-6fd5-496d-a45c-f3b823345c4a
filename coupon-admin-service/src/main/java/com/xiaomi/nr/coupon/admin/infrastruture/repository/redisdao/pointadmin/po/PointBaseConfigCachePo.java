package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.pointadmin.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2023/12/13 17:14
 */
@Data
public class PointBaseConfigCachePo implements Serializable {
    private static final long serialVersionUID = 5830281354715043112L;

    /**
     * id
     */
    @SerializedName("id")
    private Long id;

    /**
     * 有效期
     */
    @SerializedName("vt")
    private Integer validTime;

    /**
     * 抵扣比例
     */
    @SerializedName("dr")
    private Integer discountRatio;

    /**
     * 订单比例
     */
    @SerializedName("or")
    private Integer orderRatio;

    /**
     * 更新时间
     */
    @SerializedName("ut")
    private Timestamp updateTime;

    /**
     * 黑名单ssuId列表
     */
    @SerializedName("bs")
    private String blacklistSsu;

}
