package com.xiaomi.nr.coupon.admin.infrastruture.rpc;

import com.google.common.collect.Lists;
import com.mi.oa.infra.oaucf.ems.enums.SystemSourceEnum;
import com.mi.oa.infra.oaucf.ems.req.BudgetReleaseReq;
import com.mi.oa.infra.oaucf.ems.req.BudgetVerifyReq;
import com.mi.oa.infra.oaucf.ems.resp.BudgetVerifyResp;
import com.mi.oa.infra.oaucf.ems.resp.EmsResp;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/03/19
 */

@Slf4j
@Component
public class PointBudgetService {

    @Autowired
    private BrProxy brProxy;

    /**
     * 占用br预算金额
     *
     * @param
     * @return
     */
    public void preOccupyBudget(CarPointsBatchConfigPo batchConfigPo) throws BizError {
        log.info("PointBudgetService.preOccupyBudget begin, batchConfigPo = {}", batchConfigPo);
        try {
            BudgetVerifyReq req = new BudgetVerifyReq();
            req.setSystemSource(SystemSourceEnum.EQUITY_CENTER.getCode());
            req.setBusinessScenarioCode("EMS-BR01");
            req.setApplyNo(batchConfigPo.getBrApplyNo());//br申请单号（唯一标识）
            req.setProjectCode(batchConfigPo.getBudgetApplyNo() + "_" + batchConfigPo.getLineNum());
            req.setOccupyTime(batchConfigPo.getBudgetCreateTime());
            req.setApplyAmount(String.valueOf((double)batchConfigPo.getApplyCount()/10));
            req.setCurrencyCode("CNY");
            brProxy.preOccupyBudget(Lists.newArrayList(req));
        } catch (Exception e) {
            log.error("PointBudgetService.preOccupyBudget error batchConfigPo:{}", batchConfigPo, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
    }

    /**
     * 追加br预算金额
     *
     * @param
     * @return
     */
    public EmsResp<BudgetVerifyResp> addBudget(CarPointsBatchConfigPo batchConfigPo, long gap) throws BizError {
        log.info("PointBudgetService.addBudget begin, batchConfigPo = {}, gap = {}}", batchConfigPo, gap);
        try {
            BudgetVerifyReq req = new BudgetVerifyReq();
            req.setSystemSource(SystemSourceEnum.EQUITY_CENTER.getCode());
            req.setBusinessScenarioCode("EMS-BR01");
            req.setApplyNo(batchConfigPo.getBrApplyNo());//br申请单号（唯一标识）
            req.setProjectCode(batchConfigPo.getBudgetApplyNo() + "_" + batchConfigPo.getLineNum());
            req.setOccupyTime(batchConfigPo.getBudgetCreateTime());
            req.setApplyAmount(String.valueOf((double)gap/10));
            req.setCurrencyCode("CNY");
            return brProxy.addBudget(Lists.newArrayList(req));
        } catch (Exception e) {
            log.error("PointBudgetService.addBudget error batchConfigPo:{}", batchConfigPo, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
    }

    /**
     * 返还br预算金额
     *
     * @param
     * @return
     */
    public EmsResp<Boolean> reduceBudget(CarPointsBatchConfigPo batchConfigPo, long gap) throws BizError {
        log.info("PointBudgetService.reduceBudget begin, batchConfigPo = {}, gap = {}", batchConfigPo, gap);
        try {
            BudgetReleaseReq req = new BudgetReleaseReq();
            req.setSystemSource(SystemSourceEnum.EQUITY_CENTER.getCode());
            req.setBusinessScenarioCode("EMS-BR01");
            req.setApplyNo(batchConfigPo.getBrApplyNo());//br申请单号（唯一标识）
            req.setProjectCode(batchConfigPo.getBudgetApplyNo() + "_" + batchConfigPo.getLineNum());
            req.setReleaseTime(batchConfigPo.getBudgetCreateTime());
            req.setReleaseAmount(String.valueOf((double)gap/10));
            req.setCurrencyCode("CNY");
            return brProxy.reduceBudget(Lists.newArrayList(req));
        } catch (Exception e) {
            log.error("PointBudgetService.reduceBudget error batchConfigPo:{}", batchConfigPo, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
    }
}
