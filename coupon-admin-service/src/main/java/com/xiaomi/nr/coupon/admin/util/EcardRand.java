package com.xiaomi.nr.coupon.admin.util;

import com.xiaomi.keycenter.common.iface.DataProtectionException;
import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.ecard.EcardConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Random;

/**
 * 礼品卡生成器
 *
 * <AUTHOR>
 */
@Slf4j
public class EcardRand {

    @Value("${keycenter.sid}")
    private String sidTmp;
    private static String sid;

    @Autowired
    private void set() {
        sid = sidTmp;
    }

    private static Random rand;

    static {
        try {
            rand = SecureRandom.getInstanceStrong();
        } catch (Exception e) {
            log.warn("keycenter组件static初始化失败, err={}", e.getMessage());
        }
    }


    /**
     * 生成卡号
     *
     * @param isVirtual Integer
     * @param money     String
     * @return Long
     */
    public static Long getEcardId(Integer isVirtual, String money) {
        long cardId;
        int cardType;
        if (isVirtual == 1) {
            cardType = 2;
        } else {
            cardType = 1;
        }
        Integer moneyInt = (int) Float.parseFloat(money);
        String randStr = randomizeNum(EcardConstant.CARDID_LEN, EcardConstant.randNum());
        String cardIdStr = String.format("%d%04d%s", cardType, moneyInt, randStr);
        cardId = Long.parseLong(cardIdStr);
        return cardId;
    }

    /**
     * 生成卡SN
     *
     * @param skuStr String
     * @return String
     */
    public static String getSn(String skuStr) {
        long sku = Long.parseLong(skuStr);
        if (sku == 0) {
            return "";
        }
        String randStr = randomizeNum(EcardConstant.SN_LEN, EcardConstant.randNum());
        return String.format("%d%s", sku, randStr);
    }

    /**
     * 生成卡密
     *
     * @return String
     */
    public static String getSecretPassword() {
        String passStr = randomizeNum(EcardConstant.PASSWORD_LEN, EcardConstant.randNum());
        int sign = verifyFormula(passStr);
        String password = String.format("%s%d", passStr, sign);
        String passwordKeyCenter;
        try {
            passwordKeyCenter = KeyCenterUtil.encrypt(password, sid);
        } catch (DataProtectionException e) {
            log.warn("keycenter密码加密错误！");
            throw new BaseException(-1, "keycenter密码加密错误！");
        }
        return passwordKeyCenter;
    }

    /**
     * 生成指定位数的随机数
     *
     * @param length int
     * @param numSet int[]
     * @return String
     */
    public static String randomizeNum(int length, int[] numSet) {
        int lenStr = numSet.length;
        StringBuilder str = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int idx = rand.nextInt(lenStr);
            str.append(numSet[idx]);
        }
        return str.toString();
    }

    /**
     * 生成校验位公式
     *
     * @param str String
     * @return int
     */
    public static int verifyFormula(String str) {
        String[] strs = str.split("");
        int[] strInt = Arrays.stream(strs).mapToInt(Integer::parseInt).toArray();
        if (strInt.length < EcardConstant.CARDID_LEN) {
            return 0;
        }
        return ((strInt[2] - '0') + (strInt[0] - '0') * 2 + (strInt[1] - '0') + (strInt[5] - '0') * 2) % 10;
    }

}
