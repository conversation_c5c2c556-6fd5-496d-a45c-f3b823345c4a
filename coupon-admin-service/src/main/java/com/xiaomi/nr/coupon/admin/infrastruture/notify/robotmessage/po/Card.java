package com.xiaomi.nr.coupon.admin.infrastruture.notify.robotmessage.po;

import com.xiaomi.nr.coupon.admin.enums.couponconfig.WarningType;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.point.PointConstant;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: hejiapeng
 * @Date 2022/7/22 11:27 上午
 * @Version: 1.0
 **/
@Data
public class Card {
    private Header header;
    private List<Element> elements;

    public static Card getCouponWarningCard(Map<Integer, List<Long>> warningCheckResult, String userName) {

        StringBuilder content = new StringBuilder();
        for (Map.Entry<Integer, List<Long>> item : warningCheckResult.entrySet()) {
            content.append(WarningType.getByCode(item.getKey()).getDesc())
                    .append(":")
                    .append(StringUtils.join(item.getValue(),","))
                    .append("\n");
        }
        content.append("最近维护人:").append(userName);

        Card card = new Card();

        Header header = new Header();
        header.setTitle(new HashMap<String, String>() {{
            put("tag", "plain_text");
            put("content", "优惠券预警提示");
        }});

        card.setHeader(header);
        card.setElements(Lists.newArrayList());

        Element element = new Element();

        element.setTag("div");
        element.setText(new HashMap<String, String>() {{
            put("tag", "plain_text");
            put("content", content.toString());
        }});

        card.getElements().add(element);

        Element element2 = new Element();

        Action action = new Action();
        action.setTag("button");
        action.setText(new HashMap<String, String>() {{
            put("tag", "plain_text");
            put("content", "打开优惠券系统");
        }});
        action.setType("default");
        action.setUrl("https://work.be.mi.com/main-coupon-manage/");

        element2.setTag("action");
        element2.setActions(Lists.newArrayList(action));
        card.getElements().add(element2);

        return card;
    }


    public static Card getCouponCodeCard(long configId, String couponName, String fdsUrl) {

        StringBuilder content = new StringBuilder();

        content.append("优惠券名称: ").append(couponName).append("\n");
        content.append("优惠券ID: ").append(configId).append("\n");
        content.append("文件名称: ").append("优惠码-").append(configId);

        Card card = new Card();

        Header header = new Header();
        header.setTitle(new HashMap<String, String>() {{
            put("tag", "plain_text");
            put("content", "优惠码下载");
        }});

        card.setHeader(header);
        card.setElements(Lists.newArrayList());

        Element element = new Element();

        element.setTag("div");
        element.setText(new HashMap<String, String>() {{
            put("tag", "plain_text");
            put("content", content.toString());
        }});

        card.getElements().add(element);

        Element element2 = new Element();

        Action action = new Action();
        action.setTag("button");
        action.setText(new HashMap<String, String>() {{
            put("tag", "plain_text");
            put("content", "点击下载");
        }});
        action.setType("default");
        action.setUrl(fdsUrl);

        element2.setTag("action");
        element2.setActions(Lists.newArrayList(action));
        card.getElements().add(element2);

        return card;
    }


    /**
     * 飞书商品可用券下载消息卡片
     * @param goodsId 商品id
     * @param fdsUrl 下载地址
     * @return Card
     */
    public static Card getGoodsCouponCard(long goodsId, String fdsUrl) {

        StringBuilder content = new StringBuilder();

        content.append("商品(SKU/GID/套装): ").append(goodsId).append("\n");
        content.append("文件名称: ").append("商品对应可用券-").append(goodsId);

        Card card = new Card();

        Header header = new Header();
        header.setTitle(new HashMap<String, String>() {{
            put("tag", "plain_text");
            put("content", "商品可用券下载");
        }});

        card.setHeader(header);
        card.setElements(Lists.newArrayList());

        Element element = new Element();

        element.setTag("div");
        element.setText(new HashMap<String, String>() {{
            put("tag", "plain_text");
            put("content", content.toString());
        }});

        card.getElements().add(element);

        Element element2 = new Element();

        Action action = new Action();
        action.setTag("button");
        action.setText(new HashMap<String, String>() {{
            put("tag", "plain_text");
            put("content", "点击下载");
        }});
        action.setType("default");
        action.setUrl(fdsUrl);

        element2.setTag("action");
        element2.setActions(Lists.newArrayList(action));
        card.getElements().add(element2);

        return card;
    }

    /**
     * 积分低库存预警卡片
     *
     * @param batchId       批次id
     * @param batchName     批次名称
     * @param creator       创建人
     * @param updater       最近维护人
     * @param remainRatio   剩余比例
     * @return              飞书卡片
     */
    public static Card getPointWarningCard(Long batchId, String batchName, String creator, String updater, double remainRatio) {

        StringBuilder content = new StringBuilder();
        //content.append("低库存预警\n");
        content.append("积分批次id: ").append(batchId).append("\n");
        content.append("积分批次名称: ").append(batchName).append("\n");
        content.append("剩余比例: ").append(new DecimalFormat("#.##").format(remainRatio)).append("%").append("\n");
        content.append("创建人: ").append(creator);
        if (StringUtils.isNotBlank(updater)) {
            content.append("\n");
            content.append("最近维护人: ").append(updater);
        }

        Card card = new Card();

        Header header = new Header();
        header.setTitle(new HashMap<String, String>() {{
            put("tag", "plain_text");
            put("content", "积分低库存预警提示");
        }});

        card.setHeader(header);
        card.setElements(Lists.newArrayList());

        Element element = new Element();

        element.setTag("div");
        element.setText(new HashMap<String, String>() {{
            put("tag", "plain_text");
            put("content", content.toString());
        }});

        card.getElements().add(element);

        Element element2 = new Element();

        Action action = new Action();
        action.setTag("button");
        action.setText(new HashMap<String, String>() {{
            put("tag", "plain_text");
            put("content", "打开积分后台");
        }});
        action.setType("default");
        action.setUrl(PointConstant.POINT_ADMIN_URL);

        element2.setTag("action");
        element2.setActions(Lists.newArrayList(action));
        card.getElements().add(element2);

        return card;
    }

}

@Data
class Header {
    private Map<String, String> title;
}

@Data
class Element {
    private String tag;
    private List<Action> actions;
    private Map<String,String> text;
}

@Data
class Action {
    private String tag;
    private Map<String, String> text;
    private String type;
    private String url;
}
