package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/5 20:06
 */
@Data
public class CarPointsBatchConfigPo implements Serializable {

    private static final long serialVersionUID = 4605238037987082120L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 批次名称
     */
    private String name;
    /**
     * 预算池Id
     */
    private Long budgetId;
    /**
     * 预算申请单号
     */
    private String budgetApplyNo;
    /**
     * 行号
     */
    private Long lineNum;
    /**
     * 预算单创建时间
     */
    private String budgetCreateTime;
    /**
     * br申请单号（唯一标识）
     */
    private String brApplyNo;
    /**
     * 发放场景
     */
    private String sendScene;
    /**
     * 开始发放时间
     */
    private Long startTime;
    /**
     * 结束发放时间
     */
    private Long endTime;
    /**
     * 使用时间类型（1相对时间 2绝对时间）
     */
    private Integer useTimeType;
    /**
     * 积分总额
     */
    private Long applyCount;
    /**
     * 已发总额,非实时
     */
    private Long sendCount;
    /**
     * 释放数量
     */
    private Long releaseCount;
    /**
     * 预警阈值
     */
    private Integer warningRatio;
    /**
     * 状态 0创建 1上线 2下线
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String creator;
}
