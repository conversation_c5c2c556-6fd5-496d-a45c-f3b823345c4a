package com.xiaomi.nr.coupon.admin.enums;

import com.xiaomi.youpin.aries.enums.SourceEnum;

public enum AriesSourceEnum {

    YP("yp", "有品"),
    SHOP("shop", "商城"),
    POINT("carPoint","汽车积分");

    public String code;
    public String name;

    AriesSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static SourceEnum getSourceEnumByCode(String code) {
        for (SourceEnum source : SourceEnum.values()) {
            if (source.code.equals(code)) {
                return source;
            }
        }
        return null;
    }
}
