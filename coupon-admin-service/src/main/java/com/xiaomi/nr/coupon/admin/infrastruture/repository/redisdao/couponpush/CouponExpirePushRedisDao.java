package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponpush;


/**
 * 优惠券过期push
 * <AUTHOR>
 */
public interface CouponExpirePushRedisDao {

    /**
     * 设置数据区间结束时间节点
     *
     * @param timeMark String
     * @param endTime long
     */
    void setLastEndTime(String timeMark, long endTime);

    /**
     * 获取数据区间结束时间节点
     *
     * @param timeMark String
     * @return long
     */
    long getLastEndTime(String timeMark);
}
