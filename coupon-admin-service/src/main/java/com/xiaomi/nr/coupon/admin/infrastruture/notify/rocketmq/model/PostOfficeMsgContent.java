package com.xiaomi.nr.coupon.admin.infrastruture.notify.rocketmq.model;

import lombok.Data;

import java.util.Map;

@Data
public class PostOfficeMsgContent {

    /**
     * 消息通道 1-站内信，2-短信，3-push，4-微信公众号,5-小米办公，6-邮件
     */
    private int channel;

    /**
     * 专门给邮件用的接收人，不同的接收人之间，用半角分号隔开，其他类型消息可以不设置
     * <EMAIL>;<EMAIL>
     */
    private String emailReceivers;

    /**
     * 模版里配置的占位参数 {"p1":"aaa","p2":"bbb"}
     */
    private Map<String, Object> params;


}
