package com.xiaomi.nr.coupon.admin.enums.optrecord;

import lombok.Getter;

@Getter
public enum OPtRecordStatusEnum {

    DEFAULT(0, "未执行"),
    SUCCESS(1, "成功"),
    FAIL(2, "失败"),


    ;

    private final int value;
    private final String name;

    OPtRecordStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }


    public static OPtRecordStatusEnum findByValue(int value) {
        OPtRecordStatusEnum[] values = OPtRecordStatusEnum.values();
        for (OPtRecordStatusEnum statusEnum : values) {
            if (value == statusEnum.value) {
                return statusEnum;
            }
        }
        return null;
    }



}
