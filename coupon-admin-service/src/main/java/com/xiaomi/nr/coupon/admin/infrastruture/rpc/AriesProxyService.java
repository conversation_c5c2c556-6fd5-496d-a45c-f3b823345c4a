package com.xiaomi.nr.coupon.admin.infrastruture.rpc;

import com.xiaomi.nr.coupon.admin.util.ResultValidator;
import com.xiaomi.youpin.aries.api.AriesDubboService;
import com.xiaomi.youpin.aries.model.nr.CancelRequest;
import com.xiaomi.youpin.aries.model.nr.CancelResponse;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

/**
 * 调用aries接口
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AriesProxyService {

    /**
     * 积分服务
     */
    @Reference(check = false, interfaceClass = AriesDubboService.class, group = "${aries.dubbo.group}", version = "1.0", timeout = 5000)
    private AriesDubboService ariesDubboService;

    /**
     * 作废积分
     *
     * @param request CancelRequest
     * @return CancelResponse
     * @throws BizError 业务异常
     */
    public CancelResponse cancel(CancelRequest request) throws BizError {
        Result<CancelResponse> result = ariesDubboService.cancel(request);
        ResultValidator.validate(result, "作废积分失败");
        return result.getData();
    }
}
