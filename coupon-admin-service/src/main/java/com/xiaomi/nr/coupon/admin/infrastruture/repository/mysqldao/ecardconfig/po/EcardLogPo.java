package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecardconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class EcardLogPo implements Serializable {
    /**
     * 礼品卡ID
     */
    @SerializedName("card_id")
    private Long cardId;

    /**
     * 用户ID
     */
    @SerializedName("user_id")
    private Long userId;

    /**
     * 订单ID
     */
    @SerializedName("order_id")
    private String orderId;

    /**
     * 退款单ID
     */
    @SerializedName("refund_no")
    private Long refundNo;

    /**
     * 日志类型：-1系统异常日志，0系统正常日志，1消费（含退款）
     */
    @SerializedName("log_type")
    private Integer logType;

    /**
     * 金额变化
     */
    @SerializedName("income")
    private BigDecimal income;

    /**
     * 老余额
     */
    @SerializedName("old_balance")
    private BigDecimal oldBalance;

    /**
     * 新余额
     */
    @SerializedName("new_balance")
    private BigDecimal newBalance;

    /**
     * 操作人ID，系统操作为0
     */
    @SerializedName("operator_id")
    private Long operatorId;

    /**
     * 添加时间
     */
    @SerializedName("add_time")
    private Long addTime;

    /**
     * 描述
     */
    @SerializedName("description")
    private String description;

    /**
     * 最后更新时间
     */
    @SerializedName("last_update_time")
    private String lastUpdateTime;

    /**
     * hash_code值
     */
    @SerializedName("hash_code")
    private String hashCode;

    /**
     * 线下使用
     */
    private Integer offline;
}
