package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.oldcoupon;

import com.google.gson.annotations.SerializedName;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 政策
 *
 * <AUTHOR>
 * @date 2021/5/14
 */
@Data
public class Policy implements Serializable {
    private static final long serialVersionUID = -1187346700211909224L;

    /**
     * 券或者活动的类型
     */
    private Integer type = 0;

    /**
     * 各优先级的政策
     */
    @SerializedName(value = "policy")
    private List<PolicyLevel> policies;
}
