package com.xiaomi.nr.coupon.admin.domain.pointadmin;

import com.alibaba.excel.EasyExcel;
import com.mi.framework.http.HttpClient;
import com.xiaomi.nr.coupon.admin.enums.task.DownloadTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.point.PointFillPathConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponTaskRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.PointFillDetailPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.PointFillPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.fds.impl.FileServiceImpl;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.hdfs.HdfsHelper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.FillCouponTaskPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.Param;
import com.xiaomi.nr.coupon.admin.util.FileUtils;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FSDataInputStream;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description 灌积分文件下载工具
 * <AUTHOR>
 * @date 2024-08-15 20:29
*/
@Component
@Slf4j
public class PointDownLoadHelper {

    @Autowired
    private HdfsHelper hdfsHelper;

    @Autowired
    private FileServiceImpl fileService;

    @Autowired
    private CouponTaskRepository fillCouponTaskRepository;

    public List<PointFillPO> getPointFillDataByFdsPath(String fdsPath) throws BizError {
        if (StringUtils.isBlank(fdsPath)) {
            return Collections.emptyList();
        }

        byte[] bytes = new HttpClient().get(fdsPath).executeAsFile();
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);

        List<PointFillPO> pointFillList = FileUtils.getExcelList(inputStream, PointFillPO.class, PointFillPO.HEADROWNUMBER);

        if (CollectionUtils.isEmpty(pointFillList)) {
            throw ExceptionHelper.create(ErrCode.POINT, "获取灌积分数据失败");
        }

        return pointFillList;
    }


    /**
     * 异步上传灌积分数据到HDFS
     * @param pointFillList 灌积分数据列表
     */
    @Async("asyncExecutor")
    public void uploadPointFillDataToHdfsAsync(List<PointFillPO> pointFillList, String fileName, String destPath) throws BizError {

        if (CollectionUtils.isEmpty(pointFillList)) {
            throw ExceptionHelper.create(ErrCode.POINT, "灌积分数据为空");
        }

        try {
            String pointFillListStr = pointFillList.stream().map(PointFillPO::toString).collect(Collectors.joining(System.lineSeparator()));
            InputStream inputStream = new ByteArrayInputStream(pointFillListStr.getBytes());
            hdfsHelper.copyFileToHDFS(fileName, inputStream, destPath + fileName);
        } catch (Exception e) {
            log.error("uploadPointFillDataToHdfsAsync error", e);
            throw ExceptionHelper.create(ErrCode.POINT, "上传灌积分数据失败");
        }
    }

    /**
     * 下载灌积分数据
     *
     * @param hdfsAddr
     * @return
     * @throws Exception
     */
    public String downloadPointFillDataByHdfsAddress(String hdfsAddr) throws Exception {
        if (StringUtils.isBlank(hdfsAddr)) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "hdfs地址不能为空");
        }

        URI fileUri = hdfsHelper.getFileUri(hdfsAddr);

        if (Objects.isNull(fileUri)) {
            throw ExceptionHelper.create(ErrCode.POINT, "文件不存在");
        }

        // 获取hdfs文件目录
        FileStatus[] files;
        try {
            files = hdfsHelper.listStatus(fileUri, hdfsAddr);
        } catch (FileNotFoundException e) {
            throw ExceptionHelper.create(ErrCode.POINT, "文件不存在");
        }

        FileSystem fs = FileSystem.get(fileUri, new Configuration());
        List<PointFillPO> list = new LinkedList<>();

        try {
            for (FileStatus file : files) {
                // 读取文件
                try (FSDataInputStream in = fs.open(file.getPath());
                     BufferedReader reader = new BufferedReader(new InputStreamReader(in, StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        String[] parts = line.split(",");
                        if (parts.length >= 2) {
                            PointFillPO data = new PointFillPO();
                            data.setMid(Long.parseLong(parts[0]));
                            data.setPointCount(Long.parseLong(parts[1]));
                            list.add(data);
                        }
                    }
                }
            }
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            EasyExcel.write(byteArrayOutputStream, PointFillPO.class)
                    .sheet("灌积分数据")
                    .doWrite(list);

            ByteArrayInputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            String fileName = "灌积分数据" + "_" + TimeUtil.getNowUnixMillis() + ".xlsx";

            return fileService.uploadFileToFds(fileName, inputStream, true);

        } catch (Exception e) {
            log.error("downloadPointFillData error. hdfsAddress: {}", hdfsAddr, e);
            throw ExceptionHelper.create(ErrCode.POINT, "下载灌积分数据失败");
        }
    }


    public List<PointFillPO> downloadEntityList(String hdfsPath) throws Exception {
        URI fileUri = hdfsHelper.getFileUri(hdfsPath);

        FileStatus[] files;

        // 获取hdfs文件目录
        try {
            files = hdfsHelper.listStatus(fileUri, hdfsPath);
        } catch (Exception e) {
            throw ExceptionHelper.create(ErrCode.POINT, "用户集已被清理或不存在");
        }

        if (files.length > 20000) {
            throw ExceptionHelper.create(ErrCode.POINT, "灌积分用户集大于2W, 请联系RD同学下载");
        }

        Configuration config = new Configuration();
        FileSystem fs = FileSystem.get(fileUri, config);
        if (fs == null) {
            throw ExceptionHelper.create(ErrCode.POINT, "用户集已被清理或不存在");
        }

        List<PointFillPO> list = new LinkedList<>();

        try {
            for (FileStatus file : files) {
                // 读取文件
                try (FSDataInputStream in = fs.open(file.getPath());
                     BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(in))) {
                    String line;
                    while ((line = bufferedReader.readLine()) != null) {
                        if (org.apache.commons.lang.StringUtils.isNotBlank(line)) {
                            String[] dataParts = line.split(",");
                            Long mid = Long.parseLong(dataParts[0]);
                            Long pointCount = Long.parseLong(dataParts[1]);
                            PointFillPO pointFillPO = new PointFillPO(mid, pointCount);
                            list.add(pointFillPO);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("downloadFillPointDetail error. path:{}, e", hdfsPath, e);
            return null;
        }

        return list;
    }


    public List<PointFillDetailPO> getFailPointFillDetail(long taskId, DownloadTypeEnum downloadTypeEnum) throws IOException {
        URI fileUri = hdfsHelper.getFileUri(downloadTypeEnum.getHdfsPath() + taskId);
        if (Objects.isNull(fileUri)) {
            return null;
        }

        if (!hdfsHelper.fileExists(fileUri, downloadTypeEnum.getHdfsPath() + taskId) && DownloadTypeEnum.TASK_DETAIL.equals(downloadTypeEnum)) {
            FillCouponTaskPO fillCouponTaskPO = fillCouponTaskRepository.getDetailTaskById(taskId);
            Param param = GsonUtil.fromJson(fillCouponTaskPO.getParams(), Param.class);
            if (param.getApplyCount() > param.getCount()) {
                return null;
                //throw ExceptionHelper.create(ErrCode.COUPON, "任务申请发放数量大于用户数量，失败数量："+(param.getApplyCount()-param.getCount())+"个");
            }
        }

        // 获取hdfs文件目录
        FileStatus[] files = new FileStatus[0];
        try {
            files = hdfsHelper.listStatus(fileUri, downloadTypeEnum.getHdfsPath() + taskId);
        } catch (FileNotFoundException e) {
            return null;
        }

        FileSystem fs = FileSystem.get(fileUri, new Configuration());
        List<PointFillDetailPO> list = new LinkedList<>();

        try {
            for (FileStatus file : files) {
                // 读取文件
                try (FSDataInputStream in = fs.open(file.getPath());
                     BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(in))) {
                    String failStr;
                    String[] subStr;
                    while ((failStr = bufferedReader.readLine()) != null) {
                        subStr = failStr.split(",");
                        if (StringUtils.isNotBlank(subStr[0])) {
                            Long mid = Long.parseLong(subStr[0]);
                            Long pointCount = Long.parseLong(subStr[1]);
                            String reason = subStr[2];
                            list.add(new PointFillDetailPO(mid, pointCount, reason));
                        } else {
                            log.info("fail userId:{} is not a number", subStr[0]);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("downloadFillPointDetail error. taskId:{}, e", taskId, e);
            return null;
        }

        return list;
    }

}
