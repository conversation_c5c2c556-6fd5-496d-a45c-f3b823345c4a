package com.xiaomi.nr.coupon.admin.enums.pointadmin;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PointsConfigLogOptTypeEnum {

    /**
     * 1: 创建
     */
    CREATE(1, "创建"),
    /**
     * 2: 修改
     */
    UPDATE(2, "修改"),
    /**
     * 3: 上线
     */
    ONLINE(3, "上线"),
    /**
     * 4: 下线
     */
    OFFLINE(4, "下线"),

    ;

    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String desc;


}
