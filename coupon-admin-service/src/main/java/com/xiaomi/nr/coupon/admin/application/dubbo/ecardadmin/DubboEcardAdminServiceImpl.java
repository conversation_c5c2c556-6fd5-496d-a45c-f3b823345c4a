package com.xiaomi.nr.coupon.admin.application.dubbo.ecardadmin;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.EcardBaseRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.UserEcardLogVO;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.UserEcardVO;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.UpdateUserEcardRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.UserEcardListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.UserEcardUseInfoResponse;
import com.xiaomi.nr.coupon.admin.api.service.ecardadmin.DubboEcardAdminService;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.UserEcardConvert;
import com.xiaomi.nr.coupon.admin.domain.ecard.EcardService;
import com.xiaomi.nr.coupon.admin.infrastruture.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.notify.robotmessage.RobotSendMessageUtil;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.EcardConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.EcardRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecardconfig.po.EcardLogPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecardconfig.po.EcardPo;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 礼品卡后台服务接口
 */
@Slf4j
@Component
@Service(timeout = 5000, group = "${dubbo.group}", version = "1.0", delay = 5000)
public class DubboEcardAdminServiceImpl implements DubboEcardAdminService {

    @Autowired
    private EcardService ecardService;

    @Autowired
    private UserEcardConvert ecardConvert;

    @Autowired
    private EcardRepository ecardRepository;

    @Autowired
    private RobotSendMessageUtil robotMessageUtil;

    @Autowired
    private EcardConfigRepository ecardConfigRepository;

    @Override
    public Result<BasePageResponse<UserEcardVO>> userEcardList(UserEcardListRequest request) {
        try {
            Long userId = request.getUserId();
            Long cardId = request.getCardId();
            checkUserAndCardId(userId, cardId, true);

            List<EcardPo> ecardPoList;
            List<UserEcardVO> ecardVOList;
            BasePageResponse<UserEcardVO> response = new BasePageResponse<>(request.getPageNo(), request.getPageSize());

            //根据礼品卡ID查,不分页
            if (Objects.isNull(userId)) {
                ecardPoList = ecardRepository.getEcardById(userId, cardId);

                if (CollectionUtils.isEmpty(ecardPoList)) {
                    EcardPo ecardPo = ecardConfigRepository.getEcardById(cardId);
                    ecardPoList = Objects.isNull(ecardPo) ? null : (Collections.singletonList(ecardPo));
                }

                ecardVOList = ecardConvert.convertEcardPoToVo(ecardPoList);

                response.setList(ecardVOList);
                response.setTotalPage(CommonConstant.ONE_LONG);
                response.setTotalCount(CommonConstant.ONE_LONG);

                return Result.success(response);
            }

            //根据用户ID查,分页
            PageHelper.startPage(request.getPageNo(), request.getPageSize());
            ecardPoList = ecardRepository.getEcardById(userId, cardId);

            ecardVOList = ecardConvert.convertEcardPoToVo(ecardPoList);
            PageInfo<EcardPo> pageInfo = new PageInfo<>(ecardPoList);

            response.setList(ecardVOList);
            response.setTotalPage(pageInfo.getPages());
            response.setTotalCount(pageInfo.getTotal());
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboEcardAdminService.userEcardList Exception request={}, error: ", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<UserEcardUseInfoResponse> userEcardUseInfo(EcardBaseRequest request) {
        try {
            checkUserAndCardId(request.getUserId(), request.getCardId(), false);

            List<EcardLogPo> ecardLogPoList = ecardRepository.getEcardLogById(request.getUserId(), request.getCardId());

            List<UserEcardLogVO> ecardLogVOList = ecardConvert.convertEcardLogPoToVo(ecardLogPoList);

            return Result.success(new UserEcardUseInfoResponse(ecardLogVOList));
        } catch (Exception e) {
            log.error("DubboEcardAdminService.userEcardUseInfo Exception request={}, error: ", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<Void> updateUserEcard(UpdateUserEcardRequest request) {
        try {
            checkUserAndCardId(request.getUserId(), request.getCardId(), false);

            EcardPo ecardPo = ecardRepository.getEcardByCardId(request.getUserId(), request.getCardId());

            boolean isOnlyConfig = false;
            if(Objects.isNull(ecardPo)){
                isOnlyConfig = true;
                ecardPo = ecardConfigRepository.getEcardById(request.getCardId());
            }

            ecardService.checkUpdateDelayEcard(ecardPo, request.getEndTime());

            String operator = UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix();

            EcardLogPo logPo = ecardConvert.buildEcardLogPo(ecardPo, request.getEndTime(), operator);

            if(!isOnlyConfig){
                ecardRepository.updateTime(request.getCardId(), request.getEndTime(), logPo);
            }

            asyncUpdateEcardConfigAndLog(logPo, request.getEndTime(), operator);

            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboEcardAdminService.updateUserEcard Exception request={}, error: ", request, e);
            return Result.fromException(e);
        }
    }


    /**
     * 异步更新配置库数据 & 写入日志
     * @param logPo      日志信息
     * @param delayTime  延期时间
     * @param operator   操作人
     */
    @Async("asyncExecutor")
    private void asyncUpdateEcardConfigAndLog(EcardLogPo logPo, Long delayTime, String operator) {

        Long cardId = logPo.getCardId();
        try{
            ecardConfigRepository.updateTime(cardId, delayTime, logPo);
        }catch (Exception e){
            log.error("asyncUpdateEcardConfigAndLog Exception, ecardId={},delayTime={},error={}", cardId, delayTime, e);
            String msg = "异步更新礼品卡信息失败, 操作人:["+operator+"];操作时间：[" + TimeUtil.getNowDateTime()+"];卡ID:["+cardId+"];延期时间:["+delayTime+"]";
            robotMessageUtil.sendPrivateChat(msg, "yangmenghui");
        }
        log.info("asyncUpdateEcardConfigAndLog execute success, ecardId={},delayTime={}", cardId, delayTime);
    }


    /**
     * 检验cardId & userId
     *
     * @param userId userId
     * @param cardId cardId
     * @throws BizError 参数异常
     */
    private void checkUserAndCardId(Long userId, Long cardId, boolean isAll) throws BizError {
        if (Objects.isNull(userId) && Objects.isNull(cardId)) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "用户ID和礼品卡ID均为空,请验证");
        }

        if(!isAll && (Objects.isNull(cardId) || cardId < CommonConstant.ONE_LONG)){
            throw ExceptionHelper.create(GeneralCodes.ParamError, "礼品卡ID不能为空,请验证");
        }
    }
}
