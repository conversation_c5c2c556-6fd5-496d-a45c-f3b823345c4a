package com.xiaomi.nr.coupon.admin.domain.pointadmin.model;

import com.xiaomi.nr.coupon.admin.infrastruture.annotation.FieldsAlias;
import lombok.Data;

/**
 * @Description: 券修改范围
 * @Date: 2022.03.10 22:00
 */
@Data
public class PointModifyDimension {

    @FieldsAlias(alias = "积分批次名称")
    private String name;

    @FieldsAlias(alias = "发放周期开始时间")
    private String startTime;

    @FieldsAlias(alias = "发放周期结束时间")
    private String endTime;

    @FieldsAlias(alias = "积分总额")
    private Long applyCount;

    @FieldsAlias(alias = "预警阀值")
    private Integer warningRatio;

}
