package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.datapreparewrapper;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.EventContext;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.GoodsItemPo;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.goods.GmsProxyService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/14
 */
@Component
public class CarDataPrepareWrapper extends BaseDataPrepareWrapper {

    @Resource
    private GmsProxyService gmsProxyService;

    /**
     * 准备商品信息
     *
     * @param eventContext      后置事件处理器参数传递上下文
     * @param couponConfigPO    券配置po
     * @throws Exception        异常
     */
    @Override
    public void prepareGoodsInfo(EventContext eventContext, CouponConfigPO couponConfigPO) throws Exception {
        // 转换商品
        GoodsItemPo goodsItemPo = eventContext.getGoodsItemPo();

        // 整车查询ssu
        List<Long> ssuList = goodsItemPo.getSsuList();
        if (CollectionUtils.isNotEmpty(ssuList)) {
            eventContext.setSsuDtos(gmsProxyService.queryListBySsuIds(ssuList));
        }
    }
}
