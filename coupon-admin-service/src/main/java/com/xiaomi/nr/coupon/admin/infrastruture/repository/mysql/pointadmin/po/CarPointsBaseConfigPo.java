package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po;

import lombok.Data;

import java.util.Date;

/**
 * 汽车积分通用配置
 *
 * <AUTHOR>
 * @date 2023/12/6
 */
@Data
public class CarPointsBaseConfigPo {
    /**
     * 主键
     */
    private Long id;
    /**
     * 有效期
     */
    private Long validTime;
    /**
     * 抵扣比例
     */
    private Integer discountRatio;
    /**
     * 订单比例
     */
    private Integer orderRatio;
    /**
     * 状态: 1-有效 2-无效
     */
    private Integer status;
    /**
     * 扩展信息
     */
    private String extInfo;
    /**
     * 最后修改人
     */
    private String updater;
    /**
     * 最后更新时间
     */
    private Date updateTime;
}
