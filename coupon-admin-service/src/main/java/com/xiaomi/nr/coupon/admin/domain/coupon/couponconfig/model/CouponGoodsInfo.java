package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CouponGoodsInfo {

    /**
     * 商品范围类型  1 商品券 2 分类券
     */
    private int scopeType;

    /**
     * 可用商品列表 key： sku package suit
     */
    private Map<String, List<Long>> goodsInclude;

    /**
     * 不可用商品列表 key： sku package
     */
    private Map<String,List<Long>> goodsExclude;

    /**
     * 三级类目Id列表
     */
    private List<Long> categoryIds;

    /**
     * 工时ssu
     */
    private Map<Long, Integer> labourHourSsu;

    /**
     * 配件ssu
     */
    private Map<Long, Integer> partsSsu;

}
