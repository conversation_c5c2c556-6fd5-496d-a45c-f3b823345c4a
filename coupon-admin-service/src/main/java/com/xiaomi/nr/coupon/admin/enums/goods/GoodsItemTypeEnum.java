package com.xiaomi.nr.coupon.admin.enums.goods;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description 
 * <AUTHOR>
 * @date 2024-11-06 17:31
*/
@Getter
@AllArgsConstructor
public enum GoodsItemTypeEnum {

    /**
     * 单品
     */
    SINGLE(0, "单品"),

    /**
     * 新套装
     */
    SUIT(1, "新套装"),

    /**
     * 主副品
     */
    BUNDLE(2, "主副品"),

    /**
     * 老套装
     */
    PACKAGE(10, "老套装"),

    /**
     * 虚拟资产
     */
    VIRTUAL(20, "虚拟资产"),

    ;

    private final int value;
    private final String desc;
}
