package com.xiaomi.nr.coupon.admin.infrastruture.login;

import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcContext;

/**
 * 用户信息领域
 */
public class UserInfoItem {

    /**
     * 邮箱
     */
    private String email;

    /**
     * 邮箱前缀
     */
    private String emailPrefix;

    /**
     * 小米账号
     */
    private long miId;

    /**
     * 用户姓名
     */
    private String userName;


    public String getEmail() {
        return RpcContext.getContext().getAttachment("$upc_email");
    }

    public String getEmailPrefix() {
        return RpcContext.getContext().getAttachment("$upc_account");
    }

    public String getValidateEmailPrefix() throws BizError {
        String account= RpcContext.getContext().getAttachment("$upc_account");
        if (StringUtils.isBlank(account)) {
            throw ExceptionHelper.create(GeneralCodes.NotAuthorized, "未找到登录用户信息");
        }
        return account;
    }

    public long getMiId() {
        String miId = RpcContext.getContext().getAttachment("$upc_miID");
        return StringUtils.isBlank(miId) ? 0L: Long.parseLong(miId);
    }

    public String getUserName() {
        return RpcContext.getContext().getAttachment("$upc_userName");
    }



}
