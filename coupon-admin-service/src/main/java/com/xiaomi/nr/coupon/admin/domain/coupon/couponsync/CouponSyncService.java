package com.xiaomi.nr.coupon.admin.domain.coupon.couponsync;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sync.request.SyncXiguaMarketCouponReceiveRecordRequest;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponCodeConvert;
import com.xiaomi.nr.coupon.admin.enums.scene.SendTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponCodeRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.CodeCouponReceiveRecordPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.coupon.po.CouponCodePO;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.WatermelonCouponServiceProxy;
import com.xiaomi.nr.coupon.admin.util.AesUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.nr.phoenix.api.dto.request.watermelon.WatermelonCodeCouponSyncReq;
import com.xiaomi.nr.phoenix.api.dto.response.watermelon.WatermelonCouponSyncResp;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import groovy.util.logging.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/24 11:24
 */
@Component
@Slf4j
public class CouponSyncService {
    private static final Logger log = LoggerFactory.getLogger(CouponSyncService.class);
    @Resource
    private CouponCodeRepository couponCodeRepository;

    @Resource
    private CouponConfigRepository couponConfigRepository;

    @Resource
    private CouponCodeConvert couponCodeConvert;

    @Resource
    private WatermelonCouponServiceProxy watermelonCouponServiceProxy;

    @Autowired
    @Qualifier("redissonClient")
    private RedissonClient redissonClient;

    /**
     * 同步券码信息单批次数量
     */
    private static final int SYNC_BATCH_SIZE = 1000;

    private static final String lockName = "XIGUA_COUPON_SYNC_LOCK";

    private static final String AES_PASSWORD = "M6EED09BCC6539AD";

    /**
     * 券码信息同步
     */
    public void syncXiguaMarketCouponMessage() throws Exception {
        // 分布式锁
        RLock lock = redissonClient.getLock(lockName);
        boolean tryLock = false;

        // 同步5次
        for (int i = 0; i < 5; i++) {
            try {
                // 尝试获取锁，等待时间1秒，锁定时间60秒
                tryLock = lock.tryLock(1000, 60000, TimeUnit.MILLISECONDS);

                if (!tryLock) {
                    break;
                }

                // 筛选tb_codecoupon表中所有西瓜投放场景未同步的优惠码，order by type_id
                List<CouponCodePO> couponCodePos = couponCodeRepository.getNotSyncCouponCodes(SendTypeEnum.XIGUA_MARKET.getCode(), TimeUtil.getNowUnixSecond());

                if (CollectionUtils.isEmpty(couponCodePos)) {
                    return;
                }

                // 按照券批次id聚合
                Map<Long, List<CouponCodePO>> couponCodeMap = couponCodePos.stream()
                        .collect(Collectors.groupingBy(CouponCodePO::getTypeId));

                for (Map.Entry<Long, List<CouponCodePO>> entry : couponCodeMap.entrySet()) {
                    Long typeId = entry.getKey();

                    for (List<CouponCodePO> subList : Lists.partition(entry.getValue(), SYNC_BATCH_SIZE)) {
                        List<String> couponCodes = decryptAndSortCouponCodes(subList);

                        if (CollectionUtils.isNotEmpty(couponCodes)) {
                            WatermelonCodeCouponSyncReq req = createSyncRequest(typeId, couponCodes);

                            WatermelonCouponSyncResp resp = syncCouponCodes(req);

                            if (resp != null && resp.getSuccessCount() > 0) {
                                updateSyncStatus(resp.getSuccessCodeList());
                            }
                        }
                    }
                }
            } catch (InterruptedException e) {
                log.error("syncXiguaMarketCouponMessage 获取redis锁失败");
                throw e;
            } catch (Exception e) {
                log.error("syncXiguaMarketCouponMessage has error, e = ", e);
                throw e;
            } finally {
                // 释放锁
                if (tryLock) {
                    lock.unlock();
                }
            }
        }
    }

    /**
	 * 解密并排序优惠券代码列表
	 *
	 * @param subList 包含优惠券代码的列表
	 * @return 解密并按字母顺序排序后的优惠券代码列表
	 */
    private List<String> decryptAndSortCouponCodes(List<CouponCodePO> subList) {
        return subList.stream().map(e -> {
                    try {
                        // 使用AES密码解密优惠券代码
                        return AesUtil.decrypt(AES_PASSWORD, e.getCouponCode());
                    } catch (Exception ex) {
                        // 记录解密失败的错误日志，并抛出运行时异常
                        log.error("CouponSyncService.syncXiguaMarketCouponMessage couponCodes解密失败! couponCode = {}", e.getCouponCode());
                        throw new RuntimeException(ex);
                    }
                })
                // 按字母顺序排序解密后的优惠券代码
                .sorted()
                // 收集结果到列表中
                .collect(Collectors.toList());
    }

    /**
	 * 创建一个用于同步优惠券码的请求对象
	 *
	 * @param typeId 优惠券模板ID
	 * @param couponCodes 优惠券码列表
	 * @return 用于同步优惠券码的请求对象
	 */
	private WatermelonCodeCouponSyncReq createSyncRequest(Long typeId, List<String> couponCodes) {
        // 创建一个新的WatermelonCodeCouponSyncReq对象
        WatermelonCodeCouponSyncReq req = new WatermelonCodeCouponSyncReq();

        // 设置优惠券代码列表
        req.setCodes(couponCodes);

        // 设置渠道为"XIAOMI"
        req.setChannel("XIAOMI");

        // 设置模板代码为typeId的字符串形式
        req.setTemplateCode(String.valueOf(typeId));

        // 设置同步业务范围为"TEMPLATE_CODE"
        req.setSyncBizScope("TEMPLATE_CODE");

        // 设置外部业务编号为第一个优惠券代码和优惠券代码列表的哈希码的组合
        req.setOutBizNo(couponCodes.get(0) + "-" + couponCodes.hashCode());

        // 返回创建的请求对象
        return req;
    }

    /**
	 * 同步西瓜券码信息
	 *
	 * @param req 请求参数，包含需要同步的券码信息
	 * @return 同步结果的响应对象
	 */
	private WatermelonCouponSyncResp syncCouponCodes(WatermelonCodeCouponSyncReq req) {
        WatermelonCouponSyncResp resp = null;
        try {
            resp = watermelonCouponServiceProxy.syncCodeCoupon(req);
        } catch (Exception e) {
            log.info("syncXiguaMarketCouponMessage 同步券码信息失败, 重试一次 req = {}, e = ", GsonUtil.toJson(req), e);
            try {
                resp = watermelonCouponServiceProxy.syncCodeCoupon(req);
            } catch (Exception retryException) {
                log.error("syncXiguaMarketCouponMessage 重试同步券码信息失败 req = {}, retryException = ", GsonUtil.toJson(req), retryException);
            }
        }
        return resp;
    }

    /**
	 * 更新同步状态
	 *
	 * @param successCodeList 成功的券码列表
	 */
	private void updateSyncStatus(List<String> successCodeList) {
        for (String couponCode : successCodeList) {
            String couponIndex = DigestUtils.md5Hex(couponCode.getBytes());
            try {
                couponCodeRepository.updateSyncSuccessStatus(couponIndex, AesUtil.encrypt(AES_PASSWORD, couponCode));
            } catch (Exception e) {
                log.error("syncXiguaMarketCouponMessage 修改券码同步状态失败, successCouponIds = {}", successCodeList);
            }
        }
    }

    /**
     * 西瓜侧券码领取记录同步
     */
    public void syncXiguaMarketCouponReceiveRecord(SyncXiguaMarketCouponReceiveRecordRequest request) throws Exception {
        String voucherCode = request.getVoucherCode();

        // 1、幂等性校验
        String couponCode = AesUtil.encrypt(AES_PASSWORD, voucherCode);
        String couponIndex = DigestUtils.md5Hex(voucherCode);
        CodeCouponReceiveRecordPo receiveRecord = couponCodeRepository.getReceiveRecordByCouponCode(couponCode, couponIndex);
        if (receiveRecord != null) {
            log.info("CouponSyncService.syncXiguaMarketCouponReceiveRecord 请求幂等, request = {}", GsonUtil.toJson(request));
            return;
        }

        // 2、校验券码
        CouponCodePO couponCodePo = couponCodeRepository.getByCouponCode(couponCode, couponIndex);
        if (couponCodePo == null) {
            log.error("CouponSyncService.syncXiguaMarketCouponReceiveRecord couponCode不存在, request = {}", GsonUtil.toJson(request));
            throw ExceptionHelper.create(GeneralCodes.NotFound, "优惠码不存在");
        }

        if (!SendTypeEnum.XIGUA_MARKET.getCode().equals(couponCodePo.getSendType())) {
            log.error("CouponSyncService.syncXiguaMarketCouponReceiveRecord couponCode优惠码投放类型非法, request = {}, couponCodePo = {}",
                    GsonUtil.toJson(request), couponCodePo);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "优惠码投放类型非法");
        }

        // 3、构建CodeCouponReceiveRecordPo
        CodeCouponReceiveRecordPo receiveRecordPo = couponCodeConvert.buildCodeCouponReceiveRecordPo(request);
        if (receiveRecordPo != null) {
            // 4、记录领取记录到db
            couponCodeRepository.insertReceiveRecord(receiveRecordPo);
        }
    }
}
