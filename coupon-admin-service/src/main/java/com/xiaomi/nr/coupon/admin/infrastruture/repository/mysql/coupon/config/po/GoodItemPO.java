package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @description: 商品PO
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/3/5 9:52 上午
 * @Version: 1.0
 **/
@Data
public class GoodItemPO  implements Serializable {

    private List<Long> sku;

    private List<Long> goods;

    private List<Long> packages;

    private List<Long> ssu;

    private Map<Long, Integer> labourHourSsu;

    private Map<Long, Integer> partsSsu;

    private List<Long> suit;
}
