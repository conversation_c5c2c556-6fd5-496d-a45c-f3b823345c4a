package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class UserCouponPO {

    /**
     * 优惠券ID
     */
    private long id;

    /**
     * 用户ID
     */
    private long userId;

    /**
     * 优惠券配置ID
     */
    private long typeId;

    /**
     * 开始时间
     */
    private long startTime;

    /**
     * 结束时间
     */
    private long endTime;

    /**
     * 有效天数
     */
    private Integer days;

    /**
     * 优惠券状态
     */
    private String stat;

    /**
     * 订单号
     */
    private long orderId;

    /**
     * 使用时间
     */
    private long useTime;

    /**
     * 过期时间
     */
    private long expireTime;

    /**
     * 添加时间
     */
    private long addTime;

    /**
     * 作废时间
     */
    private long invalidTime;

    /**
     * 请求id（长度限制在64个字符内，非常重要，要求单用户级的全局唯一）
     */
    private String requestId;

    /**
     * 发放方式
     */
    private String sendType;

    /**
     * 0:线上使用，1:门店使用
     */
    private int offline;

    /**
     * 发放活动 / 任务id
     */
    private String activityId;

    /**
     * 扩展信息
     */
    private String extendInfo;

    /**
     * vid
     */
    private String vid;

    /**
     * 所属业务 0 零售业务 3 整车销售 4 汽车售后
     */
    private Integer bizPlatform;

    /**
     * 发券的订单号
     */
    private String fromOrderId;

    /**
     * 实际抵用金额
     */
    private BigDecimal replaceMoney;

    /**
     * 减免邮费
     */
    private BigDecimal reduceExpress;

    /**
     * 券的父id
     */
    private Long parentId;

}




