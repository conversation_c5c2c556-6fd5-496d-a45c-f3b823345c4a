package com.xiaomi.nr.coupon.admin.domain.coupon.goods;

import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoDTO;
import com.xiaomi.goods.gms.dto.batch.BatchedInfoDto;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.request.GoodsDiscountLevelRequest;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.UploadGoodsFailPO;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface GoodsService {


    /**
     * 上传商品文件到fds
     * @param goodsInclude
     * @param fileName
     */
    void uploadGoodsExcelFile(Map<String, List<Long>> goodsInclude,String fileName) throws Exception;


    /**
     * 获取fds文件路径
     * @param fileName
     * @return
     */
    String getBpmGoodsFdsUrl(String fileName);



    /**
     * 从fds文件，获取商品id
     *
     * @param fileUrl
     * @return
     */
    List<Long> queryGoodsList(String fileUrl, GoodsLevelEnum goodsLevelEnum) throws Exception;

    /**
     * 查询商品详细信息
     * @throws Exception 业务异常
     */
    GoodsRuleDetailVO searchGoodsDetailInfo(GoodsRuleVO goodsRuleVO, Integer promotionType, Integer bizPlatform) throws Exception;

    /**
     * 查询sku信息
     * @param skuList sku
     * @return sku信息
     * @throws Exception 业务异常
     */
    List<SkuInfoVO> searchSkuInfo(List<Long> skuList) throws Exception;

    /**
     * 查询套装信息
     * @param packageList 套装id
     * @return 套装信息
     * @throws Exception 业务异常
     */
    List<PackageInfoVO> searchPackageInfo(List<Long> packageList) throws Exception;

    /**
     * 查询（新）套装信息
     * @param suitList 套装id
     * @return 套装信息
     * @throws Exception 业务异常
     */
    List<PackageInfoVO> searchSuitInfo(List<Long> suitList) throws Exception;

    /**
     * 查询ssu
     * @param ssuList ssu
     * @return ssu信息
     * @throws Exception 业务异常
     */
    List<SsuInfoVO> searchSsuInfo(List<Long> ssuList) throws Exception;


    /**
     * 获取预估折扣力度
     *
     * @param request
     * @return
     */
    GoodsDiscountLevelVO getGoodsDiscountLevel(GoodsDiscountLevelRequest request, Map<Long, Long> goodsPriceMap, Map<Long, Long> packagePriceMap) throws Exception;


    /**
     * 获取商品信息，bpm调用
     * @param goodsInclude
     * @return
     * @throws Exception
     */
    List<GoodsPriceVO> getGoodsPriceVO(Map<String, List<Long>> goodsInclude) throws Exception;

    /**
     * 导入商品的失败详情写入fds
     */
    void uploadGoodsFailReason(List<UploadGoodsFailPO> uploadGoodsFailPOS, String fileName) throws Exception;

    /**
     * 获取商品上传失败fds文件路径
     * @param fileName 文件名
     * @return
     */
    String getUploadGoodsFailFdsUrl(String fileName);

    /**
     * 多线程根据类目获取商品
     * @param needMiSupportShipment 为true时，SkuInfoDto的参数miSupportShipment才有意义，
     *                               1. miSupportShipment = True, 代表米家大仓发货商品
     *                               2. miSupportShipment = False, 代表米家非大仓发货商品
     *                               3. miSupportShipment = null, 代表没有配置是否米家大仓发货属性
     * @param categoryIds
     * @return
     * @throws Exception
     */
    List<SkuInfoDto> getSkuByCategoryId(Set<Integer> categoryIds, List<Integer> bussinessTypes, boolean needMiSupportShipment, List<Integer> BizSubTypeEnum) throws Exception;

    /**
     * 多线程根据类目获取商品
     * @param categoryIds
     * @return
     * @throws Exception
     */
    Map<Long,List<SkuInfoDto>> getSkuMapByCategoryId(List<Integer> categoryIds, List<Integer> bussinessTypes, boolean needMiSupportShipment) throws Exception;



    /**
     * 校验是否符合过滤条件  true-不过滤  false-过滤
     * @param couponType
     * @param skuInfoDto
     * @param uploadGoodsFailPOS
     * @return
     */
    boolean filterSku(Integer couponType, SkuInfoDto skuInfoDto, List<UploadGoodsFailPO> uploadGoodsFailPOS);

    /**
     * 校验是否符合过滤条件  true-不过滤  false-过滤
     * @param batchedInfoDto
     * @param uploadGoodsFailPOS
     * @return
     */
    boolean filterPackage(BatchedInfoDto batchedInfoDto, List<UploadGoodsFailPO> uploadGoodsFailPOS);

    /**
     * 校验是否符合过滤条件 true-不过滤 false-过滤
     * @param goodsMultiInfoDTO
     * @param uploadGoodsFailPOS
     * @return
     */
    boolean filterSuit(GoodsMultiInfoDTO goodsMultiInfoDTO, List<UploadGoodsFailPO> uploadGoodsFailPOS);

    /**
     * 匹配商品可参与折扣力度最高的商品券
     * @param skuPriceMap     sku及价格信息
     * @param packagePriceMap 套装及价格信息
     * @param request
     * @return Map
     * @throws Exception
     */
    Map<Long, Map<Integer, GoodsCouponPromVO>> matchGoodsCouponType(Map<Long, Long> skuPriceMap, Map<Long, Long> packagePriceMap, GoodsDiscountLevelRequest request) throws Exception;

}