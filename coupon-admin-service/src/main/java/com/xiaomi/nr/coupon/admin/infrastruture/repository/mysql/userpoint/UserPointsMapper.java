package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.userpoint;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.userpoint.po.UserPointsPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户积分 mapper
 *
 * <AUTHOR>
 * @date 2023/12/7
 */
@Mapper
@Component
public interface UserPointsMapper {

    /**
     * 查询用户积分记录总数
     *
     * @param mid     用户ID
     * @param batchId 批次id
     * @return Long count
     */
    @Select("<script>" +
            "select count(1) as count" +
            " from user_points_record " +
            " where 1=1 " +
            "<if test=\"mid != null and mid != 0\"> and mid=#{mid,jdbcType=BIGINT} </if>" +
            "<if test=\"batchId != null and batchId != 0\"> and batch_id=#{batchId,jdbcType=BIGINT} </if>" +
            "</script>")
    Long getUserPointsCount(@Param("mid") Long mid, @Param("batchId") Long batchId);

    /**
     * 查询用户积分记录列表（倒序）
     *
     * @param mid      用户ID
     * @param batchId  批次id
     * @param limitStart 开始位置
     * @param pageSize 页面大小
     * @return List<>  列表
     */
    @Select("<script>" +
            "select id,mid,batch_id,request_id,start_time,end_time,stat,send_scene,total_count,balance_count,extend_info,activate_time,invalid_time,send_time " +
            " from user_points_record " +
            " where 1=1 " +
            "<if test=\"mid != null and mid != 0\"> and mid=#{mid,jdbcType=BIGINT} </if>" +
            "<if test=\"batchId != null and batchId != 0\"> and batch_id=#{batchId,jdbcType=BIGINT} </if>" +
            " order by id desc " +
            " limit #{limitStart}, #{pageSize} " +
            "</script>")
    List<UserPointsPo> getUserPointsList(@Param("mid") Long mid, @Param("batchId") Long batchId, @Param("limitStart") int limitStart, @Param("pageSize") int pageSize);

    /**
     * 查询用户积分记录列表（倒序）
     *
     * @param batchId  批次id
     * @param lastId   分页lastId
     * @param pageSize 页面大小
     * @return List<>  列表
     */
    @Select("<script>" +
            "select id,mid,batch_id,request_id,start_time,end_time,stat,send_scene,total_count,balance_count,extend_info,activate_time,invalid_time,send_time" +
            " from user_points_record " +
            " where batch_id=#{batchId,jdbcType=BIGINT} and id > #{lastId,jdbcType=BIGINT} " +
            " order by id asc " +
            " limit #{pageSize} " +
            "</script>")
    List<UserPointsPo> getBatchPointsList(@Param("batchId") Long batchId, @Param("lastId") Long lastId, @Param("pageSize") int pageSize);


    /**
     * 查询用户积分记录
     *
     * @param mid      用户mid
     * @param pointId 用户积分Id
     * @return UserPointsPo
     */
    @Select("select id,mid,batch_id,request_id,start_time,end_time,stat,send_scene,total_count,balance_count,extend_info,activate_time,invalid_time,send_time" +
            " from user_points_record where id=#{pointId} and mid=#{mid}")
    UserPointsPo getUserPointById(@Param("mid") Long mid, @Param("pointId") Long pointId);

    /**
     * 作废用户积分
     * @param mid       用户mid
     * @param pointId   用户积分Id
     * @param newStatus 作废状态
     * @param invalidTime  作废时间
     * @return
     */
    @Update("update user_points_record set stat=#{newStatus}, invalid_time=#{invalidTime} where id =#{pointId} and mid=#{mid}")
    Long cancelUserPoint(@Param("mid") Long mid, @Param("pointId") long pointId, @Param("newStatus") Integer newStatus, @Param("invalidTime") long invalidTime);


}
