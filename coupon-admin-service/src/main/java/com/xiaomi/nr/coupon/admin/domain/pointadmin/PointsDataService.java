package com.xiaomi.nr.coupon.admin.domain.pointadmin;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointsBatchDataStat;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointsBatchDataStatRequest;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.PointsStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.UserPointsRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.userpoint.po.UserPointsPo;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 积分数据服务
 *
 * <AUTHOR>
 * @date 2023/12/6
 */
@Service
@Slf4j
public class PointsDataService {

    public PointsBatchDataStat summaryCarPointBatchData(List<UserPointsPo> userPointsPoList){

        long sendCnt=0L;
        long unUsedCnt=0L;
        long usedCnt=0L;
        long expiredCnt=0L;
        long canceledCnt=0L;

        Long nowTime = TimeUtil.getNowUnixSecond();

        for (UserPointsPo userPointsPo : userPointsPoList) {
            sendCnt += userPointsPo.getTotalCount();
            usedCnt += userPointsPo.getTotalCount() - userPointsPo.getBalanceCount();
            if (!PointsStatusEnum.INVALID.getCode().equals(userPointsPo.getStat()) && userPointsPo.getBalanceCount() > 0) {
                if (userPointsPo.getEndTime() >= nowTime) {
                    unUsedCnt += userPointsPo.getBalanceCount();
                } else {
                    expiredCnt += userPointsPo.getBalanceCount();
                }
            }
            if (PointsStatusEnum.INVALID.getCode().equals(userPointsPo.getStat())) {
                canceledCnt += userPointsPo.getBalanceCount();
            }
        }

        return PointsBatchDataStat.builder()
                .sendCnt(sendCnt)
                .unUseCnt(unUsedCnt)
                .usedCnt(usedCnt)
                .expiredCnt(expiredCnt)
                .invalidCnt(canceledCnt)
                .build();
    }


}
