package com.xiaomi.nr.coupon.admin.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

/**
 * HTTP Client
 *
 * <AUTHOR>
 */
public class HttpClientUtil {

    /**
     * 连接超时时间（毫秒）
     */
    private static final Integer CONNECT_TIMEOUT = 10000;

    /**
     * 请求超时时间（毫秒）
     */
    private static final Integer REQUEST_TIMEOUT = 30000;

    /**
     * 读取数据超时时间（毫秒）
     */
    private static final Integer SOCKET_TIMEOUT = 60000;

    /**
     * get client
     *
     * @param url            String URL
     * @param header         Map<String, String> 头部信息
     * @param connectTimeout Integer 连接超时时间（毫秒，默认10秒）
     * @param requestTimeout Integer 请求超时时间（毫秒，默认30秒）
     * @param socketTimeout  Integer 读取数据超时时间（毫秒，默认60秒）
     * @return String
     * @throws Exception
     */
    public static String doGet(String url, Map<String, String> header, Integer connectTimeout, Integer requestTimeout, Integer socketTimeout) throws Exception {
        if (connectTimeout == null) {
            connectTimeout = CONNECT_TIMEOUT;
        }
        if (requestTimeout == null) {
            requestTimeout = REQUEST_TIMEOUT;
        }
        if (socketTimeout == null) {
            socketTimeout = SOCKET_TIMEOUT;
        }

        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        String result = "";
        try {
            // 通过址默认配置创建一个httpClient实例
            httpClient = HttpClients.createDefault();

            // 创建httpGet远程连接实例
            HttpGet httpGet = new HttpGet(url);

            // 设置请求头信息
            if (header != null && !header.isEmpty()) {
                for (Map.Entry<String, String> entry : header.entrySet()) {
                    httpGet.setHeader(entry.getKey(), entry.getValue());
                }
            }

            // 设置配置请求参数
            // 连接主机服务超时时间
            // 请求超时时间
            // 数据读取超时时间
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(connectTimeout)
                    .setConnectionRequestTimeout(requestTimeout)
                    .setSocketTimeout(socketTimeout)
                    .build();

            // 为httpGet实例设置配置
            httpGet.setConfig(requestConfig);

            // 执行get请求得到返回对象
            response = httpClient.execute(httpGet);

            // 通过返回对象获取返回数据
            HttpEntity entity = response.getEntity();

            // 通过EntityUtils中的toString方法将结果转换为字符串
            result = EntityUtils.toString(entity);
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        } finally {
            // 关闭资源
            if (null != response) {
                try {
                    response.close();
                } catch (Exception e) {
                    throw new Exception(e.getMessage());
                }
            }
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (Exception e) {
                    throw new Exception(e.getMessage());
                }
            }
        }
        return result;
    }

    /**
     * post client
     *
     * @param url            String URL
     * @param paramMap       Map<String, Object> 参数
     * @param header         Map<String, String> 头部信息
     * @param connectTimeout Integer 连接超时时间（毫秒，默认10秒）
     * @param requestTimeout Integer 请求超时时间（毫秒，默认30秒）
     * @param socketTimeout  Integer 读取数据超时时间（毫秒，默认60秒）
     * @return String
     * @throws Exception
     */
    public static String doPost(String url, Map<String, Object> paramMap, Map<String, String> header, Integer connectTimeout, Integer requestTimeout, Integer socketTimeout) throws Exception {
        if (connectTimeout == null) {
            connectTimeout = CONNECT_TIMEOUT;
        }
        if (requestTimeout == null) {
            requestTimeout = REQUEST_TIMEOUT;
        }
        if (socketTimeout == null) {
            socketTimeout = SOCKET_TIMEOUT;
        }

        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        String result = "";

        try {
            // 创建httpClient实例
            httpClient = HttpClients.createDefault();

            // 创建httpPost远程连接实例
            HttpPost httpPost = new HttpPost(url);

            // 配置请求参数实例
            // 设置连接主机服务超时时间
            // 设置连接请求超时时间
            // 设置读取数据连接超时时间
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(connectTimeout)
                    .setConnectionRequestTimeout(requestTimeout)
                    .setSocketTimeout(socketTimeout)
                    .build();

            // 为httpPost实例设置配置
            httpPost.setConfig(requestConfig);

            // 设置请求头信息
            if (header != null && !header.isEmpty()) {
                for (Map.Entry<String, String> entry : header.entrySet()) {
                    httpPost.setHeader(entry.getKey(), entry.getValue());
                }
            }

            // 封装post请求参数
            if (null != paramMap && paramMap.size() > 0) {
                List<NameValuePair> nameValuePairs = new ArrayList<>();

                // 通过map集成entrySet方法获取entity
                Set<Entry<String, Object>> entrySet = paramMap.entrySet();

                // 循环遍历，获取迭代器
                for (Entry<String, Object> mapEntry : entrySet) {
                    nameValuePairs.add(new BasicNameValuePair(mapEntry.getKey(), mapEntry.getValue().toString()));
                }

                // 为httpPost设置封装好的请求参数
                httpPost.setEntity(new UrlEncodedFormEntity(nameValuePairs, "UTF-8"));
            }


            // httpClient对象执行post请求,并返回响应参数对象
            httpResponse = httpClient.execute(httpPost);

            // 从响应对象中获取响应内容
            HttpEntity entity = httpResponse.getEntity();

            result = EntityUtils.toString(entity);
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        } finally {
            // 关闭资源
            if (null != httpResponse) {
                try {
                    httpResponse.close();
                } catch (Exception e) {
                    throw new Exception(e.getMessage());
                }
            }
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (Exception e) {
                    throw new Exception(e.getMessage());
                }
            }
        }
        return result;
    }





    /**
     * post client
     *
     * @param url            String URL
     * @param paramMap       Map<String, Object> 参数
     * @param header         Map<String, String> 头部信息
     * @param connectTimeout Integer 连接超时时间（毫秒，默认10秒）
     * @param requestTimeout Integer 请求超时时间（毫秒，默认30秒）
     * @param socketTimeout  Integer 读取数据超时时间（毫秒，默认60秒）
     * @return String
     * @throws Exception
     */
    public static String doPostV2(String url, Map<String, Object> paramMap, Map<String, String> header, Integer connectTimeout, Integer requestTimeout, Integer socketTimeout) throws Exception {
        if (connectTimeout == null) {
            connectTimeout = CONNECT_TIMEOUT;
        }
        if (requestTimeout == null) {
            requestTimeout = REQUEST_TIMEOUT;
        }
        if (socketTimeout == null) {
            socketTimeout = SOCKET_TIMEOUT;
        }

        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        String result = "";

        try {
            // 创建httpClient实例
            httpClient = HttpClients.createDefault();

            // 创建httpPost远程连接实例
            HttpPost httpPost = new HttpPost(url);

            // 配置请求参数实例
            // 设置连接主机服务超时时间
            // 设置连接请求超时时间
            // 设置读取数据连接超时时间
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(connectTimeout)
                    .setConnectionRequestTimeout(requestTimeout)
                    .setSocketTimeout(socketTimeout)
                    .build();

            // 为httpPost实例设置配置
            httpPost.setConfig(requestConfig);

            // 设置请求头信息
            if (header != null && !header.isEmpty()) {
                for (Map.Entry<String, String> entry : header.entrySet()) {
                    httpPost.setHeader(entry.getKey(), entry.getValue());
                }
            }

            // 封装post请求参数
            if (null != paramMap && paramMap.size() > 0) {
                List<NameValuePair> nameValuePairs = new ArrayList<>();

                // 通过map集成entrySet方法获取entity
                Set<Entry<String, Object>> entrySet = paramMap.entrySet();

                // 循环遍历，获取迭代器
                for (Entry<String, Object> mapEntry : entrySet) {
                    nameValuePairs.add(new BasicNameValuePair(mapEntry.getKey(), mapEntry.getValue().toString()));
                }

                // 为httpPost设置封装好的请求参数
                //httpPost.setEntity(new UrlEncodedFormEntity(nameValuePairs, "UTF-8"));
                String tt = GsonUtil.toJson(paramMap);
                httpPost.setEntity(new StringEntity(GsonUtil.toJson(paramMap), ContentType.create("application/json", Consts.UTF_8)));
            }


            // httpClient对象执行post请求,并返回响应参数对象
            httpResponse = httpClient.execute(httpPost);

            // 从响应对象中获取响应内容
            HttpEntity entity = httpResponse.getEntity();

            result = EntityUtils.toString(entity);
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        } finally {
            // 关闭资源
            if (null != httpResponse) {
                try {
                    httpResponse.close();
                } catch (Exception e) {
                    throw new Exception(e.getMessage());
                }
            }
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (Exception e) {
                    throw new Exception(e.getMessage());
                }
            }
        }
        return result;
    }
}
