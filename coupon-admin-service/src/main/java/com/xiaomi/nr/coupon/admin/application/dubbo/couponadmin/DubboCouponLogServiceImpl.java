package com.xiaomi.nr.coupon.admin.application.dubbo.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.GoodsRuleVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.PromotionRuleVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.request.CouponLogListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.request.LogDetailRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.CouponBriefVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.CouponLogListResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.LogDetailResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponLogService;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponConfigConvert;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponLogConvert;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponoptlog.CouponLogDomainService;
import com.xiaomi.nr.coupon.admin.domain.coupon.goods.GoodsService;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponLogRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.optlog.po.CouponLogPO;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 优惠券日志
 * @Date: 2022.02.28 10:37
 */
@Slf4j
@Component
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
public class DubboCouponLogServiceImpl implements DubboCouponLogService {

    @Autowired
    private CouponLogRepository couponLogRepository;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private CouponLogDomainService couponLogDomainService;

    @Autowired
    private CouponLogConvert couponLogConvert;

    @Autowired
    private CouponConfigConvert couponConfigConvert;

    @Autowired
    private GoodsService goodsService;

    /**
     * 查询修改列表
     *
     * @param request
     * @return
     */
    @Override
    public Result<CouponLogListResponse> queryLogList(CouponLogListRequest request) {
        try {
            log.info("CouponLogService.queryLogList begin request:{}", request);
            CouponLogListResponse response = new CouponLogListResponse();
            int pageNo = request.getPageNo();
            int pageSize = request.getPageSize();
            Long couponId = request.getConfigId();
            //券基本信息
            CouponConfigPO couponConfigPO = couponConfigRepository.searchCouponById(couponId);
            GoodsRuleVO goodsRuleVO = couponConfigConvert.convertGoodsRuleVO(couponConfigPO);
            PromotionRuleVO promotionRuleVO = couponConfigConvert.convertPromotionRuleVO(couponConfigPO);

            CouponBriefVO couponBriefVO = couponLogConvert.convertToBriefVO(couponConfigPO);
            // 只有3C场景才需要计算成本
            if (BizPlatformEnum.RETAIL.getCode().equals(couponConfigPO.getBizPlatform())) {
                couponBriefVO.setCost(couponConfigConvert.estimatedCost(couponConfigPO.getApplyCount(), goodsService.searchGoodsDetailInfo(goodsRuleVO, couponConfigPO.getPromotionType(), BizPlatformEnum.RETAIL.getCode()),promotionRuleVO));
            }

            response.setCouponBriefVO(couponBriefVO);
            ////日志列表
            int totalCount = couponLogRepository.getTotalByCouponId(couponId);
            List<CouponLogPO> couponLogPOList = couponLogRepository.getLogPageByCouponId(couponId, (pageNo - 1) * pageSize, pageSize);
            response.setPageNo(pageNo);
            response.setPageSize(pageSize);
            response.setTotalCount(totalCount);
            response.setTotalPage(totalCount / pageSize + (totalCount % pageSize == 0 ? 0 : 1));
            response.setCouponLogListVOList(couponLogConvert.convertToVO(couponLogPOList));
            return Result.success(response);
        } catch (Exception e) {
            log.error("CouponLogService.queryLogList error request:{} e", request, e);
            return Result.fromException(e);
        }
    }


    /**
     * 查询修改详情
     *
     * @param request
     * @return
     */
    @Override
    public Result<LogDetailResponse> queryLogDetail(LogDetailRequest request) {
        try {
            log.info("CouponLogService.queryLogDetail begin request:{}", request);
            LogDetailResponse response = new LogDetailResponse(couponLogDomainService.queryLogDetail(request.getLogId()));
            return Result.success(response);
        } catch (Exception e) {
            log.error("CouponLogService.queryLogDetail error request:{}", request, e);
            return Result.fromException(e);
        }
    }




}
