package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.posthandler;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.notify.zk.ZkNotifyService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

@Slf4j
@Component
public class CacheRefreshPostHandler  extends BaseCouponPostHandler{

    @Autowired
    private ZkNotifyService zkNotifyService;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    private final List<Integer> matchBizPlatform= Lists.newArrayList(
            BizPlatformEnum.RETAIL.getCode(),
            BizPlatformEnum.CAR.getCode(),
            BizPlatformEnum.CAR_AFTER_SALE.getCode(),
            BizPlatformEnum.CAR_SHOP.getCode()
    );


    @Override
    public void createPost(CouponCreateEvent event) {
        notifyZk(event.getData().getId());
    }

    @Override
    public void updatePost(CouponUpdateEvent event) {
        notifyZk(event.getData().getId());
    }

    @Override
    public void updateStatusPost(CouponUpdateStatusEvent event) {
        notifyZk(event.getData().getId());
    }

    @Override
    public int order() {
        return 5;
    }

    @Override
    public Boolean bizPlatformMatch(Integer bizPlatform) {
        return matchBizPlatform.contains(bizPlatform);
    }

    private void notifyZk(long id) {
        Timestamp version = couponConfigRepository.searchUpdateTimeById(id);
        log.info("CacheRefreshPostHandler createPost configId:{},version:{}", id, version);
        zkNotifyService.updateConfigZkVersion(String.valueOf(version), id);
    }
}
