package com.xiaomi.nr.coupon.admin.domain.ecard.impl;

import com.xiaomi.nr.coupon.admin.api.dto.autotest.CleanEcardDataVO;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.UserEcardVO;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.CleanEcardDataRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.UserEcardListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.response.UserEcardListResponse;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.EcardDto;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.EcardLogDto;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.GetEcardInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.GetEcardLogRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.ListEcardDescResponse;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.ListEcardStatResponse;
import com.xiaomi.nr.coupon.admin.domain.ecard.EcardService;
import com.xiaomi.nr.coupon.admin.domain.ecard.convert.EcardConvert;
import com.xiaomi.nr.coupon.admin.enums.ecard.EcardStatEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrInfo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecard.EcardLogMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecard.EcardLogSearchParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecard.EcardMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecardconfig.EcardConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecardconfig.EcardLogConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecardconfig.po.EcardLogPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecardconfig.po.EcardPo;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class EcardServiceImpl implements EcardService {

    /**
     * ecard配置库crud mapper
     */
    @Autowired
    private EcardConfigMapper ecardConfigMapper;

    /**
     * ecard用户库 crud mapper
     */
    @Autowired
    private EcardMapper ecardMapper;

    /**
     * ecard_log配置库 crud mapper
     */
    @Autowired
    private EcardLogConfigMapper ecardLogConfigMapper;

    /**
     * ecard_log用户库 crud mapper
     */
    @Autowired
    private EcardLogMapper ecardLogMapper;

    @Autowired
    private EcardConvert ecardConvert;

    /**
     * 根据card_id list 查询券状态信息
     *
     * @param cardIdList 礼品卡ID列表
     * @return List<>    礼品卡状态信息返回列表
     */
    @Override
    public List<ListEcardStatResponse> listEcardStat(List<Long> cardIdList) throws BizError {
        List<EcardPo> ecardResultList = new ArrayList<>();

        // 1.先查配置库，查出cardIdList对应的所有的礼品卡信息
        List<EcardPo> ecardConfigPoList = queryConfigByCardId(cardIdList);
        if (CollectionUtils.isEmpty(ecardConfigPoList)) {
            throw ErrInfo.ERR_ECARD_LIST_FAIL;
        }

        // 用于用户库查询礼品卡列表
        List<Long> listCardId = new ArrayList<>();

        // 2.过滤已经绑定user_id的礼品卡
        for (EcardPo ecardPo : ecardConfigPoList) {
            if (ecardPo.getUserId() != null && ecardPo.getUserId() > 0) {
                listCardId.add(ecardPo.getCardId());
            } else {
                ecardResultList.add(ecardPo);
            }
        }

        // 3.根据user_id、card_id查询用户库的礼品卡信息
        List<EcardPo> ecardPoList = queryByUserCardId(listCardId);
        if (CollectionUtils.isEmpty(ecardPoList)) {
            ecardPoList = new ArrayList<>();
        }

        // 4.合并结果，以用户库数据为准
        ecardResultList.addAll(ecardPoList);
        ecardResultList = ecardSort(ecardResultList);

        return convert2ListEcardStatResponse(ecardResultList);
    }

    /**
     * 根据card_id list 查询礼品卡信息
     *
     * @param cardIdList 礼品卡ID列表
     * @return List<>    礼品卡信息返回列表
     */
    @Override
    public List<ListEcardDescResponse> listEcardDesc(List<Long> cardIdList) throws BizError {

        // 1.根据card_id查询出配置库所有的礼品卡信息，和操作日志的信息
        List<EcardPo> ecardPoConfigList = queryConfigByCardId(cardIdList);
        if (CollectionUtils.isEmpty(ecardPoConfigList)) {
            throw ErrInfo.ERR_ECARD_LIST_FAIL;
        }

        List<Long> listCardId = new ArrayList<>();
        List<Long> listEliminateCardId = new ArrayList<>();
        List<EcardPo> ecardPoResultList = new ArrayList<>();
        Map<Long, Long> mapBindTime = new HashMap<>();

        // 2.过滤已经绑定user_id的礼品卡
        for (EcardPo ecardPo : ecardPoConfigList) {
            if (ecardPo.getUserId() != null && ecardPo.getUserId() > 0) {
                listCardId.add(ecardPo.getCardId());
                if (ecardPo.getBindTime() > 0L) {
                    mapBindTime.put(ecardPo.getCardId(), ecardPo.getBindTime());
                }
            } else {
                listEliminateCardId.add(ecardPo.getCardId());
                ecardPoResultList.add(ecardPo);
            }
        }

        // 3.根据user_id、card_id查询用户库的礼品卡和操作日志信息
        List<EcardPo> ecardPoList = queryByUserCardId(listCardId);
        if (CollectionUtils.isEmpty(ecardPoList)) {
            ecardPoList = new ArrayList<>();
        }

        // 因为用户库里面小米游戏礼品卡信息的bind_time为0，所以用配置库里面的bind_time
        if (!CollectionUtils.isEmpty(ecardPoList)) {
            for (EcardPo ecardPo : ecardPoList) {
                if (mapBindTime.containsKey(ecardPo.getCardId())) {
                    ecardPo.setBindTime(mapBindTime.get(ecardPo.getCardId()));
                }
            }
        }


        List<EcardLogPo> ecardLogResultPoList = queryLogByCard(listCardId);
        if (CollectionUtils.isEmpty(ecardLogResultPoList)) {
            ecardLogResultPoList = new ArrayList<>();
        }

        // 未绑定用户信息的查询配置库的日志信息
        List<EcardLogPo> ecardLogConfigPoList = queryLogConfigByCard(listEliminateCardId);
        if (CollectionUtils.isEmpty(ecardLogConfigPoList)) {
            ecardLogConfigPoList = new ArrayList<>();
        }

        // 4.合并礼品卡信息列表结果，以用户库为准
        ecardPoResultList.addAll(ecardPoList);
        if (!CollectionUtils.isEmpty(ecardPoList)) {
            ecardLogResultPoList.addAll(ecardLogConfigPoList);
        }
        return convert2ListEcardDescResponse(ecardPoResultList, ecardLogResultPoList);
    }

    @Override
    public void checkUpdateDelayEcard(EcardPo ecardPo, Long delayTime) throws BizError {

        if (Objects.isNull(delayTime)) {
            throw ExceptionHelper.create(ErrCode.ECARD, "用户礼品卡延期时间为空,请验证");
        }

        if (delayTime <= Long.parseLong(ecardPo.getEndTime().trim())) {
            throw ExceptionHelper.create(ErrCode.ECARD, "用户礼品卡延期时间小于等于当前生效的结束时间,请验证");
        }

        if (Objects.equals(EcardStatEnum.BindCancel.getValue(), ecardPo.getStat())) {
            throw ExceptionHelper.create(ErrCode.ECARD, "用户礼品卡为绑定作废状态,不可更新或延期,请验证");
        }

        if (Objects.equals(EcardStatEnum.CreateCancel.getValue(), ecardPo.getStat())) {
            throw ExceptionHelper.create(ErrCode.ECARD, "用户礼品卡为开卡作废状态,不可更新或延期,请验证");
        }

        if (Objects.equals(EcardStatEnum.ActiveCancel.getValue(), ecardPo.getStat())) {
            throw ExceptionHelper.create(ErrCode.ECARD, "用户礼品卡为激活作废状态,不可更新或延期,请验证");
        }

        if (Objects.equals(ecardPo.getEndTime(), ecardPo.getStartTime())) {
            throw ExceptionHelper.create(ErrCode.ECARD, "用户礼品卡为即发即用类型,不可更新或延期,请验证");
        }
    }

    /**
     * 获取ecard日志列表
     *
     * @param request 获取ecard日志的请求参数
     * @return ecard日志列表
     */
    @Override
    public List<EcardLogDto> getEcardLog(GetEcardLogRequest request) throws BizError {
        // 1、入参校验
        if (request.getOrderId() == null && request.getRefundNo() == null) {
            log.error("DubboEcardServiceImpl.getEcardLog orderId、refundNo不能都为空");
            throw ExceptionHelper.create(GeneralCodes.ParamError, "订单号和退款单号不能都为空");
        }

        // 2、构建查询入参
        EcardLogSearchParam searchParam = buildEcardLogSearchParam(request);

        // 3、查询ecard日志
        List<EcardLogPo> ecardLogPos = ecardLogMapper.searchByParam(searchParam);

        // 4、convert
        List<EcardLogDto> ecardLogDtos = ecardConvert.buildEcardLogDto(ecardLogPos);

        return ecardLogDtos;
    }

    /**
     * 获取ecard信息
     *
     * @param request 包含获取ecard信息所需参数的请求对象
     * @return 包含ecard信息的列表
     * @throws BizError 业务异常，当获取ecard信息失败时抛出
     */
    @Override
    public List<EcardDto> getEcardInfo(GetEcardInfoRequest request) throws BizError {
        // 查询ecard信息
        List<EcardPo> ecardPoList = ecardMapper.listQueryByCardIds(request.getCardIdList());

        // convert
        List<EcardDto> ecardDtoList = ecardConvert.buildEcardDto(ecardPoList);

        return ecardDtoList;
    }


    /**
     * 构建EcardLogSearchParam对象
     *
     * @param request 包含查询条件的请求对象
     * @return 构建的EcardLogSearchParam对象
     */
    private EcardLogSearchParam buildEcardLogSearchParam(GetEcardLogRequest request) {
        EcardLogSearchParam searchParam = new EcardLogSearchParam();
        searchParam.setUserId(request.getUserId());
        searchParam.setLogType(request.getLogType());
        searchParam.setOrderId(request.getOrderId());
        searchParam.setRefundNo(request.getRefundNo());

        return searchParam;
    }

    @Override
    public UserEcardListResponse selectUserEcard(UserEcardListRequest request){
        UserEcardListResponse response =new UserEcardListResponse();
        List<EcardPo> list = ecardMapper.selectUserEcard(request.getUserId(),request.getCardId(),request.getTypeId());
        response.setUserEcardVOList(list.stream().map(x->{
            UserEcardVO userEcardVO =new UserEcardVO();
            BeanMapper.copy(x,userEcardVO);
            return userEcardVO;
        }).collect(Collectors.toList()));
        return response;
    }

    @Override
    public void cleanEcardData(CleanEcardDataRequest request) {
        for (CleanEcardDataVO cleanEcardDataVO:request.getCleanEcardDataVOs()) {
            ecardMapper.deleteEcard(cleanEcardDataVO.getUserId(),cleanEcardDataVO.getCardId());
            ecardConfigMapper.deleteEcard(cleanEcardDataVO.getUserId(),cleanEcardDataVO.getCardId());
            ecardLogMapper.deleteEcardLog(cleanEcardDataVO.getUserId(),cleanEcardDataVO.getCardId());
            ecardLogConfigMapper.deleteEcardLog(cleanEcardDataVO.getUserId(),cleanEcardDataVO.getCardId());
        }
    }

    /**
     * ecardPoList to EcardStatResponseList
     *
     * @param ecardPoList 礼品卡初始数据列表
     * @return List<>     礼品卡状态信息返回列表
     */
    private List<ListEcardStatResponse> convert2ListEcardStatResponse(List<EcardPo> ecardPoList) {
        if (CollectionUtils.isEmpty(ecardPoList)) {
            return Collections.emptyList();
        }
        return ecardPoList.stream().map(ecardPo -> ecardConvert.convert2ListEcardStatResponse(ecardPo)).
                collect(Collectors.toList());
    }

    /**
     * 构建礼品卡信息返回列表
     *
     * @param ecardLogPoList 礼品卡日志信息初始列表
     * @param ecardPoList    礼品卡信息初始列表
     * @return List<>        礼品卡信息返回值列表
     */
    private List<ListEcardDescResponse> convert2ListEcardDescResponse(List<EcardPo> ecardPoList, List<EcardLogPo> ecardLogPoList) {

        if (CollectionUtils.isEmpty(ecardPoList)) {
            return Collections.emptyList();
        }

        // 对结果列表排序并匹配每张礼品卡对应的日志信息
        ecardPoList = ecardSort(ecardPoList);
        List<ListEcardDescResponse> listEcardResponses = new LinkedList<>();
        for (EcardPo ecardPo : ecardPoList) {
            List<EcardLogPo> ecardLogListTmp = new LinkedList<>();
            for (EcardLogPo ecardLogPo : ecardLogPoList) {
                if (ecardPo.getCardId().equals(ecardLogPo.getCardId())) {
                    ecardLogListTmp.add(ecardLogPo);
                }
            }

            // 日志列表排序
            if (!CollectionUtils.isEmpty(ecardLogListTmp)) {
                ecardLogListTmp = ecardLogSort(ecardLogListTmp);
            }

            ListEcardDescResponse listEcardResponse = ecardConvert.convert2ListEcardDescResponse(ecardPo, ecardLogListTmp);
            listEcardResponses.add(listEcardResponse);
        }
        return listEcardResponses;
    }

    /**
     * 根据礼品卡ID查询礼品卡信息(配置库)
     *
     * @param cardIdList 礼品卡ID列表
     * @return List<>    礼品卡原始数据列表
     */
    private List<EcardPo> queryConfigByCardId(List<Long> cardIdList) {
        if (CollectionUtils.isEmpty(cardIdList)) {
            return Collections.emptyList();
        }

        List<EcardPo> ecardPoList = ecardConfigMapper.queryByCardIds(cardIdList);
        if (CollectionUtils.isEmpty(ecardPoList)) {
            log.info("coupon.admin.EcardServiceImpl,queryConfigByCardId data is null, cardIdList={}", cardIdList);
        }
        return ecardPoList;
    }

    /**
     * 根据礼品卡ID查询礼品卡信息(用户库)
     *
     * @param cardIdList 礼品卡ID列表
     * @return List<>    礼品卡原始数据列表
     */
    private List<EcardPo> queryByUserCardId(List<Long> cardIdList) {
        if (CollectionUtils.isEmpty(cardIdList)) {
            return Collections.emptyList();
        }

        List<EcardPo> ecardPoList = ecardMapper.listQueryByCardIds(cardIdList);
        if (CollectionUtils.isEmpty(ecardPoList)) {
            log.info("coupon.admin.EcardServiceImpl,queryByUserCardId data is null, cardIdList={}", cardIdList);
        }
        return ecardPoList;
    }

    /**
     * 根据礼品卡ID获取礼品卡的操作日志(配置库)
     *
     * @param cardIdList 礼品卡ID列表
     * @return List<>    礼品卡操作日志列表
     */
    private List<EcardLogPo> queryLogConfigByCard(List<Long> cardIdList) {
        if (CollectionUtils.isEmpty(cardIdList)) {
            return Collections.emptyList();
        }

        List<EcardLogPo> ecardLogPoList = ecardLogConfigMapper.queryByCardId(cardIdList);
        if (CollectionUtils.isEmpty(ecardLogPoList)) {
            log.info("coupon.admin.EcardServiceImpl,queryLogConfigByCard data is null,cardIdList={}", cardIdList);
        }
        return ecardLogPoList;
    }

    /**
     * 根据礼品卡ID获取礼品卡的操作日志(用户库)
     *
     * @param cardIdList 礼品卡ID列表
     * @return List<>    礼品卡操作日志列表
     */
    private List<EcardLogPo> queryLogByCard(List<Long> cardIdList) {
        if (CollectionUtils.isEmpty(cardIdList)) {
            return Collections.emptyList();
        }

        List<EcardLogPo> ecardLogPoList = ecardLogMapper.queryByCardId(cardIdList);
        if (CollectionUtils.isEmpty(ecardLogPoList)) {
            log.info("coupon.admin.EcardServiceImpl,queryLogByCard data is null,cardIdList={}", cardIdList);
        }
        return ecardLogPoList;
    }

    /**
     * 礼品卡信息列表按add_time倒序排列
     *
     * @param list 礼品卡信息列表
     * @return List<> 排序后的礼品卡信息列表
     */
    private List<EcardPo> ecardSort(List<EcardPo> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.info("coupon.admin.EcardServiceImpl,ecardSort error because ecardPoList={}", list);
            return Collections.emptyList();
        }
        return list.stream().sorted(Comparator.comparing(EcardPo::getAddTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 礼品卡操作日志列表按add_time正序排列
     *
     * @param list 操作日志信息列表
     * @return List<> 排序后的操作日志信息列表
     */
    private List<EcardLogPo> ecardLogSort(List<EcardLogPo> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.info("coupon.admin.EcardServiceImpl,ecardLogSort error because ecardLogPoList={}", list);
            return Collections.emptyList();
        }
        return list.stream().sorted(Comparator.comparing(EcardLogPo::getAddTime)).collect(Collectors.toList());
    }

}



