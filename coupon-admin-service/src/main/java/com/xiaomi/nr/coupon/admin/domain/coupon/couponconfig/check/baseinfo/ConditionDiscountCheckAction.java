package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.BottomTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;

/**
 * <AUTHOR>
 * @date 2023/12/25 10:52
 */
public abstract class ConditionDiscountCheckAction extends CouponConfigBaseCheck {

    /**
     * 券优惠类型
     */
    @Override
    public PromotionTypeEnum getPromotionType() {
        return PromotionTypeEnum.ConditionDiscount;
    }

    /**
     * 券创建基础校验
     */
    @Override
    public void createCommonCheck(CouponBaseInfo info) throws BizError {
        // 券创建、修改公共校验
        commonCheck(info);

        // 商品券类型校验
        couponTypeCheck(info);

        if (info.getId() > 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券id必须为空");
        }
        if (BottomTypeEnum.OverYuan.getValue() == info.getBottomType() || BottomTypeEnum.PerOverYuan.getValue() == info.getBottomType()) {
            if (info.getBottomPrice() <= 0) {
                throw ExceptionHelper.create(ErrCode.COUPON, "门槛值满元不能为空");
            }
        } else if (BottomTypeEnum.OverCount.getValue() == info.getBottomType() || BottomTypeEnum.PerOverCount.getValue() == info.getBottomType()) {
            if (info.getBottomCount() <= 0) {
                throw ExceptionHelper.create(ErrCode.COUPON, "门槛值满件不能为空");
            }
        } else {
            throw ExceptionHelper.create(ErrCode.COUPON, "不支持的门槛类型");
        }
        if (info.getMaxReduce() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "最大减免金额必须大于0");
        }
    }
}
