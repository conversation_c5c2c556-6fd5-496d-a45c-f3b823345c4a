package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.goods;


import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoDTO;
import com.xiaomi.goods.gms.dto.batch.BatchedInfoDto;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.UseChannelVO;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.BizSubTypeEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponGoodsInfo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponScopeTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsItemTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.nacosconfig.NacosSwitchConfig;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.goods.GisProxyService;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.goods.GmsProxyService;

import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 券商品基础处理类
 */
//@RefreshScope
//@Configuration
public abstract class CouponGoodsBaseHandler {

    @Autowired
    private GmsProxyService gmsProxyService;

    @Autowired
    private GisProxyService gisProxyService;

    @Autowired
    private NacosSwitchConfig nacosSwitchConfig;


    /**
     * 处理商品
     */
    public abstract void handleGoods(CouponConfigItem couponConfigItem) throws Exception;


    /**
     * 商品上传方式
     */
    public abstract CouponScopeTypeEnum getScopeType();


    protected void checkNyuan(CouponConfigItem couponConfigItem) throws Exception {
        CouponBaseInfo couponBaseInfo = couponConfigItem.getCouponBaseInfo();
        if (PromotionTypeEnum.NyuanBuy.getValue() != couponBaseInfo.getPromotionType()) {
            return;
        }
        CouponGoodsInfo couponGoodsInfo = couponConfigItem.getCouponGoodsInfo();
        Map<String, List<Long>> goodsInclude = couponGoodsInfo.getGoodsInclude();
        Set<Long> pidSet = new HashSet<>();
        List<Map<Long, Long>> lowPriceList = new LinkedList<>();
        if (goodsInclude.containsKey(GoodsLevelEnum.Sku.getValue()) && CollectionUtils.isNotEmpty(goodsInclude.get(GoodsLevelEnum.Sku.getValue()))) {
            //因为前面的流程是允许配置普通3c商品和泛全商品，但渠道仅为授权店时，才允许配置泛全商品，其他的渠道只能配置普通3c商品，所以这里先取这两种类型的商品，下面再进行详细判断
            List<Integer> bizSubTypeList = Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode(), BizSubTypeEnum.CARRIER_PAN_COMPLETE.getCode());
            List<SkuInfoDto> skuInfoDtoList = gmsProxyService.queryListBySkuIds(goodsInclude.get(GoodsLevelEnum.Sku.getValue()), false, true, false, bizSubTypeList);
            if (CollectionUtils.isEmpty(skuInfoDtoList)) {
                throw ExceptionHelper.create(ErrCode.COUPON, "商品信息未找到");
            }

            //渠道仅配置为授权时，优惠券允许配置泛全商品
            Map<Integer, UseChannelVO> useChannel = couponBaseInfo.getUseChannel();
            boolean onlyAuthStore = Objects.nonNull(useChannel) && useChannel.size() == 1 && useChannel.containsKey(UseChannelsEnum.AUTHORIZED_STORE.getValue());
            List<Long> notAllowSku = skuInfoDtoList.stream().filter(x -> !onlyAuthStore && !BizSubTypeEnum.ORDINARY_3C.getCode().equals(x.getBizSubType())).map(SkuInfoDto::getSku).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notAllowSku)) {
                throw ExceptionHelper.create(ErrCode.COUPON, "非授权店渠道只允许配置普通3C商品");
            }

            skuInfoDtoList.forEach(x -> {
                pidSet.add(x.getProductId());
                if (x.getMarketPrice() < couponBaseInfo.getPromotionValue()) {
                    Map<Long, Long> sku = new HashMap<>();
                    sku.put(x.getSku(), x.getMarketPrice());
                    lowPriceList.add(sku);
                }
            });
        }

        if (goodsInclude.containsKey(GoodsLevelEnum.Package.getValue()) && CollectionUtils.isNotEmpty(goodsInclude.get(GoodsLevelEnum.Package.getValue()))) {
            List<BatchedInfoDto> batchedInfoDtoList = gmsProxyService.queryListByPackageIds(goodsInclude.get(GoodsLevelEnum.Package.getValue()), false);
            if (CollectionUtils.isEmpty(batchedInfoDtoList)) {
                throw ExceptionHelper.create(ErrCode.COUPON, "套装信息未找到");
            }

            batchedInfoDtoList.forEach(x -> {
                if (x.getMarketPriceMax() < couponBaseInfo.getPromotionValue()) {
                    Map<Long, Long> batch = new HashMap<>();
                    batch.put(x.getBatchedId(), x.getMarketPriceMax());
                    lowPriceList.add(batch);
                }
            });
        }

        // check ssu valid
        if (goodsInclude.containsKey(GoodsLevelEnum.Ssu.getValue()) && CollectionUtils.isNotEmpty(goodsInclude.get(GoodsLevelEnum.Ssu.getValue()))) {
            List<Long> ssuIds = goodsInclude.get(GoodsLevelEnum.Ssu.getValue());
            Map<Long, GoodsMultiInfoDTO> goodsMultiInfoDTOMap = gisProxyService.queryGoodsInfoBySsuIds(ssuIds, false);
            if (MapUtils.isEmpty(goodsMultiInfoDTOMap)) {
                throw ExceptionHelper.create(ErrCode.COUPON, "商品信息未找到");
            }

            goodsMultiInfoDTOMap.values().forEach(x -> {
                pidSet.add(x.getProductId());
                if (x.getMarketPrice() < couponBaseInfo.getPromotionValue()) {
                    Map<Long, Long> ssu = new HashMap<>();
                    ssu.put(x.getGoodsId(), x.getMarketPrice());
                    lowPriceList.add(ssu);
                }
            });
        }

        if(goodsInclude.containsKey(GoodsLevelEnum.Suit.getValue()) && CollectionUtils.isNotEmpty(goodsInclude.get(GoodsLevelEnum.Suit.getValue()))) {
            List<GoodsMultiInfoDTO> goodsMultiInfoDTOList = gisProxyService.queryGoodsMultiInfoByGoodsIds(goodsInclude.get(GoodsLevelEnum.Suit.getValue()), GoodsItemTypeEnum.SUIT);
            if (CollectionUtils.isEmpty(goodsMultiInfoDTOList)) {
                throw ExceptionHelper.create(ErrCode.COUPON, "套装信息未找到");
            }

            goodsMultiInfoDTOList.forEach(x -> {
                if (x.getMarketPrice() < couponBaseInfo.getPromotionValue()) {
                    Map<Long, Long> suit = new HashMap<>();
                    suit.put(x.getGoodsId(), x.getMarketPrice());
                    lowPriceList.add(suit);
                }
            });
        }



        if (pidSet.size() > 1) {
            throw ExceptionHelper.create(ErrCode.COUPON, "N元券只能设置在同一个pid维度下的商品，请检查后提交！");
        }

        if (CollectionUtils.isNotEmpty(lowPriceList)) {
            BigDecimal promotionValue = BigDecimal.valueOf(couponBaseInfo.getPromotionValue()).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
            StringBuilder stringBuilder = new StringBuilder();
            lowPriceList.forEach(x -> x.forEach((k, v) -> stringBuilder.append(k).append(",¥").append(BigDecimal.valueOf(v).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP)).append("；")));
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券门槛（" + promotionValue + "）低于商品最高价（" + stringBuilder.toString() + "），无法使用N元券，请检查后提交！");
        }
    }

    // 气门芯帽新年OK礼
    protected void checkGift(CouponConfigItem couponConfigItem) throws Exception {
        CouponBaseInfo couponBaseInfo = couponConfigItem.getCouponBaseInfo();
        if (PromotionTypeEnum.GIFT.getValue() != couponBaseInfo.getPromotionType()) {
            return;
        }
        // 业务领域必须是车商城，否则报错
        if (!Objects.equals(BizPlatformEnum.CAR_SHOP.getCode(), couponBaseInfo.getBizPlatform())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "业务领域必须是车商城");
        }
        //nacosSwitchConfig.
        List<String> stringList = nacosSwitchConfig.getCarShopGiftSsuWhitelist();
        if(CollectionUtils.isEmpty(stringList)){
            throw ExceptionHelper.create(ErrCode.COUPON, "商品白名单为空");
        }
        List<Long> ssuList = stringList.stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());
        // 2.商品白名单
        CouponGoodsInfo couponGoodsInfo = couponConfigItem.getCouponGoodsInfo();
        Map<String, List<Long>> goodsInclude = couponGoodsInfo.getGoodsInclude();
        Set<Long> pidSet = new HashSet<>();
        // check ssu valid
        if (goodsInclude.containsKey(GoodsLevelEnum.Ssu.getValue()) && CollectionUtils.isNotEmpty(goodsInclude.get(GoodsLevelEnum.Ssu.getValue()))) {
            List<Long> ssuIds = goodsInclude.get(GoodsLevelEnum.Ssu.getValue());
            // 判断商品是否在白名单中
            if(!ssuList.containsAll(ssuIds)){
                throw ExceptionHelper.create(ErrCode.COUPON, "有商品不在“礼品券可用商品白名单”内，请检查适用商品，或联系产品同学添加白名单");
            }

            Map<Long, GoodsMultiInfoDTO> goodsMultiInfoDTOMap = gisProxyService.queryGoodsInfoBySsuIds(ssuIds, false);
            if (MapUtils.isEmpty(goodsMultiInfoDTOMap)) {
                throw ExceptionHelper.create(ErrCode.COUPON, "商品信息未找到");
            }
        }
    }
}
