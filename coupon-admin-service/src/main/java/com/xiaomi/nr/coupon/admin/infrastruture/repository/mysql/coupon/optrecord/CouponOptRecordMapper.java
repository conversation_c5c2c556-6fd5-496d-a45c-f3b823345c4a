package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.optrecord;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.optrecord.po.CouponOptRecordPO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

@Component
public interface CouponOptRecordMapper {

    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert(" insert into nr_coupon_opt_record" +
            "(opt_type, opt_info)" +
            " values (#{optType}, #{optInfo})")
    void insert(CouponOptRecordPO record);

    @Select("select * from nr_coupon_opt_record where id = #{id}")
    CouponOptRecordPO selectById(Long id);


    @Select("<script>select * from nr_coupon_opt_record where (opt_status=0 and create_time <![CDATA[<=]]> #{createTime}) or opt_status = 2 order by id asc</script>")
    List<CouponOptRecordPO> selectByStatus(@Param("createTime") Timestamp createTime);


    @Update("update nr_coupon_opt_record set opt_status = #{status} where id = #{id}")
    Integer updateStatusById(@Param("id") Long id,@Param("status") int status);


    @Update("<script>update nr_coupon_opt_record" +
            "<set >" +
            "<if test='optStatus != null' >opt_status = #{optStatus},</if>" +
            "<if test='retryTime != null' >retry_time = #{retryTime},</if>" +
            "</set><where> id=#{id}</where></script>")
    Integer updateById(CouponOptRecordPO record);

}