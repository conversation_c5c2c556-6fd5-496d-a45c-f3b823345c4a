package com.xiaomi.nr.coupon.admin.infrastruture.constant.ecard;

/**
 * 礼品卡常量
 *
 * <AUTHOR>
 */
public class EcardConstant {

    /**
     * 一次最多查询多少张卡
     */
    public static final int GET_ECARD_BY_CARDIDS_MAX_SIZE = 100;

    /**
     * SnLen 规则：[sku]/[9位随机数]
     */
    public static final int SN_LEN = 9;

    /**
     * CardIDLen id 规则：[1/2][4位面额/10][9位随机数]
     */
    public static final int CARDID_LEN = 9;

    /**
     * PasswordLen 规则：20位随机数字
     */
    public static final int PASSWORD_LEN = 20;

    /**
     * 随机数生成数字集
     *
     * @return int[]
     */
    public static int[] randNum() {
        return new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    }


}
