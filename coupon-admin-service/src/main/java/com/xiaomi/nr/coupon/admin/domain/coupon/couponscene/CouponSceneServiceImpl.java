package com.xiaomi.nr.coupon.admin.domain.coupon.couponscene;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.request.CreateOrUpdateSendSceneRequest;
import com.xiaomi.nr.coupon.admin.enums.scene.CreateOrUpdateSendSceneEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneIdGenerationEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 创建或修改优惠券场景
 * @Date: 2022.03.03 18:56
 * <AUTHOR>
 */
@Service
public class CouponSceneServiceImpl implements CouponSceneDomainService {

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    private static String[] SHUFFLE_ARRAY = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R",
            "S", "T", "U", "V", "W", "X", "Y", "Z", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s",
            "t", "u", "v", "w", "x", "y", "z", "1", "2", "3", "4", "5", "6", "7", "8", "9", "0"};

    /**
     * 创建或修改优惠券场景
     *
     * @param request
     * @return
     */
    @Override
    public String createOrUpdateSendScene(CreateOrUpdateSendSceneRequest request) throws Exception {
        CouponScenePO couponScenePO = new CouponScenePO();
        checkoutReq(request);
        if (request.getType() == CreateOrUpdateSendSceneEnum.UPDATE.getCode()) {
            couponScenePO = checkUnEditableParam(request);
        }
        convertToPO(request, couponScenePO);
        if (request.getType() == CreateOrUpdateSendSceneEnum.CREATE.getCode()) {
            couponSceneRepository.insert(couponScenePO);
        } else {
            couponSceneRepository.update(couponScenePO);
        }
        return couponScenePO.getSceneCode();
    }

    private void checkoutReq(CreateOrUpdateSendSceneRequest request) throws Exception {
        // 校验场景名称
        CouponScenePO scenePO = couponSceneRepository.searchSceneByName(request.getName());
        if ((request.getType() == CreateOrUpdateSendSceneEnum.CREATE.getCode() && scenePO != null) ||
                (request.getType() == CreateOrUpdateSendSceneEnum.UPDATE.getCode() &&
                        scenePO != null && !scenePO.getId().equals(request.getSceneId()))) {
            throw ExceptionHelper.create(ErrCode.COUPON, "存在同名场景！");
        }
        // 校验关联一级场景
        Integer relationSceneId = request.getRelationSceneId();
        SceneEnum relationScene = SceneEnum.getByCode(relationSceneId);
        if (relationScene == null) {
            throw ExceptionHelper.create(ErrCode.COUPON, "关联一级场景非法");
        }
        if (request.getBizPlatform() != relationScene.getBizPlatform()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "关联一级场景与业务平台不符");
        }
    }

    private CouponScenePO checkUnEditableParam(CreateOrUpdateSendSceneRequest request) throws Exception {
        if (request.getSceneId() == null) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "场景id不能为空");
        }
        CouponScenePO couponScenePO = couponSceneRepository.searchSceneById(request.getSceneId());
        if (couponScenePO == null) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "无效场景id");
        }
        if (!couponScenePO.getIdGenerationType().equals(request.getIdGenerationType())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "场景编码方式不允许被修改");
        }
        if (!couponScenePO.getSceneCode().equals(request.getSceneCode())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "场景编码不允许被修改");
        }
        if (!couponScenePO.getSendMode().equals(request.getSendMode())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "投放方式不允许被修改");
        }
        return couponScenePO;
    }

    private void convertToPO(CreateOrUpdateSendSceneRequest request, CouponScenePO couponScenePO) throws Exception {
        couponScenePO.setName(request.getName());
        couponScenePO.setSceneDesc(request.getSceneDesc());
        couponScenePO.setApplyMark(request.getApplyMark());
        couponScenePO.setCouponType(StringUtil.join(",", request.getCouponTypeList()));
        couponScenePO.setExtProps(StringUtil.join(",", request.getExtProps()));
        if (request.getType() == CreateOrUpdateSendSceneEnum.CREATE.getCode()) {
            couponScenePO.setIdGenerationType(request.getIdGenerationType());
            couponScenePO.setRelationSceneId(request.getRelationSceneId());
            couponScenePO.setSceneCode(request.getIdGenerationType() == SceneIdGenerationEnum.SYSTEM.getCode() ?
                    generateRandomCode() : request.getSceneCode());
            couponScenePO.setSendMode(request.getSendMode());
            couponScenePO.setAssignMode(StringUtil.join(",", request.getAssignMode()));
            couponScenePO.setStatus(SceneStatusEnum.OFFLINE.getCode());
            couponScenePO.setCreator(request.getCreator());
        } else {
            couponScenePO.setRelationSceneId(request.getRelationSceneId());
            couponScenePO.setAssignMode(StringUtil.join(",", request.getAssignMode()));
            couponScenePO.setUpdateTime(System.currentTimeMillis() / 1000);
            couponScenePO.setModifier(request.getCreator());
        }
        couponScenePO.setBizPlatform(request.getBizPlatform());
    }

    public String generateRandomCode() throws Exception {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        List<String> shuffleArry = new ArrayList<>(SHUFFLE_ARRAY.length);
        Collections.addAll(shuffleArry, SHUFFLE_ARRAY);
        Collections.shuffle(shuffleArry);
        String shuffleStr = org.apache.commons.lang.StringUtils.join(shuffleArry, "");
        byte[] byteArray = md5.digest(shuffleStr.getBytes());
        StringBuffer md5StrBuff = new StringBuffer();
        for (int i = 0; i < byteArray.length; i++) {
            if (Integer.toHexString(0xFF & byteArray[i]).length() == 1) {
                md5StrBuff.append("0").append(Integer.toHexString(0xFF & byteArray[i]));
            } else {
                md5StrBuff.append(Integer.toHexString(0xFF & byteArray[i]));
            }
        }
        return md5StrBuff.toString().toUpperCase();
    }
}
