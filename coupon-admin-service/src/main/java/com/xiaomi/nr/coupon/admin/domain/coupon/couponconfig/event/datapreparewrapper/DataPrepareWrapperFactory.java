package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.datapreparewrapper;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/14
 */
@Component
public class DataPrepareWrapperFactory {

    @Resource
    private RetailDataPrepareWrapper retailDataPrepareWrapper;

    @Resource
    private CarDataPrepareWrapper carDataPrepareWrapper;

    @Resource
    private CarAfterSaleDataPrepareWrapper carAfterSaleDataPrepareWrapper;

    @Resource
    private CarShopDataPrepareWrapper carShopDataPrepareWrapper;

    public BaseDataPrepareWrapper getDataPrepareWrapper(BizPlatformEnum bizPlatform) throws BizError {

        if (Objects.isNull(bizPlatform)) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "业务领域不能为空");
        }

        switch (bizPlatform) {
            case RETAIL:
            case AUTO_TEST:
                return retailDataPrepareWrapper;
            case CAR:
                return carDataPrepareWrapper;
            case CAR_AFTER_SALE:
                return carAfterSaleDataPrepareWrapper;
            case CAR_SHOP:
                return carShopDataPrepareWrapper;
            default:
                throw ExceptionHelper.create(GeneralCodes.ParamError, "暂不支持当前业务领域");
        }
    }
}
