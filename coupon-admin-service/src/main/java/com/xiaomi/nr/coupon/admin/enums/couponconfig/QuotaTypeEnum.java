package com.xiaomi.nr.coupon.admin.enums.couponconfig;

/**
 * 配额类型 枚举
 *
 * <AUTHOR>
 */
public enum QuotaTypeEnum {

    /**
     * 满元
     */
    Money(1,"money", "money", "满元"),

    /**
     * 满件
     */
    Count(2,"count", "count", "满件"),

    /**
     * 满件且满元
     */
    MoneyCount(5,"money_count", "money_count", "满元且满件"),

    /**
     * 每满元
     */
    EveMoney(3,"eve_money", "eve_money", "每满元"),

    /**
     * 每满件
     */
    EveCount(4,"eve_count", "eve_count", "每满件");

    private final int code;
    private final String redisValue;
    private final String mysqlValue;
    private final String name;

    QuotaTypeEnum(int code, String redisValue, String mysqlValue, String name) {
        this.code = code;
        this.redisValue = redisValue;
        this.mysqlValue = mysqlValue;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getRedisValue() {
        return this.redisValue;
    }

    public String getMysqlValue() {
        return this.mysqlValue;
    }

    public String getName() {
        return name;
    }

    public static QuotaTypeEnum findByCode(int code) {
        QuotaTypeEnum[] values = QuotaTypeEnum.values();
        for (QuotaTypeEnum item : values) {
            if (item.getCode() == code) {
                return item;
            }
        }
        return null;
    }

    public static String findRedisValueByMysqlValue(String value) {
        QuotaTypeEnum[] values = QuotaTypeEnum.values();
        for (QuotaTypeEnum item : values) {
            if (item.getMysqlValue().equals(value)) {
                return item.getRedisValue();
            }
        }
        return null;
    }

    public static QuotaTypeEnum findByMysqlValue(String value) {
        QuotaTypeEnum[] values = QuotaTypeEnum.values();
        for (QuotaTypeEnum item : values) {
            if (item.getMysqlValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}

