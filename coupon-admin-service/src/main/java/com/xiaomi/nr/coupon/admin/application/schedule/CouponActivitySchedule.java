package com.xiaomi.nr.coupon.admin.application.schedule;

import com.xiaomi.nr.coupon.admin.application.schedule.constant.ScheduleConstant;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponactivity.model.*;
import com.xiaomi.nr.coupon.admin.enums.couponactivity.EventApiIdTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponmission.MissionMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponmission.po.MissionJoinConfigPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponactivity.CouponActivityRedisDao;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.HttpClientUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.nr.infra.aop.clustertask.ClusterTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 商城领券活动缓存定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CouponActivitySchedule {

    @Value("${miShop.activity.event.api}")
    private String eventApi;

    @Autowired
    private CouponActivityRedisDao couponActivityRedisDao;

    @Autowired
    private MissionMapper missionMapper;

    /**
     * 1分钟更新一次
     */
    @Scheduled(fixedDelay = 1000 * 60 * 1, initialDelay = 1000 * 60 * 1)
    @ClusterTask(value = "", zkLockPath = ScheduleConstant.MI_SHOP_COUPON_ACTIVITY_CACHE_GEN_PATH)
    public void genMiShopCouponActivityCacheScheduleTask(){
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            log.info("genMiShopCouponActivityCacheScheduleTask, start");
            genMiShopCouponActiveCache();
            log.info("genMiShopCouponActivityCacheScheduleTask, execute success, runTime={}ms", TimeUtil.sinceMillis(runStartTime));
        } catch (Exception e) {
            log.error("genMiShopCouponActivityCacheScheduleTask, execute fail, runTime={}ms, {}", TimeUtil.sinceMillis(runStartTime), e);
        }
    }


    /**
     * 取出小米商城领券活动信息并更新新缓存
     *
     * @throws Exception
     */
    private void genMiShopCouponActiveCache() throws Exception {
        log.info("genMiShopCouponActivityCacheScheduleTask, 调用领券活动系统-开始");
        long runStartTime = TimeUtil.getNowUnixMillis();

        //调用b.d的接口获取信息
        String respStr = HttpClientUtil.doGet(eventApi + "/activity/coupon", null, 5000, 10000, 10000);
        if("".equals(respStr)){
            throw new Exception("无法从领券活动接口获取到有效信息");
        }

        log.info("genMiShopCouponActivityCacheScheduleTask, 调用领券活动系统-结束, runTime={}ms, resp={}", TimeUtil.sinceMillis(runStartTime), respStr);

        EventApiResponse resp = GsonUtil.fromJson(respStr, EventApiResponse.class);
        if(resp == null) {
            throw new Exception("无法解析领券活动接口返回的信息");
        }

        if(resp.getCode() != 0) {
            throw new Exception("领券活动接口返回码为非正常值");
        }

        if(resp.getData() == null) {
            throw new Exception("领券活动接口返回的信息异常");
        }

        //组装缓存信息
        String cacheInfo = makeCacheInfo(resp);

        //写入redis缓存
        couponActivityRedisDao.set(cacheInfo);
    }

    /**
     * 组装缓存信息
     * @param resp EventApiResponse
     * @return String
     */
    private String makeCacheInfo(EventApiResponse resp) {
        //获取所有mission_id
        List<Integer> missionIds = new ArrayList<>();
        for(EventApiDataItem item : resp.getData()){
            for(EventApiCouponItem cItem : item.getCoupons()) {
                if (EventApiIdTypeEnum.Mission.getValue().equals(cItem.getType())) {
                    missionIds.add(cItem.getId());
                }
            }
        }

        //排查查询mission_id对应的券配置ID
        Map<Integer, Integer> missionConfigIds = new HashMap<>();
        if(missionIds.size() > 0){
            missionConfigIds = getConfigIds(missionIds);
        }


        List<ActivityCacheItem> data = new ArrayList<>();
        for(EventApiDataItem item : resp.getData()){
            if(item==null || item.getCoupons() == null || item.getActCode() == null || item.getActName() == null || item.getStartTime() == null || item.getEndTime() == null) {
                log.error("genMiShopCouponActivityCacheScheduleTask, 领券活动接口返回的信息里存在null的情况，info={}", resp);
                continue;
            }

            List<ActivityCacheCouponItem> coupons = new ArrayList<>();
            for(EventApiCouponItem cItem : item.getCoupons()) {
                ActivityCacheCouponItem couponInfo = new ActivityCacheCouponItem();
                couponInfo.setId(cItem.getId());
                couponInfo.setName(cItem.getName());
                couponInfo.setSid(cItem.getSid());
                couponInfo.setChannel(cItem.getChannel());
                couponInfo.setClientId(cItem.getClientId());
                couponInfo.setCouponTag(cItem.getCouponTag());
                couponInfo.setActivityTag(cItem.getActivityTag());
                couponInfo.setCrowd(cItem.getDataBlockId());

                if(EventApiIdTypeEnum.Config.getValue().equals(cItem.getType())) {
                    couponInfo.setConfigId(cItem.getId());
                }else if(EventApiIdTypeEnum.Mission.getValue().equals(cItem.getType())) {
                    Integer configId = missionConfigIds.get(cItem.getId());
                    if(configId == null || configId <= 0){
                        log.error("genMiShopCouponActivityCacheScheduleTask, 优惠券发放任务ID匹配不到券配置ID，missionId={}", cItem.getId());
                        continue;
                    }
                    couponInfo.setConfigId(configId);
                }else{
                    log.warn("genMiShopCouponActivityCacheScheduleTask, 超出已知范围的领券活动，missionId={}", cItem.getId());
                    continue;
                }

                coupons.add(couponInfo);
            }

            ActivityCacheItem cacheItem = new ActivityCacheItem();
            cacheItem.setActCode(item.getActCode());
            cacheItem.setActName(item.getActName());
            cacheItem.setStartTime(item.getStartTime());
            cacheItem.setEndTime(item.getEndTime());
            cacheItem.setCoupons(coupons);

            data.add(cacheItem);
        }

        ActivityCacheInfo cache = new ActivityCacheInfo();
        cache.setCode(resp.getCode());
        cache.setMsg(resp.getMsg());
        cache.setData(data);
        cache.setCacheCreateTime(TimeUtil.getNowUnixSecond());

        return GsonUtil.toJson(cache);
    }

    /**
     * 从库里匹配发放任务ID对应的券配置ID
     *
     * @param missionIds List<Integer>
     * @return Map<Integer, Integer>
     */
    private Map<Integer, Integer> getConfigIds(List<Integer> missionIds){
        Map<Integer, Integer> result = new HashMap<>();
        if(missionIds == null || missionIds.isEmpty()) {
            return result;
        }

        List<Long> longMissionIds = new ArrayList<>();
        for(Integer id : missionIds) {
            longMissionIds.add(id.longValue());
        }

        List<MissionJoinConfigPo> list = missionMapper.getMissionByIdList(longMissionIds);
        if(list == null || list.isEmpty()) {
            log.error("genMiShopCouponActivityCacheScheduleTask, 优惠券发放任务ID在DB里未查到，missionIds={}", longMissionIds);
            return result;
        }

        for(MissionJoinConfigPo item : list) {
            result.put(item.getMissionId().intValue(), item.getCouponConfigId().intValue());
        }

        return result;
    }
}
