package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.bdmall.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * @description:
 * @author: heji<PERSON><PERSON>
 * @Date 2022/3/30 12:59 下午
 * @Version: 1.0
 **/
@Data
public class MallGoodsItemPO {

    @SerializedName("goods_id")
    private Long goodsId;

    @SerializedName("commodity_id")
    private Long commodityId;

    @SerializedName("product_id")
    private Long productId;

    private String type;
}
