package com.xiaomi.nr.coupon.admin.domain.coupon.fillcoupontask;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.FillCouponDetailVO;
import com.xiaomi.nr.coupon.admin.enums.task.DownloadTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.coupon.ZkPathConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponTaskRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.hdfs.HdfsHelper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.FillCouponTaskPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.Param;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FSDataInputStream;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URI;
import java.util.*;

@Component
@Slf4j
public class DownLoadHelper {


    @Autowired
    private HdfsHelper hdfsHelper;

    @Autowired
    private CouponTaskRepository fillCouponTaskRepository;

    @Autowired
    private FillCouponConvert fillCouponConvert;

    /**
     * 下载用户集
     *
     * @param hdfsPath  hdfs地址
     * @return List<Long> 用户id
     * @throws IOException io异常
     */
    public List<String> downloadEntityList(String hdfsPath) throws IOException, BizError {

        URI fileUri = hdfsHelper.getFileUri(hdfsPath);

        FileStatus[] files = null;
        //获取hdfs文件目录
        try {
            files = hdfsHelper.listStatus(fileUri, hdfsPath);

        }catch (Exception e){
            throw ExceptionHelper.create(ErrCode.COUPON, "用户集已被清理或不存在");
        }

        if(files.length > CommonConstant.USER_LIST_LIMIT){
            throw ExceptionHelper.create(ErrCode.COUPON, "灌券用户集大于1W, 请联系RD同学下载");
        }
        Configuration config = new Configuration();
        FileSystem fs = FileSystem.get(fileUri, config);
        if(fs == null){
            throw ExceptionHelper.create(ErrCode.COUPON, "用户集已被清理或不存在");
        }

        FSDataInputStream in = null;
        List<String> list = new LinkedList<>();

        try {

            for (FileStatus file : files) {
                //读取文件
                in = fs.open(file.getPath());
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(in));
                String line = null;
                while((line = bufferedReader.readLine()) != null){
                    if (StringUtils.isNotBlank(line)) {
                        list.add(line);
                    }
                }
            }
        } catch (Exception e) {
            log.error("downloadFillCouponDetail error. path:{}, e",hdfsPath, e);
            return null;
        } finally {
            if(in != null){
                in.close();
            }
        }

        return list;
    }




    /**
     * 下载灌券信息
     *
     * @param taskId            任务id
     * @param downloadTypeEnum 下载类型
     * @return  失败详情
     * @throws Exception io异常
     */
    public List<FillCouponDetailVO> getFailUserDetail(long taskId, DownloadTypeEnum downloadTypeEnum) throws Exception {

        URI fileUri = hdfsHelper.getFileUri(downloadTypeEnum.getHdfsPath()+taskId);
        if(Objects.isNull(fileUri)){
            return null;
        }

        if(!hdfsHelper.fileExists(fileUri, downloadTypeEnum.getHdfsPath()+taskId) && DownloadTypeEnum.TASK_DETAIL.equals(downloadTypeEnum)){
            FillCouponTaskPO fillCouponTaskPO =fillCouponTaskRepository.getDetailTaskById(taskId);
            Param param = GsonUtil.fromJson(fillCouponTaskPO.getParams(), Param.class);
            if(param.getApplyCount() > param.getCount()){
                return null;
                //throw ExceptionHelper.create(ErrCode.COUPON, "任务申请发放数量大于用户数量，失败数量："+(param.getApplyCount()-param.getCount())+"个");
            }
        }

        //获取hdfs文件目录
        FileStatus[] files = new FileStatus[0];
        try{
            files = hdfsHelper.listStatus(fileUri, downloadTypeEnum.getHdfsPath()+taskId);
        }catch (FileNotFoundException e){
            return null;
        }

        FileSystem fs = FileSystem.get(fileUri, new Configuration());
        FSDataInputStream in = null;
        List<FillCouponDetailVO> list = new LinkedList<>();

        try {

            for (FileStatus file : files) {
                //读取文件
                in = fs.open(file.getPath());
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(in));
                String failStr = null;
                String[] subStr = null;
                while((failStr = bufferedReader.readLine()) != null){
                    subStr = failStr.split(",");
                    if(StringUtils.isNotBlank(subStr[0])){
                        list.add(new FillCouponDetailVO(subStr[0], subStr[1]));
                    }else{
                        log.info("fail userId:{} is not a number", subStr[0]);
                    }
                }
            }
        } catch (Exception e) {
            log.error("downloadFillCouponDetail error. taskId:{}, e", taskId, e);
            return null;
        } finally {
            if(in != null){
                in.close();
            }
        }
        return list;
    }



    /**
     * 上传人群包至hdfs
     * @param uidList
     * @param fileName
     * @throws Exception
     */
    @Async("asyncExecutor")
    public void uploadUidToHDFSAsync(List<Long> uidList, String fileName) throws Exception {
        //转换IO流并上传hdfs
        if (CollectionUtils.isNotEmpty(uidList)) {
            InputStream inputStream = new ByteArrayInputStream(StringUtils.join(uidList, System.getProperty("line.separator")).getBytes());
            hdfsHelper.copyFileToHDFS(fileName, inputStream, ZkPathConstant.FILL_COUPON_UPLOAD_PATH + fileName);
        } else {
            throw ExceptionHelper.create(ErrCode.COUPON, "上传人群包数据出错了，请联系管理员");
        }

    }

    /**
     * 上传人群包至hdfs
     * @param uidList
     * @param fileName
     * @throws Exception
     */
    @Async("asyncExecutor")
    public void uploadDataToHDFSAsync(List<String> uidList, String fileName) throws Exception {
        //转换IO流并上传hdfs
        if (CollectionUtils.isNotEmpty(uidList)) {
            InputStream inputStream = new ByteArrayInputStream(StringUtils.join(uidList, System.getProperty("line.separator")).getBytes());
            hdfsHelper.copyFileToHDFS(fileName, inputStream, ZkPathConstant.FILL_COUPON_UPLOAD_PATH + fileName);
        } else {
            throw ExceptionHelper.create(ErrCode.COUPON, "上传人群包数据出错了，请联系管理员");
        }

    }


    /**
     * 获取该任务的灌券详情
     * @param taskId   任务id
     * @param downloadTypeEnum 下载类型
     * @return list
     * @throws Exception 业务异常
     */
    public List<FillCouponDetailVO> downloadFillCouponDetail(long taskId, DownloadTypeEnum downloadTypeEnum) throws Exception {
        List<String> allEntities = getFillCouponEntityList(taskId);
        List<FillCouponDetailVO> failEntityDetails = getFailUserDetail(taskId, downloadTypeEnum);
        return fillCouponConvert.convertToFillCouponDetail(allEntities, failEntityDetails);
    }


    /**
     * 获取该任务的灌券用户集
     * @param taskId 任务id
     */
    private List<String> getFillCouponEntityList(long taskId) throws Exception {

        String paramStr = fillCouponTaskRepository.getTaskParamById(taskId);
        Param param = GsonUtil.fromJson(paramStr, Param.class);

        if(Objects.isNull(param) || StringUtils.isEmpty(param.getAddress())){
            throw ExceptionHelper.create(ErrCode.COUPON, "灌券用户集不存在,taskId="+taskId);
        }

        if(param.getCount() > 10000){
            throw ExceptionHelper.create(ErrCode.COUPON, "灌券用户集大于1W, 请联系RD同学下载");
        }

        try {
            return downloadEntityList(param.getAddress());
        } catch (Exception e) {
            log.error("coupon-admin.DownLoadHelper.getFillCouponUserList,下载灌券用户集异常,taskId={},err={}", taskId, e);
            throw e;
        }
    }


}
