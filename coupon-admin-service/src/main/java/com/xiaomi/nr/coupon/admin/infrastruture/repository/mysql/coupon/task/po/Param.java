package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po;

import lombok.Data;

import java.io.Serializable;

/**
 * param: 任务相关参数
 */
@Data
public class Param implements Serializable {
    private static final long serialVersionUID = 7144804295064062631L;

    /**
     * 手动重试
     */
    private int manualRetry;

    /**
     * 数据源地址
     */
    private String address;

    /**
     * 人群包ID
     */
    private Long batchId;

    /**
     * 人群包名称
     */
    private String batchName;

    /**
     * 总数
     */
    private long count;

    /**
     * 人群包类型
     */
    private Integer dataType;

    /**
     * 上传类型
     */
    private Integer customizeGroup;

    /**
     * 申请发放数量
     */
    private long applyCount;

    /**
     * 申请发放积分总数
     */
    private long applyPointCount;

    /**
     * 结束领取时间
     */
    private long endFetchTime;

    /**
     * 发放场景
     */
    private String sendScene;

    /**
     * 人群包是否去重
     * 0：去重
     * 1：不去重
     */
    private int distinct;

    /**
     * 发放模式 1 发放给mid  2 发放给mid和vid
     */
    private Integer sendModel;

}
