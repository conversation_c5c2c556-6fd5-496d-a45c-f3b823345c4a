package com.xiaomi.nr.coupon.admin.application.dubbo.couponadmin;

import com.google.common.base.Stopwatch;
import com.mi.framework.http.HttpClient;
import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.budget.request.QueryByUploadUrlFileRequest;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboBudgetService;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.BrProxy;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.UrlUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.Service;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import static org.apache.poi.ss.usermodel.CellType.NUMERIC;

/**
 * 预算信息
 *
 * <AUTHOR>
 * @date 2024/9/19
 */
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
@Component
@Slf4j
public class DubboBudgetServiceImpl implements DubboBudgetService {

    @Autowired
    private BrProxy brProxy;

    /**
     * 通过上传文件查询预算信息
     *
     * @param request 文件url和预算类型
     * @return 预算信息
     */
    @Override
    public Result<BudgetInfoDto> queryByUploadBudgetFileUrl(QueryByUploadUrlFileRequest request) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            String url = request.getUrl();
            if (!UrlUtil.isSafety(url)) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "文件url域名错误");
            }
            if (StringUtils.isBlank(url)) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "文件url不能为空");
            }
            byte[] bytes = new HttpClient().get(url).executeAsFile();
            String feeType = request.getFeeType();
            Pair<String, Long> brRequest = null;
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes)) {
                brRequest = getBrRequest(inputStream);
            }
            BudgetInfoDto budgetInfoDto = brProxy.queryBrInfo(brRequest.getKey(), brRequest.getValue(), feeType);
            log.info("DubboBudgetServiceImpl.queryByUploadBudgetFileUrl success. runTime={}ms, req={}, resp={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), GsonUtil.toJson(request), GsonUtil.toJson(budgetInfoDto));
            return Result.success(budgetInfoDto);
        } catch (BizError e) {
            log.error("DubboBudgetServiceImpl.queryByUploadBudgetFileUrl error! ", e);
            return Result.fromException(e);
        } catch (Exception e) {
            log.error("DubboBudgetServiceImpl.queryByUploadBudgetFileUrl error! ", e);
            return Result.fail(GeneralCodes.InternalError, "内部错误");
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 解析预算请求信息。key=预算单号  value=预算传递标识
     *
     * @param inputStream 文件流
     * @return 预算请求信息
     */
    private Pair<String, Long> getBrRequest(ByteArrayInputStream inputStream) throws BizError {
        Workbook workbook;
        try {
            workbook = new XSSFWorkbook(inputStream);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            try {
                workbook = new HSSFWorkbook(inputStream);
            } catch (IOException ioException) {
                log.error("解析Excel文件失败", ioException);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "解析Excel文件失败");
            }
        }
        int numberOfSheets = workbook.getNumberOfSheets();
        if (1 != numberOfSheets) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请使用模板进行导入操作，只允许存在1个sheet页");
        }
        Sheet sheet = workbook.getSheetAt(0);
        Row row = sheet.getRow(0);
        if (row == null) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请使用模板进行导入操作");
        }
        Cell cell1 = row.getCell(0);
        Cell cell2 = row.getCell(1);
        if (cell1 == null || cell2 == null) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请使用模板进行导入操作");
        }
        Row dataRow = sheet.getRow(2);
        if (dataRow == null) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "第3行无预算数据");
        }
        String budgetApplyNo;
        Cell dataCell1 = dataRow.getCell(0);
        if (dataCell1 == null || StringUtils.isBlank(dataCell1.getStringCellValue().trim())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "第3行第1列无预算单号信息");
        }
        budgetApplyNo = dataCell1.getStringCellValue().trim();
        Cell dataCell2 = dataRow.getCell(1);
        if(dataCell2 == null || !dataCell2.getCellTypeEnum().equals(CellType.NUMERIC)){
            throw ExceptionHelper.create(GeneralCodes.ParamError, "第3行第2列必须为数字类型");
        }
        long LineNum;
        try {
            LineNum = (long) dataCell2.getNumericCellValue();
        } catch (NumberFormatException e) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "第3行第2列数据类型非法，预算传递标识应为整形数字");
        }
        return Pair.of(budgetApplyNo, LineNum);
    }
}
