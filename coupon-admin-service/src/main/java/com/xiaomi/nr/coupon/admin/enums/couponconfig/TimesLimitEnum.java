package com.xiaomi.nr.coupon.admin.enums.couponconfig;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

/**
 * 次数限制枚举
 *
 * <AUTHOR>
 * @date 2024/5/10 10:01
 */
@Getter
@AllArgsConstructor
public enum TimesLimitEnum {
    /**
     * 1: 限制
     */
    LIMIT(1, "限制"),

    /**
     * 2: 不限制
     */
    NO_LIMIT(2, "不限制"),

    ;

    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String desc;

    private static final HashMap<Integer, TimesLimitEnum> MAPPING = new HashMap<>();

    static {
        for (TimesLimitEnum e : TimesLimitEnum.values()) {
            MAPPING.put(e.getCode(), e);
        }
    }

    public static TimesLimitEnum valueOf(Integer code) {
        return MAPPING.get(code);
    }
}
