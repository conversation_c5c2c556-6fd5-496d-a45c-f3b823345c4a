package com.xiaomi.nr.coupon.admin.util.beandiff;

import com.xiaomi.nr.coupon.admin.infrastruture.annotation.FieldsAlias;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: Diff查找工具
 * @author: hejiapeng
 * @create: 2019-08-21 14:46
 */
public class BeanDiffUtil {

    /**
     * 对比对象diff信息
     *
     * @param oldObject
     * @param newObject
     * @return
     */
    public static Map<String, BeanDiffEntity> compareObjectDiff(Object oldObject, Object newObject) throws Exception {
        Map<String, BeanDiffEntity> objectDiffList = new HashMap<>();
        Class<Object> oldClazz = (Class<Object>) oldObject.getClass();
        Class<Object> newClazz = (Class<Object>) newObject.getClass();
        if (oldClazz != newClazz) {
            return objectDiffList;
        }
        Field[] fields = oldClazz.getDeclaredFields();
        for (Field field : fields) {
            FieldsAlias fieldAnnotation = field.getAnnotation(FieldsAlias.class);
            if (fieldAnnotation != null) {
                String fieldName = field.getName();
                String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                Method oMethod = oldClazz.getMethod(getMethodName);
                Method nMethod = newClazz.getMethod(getMethodName);
                Object oldVal = oMethod.invoke(oldObject);
                Object newVal = nMethod.invoke(newObject);
                if (oldVal == null && newVal == null) {
                    continue;
                }
                if (oldVal == null) {
                    objectDiffList.put(fieldName, new BeanDiffEntity(fieldAnnotation.alias(), null, newVal));
                }
                if (oldVal != null) {
                    if (!oldVal.equals(newVal)) {
                        objectDiffList.put(fieldName, new BeanDiffEntity(fieldAnnotation.alias(), oldVal, newVal));
                    }
                }
            }
        }
        return objectDiffList;
    }
}
