package com.xiaomi.nr.coupon.admin.infrastruture.repository.fds;

import java.io.File;
import java.io.InputStream;

/**
 * 文件操作服务类 ，上传文件，下载文件
 */
public interface FileService {

    /**
     * 上传文件
     * @param objectName
     * @param file
     * @param publicFlag 是否设为公开
     * @return objectName
     */
    String uploadFileToFds(String objectName, File file, boolean publicFlag) throws Exception;

    /**
     * 上传文件
     *
     * @param fileName
     * @param inputStream
     * @param publicFlag
     * @return
     * @throws Exception
     */
    String uploadFileToFds(String fileName, InputStream inputStream, boolean publicFlag) throws Exception;

    /**
     * 获取endpoint
     * @return
     */
    String getEndpoint();

    /**
     * 获取BucketName
     * @return
     */
    String getBucketName();


}
