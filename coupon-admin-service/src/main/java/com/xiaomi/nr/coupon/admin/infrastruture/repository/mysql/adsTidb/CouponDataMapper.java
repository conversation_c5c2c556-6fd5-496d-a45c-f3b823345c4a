package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.adsTidb;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.adsTidb.entity.SearchCouponDataParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.adsTidb.po.CouponStatisticPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 券发放任务mapper
 *
 * <AUTHOR>
 */
@Mapper
@Component
public interface CouponDataMapper {

    String SELECT_COUPON_DATA_SQL = "select id,apply_time,coupon_id,coupon_name,coupon_type_id,coupon_type_name,usage_condition,send_scene," +
            "send_mode,apply_cnt,fetch_cnt,used_cnt,used_channel,ord_amt,reduce_amt from ads_bi_coupon_use_data where ";

    String SELECT_FILL_COUPON_DATA_SQL = "select id,apply_time,act_id,coupon_id,coupon_name,coupon_type_id,coupon_type_name,usage_condition,send_scene," +
            "send_mode,apply_cnt,fetch_cnt,used_cnt,used_channel,ord_amt,reduce_amt from ads_bi_coupon_act_data where ";

    String COUPON_DATA_TABLE = "ads_bi_coupon_use_data";

    String FILL_COUPON_DATA_TABLE = "ads_bi_coupon_act_data";


    /**
     * 根据查询条件获取优惠券分析数据总数据
     *
     * @param searchCouponDataParam 查询参数
     * @return
     */
    @Select("<script> select count(distinct(coupon_id)) from " + COUPON_DATA_TABLE + " where 1=1" +
            "<if test='startTime!=null and endTime!=null'>and apply_time <![CDATA[>=]]> #{startTime} and apply_time <![CDATA[<=]]> #{endTime}</if>" +
            "<if test='sendType!=null'>and send_mode=#{sendType}</if>" +
            "<if test='couponIds!=null and couponIds.size>0'> and coupon_id in <foreach item='couponId' index='index' collection='couponIds' open='(' separator=',' close=')'>#{couponId}</foreach> </if>" +
            "<if test='useChannels!=null'> and used_channel in <foreach item='useChannel' index='index' collection='useChannels' open='(' separator=',' close=')'>#{useChannel}</foreach> </if>" +
            "</script>")
    Integer getCouponDataDetailCountById(SearchCouponDataParam searchCouponDataParam);

    /**
     * 根据查询条件获取优惠券分析数据券Id列表
     *
     * @param searchCouponDataParam 查询参数
     * @return
     */
    @Select("<script>select t.couponId from (select distinct(coupon_id) as couponId, apply_cnt from " + COUPON_DATA_TABLE + " where 1=1" +
            "<if test='startTime!=null and endTime!=null'>and apply_time &gt;= #{startTime} and apply_time &lt;= #{endTime}</if>" +
            "<if test='sendType!=null'>and send_mode=#{sendType}</if>" +
            "<if test='couponIds!=null and couponIds.size>0'> and coupon_id in <foreach item='couponId' index='index' collection='couponIds' open='(' separator=',' close=')'>#{couponId}</foreach> </if>" +
            "<if test='useChannels!=null'> and used_channel in <foreach item='useChannel' index='index' collection='useChannels' open='(' separator=',' close=')'>#{useChannel}</foreach> </if>" +
            " order by ${orderBy}  ${orderDirection}) t" +
            "<if test='canPage!=null and canPage'> limit #{startSize,jdbcType=BIGINT},#{pageSize,jdbcType=BIGINT}</if>" +
            "</script>")
    List<Long> getCouponDataDetailIdList(SearchCouponDataParam searchCouponDataParam);

    /**
     * 根据查询条件获取优惠券分析数据总数据
     *
     * @param searchCouponDataParam 查询参数
     * @return
     */
    @Select("<script>" + SELECT_COUPON_DATA_SQL  + " 1=1 " +
            "<if test='couponIds!=null and couponIds.size>0'> and coupon_id in <foreach item='couponId' index='index' collection='couponIds' open='(' separator=',' close=')'>#{couponId}</foreach></if>" +
            "<if test='startTime!=null and endTime!=null'>and apply_time &lt;= #{endTime}</if>" +
            "<if test='sendType!=null'>and send_mode=#{sendType}</if>" +
            "<if test='useChannels!=null'> and used_channel in <foreach item='useChannel' index='index' collection='useChannels' open='(' separator=',' close=')'>#{useChannel}</foreach> </if>" +
            " order by ${orderBy}  ${orderDirection}" +
            "</script>")
    List<CouponStatisticPo> getCouponDataDetailList(SearchCouponDataParam searchCouponDataParam);


    /**
     * 根据查询条件获取灌券分析数据总数据
     *
     * @param searchCouponDataParam 查询参数
     * @return
     */
    @Select("<script> select count(distinct(act_id)) from " + FILL_COUPON_DATA_TABLE + " where 1=1" +
            "<if test='startTime!=null and endTime!=null'>and apply_time &gt;= #{startTime} and apply_time &lt;= #{endTime}</if>" +
            "<if test='couponIds!=null and couponIds.size>0'> and coupon_id in <foreach item='couponId' index='index' collection='couponIds' open='(' separator=',' close=')'>#{couponId}</foreach> </if>" +
            "<if test='actIds!=null'> and act_id in <foreach item='actId' index='index' collection='actIds' open='(' separator=',' close=')'>#{actId}</foreach> </if>" +
            "<if test='useChannels!=null'> and used_channel in <foreach item='useChannel' index='index' collection='useChannels' open='(' separator=',' close=')'>#{useChannel}</foreach> </if>" +
            "</script>")
    Integer getFillCouponDataDetailCount(SearchCouponDataParam searchCouponDataParam);


    /**
     * 根据查询条件获取优惠券分析数据券Id列表
     *
     * @param searchCouponDataParam 查询参数
     * @return
     */
    @Select("<script> select t.act from (select distinct(act_id) as act, coupon_id, apply_cnt from " + FILL_COUPON_DATA_TABLE + " where 1=1" +
            "<if test='startTime!=null and endTime!=null'>and apply_time &gt;= #{startTime} and apply_time &lt;= #{endTime}</if>" +
            "<if test='couponIds!=null and couponIds.size>0'> and coupon_id in <foreach item='couponId' index='index' collection='couponIds' open='(' separator=',' close=')'>#{couponId}</foreach> </if>" +
            "<if test='actIds!=null'> and act_id in <foreach item='actId' index='index' collection='actIds' open='(' separator=',' close=')'>#{actId}</foreach> </if>" +
            "<if test='useChannels!=null'> and used_channel in <foreach item='useChannel' index='index' collection='useChannels' open='(' separator=',' close=')'>#{useChannel}</foreach> </if>" +
            " order by ${orderBy}  ${orderDirection}) t" +
            "<if test='canPage!=null and canPage'> limit #{startSize,jdbcType=BIGINT},#{pageSize,jdbcType=BIGINT}</if>" +
            "</script>")
    List<String> getFillCouponDataDetailIdList(SearchCouponDataParam searchCouponDataParam);

    /**
     * 根据查询条件获取灌券分析数据总数据
     *
     * @param searchCouponDataParam 查询参数
     * @return
     */
    @Select("<script>" + SELECT_FILL_COUPON_DATA_SQL + " 1=1 " +
            "<if test='startTime!=null and endTime!=null'> and apply_time &lt;= #{endTime}</if>" +
            "<if test='actIds!=null'> and act_id in <foreach item='actId' index='index' collection='actIds' open='(' separator=',' close=')'>#{actId}</foreach> </if>" +
            "<if test='useChannels!=null'> and used_channel in <foreach item='useChannel' index='index' collection='useChannels' open='(' separator=',' close=')'>#{useChannel}</foreach> </if>" +
            " order by ${orderBy}  ${orderDirection}" +
            "</script>")
    List<CouponStatisticPo> getFillCouponDataDetailList(SearchCouponDataParam searchCouponDataParam);


}
