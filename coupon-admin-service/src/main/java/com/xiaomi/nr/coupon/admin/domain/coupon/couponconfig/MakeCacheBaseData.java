package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig;

import com.xiaomi.nr.coupon.admin.enums.goods.GoodsOnlineStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig.OldCouponConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig.SkuGroupMapMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig.po.CouponConfigPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig.po.SkuGroupMapPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.goods.po.GoodsPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.goods.po.PackagePo;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.goods.GmsProxyService;
import com.xiaomi.nr.coupon.admin.infrastruture.staticdata.UseChannelClientRel;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 生成券配置缓存所需基础数据
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Deprecated
public class MakeCacheBaseData {

    @Autowired
    private OldCouponConfigMapper couponConfigMapper;

    @Autowired
    private GmsProxyService gmsGoodsInfo;

    @Autowired
    private SkuGroupMapMapper skuGroupMapMapper;

    @Autowired
    private UseChannelClientRel useChannelClientRel;

    /**
     * 获取基础数据
     */
    public BaseData get(String timeStart) throws BizError {
        if (StringUtils.isEmpty(timeStart)) {
            //默认为24小时
            timeStart = TimeUtil.formatSecond(TimeUtil.getNowUnixSecond() - 24 * 3600);
        }

        BaseData result = new BaseData();
        result.setConfigList(new ArrayList<>());

        long runStartTime = TimeUtil.getNowUnixMillis();
        List<CouponConfigPo> configs = getAllCacheConfigList(timeStart);
        log.info("task.couponConfig.cache, 获取基础数据[券配置列表]完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));
        if (configs.size() <= 0) {
            return result;
        }

        //取货品列表
        runStartTime = TimeUtil.getNowUnixMillis();
        List<GoodsPo> goodsList = getGoodsList();
        log.info("task.couponConfig.cache, 获取基础数据[货品列表]完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));

        //取套装列表
        runStartTime = TimeUtil.getNowUnixMillis();
        List<PackagePo> packageList = getPackageList();
        log.info("task.couponConfig.cache, 获取基础数据[套装列表]完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));

        //取sku和套装与品类的关系
        runStartTime = TimeUtil.getNowUnixMillis();
        List<SkuGroupMapPo> skuGroupMapList = getSkuGroupMapList();
        log.info("task.couponConfig.cache, 获取基础数据[品类与SKU套装关系列表]完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));

        //货品转换成map
        Map<Long, GoodsPo> goodsMap = new HashMap<>();
        for (GoodsPo item : goodsList) {
            goodsMap.put(item.getGoodsId(), item);
        }

        //套装转换成map
        Map<Long, PackagePo> packageMap = new HashMap<>();
        for (PackagePo item : packageList) {
            packageMap.put(item.getPackageId(), item);
        }

        //品类转换成map
        Map<Long, List<SkuGroupMapPo>> skuGroupMap = new HashMap<>();
        for (SkuGroupMapPo item : skuGroupMapList) {
            Long groupId = item.getGroupId();
            List<SkuGroupMapPo> goods = skuGroupMap.get(groupId);
            if (goods == null) {
                goods = new ArrayList<>();
            }
            goods.add(item);
            skuGroupMap.put(groupId, goods);
        }

        //使用渠道与client_id关系
        Map<String, UseChannelClientRelationDo> useChannelClientRelation = useChannelClientRel.getUseChannelClientRelation();

        //make common data
        result.setGoodsMap(goodsMap);
        result.setPackageMap(packageMap);
        result.setSkuGroupMap(skuGroupMap);
        result.setConfigList(configs);
        result.setUseChannelClientRelation(useChannelClientRelation);
        return result;
    }


    /**
     * 获取基础数据
     */
    public BaseData getV2() throws BizError {
        BaseData result = new BaseData();

        long runStartTime = TimeUtil.getNowUnixMillis();

        //取货品列表
        List<GoodsPo> goodsList = getGoodsListV2();
        log.info("task.couponConfig.cache, 获取基础数据[货品列表]完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));

        //取套装列表
        runStartTime = TimeUtil.getNowUnixMillis();
        List<PackagePo> packageList = getPackageList();
        log.info("task.couponConfig.cache, 获取基础数据[套装列表]完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));

        //取sku和套装与品类的关系
        runStartTime = TimeUtil.getNowUnixMillis();
        List<SkuGroupMapPo> skuGroupMapList = getSkuGroupMapList();
        log.info("task.couponConfig.cache, 获取基础数据[品类与SKU套装关系列表]完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));

        //货品转换成map
        Map<Long, GoodsPo> goodsMap = new HashMap<>();
        for (GoodsPo item : goodsList) {
            goodsMap.put(item.getGoodsId(), item);
        }

        //套装转换成map
        Map<Long, PackagePo> packageMap = new HashMap<>();
        for (PackagePo item : packageList) {
            packageMap.put(item.getPackageId(), item);
        }

        //品类转换成map
        Map<Long, List<SkuGroupMapPo>> skuGroupMap = new HashMap<>();
        for (SkuGroupMapPo item : skuGroupMapList) {
            Long groupId = item.getGroupId();
            List<SkuGroupMapPo> goods = skuGroupMap.get(groupId);
            if (goods == null) {
                goods = new ArrayList<>();
            }
            goods.add(item);
            skuGroupMap.put(groupId, goods);
        }

        //使用渠道与client_id关系
        Map<String, UseChannelClientRelationDo> useChannelClientRelation = useChannelClientRel.getUseChannelClientRelation();

        //make common data
        result.setGoodsMap(goodsMap);
        result.setPackageMap(packageMap);
        result.setSkuGroupMap(skuGroupMap);
        result.setUseChannelClientRelation(useChannelClientRelation);
        return result;
    }

    /**
     * 获取需要缓存的券配置列表
     *
     * @param timeStart String
     * @return List<CouponConfigPo>
     */
    private List<CouponConfigPo> getAllCacheConfigList(String timeStart) {
        long runStartTime = TimeUtil.getNowUnixMillis();
        List<CouponConfigPo> la = getCacheConfigListForValidityPeriod();
        log.info("task.couponConfig.cache, 获取基础数据[过期不超过*小时券配置列表]完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));

        runStartTime = TimeUtil.getNowUnixMillis();
        List<CouponConfigPo> lb = getCacheConfigListForUpdateTime(timeStart);
        log.info("task.couponConfig.cache, 获取基础数据[*小时内发生过变更的券配置列表]完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));

        Set<Long> ids = new HashSet<>();
        for(CouponConfigPo item : la) {
            ids.add(item.getId());
        }

        for(CouponConfigPo item : lb) {
            if(ids.contains(item.getId())) {
                continue;
            }
            la.add(item);
        }
        return la;
    }


    /**
     * 获取需要生成缓存的优惠券配置列表（过期不超过24小时）
     * todo 优化分页查询方式 使用id查 或者 根据 id > 上次查询结果查
     * @return List<CouponConfigPo>
     */
    private List<CouponConfigPo> getCacheConfigListForValidityPeriod() {
        List<CouponConfigPo> result = new ArrayList<>();
        long startSize = 0;
        long pageSize = 500;
        long timeStart = TimeUtil.getNowUnixSecond()-24*3600;
        while (true) {
            List<CouponConfigPo> list = couponConfigMapper.getCacheConfigListForValidityPeriod(timeStart, startSize, pageSize);
            if (list == null || list.isEmpty()) {
                break;
            }
            result.addAll(list);
            if (list.size() < pageSize) {
                break;
            }
            startSize += pageSize;
        }
        return result;
    }

    /**
     * 获取需要生成缓存的优惠券配置列表（24小时内发生过变更的）
     * todo 优化分页查询方式 使用id查 或者 根据 id > 上次查询结果查
     * @param timeStart String
     * @return List<CouponConfigPo>
     */
    private List<CouponConfigPo> getCacheConfigListForUpdateTime(String timeStart) {
        List<CouponConfigPo> result = new ArrayList<>();
        long startSize = 0;
        long pageSize = 500;
        while (true) {
            List<CouponConfigPo> list = couponConfigMapper.getCacheConfigListForUpdateTime(timeStart, startSize, pageSize);
            if (list == null || list.isEmpty()) {
                break;
            }
            result.addAll(list);
            if (list.size() < pageSize) {
                break;
            }
            startSize += pageSize;
        }
        return result;
    }


    /**
     * 获取所有货品列表
     *
     * @return List<GoodsPo>
     */
    private List<GoodsPo> getGoodsList() throws BizError {
        //获取线上上架的货品
        List<GoodsPo> onlineGoods = getGoodsListByStat(GoodsOnlineStatusEnum.ON_SHELF_ONLINE);
        //获取线下上架的货品
        List<GoodsPo> offlineGoods = getGoodsListByStat(GoodsOnlineStatusEnum.ON_SHELF_OFFLINE);
        onlineGoods.addAll(offlineGoods);
        HashSet<GoodsPo> set = new HashSet<>(onlineGoods);
        onlineGoods.clear();
        onlineGoods.addAll(set);
        return onlineGoods;
    }

    /**
     * 获取所有货品列表
     *
     * @return List<GoodsPo>
     */
    private List<GoodsPo> getGoodsListV2() throws BizError {
        List<GoodsPo> result = Lists.newArrayList();
        int pageNum = 1;
        int pageSize = 1000;
        while (true) {
            List<GoodsPo> list = gmsGoodsInfo.getGoodsList(pageNum, pageSize);
            if (list == null || list.isEmpty()) {
                break;
            }
            result.addAll(list);
            if (list.size() < pageSize) {
                break;
            }
            pageNum ++;
        }
        return result;
    }

    /**
     * 根据商品的上下架状态获取商品信息
     *
     * @param goodsOnlineStatusEnum 商品状态枚举
     * @return List<>   商品信息
     * @throws BizError 业务异常
     */
    private List<GoodsPo> getGoodsListByStat(GoodsOnlineStatusEnum goodsOnlineStatusEnum) throws BizError {
        List<GoodsPo> result = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 1000;
        while (true) {
            List<GoodsPo> list = gmsGoodsInfo.getGoodsList(pageNum, pageSize, goodsOnlineStatusEnum);
            if (list == null || list.isEmpty()) {
                break;
            }
            result.addAll(list);
            if (list.size() < pageSize) {
                break;
            }
            pageNum ++;
        }

        return result;
    }


    /**
     * 获取所有套装列表
     *
     * @return List<PackagePo>
     */
    private List<PackagePo> getPackageList() throws BizError {
        List<PackagePo> result = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 1000;
        while (true) {
            List<PackagePo> list = gmsGoodsInfo.getPackageList(pageNum, pageSize);
            if (list == null || list.isEmpty()) {
                break;
            }
            result.addAll(list);
            if (list.size() < pageSize) {
                break;
            }
            pageNum++;
        }
        return result;
    }


    /**
     * 获取所有品类关系列表
     *
     * @return List<SkuGroupMapPo>
     */
    private List<SkuGroupMapPo> getSkuGroupMapList() {
        List<SkuGroupMapPo> result = new ArrayList<>();
        long startSize = 0;
        long pageSize = 1000;
        while (true) {
            List<SkuGroupMapPo> list = skuGroupMapMapper.getList(startSize, pageSize);
            if (list == null || list.isEmpty()) {
                break;
            }
            result.addAll(list);
            if (list.size() < pageSize) {
                break;
            }
            startSize += pageSize;
        }
        return result;
    }


    /**
     * 获取基础数据
     */
    public BaseData getV3() throws BizError {
        BaseData result = new BaseData();

        long runStartTime = TimeUtil.getNowUnixMillis();

        //取货品列表
        List<GoodsPo> goodsList = getGoodsListV2();
        log.info("task.couponConfig.cache, 获取基础数据[货品列表]完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));

        //取sku和套装与品类的关系
        runStartTime = TimeUtil.getNowUnixMillis();
        List<SkuGroupMapPo> skuGroupMapList = getSkuGroupMapList();
        log.info("task.couponConfig.cache, 获取基础数据[品类与SKU套装关系列表]完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));

        //取套装列表
        runStartTime = TimeUtil.getNowUnixMillis();
        List<PackagePo> packageList = getPackageList();
        log.info("task.couponConfig.cache, 获取基础数据[套装列表]完成, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));

        //货品转换成map
        Map<Long, GoodsPo> goodsMap = new HashMap<>();
        for (GoodsPo item : goodsList) {
            goodsMap.put(item.getGoodsId(), item);
        }

        //品类转换成map
        Map<Long, List<SkuGroupMapPo>> skuGroupMap = new HashMap<>();
        for (SkuGroupMapPo item : skuGroupMapList) {
            Long groupId = item.getGroupId();
            List<SkuGroupMapPo> goods = skuGroupMap.get(groupId);
            if (goods == null) {
                goods = new ArrayList<>();
            }
            goods.add(item);
            skuGroupMap.put(groupId, goods);
        }

        //套装转换成map
        Map<Long, PackagePo> packageMap = new HashMap<>();
        for (PackagePo item : packageList) {
            packageMap.put(item.getPackageId(), item);
        }


        //make common data
        result.setGoodsMap(goodsMap);
        result.setSkuGroupMap(skuGroupMap);
        result.setPackageMap(packageMap);
        return result;
    }



}
