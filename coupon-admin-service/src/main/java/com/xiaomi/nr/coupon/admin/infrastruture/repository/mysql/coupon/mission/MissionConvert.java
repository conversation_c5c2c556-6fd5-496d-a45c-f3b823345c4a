package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.mission;


import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.mission.po.CouponMissionPO;

public class MissionConvert {

    public static CouponMissionPO convertCouponMissionPO(CouponConfigPO couponConfigPO){
        CouponMissionPO couponMissionPO =new CouponMissionPO();
        couponMissionPO.setId(couponConfigPO.getId());
        if(CouponConfigStatusEnum.STOP_FETCHING.getCode() == couponConfigPO.getStatus() ||
                CouponConfigStatusEnum.OFFLINE.getCode() == couponConfigPO.getStatus()){
            couponMissionPO.setStat("cancel");
        }else{
            couponMissionPO.setStat("approved");
        }
        couponMissionPO.setSendNum(couponConfigPO.getApplyCount());
        couponMissionPO.setTypeId(couponConfigPO.getId());
        couponMissionPO.setCouponStartTime(couponConfigPO.getStartUseTime());
        couponMissionPO.setCouponEndTime(couponConfigPO.getEndUseTime());
        couponMissionPO.setSendTime(couponConfigPO.getStartFetchTime());
        couponMissionPO.setSendEndTime(couponConfigPO.getEndFetchTime());
        couponMissionPO.setAdminName(couponConfigPO.getCreator());
        couponMissionPO.setAddTime(couponConfigPO.getCreateTime());
        couponMissionPO.setMissionType(1);
        couponMissionPO.setCouponDays(couponConfigPO.getUseDuration());
        couponMissionPO.setMaxNum(couponConfigPO.getApplyCount());


        couponMissionPO.setName("");
        couponMissionPO.setGroupId(0L);
        couponMissionPO.setAdminId(0L);

        couponMissionPO.setAreaId(0L);
        couponMissionPO.setDownload("");
        couponMissionPO.setNowNum(0L);
        couponMissionPO.setApprovedId(0L);

        return couponMissionPO;
    }

}
