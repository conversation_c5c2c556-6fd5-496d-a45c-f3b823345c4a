package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.warning.handler;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.warning.WarnCheckResult;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.warning.WarningCheckHandlerConfig;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 预警校验抽象类
 * @author: hejiapeng
 * @Date 2022/7/11 8:56 下午
 * @Version: 1.0
 **/

public abstract class AbstractWarningCheckHandler {

    @Getter
    @Setter
    protected WarningCheckHandlerConfig config;

    /**
     * 责任链
     */
    @Getter
    @Setter
    private AbstractWarningCheckHandler nextHandler;

    /**
     * 获取next
     *
     * @return
     */
    public AbstractWarningCheckHandler next() {
        return nextHandler;
    }


    /**
     * 预警校验执行方法
     * <p>所有执行规则都继承该方法<p/>
     *
     * @param couponConfigPO
     * @return
     */
    public abstract WarnCheckResult doWarningCheck(CouponConfigPO couponConfigPO) throws BizError;
}
