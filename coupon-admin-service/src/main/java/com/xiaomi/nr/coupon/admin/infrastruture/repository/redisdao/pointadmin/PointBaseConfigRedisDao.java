package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.pointadmin;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.pointadmin.po.PointBaseConfigCachePo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

/**
 * <AUTHOR>
 * @date 2023/12/13 19:06
 */
public interface PointBaseConfigRedisDao {
    /**
     * 设置积分通用配置缓存
     *
     * @param cachePo cachePo
     */
    void setPointBaseBatchConfigCache(PointBaseConfigCachePo cachePo) throws BizError;

    /**
     * 获取积分通用配置缓存
     *
     * @return PointBaseConfigCachePo
     */
    PointBaseConfigCachePo getPointBaseBatchConfigCache();
}
