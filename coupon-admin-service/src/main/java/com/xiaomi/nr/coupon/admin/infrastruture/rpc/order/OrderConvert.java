package com.xiaomi.nr.coupon.admin.infrastruture.rpc.order;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.OrderInfoVO;
import com.xiaomi.nr.coupon.admin.enums.OrderStatusNodeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.nr.order.api.dto.order.lightorder.LightOrderDto;
import com.xiaomi.nr.order.api.dto.response.order.LightOrderInfoResp;
import com.xiaomi.nr.order.api.dto.response.orderquery.OrderDetailDto;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

@Component
public class OrderConvert {


    /**
     * 线上订单交易信息转换
     * @param orderResponse
     * @return OrderInfoVO
     */
    public OrderInfoVO convertOnlineOrderInfoVO(LightOrderInfoResp orderResponse){

        if(Objects.isNull(orderResponse) || !Objects.equals(CommonConstant.ONE_INT, orderResponse.getOrderList().size())){
            return null;
        }

        OrderInfoVO orderInfoVO = new OrderInfoVO();
        LightOrderDto orderInfo = orderResponse.getOrderList().get(CommonConstant.ZERO_INT);

        Integer status = orderInfo.getOrderStatus();
        orderInfoVO.setOrderId(String.valueOf(orderInfo.getOrderId()));
        orderInfoVO.setOrderStatus(status);
        orderInfoVO.setOrderStatusDesc(Optional.ofNullable(OrderStatusNodeEnum.findByDescText(status)).orElse("状态("+status+")"));
        orderInfoVO.setUseTime(TimeUtil.convertLongToDate(orderInfo.getAddTime()));
        orderInfoVO.setUseChannel(UseChannelEnum.MiShop.getName());

        return orderInfoVO;
    }


    /**
     * 门店订单信息转换
     * @param orderInfo
     * @return
     */
    public OrderInfoVO convertOfflineOrderInfoVO(OrderDetailDto orderInfo){

        if(Objects.isNull(orderInfo)){
            return null;
        }
        Integer status = orderInfo.getOrderInfo().getOrderStatus();
        OrderInfoVO orderInfoVO = new OrderInfoVO();
        orderInfoVO.setOrderId(orderInfo.getOrderInfo().getOrderId());
        orderInfoVO.setOrderStatus(status);
        orderInfoVO.setOrderStatusDesc(orderInfo.getOrderInfo().getOrderStatusDesc());
        orderInfoVO.setOrderStatusDesc(Optional.ofNullable(OrderStatusNodeEnum.findByDescText(status)).orElse("状态("+status+")"));
        orderInfoVO.setUseTime(TimeUtil.formatDateStr(orderInfo.getOrderInfo().getCreatedTime()));
        orderInfoVO.setUseChannel(convertUseChannel(orderInfo.getOrderInfo().getSiteId()));
        orderInfoVO.setUseOrg(orderInfo.getOrderInfo().getOrgId());

        return orderInfoVO;
    }

    private String convertUseChannel(int siteId){
        if(1 == siteId){
            return UseChannelEnum.MiHome.getName();
        }
        if(5 == siteId){
            return UseChannelEnum.MiAuthorized.getName();
        }
        return null;
    }

}
