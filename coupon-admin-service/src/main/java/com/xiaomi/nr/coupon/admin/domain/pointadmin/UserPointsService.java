package com.xiaomi.nr.coupon.admin.domain.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.UserPointsListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.UsersPointDto;
import com.xiaomi.nr.coupon.admin.enums.AriesSourceEnum;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.PointsStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CarPointsSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.PointConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.UserPointsRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsScenePo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.userpoint.po.UserPointsPo;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.AriesProxyService;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.SidProxy;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.aries.model.nr.CancelRequest;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 积分数据服务
 *
 * <AUTHOR>
 * @date 2023/12/6
 */
@Service
@Slf4j
public class UserPointsService {

    @Resource
    private UserPointsRepository userPointsRepository;

    @Resource
    private PointConfigRepository pointConfigRepository;

    @Resource
    private CarPointsSceneRepository carPointsSceneRepository;

    @Autowired
    private SidProxy sidProxy;

    @Autowired
    private AriesProxyService ariesProxyService;

    public BasePageResponse<UsersPointDto> getUserPointsList(UserPointsListRequest req) {
        int pageNo = Objects.nonNull(req.getPageNo()) && req.getPageNo() > 0 ? req.getPageNo() : 0;
        int pageSize = Objects.nonNull(req.getPageSize()) && req.getPageSize() > 0 ? req.getPageSize() : 10;
        long lastId = 0L;
        long totalCount = userPointsRepository.getUserPointsCount(req.getMid(), req.getBatchId());
        long totalPage = totalCount % pageSize > 0 ? totalCount / pageSize + 1 : totalCount / pageSize;

        List<UsersPointDto> dataList = new ArrayList<>();
        List<UserPointsPo> pointList = userPointsRepository.getUserPointsList(req.getMid(), req.getBatchId(), (pageNo - 1) * pageSize, pageSize);
        if (CollectionUtils.isNotEmpty(pointList)) {
            List<Long> batchIdList = pointList.stream().map(UserPointsPo::getBatchId).distinct().collect(Collectors.toList());
            List<String> sceneList = pointList.stream().map(UserPointsPo::getSendScene).distinct().collect(Collectors.toList());
            Map<Long, CarPointsBatchConfigPo> batchMap = pointConfigRepository.batchGetConfigInfo(batchIdList);
            Map<String, CarPointsScenePo> sceneMap = carPointsSceneRepository.batchGetSceneInfo(sceneList);
            long nowTime = TimeUtil.getNowUnixSecond();
            for (UserPointsPo pointPo : pointList) {
                CarPointsBatchConfigPo batchPo = batchMap.get(pointPo.getBatchId());
                CarPointsScenePo scenePo = sceneMap.get(pointPo.getSendScene());
                UsersPointDto row = makeUserPointDto(pointPo, batchPo, scenePo, nowTime);
                dataList.add(row);
            }
            lastId = Collections.min(pointList.stream().map(UserPointsPo::getId).collect(Collectors.toList()));
        }
        return new BasePageResponse<>(pageNo, pageSize, totalCount, totalPage, lastId, dataList);
    }


    public void cancelUserPoint(Long mid, Long pointId, String invalidReason) throws BizError {
        CancelRequest request = new CancelRequest();
        request.setUserId(mid);
        request.setSource(AriesSourceEnum.POINT.code);
        request.setIdList(Lists.newArrayList(pointId));
        request.setInvalidReason(invalidReason);
        ariesProxyService.cancel(request);
    }

    private UsersPointDto makeUserPointDto(UserPointsPo pointPo, CarPointsBatchConfigPo batchPo, CarPointsScenePo scenePo, long nowTime) {
        UsersPointDto r = new UsersPointDto();
        r.setPointId(pointPo.getId());
        r.setBatchId(pointPo.getBatchId());
        r.setBatchName(batchPo.getName());
        r.setSceneCode(scenePo.getSceneCode());
        r.setSceneName(scenePo.getName());
        if (pointPo.getSendTime() > 0L) {
            r.setSendTime(TimeUtil.convertLongToDate(pointPo.getSendTime()));
        }
        if (pointPo.getActivateTime() > 0) {
            r.setActivateTime(TimeUtil.convertLongToDate(pointPo.getActivateTime()));
        }
        if (pointPo.getInvalidTime() > 0) {
            r.setInvalidTime(TimeUtil.convertLongToDate(pointPo.getInvalidTime()));
        }
        r.setSendCount(pointPo.getTotalCount());
        r.setBalanceCount(pointPo.getBalanceCount());

        int stat = getPointStat(pointPo, nowTime);
        String statName = Objects.nonNull(PointsStatusEnum.valueOf(stat)) ? PointsStatusEnum.valueOf(stat).getDesc() : "";
        r.setStat(stat);
        r.setStatName(statName);
        r.setMid(pointPo.getMid());
        r.setOperator(batchPo.getCreator());
        return r;
    }


    private int getPointStat(UserPointsPo pointPo, long nowTime) {
        if (pointPo.getStat().equals(PointsStatusEnum.INVALID.getCode())) {
            return pointPo.getStat();
        }
        if (pointPo.getBalanceCount().equals(0L)) {
            return PointsStatusEnum.USED.getCode();
        }
        if (pointPo.getEndTime() <= nowTime) {
            return PointsStatusEnum.EXPIRED.getCode();
        }
        return pointPo.getStat();
    }

}
