package com.xiaomi.nr.coupon.admin.domain.ecard.convert;

import com.xiaomi.nr.coupon.admin.api.dto.ecard.EcardDto;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.EcardLogDto;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.ListEcardDescResponse;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.LogDesc;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.ListEcardStatResponse;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecardconfig.po.EcardLogPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecardconfig.po.EcardPo;
import com.xiaomi.nr.coupon.admin.enums.ecard.EcardLogTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.ecard.EcardStatEnum;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class EcardConvert {

    /**
     * EcardPo to ListEcardResponse (stat)
     *
     * @param ecardPo EcardPo
     * @return listEcardResponse
     */
    public ListEcardStatResponse convert2ListEcardStatResponse(EcardPo ecardPo) {
        if (ecardPo == null) {
            return null;
        }
        ListEcardStatResponse listEcardStatResponse = new ListEcardStatResponse();
        Long ecardId = ecardPo.getCardId();
        Long userId = ecardPo.getUserId();
        Integer stat = ecardPo.getStat();
        String statDesc = Objects.requireNonNull(EcardStatEnum.findByValue(stat)).getName();

        listEcardStatResponse.setEcardId(ecardId);
        listEcardStatResponse.setUserId(userId);
        listEcardStatResponse.setStatDesc(statDesc);
        return listEcardStatResponse;
    }

    /**
     * EcardPo to ListEcardResponse (desc)
     *
     * @param ecardPo ecardLogPoList
     * @return listEcardResponse
     */
    public ListEcardDescResponse convert2ListEcardDescResponse(EcardPo ecardPo, List<EcardLogPo> ecardLogPoList) {
        if (ecardPo == null) {
            return null;
        }

        ListEcardDescResponse listEcardResponse = new ListEcardDescResponse();
        Long ecardId = ecardPo.getCardId();
        Long userId = ecardPo.getUserId();
        BigDecimal money = ecardPo.getMoney();
        BigDecimal balabnceMoney = ecardPo.getBalance();
        BigDecimal amountMoney = money.subtract(balabnceMoney);
        Integer stat = ecardPo.getStat();
        String statDesc = Objects.requireNonNull(EcardStatEnum.findByValue(stat)).getName();
        String endTime = ecardPo.getEndTime();

        long activeTime = ecardPo.getActiveTime() == null ? 0 : ecardPo.getActiveTime();
        long bindTime = ecardPo.getBindTime() == null ? 0 : ecardPo.getBindTime();
        long invalidTime = ecardPo.getInvalidTime() == null ? 0 : ecardPo.getInvalidTime();
        listEcardResponse.setActiveTime(activeTime);
        listEcardResponse.setBindTime(bindTime);
        listEcardResponse.setInvalidTime(invalidTime);

        listEcardResponse.setEcardId(ecardId);
        listEcardResponse.setUserId(userId);
        listEcardResponse.setMoney(String.valueOf(money));
        listEcardResponse.setBalanceMoney(String.valueOf(balabnceMoney));
        listEcardResponse.setAmountMoney(String.valueOf(amountMoney));
        listEcardResponse.setStatDesc(statDesc);
        listEcardResponse.setEndTime(Long.parseLong(endTime.trim()));
        // 礼品卡首次消费时间
        listEcardResponse.setFirstUseTime(getFirstUseTime(ecardLogPoList));
        // 礼品卡最近消费时间
        listEcardResponse.setTailUseTime(getTailUseTime(ecardLogPoList));

        List<LogDesc> logDescList = new LinkedList<>();
        if (!CollectionUtils.isEmpty(ecardLogPoList)) {
            // 加入日志信息
            ecardLogPoList.forEach(ecardLogPo -> {
                logDescList.add(convert2LogDesc(ecardLogPo));
            });
        }
        listEcardResponse.setLogList(logDescList);
        return listEcardResponse;
    }

    /**
     * EcardLogPo to LogDesc
     *
     * @param ecardLogPo EcardLogPo
     * @return LogDesc
     */
    private LogDesc convert2LogDesc(EcardLogPo ecardLogPo) {
        if (ecardLogPo == null) {
            return null;
        }
        LogDesc logDesc = new LogDesc();
        logDesc.setOrderId(ecardLogPo.getOrderId());
        logDesc.setRefundNo(ecardLogPo.getRefundNo());
        if (Objects.equals(ecardLogPo.getLogType(), EcardLogTypeEnum.CONSUM_LOG.getValue()) && ecardLogPo.getIncome().compareTo(BigDecimal.ZERO) == 1) {
            logDesc.setLogTypeDesc(Objects.requireNonNull(EcardLogTypeEnum.findByValue(EcardLogTypeEnum.REFUND_LOG.getValue())).getName());
        } else {
            logDesc.setLogTypeDesc(Objects.requireNonNull(EcardLogTypeEnum.findByValue(ecardLogPo.getLogType())).getName());
        }
        logDesc.setIncome(String.valueOf(ecardLogPo.getIncome()));
        logDesc.setOldBalance(String.valueOf(ecardLogPo.getOldBalance()));
        logDesc.setNewBalance(String.valueOf(ecardLogPo.getNewBalance()));
        logDesc.setDescription(ecardLogPo.getDescription());
        logDesc.setAddTime(ecardLogPo.getAddTime());
        return logDesc;
    }

    /**
     * 获取礼品卡首次消费时间
     *
     * @param ecardLogPoList List<EcardLogPo>
     * @return Long
     */
    private Long getFirstUseTime(List<EcardLogPo> ecardLogPoList) {
        if (!CollectionUtils.isEmpty(ecardLogPoList)) {
            for (EcardLogPo ecardLogPo : ecardLogPoList) {
                if (!Objects.isNull(ecardLogPo.getAddTime()) && ecardLogPo.getAddTime() > 0 && ecardLogPo.getIncome().compareTo(BigDecimal.ZERO) == -1) {
                    return ecardLogPo.getAddTime();
                }
            }
        }
        return 0L;
    }

    /**
     * 获取礼品卡最近消费时间
     *
     * @param ecardLogPoList List<EcardLogPo>
     * @return Long
     */
    private Long getTailUseTime(List<EcardLogPo> ecardLogPoList) {
        if (!CollectionUtils.isEmpty(ecardLogPoList)) {
            for (int i = ecardLogPoList.size() - 1; i > -1; i--) {
                EcardLogPo ecardLogPo = ecardLogPoList.get(i);
                if (!Objects.isNull(ecardLogPo.getAddTime()) && ecardLogPo.getAddTime() > 0 && ecardLogPo.getIncome().compareTo(BigDecimal.ZERO) == -1) {
                    return ecardLogPo.getAddTime();
                }
            }
        }
        return 0L;
    }

    /**
     * 将EcardLogPo列表转换为EcardLogDto列表
     *
     * @param ecardLogPos EcardLogPo对象的列表
     * @return EcardLogDto对象的列表
     */
    public List<EcardLogDto> buildEcardLogDto(List<EcardLogPo> ecardLogPos) {
        List<EcardLogDto> ecardLogDtos = Lists.newArrayList();
        for (EcardLogPo ecardLogPo : ecardLogPos) {
            EcardLogDto ecardLogDto = new EcardLogDto();
            ecardLogDto.setCardId(ecardLogPo.getCardId());
            ecardLogDto.setUserId(ecardLogPo.getUserId());
            ecardLogDto.setOrderId(Long.valueOf(ecardLogPo.getOrderId()));
            ecardLogDto.setRefundNo(ecardLogPo.getRefundNo());
            ecardLogDto.setLogType(ecardLogPo.getLogType());
            ecardLogDto.setIncome(ecardLogPo.getIncome());
            ecardLogDto.setOldBalance(ecardLogPo.getOldBalance());
            ecardLogDto.setNewBalance(ecardLogPo.getNewBalance());
            ecardLogDto.setOperatorId(ecardLogPo.getOperatorId());
            ecardLogDto.setAddTime(Math.toIntExact(ecardLogPo.getAddTime()));
            ecardLogDto.setDescription(ecardLogPo.getDescription());
            ecardLogDto.setLastUpdateTime(ecardLogPo.getLastUpdateTime());
            ecardLogDto.setHashCode(ecardLogPo.getHashCode());
            ecardLogDto.setOffline(ecardLogPo.getOffline());

            ecardLogDtos.add(ecardLogDto);
        }

        return ecardLogDtos;
    }

    /**
     * 构建EcardDto列表
     *
     * @param ecardPoList EcardPo对象的列表
     * @return EcardDto对象的列表
     */
    public List<EcardDto> buildEcardDto(List<EcardPo> ecardPoList) {
        List<EcardDto> ecardDtos = Lists.newArrayList();
        for (EcardPo ecardPo : ecardPoList) {
            EcardDto ecardDto = new EcardDto();
            ecardDto.setCardId(ecardPo.getCardId());
            ecardDto.setSn(ecardPo.getSn());
            ecardDto.setUserId(ecardPo.getUserId());
            ecardDto.setTypeId(ecardPo.getTypeId());
            ecardDto.setSku(ecardPo.getSku());
            ecardDto.setMoney(ecardPo.getMoney());
            ecardDto.setBalance(ecardPo.getBalance());
            ecardDto.setSalePrice(new BigDecimal(ecardPo.getSalePrice()));
            ecardDto.setMissionId(ecardPo.getMissionId());
            ecardDto.setAreaId(ecardPo.getAreaId());
            ecardDto.setStartTime(ecardPo.getStartTime());
            ecardDto.setEndTime(ecardPo.getEndTime());
            ecardDto.setStat(ecardPo.getStat());
            ecardDto.setIsLocked(ecardPo.getIsLocked());
            ecardDto.setIsVirtual(ecardPo.getIsVirtual());
            ecardDto.setFromUserId(ecardPo.getFromUserId());
            ecardDto.setFromOrderId(ecardPo.getFromOrderId());
            ecardDto.setSendType(ecardPo.getSendType());
            ecardDto.setAddTime(Math.toIntExact(ecardPo.getAddTime()));
            ecardDto.setInvalidTime(Math.toIntExact(ecardPo.getInvalidTime()));
            ecardDto.setActiveTime(Math.toIntExact(ecardPo.getActiveTime()));
            ecardDto.setBindTime(Math.toIntExact(ecardPo.getBindTime()));
            ecardDto.setDelayTimes(ecardPo.getDelayTimes());
            ecardDto.setLastUpdateTime(ecardPo.getLastUpdateTime());
            ecardDto.setIsCasual(ecardPo.getIsCasual());

            ecardDtos.add(ecardDto);
        }

        return ecardDtos;
    }
}
