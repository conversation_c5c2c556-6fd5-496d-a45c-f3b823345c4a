package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.mapper;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.param.CarPointsBlackSsuParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBlackSsuPo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/6
 */
@Mapper
public interface CarPointsBlackSsuMapper {

    @Select("<script>" +
            "select * from car_points_black_ssu " +
            "<where>" +
            "<if test='param.ssu != null and param.ssu != 0'>ssu = #{param.ssu} </if>" +
            "<if test='param.deleteStatus != null'>and delete_status=#{param.deleteStatus} </if>" +
            "</where>" +
            "<if test='param.limit != null'>limit #{param.limit} </if>" +
            "<if test='param.offset != null'>offset #{param.offset} </if>" +
            "</script>")
    List<CarPointsBlackSsuPo> findByParam(@Param("param") CarPointsBlackSsuParam param);

    @Select("<script>" +
            "select COUNT(1) from car_points_black_ssu " +
            "<where>" +
            "<if test='param.ssu != null and param.ssu != 0'>ssu = #{param.ssu} </if>" +
            "<if test='param.deleteStatus != null'>and delete_status=#{param.deleteStatus} </if>" +
            "</where>" +
            "</script>")
    Integer selectTotal(@Param("param") CarPointsBlackSsuParam param);

    @Update("update car_points_black_ssu set delete_status = 1 where ssu = #{ssuId}")
    int deleteBySsuId(@Param("ssuId") Long ssuId);

    @Select("<script>" +
            "select * from car_points_black_ssu " +
            "where ssu in <foreach collection='ssuIdList' item='ssuId' index='index' open='(' close=')' separator=','>#{ssuId}</foreach>" +
            "</script>")
    List<CarPointsBlackSsuPo> findBySsu(@Param("ssuIdList") List<Long> ssuIdList);

    @Update("<script>" +
            "update car_points_black_ssu set delete_status = #{deleteStatus} " +
            "where ssu in <foreach collection='ssuIdList' item='ssuId' index='index' open='(' close=')' separator=','>#{ssuId}</foreach>" +
            "</script>")
    int updateDeleteStatus(@Param("ssuIdList") List<Long> ssuIdList, @Param("deleteStatus") Integer deleteStatus);


    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("<script>" +
            "insert into car_points_black_ssu(ssu, create_user, delete_status, create_time, update_time) " +
            "values " +
            "<foreach collection='carPointsBlackSsuPos' item='carPointsBlackSsuPo' separator=','>" +
            "(#{carPointsBlackSsuPo.ssu}, #{carPointsBlackSsuPo.createUser}, #{carPointsBlackSsuPo.deleteStatus}, now(), now())" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("carPointsBlackSsuPos") List<CarPointsBlackSsuPo> carPointsBlackSsuPos);
}
