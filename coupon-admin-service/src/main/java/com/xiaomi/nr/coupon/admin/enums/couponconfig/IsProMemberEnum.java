package com.xiaomi.nr.coupon.admin.enums.couponconfig;

import lombok.Getter;

/**
 * 是否会员 枚举
 *
 * <AUTHOR>
 */
@Getter
public enum IsProMemberEnum {

    /**
     * 是
     */
    Yes(true, "1", "是"),

    /**
     * 否
     */
    No(false, "2", "否");

    private final Boolean redisValue;
    private final String mysqlValue;
    private final String name;

    IsProMemberEnum(Boolean redisValue, String mysqlValue, String name) {
        this.redisValue = redisValue;
        this.mysqlValue = mysqlValue;
        this.name = name;
    }
}

