package com.xiaomi.nr.coupon.admin.infrastruture.notify.rocketmq.model;

import lombok.Data;

import java.util.List;

@Data
public class PostOfficeMsg {

    /**
     * 业务唯一标识，为了消息去重使用
     */
    private String unikey;

    /**
     * 场景id
     */
    private int sceneId;

    /**
     * 消息体
     */
    private List<PostOfficeMsgContent>  content;

    /**
     * 消息创建时间
     */
    private String ctime=String.valueOf(System.currentTimeMillis());

    /**
     * appid，在接入消息配置平台时创建的
     */
    private String appId="nr_coupon";

    /**
     * 消息类别1-即时消息，2-延时消息
     */
    private int type=1;

}
