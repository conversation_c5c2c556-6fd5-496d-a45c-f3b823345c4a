package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.posthandler;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RedisRefreshPostHandler extends BaseCouponPostHandler {

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    private final List<Integer> matchBizPlatform = Lists.newArrayList(
            BizPlatformEnum.AUTO_TEST.getCode(),
            BizPlatformEnum.RETAIL.getCode(),
            BizPlatformEnum.CAR.getCode(),
            BizPlatformEnum.CAR_AFTER_SALE.getCode(),
            BizPlatformEnum.CAR_SHOP.getCode()
    );

    @Override
    public void createPost(CouponCreateEvent event) throws Exception {
        CouponConfigPO couponConfigPO;
        if (event.isCompensateFlag()) {
            couponConfigPO = couponConfigRepository.searchCouponById(event.getData().getId());
        } else {
            couponConfigPO = event.getData();
        }
        // 补偿时，eventContext DataPreparePostHandler已采用表里最新商品配置
        couponConfigRepository.updateCouponConfigCache(couponConfigPO, event.getEventContext());
    }

    @Override
    public void updatePost(CouponUpdateEvent event) throws Exception {
        CouponConfigPO couponConfigPO;
        if (event.isCompensateFlag()) {
            couponConfigPO = couponConfigRepository.searchCouponById(event.getData().getId());
        } else {
            couponConfigPO = event.getData();
            couponConfigPO.setUpdateTime(TimeUtil.getNowTimestamp());
        }
        // 补偿时，eventContext DataPreparePostHandler已采用表里最新商品配置
        couponConfigRepository.updateCouponConfigCache(couponConfigPO, event.getEventContext());
    }


    @Override
    public void updateStatusPost(CouponUpdateStatusEvent event) throws Exception {
        CouponConfigPO couponConfigPO;
        if (event.isCompensateFlag()) {
            couponConfigPO = couponConfigRepository.searchCouponById(event.getData().getId());
        } else {
            couponConfigPO = event.getData();
            couponConfigPO.setUpdateTime(TimeUtil.getNowTimestamp());
        }
        couponConfigRepository.updateCouponBaseInfoCache(couponConfigPO);
    }


    @Override
    public int order() {
        return 1;
    }

    @Override
    public Boolean bizPlatformMatch(Integer bizPlatform) {
        return matchBizPlatform.contains(bizPlatform);
    }
}
