package com.xiaomi.nr.coupon.admin.domain.pointadmin.event.handler;

import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.PointBatchStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.PointsConfigLogOptTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CarPointsConfigLogRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsConfigLogPo;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/8
 */
@Slf4j
@Component
public class PointBatchOptLogPostHandler extends BasePointBatchPostHandler{

    @Resource
    private CarPointsConfigLogRepository carPointsConfigLogRepository;

    @Override
    public void createPost(PointBatchConfigCreateEvent event) throws Exception {
        CarPointsBatchConfigPo newPo = event.getData();
        insertPointsConfigLogPo(null, newPo, newPo.getCreator(), PointsConfigLogOptTypeEnum.CREATE);
    }

    @Override
    public void updatePost(PointBatchConfigUpdateEvent event) throws Exception {
        insertPointsConfigLogPo(event.getOldPo(), event.getData(), event.getOperator(), PointsConfigLogOptTypeEnum.UPDATE);
    }

    @Override
    public void updateStatus(PointBatchConfigUpdateStatusEvent event) throws Exception{
        CarPointsBatchConfigPo newPo = event.getData();
        PointBatchStatusEnum pointBatchStatusEnum = PointBatchStatusEnum.valueOf(newPo.getStatus());
        PointsConfigLogOptTypeEnum optType;
        switch (pointBatchStatusEnum) {
            case ONLINE:
                optType = PointsConfigLogOptTypeEnum.ONLINE;
                break;
            case OFFLINE:
                optType = PointsConfigLogOptTypeEnum.OFFLINE;
                break;
            default:
                throw ExceptionHelper.create(ErrCode.POINT, "错误的操作类型");
        }
        insertPointsConfigLogPo(event.getOldPo(), newPo, event.getOperator(), optType);

    }

    private void insertPointsConfigLogPo(CarPointsBatchConfigPo oldPo, CarPointsBatchConfigPo newPo, String operator, PointsConfigLogOptTypeEnum optType) throws Exception {
        // 1、构造po
        CarPointsConfigLogPo pointsConfigLogPo = new CarPointsConfigLogPo();
        pointsConfigLogPo.setBatchId(newPo.getId());
        pointsConfigLogPo.setOptType(optType.getCode());
        pointsConfigLogPo.setOperator(operator);
        if (optType == PointsConfigLogOptTypeEnum.UPDATE) {
            Map<String, Object> contentMap = new HashMap<>();
            contentMap.put("new", CompressUtil.compress(Objects.requireNonNull(GsonUtil.toJson(newPo))));
            contentMap.put("old", CompressUtil.compress(Objects.requireNonNull(GsonUtil.toJson(oldPo))));
            pointsConfigLogPo.setContent(GsonUtil.toJson(contentMap));
        }
        // 2、日志入库
        carPointsConfigLogRepository.insert(pointsConfigLogPo);
    }
}
