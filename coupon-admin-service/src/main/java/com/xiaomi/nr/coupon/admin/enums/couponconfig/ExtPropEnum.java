package com.xiaomi.nr.coupon.admin.enums.couponconfig;

import lombok.Getter;

@Getter
public enum ExtPropEnum {
    FreeShipping(1, "postFree","可包邮"),
    Transfer(2, "share","可转赠"),
    GivenArea(3, "area","指定地区"),
    ProMember(4, "proMember","pro会员"),
    SpecialStore(5, "specialStore","专店专用"),
    PublicPromotion(6, "publicPromotion","公开推广"),
    CheckoutStage(7, "checkoutStage", "抵扣金额类型"),
    DisplayDate(8, "displayDate", "有效期是否展示"),

    ;

    private final int value;
    private final String field;
    private final String name;

    ExtPropEnum(int value,String field, String name) {
        this.value = value;
        this.field = field;
        this.name = name;
    }

    public static ExtPropEnum getByValue(int value) {
        ExtPropEnum[] values = ExtPropEnum.values();
        for (ExtPropEnum item : values) {
            if (item.getValue()==value) {
                return item;
            }
        }
        return null;
    }

    public static ExtPropEnum getByField(String field) {
        ExtPropEnum[] values = ExtPropEnum.values();
        for (ExtPropEnum item : values) {
            if (item.getField().equals(field)) {
                return item;
            }
        }
        return null;
    }

}
