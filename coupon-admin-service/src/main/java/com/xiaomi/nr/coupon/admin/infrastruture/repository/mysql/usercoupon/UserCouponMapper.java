package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.SearchUserCouponListParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.UserCouponPO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户优惠券mapper
 *
 * <AUTHOR>
 */
@Mapper
@Component
public interface UserCouponMapper {

    String SELECT_PARAMS = " id, user_id, type_id, activity_id, start_time, end_time,days, stat, order_id, use_time, " +
            "expire_time, is_pass, admin_id, admin_name,add_time, send_type, from_order_id, replace_money, invalid_time," +
            "last_update_time, offline, reduce_express, parent_id, send_channel, biz_platform ";

    /**
     * 查用户券总数
     */
    @Select("<script>select count(1) from tb_coupon where 1=1 " +
            "<if test='uid>0'> and user_id=#{uid}</if>" +
            "<if test='configIds!=null and configIds.size()>0'> and type_id in <foreach collection='configIds' item='configId' index='index' open='(' close=')' separator=','>#{configId}</foreach> </if>" +
            "<if test='couponStatus ==\"unused\"'> and stat='unused' and end_time &gt; unix_timestamp(now()) </if>" +
            "<if test='couponStatus == \"expired\"'> and stat in ('unused','expired') and end_time &lt;= unix_timestamp(now()) </if>" +
            "<if test='couponStatus != null and couponStatus != \"unused\" and couponStatus != \"expired\"'> and stat=#{couponStatus} </if>" +
            "<if test='couponId!=null and couponId>0'> and id=#{couponId} </if>" +
            "</script>")
    Long selectCount(SearchUserCouponListParam param);


    @Select("<script>select " + SELECT_PARAMS + " from tb_coupon where 1=1" +
            "<if test='uid>0'> and user_id=#{uid}</if>" +
            "<if test='configIds!=null and configIds.size()>0'> and type_id in <foreach collection='configIds' item='configId' index='index' open='(' close=')' separator=','>#{configId}</foreach> </if>" +
            "<if test='couponStatus ==\"unused\"'> and stat='unused' and end_time &gt; unix_timestamp(now()) </if>" +
            "<if test='couponStatus == \"expired\"'> and stat in ('unused','expired') and end_time &lt;= unix_timestamp(now()) </if>" +
            "<if test='couponStatus != null and couponStatus != \"unused\" and couponStatus != \"expired\"'> and stat=#{couponStatus} </if>" +
            "<if test='couponId!=null and couponId>0'> and id=#{couponId} </if>" +
            " order by  ${orderBy}  ${orderDirection} " +
            " limit #{offset}, #{limit}" +
            "</script>")
    List<UserCouponPO> selectList(SearchUserCouponListParam param);


    @Update("<script> update tb_coupon set stat=#{newCouponStatus},invalid_time=#{invalidTime} where id =#{couponId} and user_id=#{uid} and stat='unused' " +
            "</script>")
    Long destroyCoupon(@Param("uid") long uid, @Param("couponId") long couponId, @Param("newCouponStatus") String newCouponStatus, @Param("invalidTime") long invalidTime);


    @Select("<script> select " + SELECT_PARAMS + " from tb_coupon where user_id=#{uid} and id =#{couponId} " +
            "</script>")
    UserCouponPO selectByCouponId(@Param("uid") long uid, @Param("couponId") long couponId);

    /**
     * 根据券id查询用户券信息
     *
     * @param couponId
     * @return
     */
    @Select("<script> " +
            "select id,user_id,type_id,add_time,start_time,end_time,stat,order_id,use_time,parent_id,days,send_type,offline,activity_id,extend_info " +
            " from tb_coupon " +
            "where id =#{couponId}" +
            "</script>")
    UserCouponPO selectInfoById(@Param("couponId") long couponId);


    /**
     * 根据ID列表批量获取优惠券信息
     *
     * @param idList ID列表
     * @param limit  查询结果的限制条数
     * @param offset 查询结果的偏移量
     * @return 优惠券信息列表
     */
    @Select("<script>" +
            "select * from tb_coupon where id in <foreach item='id' index='index' collection='idList' open='(' separator=',' close=')'>#{id}</foreach>" +
            "order by id limit #{limit} offset #{offset}" +
            "</script>")
    List<UserCouponPO> batchGetById(@Param("idList") List<String> idList, @Param("limit") int limit, @Param("offset") int offset);

    /**
     * 删除优惠券数据 自动化测试
     * @param uid
     * @param couponId
     * @return
     */
    @Delete("<script>delete from tb_coupon where user_id=#{uid} <if test='couponId != null and couponId > 0'> and id =#{couponId}</if></script>")
    Long deleteCoupon(@Param("uid") Long uid, @Param("couponId") Long couponId);

    /**
     * 根据券配置id获取用户id
     * @param couponTypes 券配置id
     * @param limit 每页大小
     * @param offset 偏移量
     * @return 用户id
     */
    @Select("<script>select distinct user_id from tb_coupon where type_id in " +
            "<foreach item='typeId' index='index' collection='couponTypes' open='(' separator=',' close=')'>#{typeId}</foreach> " +
            "and add_time > #{lastTime} " +
            "order by add_time " +
            "limit #{limit} offset #{offset}</script>")
    List<Long> getUserIdByCouponType(@Param("couponTypes") List<Long> couponTypes, @Param("limit") int limit, @Param("offset") int offset, @Param("lastTime") Long lastTime);

    /**
     * 根据券配置id获取最新发券时间戳
     * @param couponTypes
     * @param lastTime
     * @return
     */
    @Select("<script>select max(add_time) from tb_coupon where type_id in " +
            "<foreach item='typeId' index='index' collection='couponTypes' open='(' separator=',' close=')'>#{typeId}</foreach> " +
            "and add_time >= #{lastTime}</script>")
    Long getMaxAddTime(@Param("couponTypes") List<Long> couponTypes, @Param("lastTime") Long lastTime);
}
