package com.xiaomi.nr.coupon.admin.domain.pointadmin;

import cn.hutool.core.util.ObjectUtil;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointSceneDto;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointSceneOpenRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointSceneRequest;
import com.xiaomi.nr.coupon.admin.enums.scene.CreateOrUpdateSendSceneEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CarPointParentSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CarPointSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsParentScenePo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsScenePo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.MessageDigest;
import java.util.*;

/**
 * @Description: 优惠券场景
 * @Date: 2022.03.03 18:54
 */
@Service
public class PointSceneService {

    @Autowired
    private CarPointSceneRepository pointSceneRepository;

    @Autowired
    private CarPointParentSceneRepository PointParentSceneRepository;

    private static String[] SHUFFLE_ARRAY = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R",
            "S", "T", "U", "V", "W", "X", "Y", "Z", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s",
            "t", "u", "v", "w", "x", "y", "z", "1", "2", "3", "4", "5", "6", "7", "8", "9", "0"};
    private static final int CODE_LENGTH = 5;
    private static final String[] CHARACTERS = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R",
            "S", "T", "U", "V", "W", "X", "Y", "Z","1", "2", "3", "4", "5", "6", "7", "8", "9", "0"};

    /**
     * 创建或修改优惠券场景
     *
     * @param request
     */
    public void createOrUpdateScene(PointSceneRequest request) throws Exception {
        checkoutReq(request);
        if (request.getType() == CreateOrUpdateSendSceneEnum.UPDATE.getCode()) {
            checkUnEditableParam(request);
        }
        CarPointsScenePo pointsScenePo = convertToPO(request.getPointSceneDTO(), request.getType(), request.getOperator());
        if (request.getType() == CreateOrUpdateSendSceneEnum.CREATE.getCode()) {
            pointSceneRepository.insert(pointsScenePo);
        } else {
            pointSceneRepository.update(pointsScenePo);
        }
    }

    /**
     * 创建或修改优惠券场景
     *
     * @param request
     */
    @Transactional(transactionManager = "nrPointAdminTransactionManager",rollbackFor = Exception.class)
    public void createOrUpdateSceneOpen(PointSceneOpenRequest request) throws Exception {

        //入参校验
        checkoutReq(request);

        if (request.getType() == CreateOrUpdateSendSceneEnum.CREATE.getCode()) {
            if (ObjectUtil.isNotNull(request.getCreateParentScene()) && request.getCreateParentScene()) {
                // 创建一级场景
                CarPointsParentScenePo pointsParentScenePo = concertToParentScenePO(request.getParentSceneName(), request.getOperator());
                PointParentSceneRepository.insert(pointsParentScenePo);

                // 获取一级场景的ID
                Long parentSceneId = pointsParentScenePo.getId();

                // 设置parentId字段
                request.setParentId(Math.toIntExact(parentSceneId));
            }
        }
        String content = createContent(request);

       CarPointsScenePo pointsScenePo = convertToPointsScenePO(request, content);
        if (request.getType() == CreateOrUpdateSendSceneEnum.CREATE.getCode()) {
            // 创建二级场景
            pointSceneRepository.insert(pointsScenePo);

        } else if (request.getType() == CreateOrUpdateSendSceneEnum.UPDATE.getCode()) {
            // 编辑二级场景
            pointSceneRepository.update(pointsScenePo);
        }
    }

    private static String createContent(PointSceneOpenRequest request) {
        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("appName", request.getAppName());
        contentMap.put("assignRecordDesc", request.getAssignRecordDesc());
        contentMap.put("expireRecordDesc", request.getExpireRecordDesc());
        contentMap.put("delayRecordDesc", request.getDelayRecordDesc());

        return GsonUtil.toJson(contentMap);
    }

    private CarPointsParentScenePo concertToParentScenePO(String parentSceneName, String operator) {
        CarPointsParentScenePo parentScenePo = new CarPointsParentScenePo();
        parentScenePo.setName(parentSceneName);
        parentScenePo.setCreator(operator);
        return parentScenePo;
    }

    private void checkoutParentScene(PointSceneOpenRequest request) throws Exception {
        if (request.getCreateParentScene()) {
            // 校验一级场景名称
            CarPointsParentScenePo parentScenePO = PointParentSceneRepository.searchParentSceneByName(request.getParentSceneName());
            if (parentScenePO != null) {
                throw ExceptionHelper.create(ErrCode.COUPON, "存在同名一级场景！");
            }
        }
    }

    private void checkoutReq(PointSceneRequest request) throws Exception {
        // 校验场景名称
        CouponScenePO scenePO = pointSceneRepository.searchSceneByName(request.getSceneName());
        if ((request.getType() == CreateOrUpdateSendSceneEnum.CREATE.getCode() && scenePO != null) ||
                (request.getType() == CreateOrUpdateSendSceneEnum.UPDATE.getCode() &&
                        scenePO != null && !scenePO.getId().equals(request.getSceneId()))) {
            throw ExceptionHelper.create(ErrCode.COUPON, "存在同名场景！");
        }
    }

    private void checkoutReq(PointSceneOpenRequest request) throws Exception {
        //创建一级场景时 校验一级场景名称
        if (ObjectUtil.isNotNull(request.getCreateParentScene()) && request.getCreateParentScene()) {
            CarPointsParentScenePo parentScenePO = PointParentSceneRepository.searchParentSceneByName(request.getParentSceneName());
            if (parentScenePO != null) {
                throw ExceptionHelper.create(ErrCode.COUPON, "存在同名一级场景！");
            }
        }
        // 校验场景名称
        CouponScenePO scenePO = pointSceneRepository.searchSceneByName(request.getSceneName());
        if ((request.getType() == CreateOrUpdateSendSceneEnum.CREATE.getCode() && scenePO != null) ||
                (request.getType() == CreateOrUpdateSendSceneEnum.UPDATE.getCode() &&
                        scenePO != null && !scenePO.getSceneCode().equals(request.getSceneCode()))) {
            throw ExceptionHelper.create(ErrCode.COUPON, "存在同名场景！");
        }
        //更新场景时 sceneId校验
        if (request.getType() == CreateOrUpdateSendSceneEnum.UPDATE.getCode()) {
            checkUnEditableParam(request);
        }
    }

    private CarPointsScenePo checkUnEditableParam(PointSceneRequest request) throws Exception {
        if (request.getSceneCode() == null) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "场景编码不能为空");
        }
        CarPointsScenePo carPointsScenePo = pointSceneRepository.searchSceneBySceneCode(request.getSceneCode());
        if (carPointsScenePo == null) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "无效场景编码");
        }
        return carPointsScenePo;
    }

    private CarPointsScenePo convertToPO(PointSceneDto pointSceneDTO, Integer type, String operator) throws Exception {
        CarPointsScenePo carPointsScenePo = new CarPointsScenePo();

        carPointsScenePo.setName(pointSceneDTO.getName());
        carPointsScenePo.setSceneDesc(pointSceneDTO.getSceneDesc());
        carPointsScenePo.setParentId(pointSceneDTO.getParentId());
        if (type == CreateOrUpdateSendSceneEnum.CREATE.getCode()) {
            carPointsScenePo.setSceneCode(StringUtils.isEmpty(pointSceneDTO.getSceneCode())?
                    generateRandomCode() : pointSceneDTO.getSceneCode());
            carPointsScenePo.setStatus(SceneStatusEnum.OFFLINE.getCode());
            carPointsScenePo.setCreator(operator);
        } else {
            carPointsScenePo.setId(pointSceneDTO.getId());
            carPointsScenePo.setModifier(operator);
        }
        return carPointsScenePo;
    }

    private CarPointsScenePo convertToPointsScenePO(PointSceneRequest request, String content) throws Exception {
        CarPointsScenePo carPointsScenePo = new CarPointsScenePo();

        carPointsScenePo.setName(request.getSceneName());
        carPointsScenePo.setSceneDesc(StringUtils.isEmpty(request.getSceneDesc()) ? "" : request.getSceneDesc());
        carPointsScenePo.setParentId(request.getParentId());
        carPointsScenePo.setContent(content);
        carPointsScenePo.setAssignMode(StringUtils.isEmpty(request.getAssignMode()) ? "1,2" : request.getAssignMode());
        if (request.getType() == CreateOrUpdateSendSceneEnum.CREATE.getCode()) {
            carPointsScenePo.setSceneCode(StringUtils.isEmpty(request.getSceneCode()) ?
                    generateRandomCode() : request.getSceneCode());
            // 生成随机场景枚举代码
            carPointsScenePo.setEnumCode(generateEnumCode());
            carPointsScenePo.setStatus(SceneStatusEnum.ONLINE.getCode());
            carPointsScenePo.setCreator(request.getOperator());
        } else if (request.getType() == CreateOrUpdateSendSceneEnum.UPDATE.getCode()) {
            carPointsScenePo.setSceneCode(request.getSceneCode());
            carPointsScenePo.setId(request.getSceneId());
            carPointsScenePo.setModifier(request.getOperator());
        }
        return carPointsScenePo;
    }

    public String generateRandomCode() throws Exception {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        List<String> shuffleArry = new ArrayList<>(SHUFFLE_ARRAY.length);
        Collections.addAll(shuffleArry, SHUFFLE_ARRAY);
        Collections.shuffle(shuffleArry);
        String shuffleStr = org.apache.commons.lang.StringUtils.join(shuffleArry, "");
        byte[] byteArray = md5.digest(shuffleStr.getBytes());
        StringBuffer md5StrBuff = new StringBuffer();
        for (int i = 0; i < byteArray.length; i++) {
            if (Integer.toHexString(0xFF & byteArray[i]).length() == 1) {
                md5StrBuff.append("0").append(Integer.toHexString(0xFF & byteArray[i]));
            } else {
                md5StrBuff.append(Integer.toHexString(0xFF & byteArray[i]));
            }
        }
        return md5StrBuff.toString().toUpperCase();
    }

    private String generateEnumCode() throws Exception {
        // 查询当前所有enumCode，转换为Set以提高查找效率
        Set<String> existingEnumCodes = new HashSet<>(pointSceneRepository.searchAllEnumCode());
        String enumCode;
        do {
            enumCode = generateRandomEnumCode();
        } while (existingEnumCodes.contains(enumCode));
        return enumCode;
    }

    private String generateRandomEnumCode() {
        Random random = new Random();
        StringBuilder sb = new StringBuilder(CODE_LENGTH);
        // 打乱初始数组
        List<String> charList = new ArrayList<>(CHARACTERS.length);
        Collections.addAll(charList, CHARACTERS);
        Collections.shuffle(charList);
        for (int i = 0; i < CODE_LENGTH; i++) {
            int index = random.nextInt(charList.size());
            sb.append(charList.get(index));
        }
        return sb.toString();
    }

}

