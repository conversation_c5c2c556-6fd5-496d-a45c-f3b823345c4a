package com.xiaomi.nr.coupon.admin.infrastruture.repository;

import com.google.common.collect.Maps;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.FillCouponTaskMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.*;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class CouponTaskRepository {
    @Autowired
    private FillCouponTaskMapper fillCouponTaskMapper;


    public List<FillCouponTaskPO> searchTaskList(SearchTaskParam searchTaskParam){
        return fillCouponTaskMapper.getFillCouponTaskByParam(searchTaskParam);
    }

    public List<FillCouponTaskPO> searchTaskPageList(CodeTaskSearchParam codeTaskSearchParam, int offset, int limit) {
        return fillCouponTaskMapper.getTaskPageByParam(codeTaskSearchParam, offset, limit);
    }

    public Integer searchTaskCount(CodeTaskSearchParam codeTaskSearchParam) {
        return fillCouponTaskMapper.getCouponTaskCountByParam(codeTaskSearchParam);
    }

    public List<FillCouponTaskPO>  searchTaskByParentIds(List<Long> ids){
        return fillCouponTaskMapper.getFillCouponTaskByParentIds(ids);
    }

    /**
     * 获取等待任务
     * @param thresholdTime 心跳阀值
     * @return TaskIdParentIdParamPO
     */
    public List<TaskIdParentIdParamPO> getReadyTask(long thresholdTime){
        return fillCouponTaskMapper.getReadyTask(thresholdTime);
    }

    /**
     * 获取运行中的任务数
     * @param thresholdTime 心跳阀值
     * @return 运行中的任务数
     */
    public Integer getRunningCount(long thresholdTime){
        return fillCouponTaskMapper.getRunningCount(thresholdTime);
    }

    /**
     * 创建灌券任务信息
     * @param fillCouponTaskPO 任务实体
     * @return 写入条数
     */
    public Integer insert(FillCouponTaskPO fillCouponTaskPO){
        return fillCouponTaskMapper.insert(fillCouponTaskPO);
    }

    /**
     * 根据任务id获取任务信息
     * @param taskId 任务id
     * @return 任务信息
     */
    public FillCouponTaskPO getDetailTaskById(long taskId){
        return fillCouponTaskMapper.getDetailTaskById(taskId);
    }

    /**
     * 根据父任务id获取子任务信息
     * @param parentId 父任务id
     * @return 子任务信息
     */
    public List<FillCouponTaskPO> getCouponTaskByParentId(long parentId){
        return fillCouponTaskMapper.getTaskByParentId(parentId);
    }

    /**
     * 修改灌券任务状态
     * @param status 任务状态
     * @return int
     */
    public Integer updateTaskStatusById(int status, long taskId){
        return fillCouponTaskMapper.updateTaskStatusById(status, taskId);
    }


    /**
     * 根据券配置id获取正在运行中的任务
     * @param configId 券配置id
     * @return 任务id
     */
    public List<Long> getTaskByConfigId(long configId){
        return fillCouponTaskMapper.getTaskByConfigId(configId);
    }

    /**
     * 根据券任务id获取灌券参数信息
     * @param taskId 券配置id
     * @return param
     */
    public String getTaskParamById(long taskId){
        return fillCouponTaskMapper.getTaskParamById(taskId);
    }

    public boolean checkPreTask(long configId){
        return CommonConstant.ONE_INT == fillCouponTaskMapper.getPreTask(configId);
    }

    public void updateTaskStatusByCoupon(long configId){
        fillCouponTaskMapper.updateTaskStatusByCoupon(configId);
    }


    public Map<Long, Long> getCodeTaskResult(List<Long> configIds){
        if(CollectionUtils.isEmpty(configIds)){
            return Collections.emptyMap();
        }

        List<FillCouponTaskPO> taskPOS = fillCouponTaskMapper.getCodeTaskResult(configIds);

        Map<Long, Long> resultMap = Maps.newHashMap();
        for(FillCouponTaskPO po : taskPOS){

            if(StringUtils.isEmpty(po.getResult())){
                resultMap.put(po.getConfigId(), CommonConstant.ZERO_LONG);
                continue;
            }

            resultMap.put(po.getConfigId(), GsonUtil.fromJson(po.getResult(), ResultOutPut.class).getSuccessCount());
        }

        return resultMap;
    }

}
