package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SearchConfigParam implements Serializable {
    private static final long serialVersionUID = 8741304516698345964L;

    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    private Integer couponType;
    /**
     * 优惠券id
     */
    private Long id;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠类型 (1:满减, 2:满折, 3:N元券, 4:立减)
     */
    private Integer promotionType;

    /**
     * 开始领取时间
     */
    private Long startFetchTime;

    /**
     * 结束领取时间
     */
    private Long endFetchTime;

    /**
     * 使用渠道
     */
    private List<Integer> useChannel;

    /**
     * 投放场景
     */
    private String sendScene;

    /**
     * 状态 (1:上线, 2:下线, 3:终止)
     */
    private Integer status;

    /**
     * 状态 (1:未开始, 2:进行中, 3:已结束, 4:已终止)
     */
    private Integer timeStatus;

    /**
     * 优惠券生效开始时间
     */
    private Long startUseTime;

    /**
     * 优惠券生效结束时间
     */
    private Long endUseTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序顺序 (desc倒序, asc顺序)
     */
    private String orderDirection;

    /**
     * 当前页码
     */
    private Integer pageNo = 1;

    /**
     * 页面大小(数据条数)
     */
    private Integer pageSize = 10;

    /**
     * 商品级别（sku|package）
     */
    private String level;

    /**
     * 优惠券所属业务平台
     */
    private Integer bizPlatform;

    /**
     * 优惠券所属业务平台
     */
    private List<Integer> bizPlatformList;

    /**
     * 服务券类型
     */
    private Integer serviceType;

    /**
     * 限领类型 1限领 2不限领
     */
    private Integer fetchLimitType;

    /**
     * 使用次数限制，1-限制，2-不限制
     */
    private Integer timesLimit;

}
