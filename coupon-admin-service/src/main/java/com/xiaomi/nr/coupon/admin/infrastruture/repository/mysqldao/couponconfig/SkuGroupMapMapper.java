package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig.po.SkuGroupMapPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * sku/套装与品类关联配置mapper
 *
 * <AUTHOR>
 */
@Mapper
@Component
public interface SkuGroupMapMapper {
    /**
     * 配置关系列表
     *
     * @param startSize long
     * @param pageSize  long
     * @return ArrayList<SkuGroupMap>
     */
    @Select("select id, sku, group_id, department_id, area_id, add_time" +
            " from v3_sku_group_map" +
            " order by add_time asc" +
            " limit #{startSize,jdbcType=BIGINT},#{pageSize,jdbcType=BIGINT}")
    ArrayList<SkuGroupMapPo> getList(@Param("startSize") long startSize, @Param("pageSize") long pageSize);

    /**
     * 配置关系列表
     *
     * @return ArrayList<SkuGroupMap>
     */
    @Select("<script>" +
            " select sku, group_id " +
            " from v3_sku_group_map " +
            " and group_id in <foreach item='groupId' index='index' collection='ids' open='(' separator=',' close=')'>#{groupId}</foreach>" +
            "</script>")
    List<SkuGroupMapPo> getListByGroupId(@Param("ids") List<Long> ids);
}
