package com.xiaomi.nr.coupon.admin.application.dubbo.convert;

import com.xiaomi.goods.gis.dto.category.CategoryDTO;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoDTO;
import com.xiaomi.goods.gms.dto.batch.BatchedInfoDto;
import com.xiaomi.goods.gms.dto.category.BusinessCategoryDto;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.PackageInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.SkuInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.SsuInfoVO;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsItemTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.goods.tob.dto.response.sale.SaleStateDTO;
import com.xiaomi.nr.goods.tob.dto.response.ssu.SsuDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 商品信息转换类
 */
@Service
@Slf4j
public class GoodsVoConvert {


    /**
     * 商品信息返回信息转换
     *
     * @param skuInfoDtoList skuInfoDtoList
     * @return GmsGoodsListDto
     */
    public List<SkuInfoVO> convert2SkuInfoVO(List<SkuInfoDto> skuInfoDtoList){
        if(CollectionUtils.isEmpty(skuInfoDtoList)){
            return Collections.emptyList();
        }

        List<SkuInfoVO> skuInfoVOList = new ArrayList<>();
        for(SkuInfoDto skuDto : skuInfoDtoList){
            SkuInfoVO skuInfoVO = new SkuInfoVO();
            skuInfoVO.setProductId(skuDto.getProductId());
            skuInfoVO.setGoodsId(skuDto.getGoodsId());
            skuInfoVO.setSku(skuDto.getSku());
            skuInfoVO.setGoodsName(skuDto.getGoodsName());

            skuInfoVO.setOnSaleOnline(skuDto.getOnSaleOnline());
            skuInfoVO.setOnSaleOffline(skuDto.getOnSaleOffline());
            skuInfoVO.setOnShelfOnline(skuDto.getOnShelfOnline());
            skuInfoVO.setOnShelfOffline(skuDto.getOnShelfOffline());
            skuInfoVO.setSqOnSale(skuDto.getSqOnSale());
            skuInfoVO.setSqOnShelf(skuDto.getSqOnShelf());

            BusinessCategoryDto category = skuDto.getCategory();
            if(!Objects.isNull(category)){
                skuInfoVO.setFirstCategoryId(category.getFirstCategoryId());
                skuInfoVO.setFirstCategoryName(category.getFirstCategoryName());
                skuInfoVO.setSecondCategoryId(category.getSecondCategoryId());
                skuInfoVO.setSecondCategoryName(category.getSecondCategoryName());
                skuInfoVO.setThirdCategoryId(category.getThirdCategoryId());
                skuInfoVO.setThirdCategoryName(category.getThirdCategoryName());
            }

            skuInfoVO.setMarketPrice(skuDto.getMarketPrice());

            skuInfoVOList.add(skuInfoVO);
        }
        return skuInfoVOList;
    }

    /**
     * 商品信息返回信息转换
     *
     * @param goodsMultiInfoDTOMap goodsMultiInfoDTOMap
     * @return List<SkuInfoVO>
     */
    public List<SkuInfoVO> convert2SkuInfoVO(Map<Long, GoodsMultiInfoDTO> goodsMultiInfoDTOMap){
        if(MapUtils.isEmpty(goodsMultiInfoDTOMap)){
            return Collections.emptyList();
        }

        List<SkuInfoVO> skuInfoVOList = new ArrayList<>();
        for(GoodsMultiInfoDTO dto : goodsMultiInfoDTOMap.values()){
            SkuInfoVO skuInfoVO = new SkuInfoVO();
            skuInfoVO.setProductId(dto.getProductId());
            skuInfoVO.setGoodsId(dto.getGoodsId());
            skuInfoVO.setSku(dto.getSku());
            skuInfoVO.setGoodsName(dto.getGoodsName());

            skuInfoVO.setOnSaleOnline(dto.getSale() ? 1 : 0);
            skuInfoVO.setOnSaleOffline(dto.getSaleMihome() ? 1 : 0);
            // todo auth
//            skuInfoVO.setSqOnSale(dto.getSaleMihome() ? 1 : 0);
            skuInfoVO.setOnSaleCarShop(dto.getSaleCarShop() ? 1 : 0);

            CategoryDTO category = dto.getProductBase().getBackgroundCategory();
            if(Objects.nonNull(category)){
                skuInfoVO.setFirstCategoryId(category.getFirstCategoryId());
                skuInfoVO.setFirstCategoryName(category.getFirstCategoryName());
                skuInfoVO.setSecondCategoryId(category.getSecondCategoryId());
                skuInfoVO.setSecondCategoryName(category.getSecondCategoryName());
                skuInfoVO.setThirdCategoryId(category.getThirdCategoryId());
                skuInfoVO.setThirdCategoryName(category.getThirdCategoryName());
            }

            skuInfoVO.setMarketPrice(Optional.ofNullable(dto.getMarketPrice()).orElse(0L));

            skuInfoVOList.add(skuInfoVO);
        }
        return skuInfoVOList;
    }

    /**
     * 商品信息返回信息转换
     *
     * @param batchedInfoDtoList batchedInfoDtoList
     * @return GmsGoodsListDto
     */
    public List<PackageInfoVO> convert2PackageDto(List<BatchedInfoDto> batchedInfoDtoList){
        if(CollectionUtils.isEmpty(batchedInfoDtoList)){
            return Collections.emptyList();
        }

        List<PackageInfoVO> packageInfoVOList = new ArrayList<>();
        for(BatchedInfoDto batchedDto : batchedInfoDtoList){

            PackageInfoVO packageInfoVO = new PackageInfoVO();
            packageInfoVO.setBatchedId(batchedDto.getBatchedId());
            packageInfoVO.setProductId(batchedDto.getProductId());
            packageInfoVO.setBatchedName(batchedDto.getBatchedName());

            packageInfoVO.setOnSaleOnline(batchedDto.getOnSaleOnline());
            packageInfoVO.setOnSaleOffline(batchedDto.getOnSaleOffline());
            packageInfoVO.setOnShelfOnline(batchedDto.getOnShelfOnline());
            packageInfoVO.setOnShelfOffline(batchedDto.getOnShelfOffline());
            packageInfoVO.setSqOnSale(batchedDto.getSqOnSale());
            packageInfoVO.setSqOnShelf(batchedDto.getSqOnShelf());

            packageInfoVO.setMarketPrice(batchedDto.getMarketPriceMax());

            // 老套装
            packageInfoVO.setSsuType(GoodsItemTypeEnum.PACKAGE.getValue());

            packageInfoVOList.add(packageInfoVO);
        }
        return packageInfoVOList;
    }

    /**
     * 商品信息返回信息转换
     *
     * @param ssuDTOList ssuDTOList
     * @return SsuInfoVO
     */
    public List<SsuInfoVO> convert2SsuInfoVO(List<SsuDTO> ssuDTOList){

        if(CollectionUtils.isEmpty(ssuDTOList)){
            return Collections.emptyList();
        }

        List<SsuInfoVO> ssuInfoVOList = new ArrayList<>();
        for(SsuDTO ssuDTO : ssuDTOList){
            SsuInfoVO ssuInfoVO = new SsuInfoVO();
            BeanUtils.copyProperties(ssuDTO, ssuInfoVO);
            ssuInfoVO.setSsuName(ssuDTO.getName());
            ssuInfoVO.setSsuType(ssuDTO.getSubBizType());
            ssuInfoVO.setSaleState(Optional.ofNullable(ssuDTO.getSaleState()).map(SaleStateDTO::getCarSalesStatus).orElse(null));
            ssuInfoVOList.add(ssuInfoVO);
        }
        return ssuInfoVOList;
    }

    /**
     * 套装信息返回信息转换 (GIS接口)
     *
     * @param goodsMultiInfoList 货品信息
     * @return List<PackageInfoVO>
     */
    public List<PackageInfoVO> convert2PackageInfoVO(List<GoodsMultiInfoDTO> goodsMultiInfoList){
        if (CollectionUtils.isEmpty(goodsMultiInfoList)) {
            return Collections.emptyList();
        }
        List<PackageInfoVO> packageInfoVOList = new ArrayList<>();
        for (GoodsMultiInfoDTO goodsMultiInfoDTO : goodsMultiInfoList) {
            PackageInfoVO infoVO = new PackageInfoVO();
            infoVO.setBatchedId(goodsMultiInfoDTO.getGoodsId());
            infoVO.setBatchedName(goodsMultiInfoDTO.getGoodsName());
            infoVO.setProductId(goodsMultiInfoDTO.getProductId());
            infoVO.setMarketPrice(goodsMultiInfoDTO.getMarketPrice());
            infoVO.setOnSaleOnline(goodsMultiInfoDTO.getSale() ? CommonConstant.ONE_INT : CommonConstant.ZERO_INT);
            infoVO.setOnSaleCarShop(goodsMultiInfoDTO.getSaleCarShop() ? CommonConstant.ONE_INT : CommonConstant.ZERO_INT);
            infoVO.setOnSaleOffline(goodsMultiInfoDTO.getSaleMihome() ? CommonConstant.ONE_INT : CommonConstant.ZERO_INT);
            // 新套装
            infoVO.setSsuType(GoodsItemTypeEnum.SUIT.getValue());
            packageInfoVOList.add(infoVO);
        }
        return packageInfoVOList;
    }
}
