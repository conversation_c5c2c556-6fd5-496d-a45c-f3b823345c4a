package com.xiaomi.nr.coupon.admin.enums.couponconfig;

import lombok.Getter;

import java.math.BigDecimal;

/**
 * 1:满元, 2:满件, 3:每满元, 4:每满件, 5:满元且满件
 */
@Getter
public enum BottomTypeEnum {
    OverYuan(1, "满元"),
    OverCount(2, "满件"),
    PerOverYuan(3, "每满元"),
    PerOverCount(4, "每满件"),
    /**
     * 新的券没有，兼容老券逻辑
     */
    OverYuanAndCount(5, "满元且满件"),

    ;

    private final int value;
    private final String name;

    BottomTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }


    public static BottomTypeEnum getByValue(int value) {
        BottomTypeEnum[] values = BottomTypeEnum.values();
        for (BottomTypeEnum item : values) {
            if (item.getValue()==value) {
                return item;
            }
        }
        return null;
    }


    public static String getDesc(BottomTypeEnum typeEnum,int bottomCount,long bottomPrice){
        BigDecimal bottomPriceYuan = new BigDecimal(bottomPrice).divide(BigDecimal.valueOf(100),2, BigDecimal.ROUND_HALF_UP);
        switch (typeEnum){
            case OverYuan:
               return  "满"+(bottomPriceYuan)+"元";
            case OverCount:
                return  "满"+(bottomCount)+"件";
            case PerOverYuan:
                return "每满"+(bottomPriceYuan)+"元";
            case PerOverCount:
                return  "每满"+(bottomCount)+"件";
            case OverYuanAndCount:
                return  "满"+(bottomPriceYuan)+"元且满"+bottomCount+"件";
        }
        return "";
    }



}
