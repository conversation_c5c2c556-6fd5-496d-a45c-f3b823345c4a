package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale.tools;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.ExtPropVO;
import com.xiaomi.nr.coupon.admin.api.enums.AnnualTypeEnum;
import com.xiaomi.nr.coupon.admin.api.enums.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale.ServiceCouponCheckTools;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale.ServiceCouponCheckToolsFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.TimesLimitEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @description 售后服务券-按需保养券-校验工具类
 * <AUTHOR>
 * @date 2024-12-26 12:53
*/
@Component
public class NeedMaintenanceCouponCheckTools extends ServiceCouponCheckTools {

    @PostConstruct
    public void init() {
        ServiceCouponCheckToolsFactory.register(CouponServiceTypeEnum.NEED_MAINTENANCE, this);
    }


    @Override
    public void goodsCheck(Map<Long, Integer> labourHourSsu, Map<Long, Integer> partsSsu) throws BizError {
        if (MapUtils.isEmpty(labourHourSsu) && MapUtils.isEmpty(partsSsu)) {
            // 工时ssu & 配件ssu 不能同时为空
            throw ExceptionHelper.create(ErrCode.COUPON, "工时ssu和配件ssu不能同时为空");
        }

        if (MapUtils.isNotEmpty(labourHourSsu)) {
            // 工时ssu数量只能为1
            boolean labourHourValid = labourHourSsu.values().stream().allMatch(value -> value == 1);
            if (!labourHourValid) {
                throw ExceptionHelper.create(ErrCode.COUPON, "工时ssu数量只能为1");
            }
        }

        if (MapUtils.isNotEmpty(partsSsu)) {
            // 配件ssu数量需大于0
            boolean partsValid = partsSsu.values().stream().allMatch(value -> value > 0);
            if (!partsValid) {
                throw ExceptionHelper.create(ErrCode.COUPON, "配件ssu数量需大于0");
            }
        }
    }

    @Override
    public void createSpecialCheck(CouponBaseInfo info) throws BizError {
        // 券类型校验
        couponTypeCheck(info);

        // 使用次数限制
        timesLimitCheck(info);
    }

    @Override
    public void updateSpecialCheck(CouponBaseInfo info) throws BizError {
        // 券类型校验
        couponTypeCheck(info);

        // 使用次数限制
        timesLimitCheck(info);
    }

    /**
     * 券类型校验
     *
     * @param info 券配置基础信息
     */
    public void couponTypeCheck(CouponBaseInfo info) throws BizError {
        CouponTypeEnum couponTypeEnum = CouponTypeEnum.getByValue(info.getCouponType());

        if (Objects.isNull(couponTypeEnum)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券类型不存在");
        }

        // 按需保养券，券类型必须为抵扣券
        if (!Objects.equals(couponTypeEnum, CouponTypeEnum.DEDUCTION)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券类型必须为抵扣券");
        }

        // 按需保养券，年度类型不能为空
        Integer annualType = Optional.ofNullable(info.getExtProp()).map(ExtPropVO::getAnnualType).orElse(null);
        if (Objects.isNull(annualType) || annualType == 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "按需保养券的单双年度类型不能为空");
        }
        AnnualTypeEnum annualTypeEnum = AnnualTypeEnum.getByValue(annualType);
        if (Objects.isNull(annualTypeEnum)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "按需保养券的单双年度类型不合法");
        }
    }

    /**
     * 使用次数校验
     *
     * @param info 券配置基础信息
     */
    public void timesLimitCheck(CouponBaseInfo info) throws BizError {
        TimesLimitEnum timesLimitEnum = TimesLimitEnum.valueOf(info.getTimesLimit());

        if (Objects.isNull(timesLimitEnum)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用次数限制参数异常");
        }

        // 按需保养券，使用次数限制为不限次
        if (!Objects.equals(timesLimitEnum, TimesLimitEnum.LIMIT)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用次数限制必须为限制");
        }
    }

}
