package com.xiaomi.nr.coupon.admin.infrastruture.repository;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.enums.usercoupon.UserCouponStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.carcoupon.CarCouponMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.UserCouponMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.UserCouponShareMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.SeachUserCouponListResult;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.SearchUserCouponListParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.UserCouponPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.UserCouponSharePO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.oceanbasedao.OceanBaseCouponMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.tidbdao.TidbCouponMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.tidbdao.po.CouponPo;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Component
public class UserCouponRepository {

    @Autowired
    private UserCouponMapper userCouponMapper;

    @Autowired
    private UserCouponShareMapper userCouponShareMapper;

    @Autowired
    private CarCouponMapper carCouponMapper;

    @Resource
    private TidbCouponMapper tidbCouponMapper;

    @Resource
    private OceanBaseCouponMapper oceanBaseCouponMapper;


    /**
     * 根据id查询
     *
     * @return
     */
    public SeachUserCouponListResult selectList(SearchUserCouponListParam param) {

        long count = 0L;
        List<UserCouponPO> couponPOList = Collections.emptyList();

        int bizPlatform = param.getBizPlatform();
        // 1、汽车售后服务券查询tb_car_coupon
        if (BizPlatformEnum.CAR_AFTER_SALE.getCode().equals(bizPlatform)) {
            count = carCouponMapper.selectCount(param);
            if (count > 0) {
                couponPOList = carCouponMapper.selectList(param);
            }
        }
        // 2、非汽车售后服务券查询tb_coupon
        else {
            count = userCouponMapper.selectCount(param);
            if (count > 0) {
                couponPOList = userCouponMapper.selectList(param);
            }
        }
        SeachUserCouponListResult result = new SeachUserCouponListResult();
        result.setUserCouponPOList(couponPOList);
        result.setTotalCount(count);
        result.setTotalPage(count % param.getLimit() == 0 ? count / param.getLimit() : count / param.getLimit() + 1);
        return result;
    }

    /**
     * 根据id修改状态
     *
     * @return
     */
    public long destroyCoupon(long uid, long couponId, UserCouponStatusEnum couponStatus) {
        return userCouponMapper.destroyCoupon(uid, couponId, couponStatus.getValue(), new Date().getTime() / 1000);
    }

    /**
     * 根据id查询
     *
     * @return
     */
    public UserCouponPO selectByCouponId(long uid, long couponId) {
        return userCouponMapper.selectByCouponId(uid, couponId);
    }


    /**
     * 查用户分享信息
     *
     * @param couponId
     * @param status
     * @return
     */
    public UserCouponSharePO selectShareByUidAndStatus(long uid, long couponId, int status) {
        return userCouponShareMapper.selectByUidAndStatus(uid, couponId, status);
    }

    /**
     * 查用户分享信息
     *
     * @param couponId
     * @return
     */
    public List<UserCouponSharePO> selectShareByUid(long uid, long couponId) {
        return userCouponShareMapper.selectByUid(uid, couponId);
    }


    /**
     * 根据用户券id查询券信息
     *
     * @return
     */
    public UserCouponPO selectInfoById(long couponId) {
        return userCouponMapper.selectInfoById(couponId);
    }


    /**
     * 根据ID列表批量获取用户优惠券信息
     *
     * @param idList 用户优惠券ID列表
     * @return 用户优惠券信息列表
     */
    public List<UserCouponPO> batchGetById(List<String> idList) {
        // 创建一个空的优惠券列表
        List<UserCouponPO> couponPOList = Lists.newArrayList();
        // 初始化页码和每页大小
        int pageNo = 1;
        int pageSize = 100;
        List<UserCouponPO> poList;
        do {
            // 计算当前页的偏移量
            int offset = (pageNo - 1) * pageSize;
            // 从数据库中获取当前页的优惠券代码信息
            poList = userCouponMapper.batchGetById(idList, pageSize, offset);
            // 将当前页的优惠券代码信息添加到总列表中
            couponPOList.addAll(poList);
            // 页码加1，准备获取下一页的数据
            pageNo++;
        } while (poList.size() == pageSize); // 如果当前页的数据量等于每页大小，继续循环

        // 返回所有获取到的优惠券代码信息
        return couponPOList;
    }

    /**
     * 根据类型ID列表批量获取用户优惠券信息
     *
     * @param typeIdList 类型ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 用户优惠券信息列表
     */
    public List<CouponPo> batchGetByTypeId(List<String> typeIdList, Long startTime, Long endTime) {
        return oceanBaseCouponMapper.batchGetByTypeId(typeIdList, startTime, endTime);
    }

    /**
     * 删除用户券，自动化测试使用
     * @return
     */
    public Long deleteCoupon(Long uid, Long couponId){
        return userCouponMapper.deleteCoupon(uid, couponId);
    }


    /**
     * 根据券配置id分页获取用户id列表
     * @param couponTypes 券配置id
     * @param limit 每页大小
     * @param offset 偏移量
     * @return 用户id列表
     */
    public List<Long> getUserIdByCouponType(List<Long> couponTypes, Integer limit, Integer offset, Long lastTime) {
        return userCouponMapper.getUserIdByCouponType(couponTypes, limit, offset, lastTime);
    }

    /**
     * 获取最新添加时间
     * @param configIds 券配置id
     * @param lastTime 上次查询时间
     * @return 最新时间
     */
    public Long getMaxAddTime(List<Long> configIds, Long lastTime) {
        return userCouponMapper.getMaxAddTime(configIds, lastTime);
    }
}
