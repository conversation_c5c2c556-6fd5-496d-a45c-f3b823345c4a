package com.xiaomi.nr.coupon.admin.application.dubbo.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointsBatchDataStat;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointsBatchDataStatRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointsBatchDataStatResponse;
import com.xiaomi.nr.coupon.admin.api.service.pointadmin.DubboPointDataService;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.PointsDataService;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.UserPointsRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.userpoint.po.UserPointsPo;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 积分数据
 *
 * <AUTHOR>
 * @date 2023/12/5
 */
@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
@Component
@Slf4j
public class DubboPointDataServiceImpl implements DubboPointDataService {


    @Resource
    private UserPointsRepository userPointsRepository;

    @Autowired
    private PointsDataService pointsDataService;

    /**
     * 积分批次数据统计
     *
     * @param request PointBatchDataStatRequest
     * @return Result
     */
    @Override
    public Result<PointsBatchDataStatResponse> pointBatchStat(PointsBatchDataStatRequest request) {
        log.info("DubboPointsDataService.pointsBatchStat, start, request:{}", request);
        try {
            // 本期先总表里查，后续量大后从数据组提供的表中查询
            List<UserPointsPo> userPointsPoList= userPointsRepository.getBatchPointsList(request.getBatchId());
            PointsBatchDataStat data = pointsDataService.summaryCarPointBatchData(userPointsPoList);
            PointsBatchDataStatResponse response = new PointsBatchDataStatResponse();
            response.setPointsBatchDataStat(data);

            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboPointsDataService.pointsBatchStat, Exception, request:{}", request, e);
            return Result.fromException(e);
        }
    }

}
