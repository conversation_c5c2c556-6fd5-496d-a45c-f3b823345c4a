package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig;

import com.google.common.collect.Lists;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.UpdateCouponGoodsRequest;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.RefreshCouponGoodsContext;
import com.xiaomi.nr.coupon.admin.domain.coupon.goods.GoodsService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.coupon.CouponConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.notify.robotmessage.RobotSendMessageUtil;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponMoveRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.GoodItemPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.UpdateGoodsIncludePO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig.OldCouponConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.couponconfig.po.CouponConfigPo;
import com.xiaomi.nr.coupon.admin.util.CouponCollectionUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.SystemHelper;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CouponGoodsRefreshService {

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private OldCouponConfigMapper oldCouponConfigMapper;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private SystemHelper systemHelper;

    @Autowired
    private MakeCacheBaseData makeCacheBaseData;

    @Autowired
    private CouponMoveRepository couponMoveRepository;

    @Autowired
    private RobotSendMessageUtil robotMessageUtil;

    @Async("asyncExecutor")
    public void handRefresh(UpdateCouponGoodsRequest request, Set<Long> notExistIds) {
        try {

            RefreshCouponGoodsContext context = buildHandRefreshCouponGoodsContext(request.getIds());
            refreshCouponGoods(context);

            String msg = "更新时间：" + TimeUtil.getNowDateTime() + " | 优惠券适用商品更新完成! 执行成功券=" + CouponCollectionUtil.removeAllSet(request.getIds(), notExistIds) + ", 不存在或非品类券=" + notExistIds;
            robotMessageUtil.sendPrivateChat(msg, request.getOperator());
        } catch (Exception e) {
            log.error("异步执行品类券适用商品失败, error=", e);
            e.printStackTrace();
        }
    }


    /**
     * 获取不存在的品类券
     *
     * @param allIds
     * @return
     */
    public Set<Long> getNotExistIds(Set<Long> allIds) {

        if (CollectionUtils.isEmpty(allIds)) {
            return Collections.emptySet();
        }

        long limitCouponId = systemHelper.isTestSystem() ? 14776L : 50000L;
        Set<Long> oldCouponIds = new HashSet<>();
        Set<Long> newCouponIds = new HashSet<>();
        for (Long id : allIds) {
            if (id < limitCouponId) {
                oldCouponIds.add(id);
                continue;
            }
            newCouponIds.add(id);
        }

        List<Long> validIds = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(oldCouponIds)) {
            validIds.addAll(Optional.ofNullable(couponMoveRepository.getOldRefreshCouponIds(oldCouponIds)).orElse(Collections.emptyList()));
        }

        if (CollectionUtils.isNotEmpty(newCouponIds)) {
            validIds.addAll(Optional.ofNullable(couponConfigRepository.getConfigForRefreshIds(newCouponIds)).orElse(Collections.emptyList()));
        }

        Set<Long> validIdSet = new HashSet<>(validIds);
        return CouponCollectionUtil.removeAllSet(allIds, validIdSet);
    }


    /**
     * 刷新品类券所包含的商品
     */
    public void refreshCouponGoods(RefreshCouponGoodsContext context) throws Exception {

        if (Objects.isNull(context)) {
            return;
        }

        // 获取三级类目下商品信息
        Map<Integer, List<SkuInfoDto>> goodsSkuMap = new HashMap<>();
        Map<Integer, List<SkuInfoDto>> postFreeSkuMap = new HashMap<>();
        getGoodsInfo(context, goodsSkuMap, postFreeSkuMap);

        // 构建更新的商品信息
        List<UpdateGoodsIncludePO> couponGoodsIncludeList = makeGoodsInclude(context, goodsSkuMap, postFreeSkuMap);

        // 获取券原始信息
        Map<Long, CouponConfigPO> oldPoMap = getOldCouponListByIds(context);

        // 商品更新落库、更新缓存等操作
        updateCouponIncrGoods(couponGoodsIncludeList, oldPoMap, context.getOperator());
    }


    /**
     * 更新券商品缓存(落库、更新缓存等操作)，全量替换商品
     *
     * @param couponGoodsPos 券商品信息列表
     * @param oldCouponPOs   券原始信息
     * @throws Exception
     */
    private void updateCouponGoods(List<UpdateGoodsIncludePO> couponGoodsPos, Map<Long, CouponConfigPO> oldCouponPOs, String operator) throws Exception {

        CouponConfigPO newPo = new CouponConfigPO();
        CouponUpdateEvent couponUpdateEvent = new CouponUpdateEvent();
        for (UpdateGoodsIncludePO couponGoodsPo : couponGoodsPos) {

            couponUpdateEvent.setOldPo(oldCouponPOs.get(couponGoodsPo.getId()));
            BeanMapper.copy(oldCouponPOs.get(couponGoodsPo.getId()), newPo);
            newPo.setGoodsInclude(couponGoodsPo.getGoodsInclude());
            couponUpdateEvent.setData(newPo);

            // 券商品更新落库
            couponUpdateEvent.setOperator(Optional.ofNullable(operator).orElse("system-updateGroupGoods"));
            couponConfigRepository.updateGoodsInclude(newPo, couponUpdateEvent);

            // 后置处理，更新缓存等操作
            applicationEventPublisher.publishEvent(couponUpdateEvent);
        }
    }

    /**
     * 更新券商品缓存(落库、更新缓存等操作),增量更新
     *
     * @param couponGoodsPos 券商品信息列表
     * @param oldCouponPOs   券原始信息
     * @throws Exception
     */
    private void updateCouponIncrGoods(List<UpdateGoodsIncludePO> couponGoodsPos, Map<Long, CouponConfigPO> oldCouponPOs, String operator) throws Exception {

        CouponConfigPO newPo = new CouponConfigPO();
        CouponUpdateEvent couponUpdateEvent = new CouponUpdateEvent();
        for (UpdateGoodsIncludePO couponGoodsPo : couponGoodsPos) {
            CouponConfigPO oldPo = oldCouponPOs.get(couponGoodsPo.getId());
            couponUpdateEvent.setOldPo(oldPo);
            BeanMapper.copy(oldPo, newPo);
            GoodItemPO goodItemPO = GsonUtil.fromJson(couponGoodsPo.getGoodsInclude(), GoodItemPO.class);
            GoodItemPO goodItemPoOld = GsonUtil.fromJson(oldPo.getGoodsInclude(), GoodItemPO.class);
            Set<Long> skus = new HashSet<>();
            skus.addAll(goodItemPO.getSku());
            skus.addAll(goodItemPoOld.getSku());
            Set<Long> packages = new HashSet<>();
            packages.addAll(goodItemPO.getPackages());
            packages.addAll(goodItemPoOld.getPackages());
            GoodItemPO goodItemPoNew = new GoodItemPO();
            goodItemPoNew.setSku(new LinkedList<>(skus));
            goodItemPoNew.setPackages(new LinkedList<>(packages));
            newPo.setGoodsInclude(GsonUtil.toJson(goodItemPoNew));
            couponUpdateEvent.setData(newPo);

            // 券商品更新落库
            couponUpdateEvent.setOperator(Optional.ofNullable(operator).orElse("system-updateGroupGoods"));
            couponConfigRepository.updateGoodsInclude(newPo, couponUpdateEvent);

            // 后置处理，更新缓存等操作
            applicationEventPublisher.publishEvent(couponUpdateEvent);
        }
    }


    /**
     * 批量获取券配置信息
     *
     * @param context 券配置id
     * @return 券信息
     */
    private Map<Long, CouponConfigPO> getOldCouponListByIds(RefreshCouponGoodsContext context) {

        List<Long> configIds = Lists.newLinkedList();
        if (CollectionUtils.isNotEmpty(context.getConfigIds())) {
            configIds.addAll(context.getConfigIds());
        }

        if (MapUtils.isNotEmpty(context.getCouponGoods())) {
            configIds.addAll(context.getCouponGoods().keySet());
        }

        int size = configIds.size();
        List<CouponConfigPO> couponConfigPOS = new ArrayList<>();
        if (size < 30) {

            couponConfigPOS.addAll(couponConfigRepository.searchCouponListById(configIds));
        } else {

            // 批量获取，每30个为一批次
            int toIndex = 30;
            for (int i = 0; i < size; i += 30) {
                if (i + 30 > size) {
                    toIndex = size - i;
                }

                couponConfigPOS.addAll(couponConfigRepository.searchCouponListById(configIds.subList(i, i + toIndex)));
            }
        }

        return couponConfigPOS.stream().collect(Collectors.toMap(CouponConfigPO::getId, Function.identity(), (key1, key2) -> key2));
    }


    /**
     * 构建品类券商品入库信息
     *
     * @param context        请求参数
     * @param goodsSkuMap    商品券对应商品
     * @param postFreeSkuMap 运费券对应商品
     * @return 落库信息
     */
    private List<UpdateGoodsIncludePO> makeGoodsInclude(RefreshCouponGoodsContext context, Map<Integer, List<SkuInfoDto>> goodsSkuMap, Map<Integer, List<SkuInfoDto>> postFreeSkuMap) {

        List<UpdateGoodsIncludePO> result = new ArrayList<>();
        boolean isPostfree;
        List<Long> skus = new LinkedList<>();
        GoodItemPO goodItemPO = new GoodItemPO();
        for (Map.Entry<Long, Set<Integer>> item : context.getCategoryIdMap().entrySet()) {
            isPostfree = CouponTypeEnum.POSTFREE.getValue().equals(context.getCouponTypeMap().get(item.getKey()));
            for (Integer categoryId : context.getCategoryIdMap().get(item.getKey())) {
                if (isPostfree) {
                    if (MapUtils.isNotEmpty(postFreeSkuMap) && postFreeSkuMap.containsKey(categoryId) && CollectionUtils.isNotEmpty(postFreeSkuMap.get(categoryId))) {
                        skus.addAll(postFreeSkuMap.get(categoryId).stream().map(SkuInfoDto::getSku).collect(Collectors.toList()));
                    }
                } else {
                    if (MapUtils.isNotEmpty(goodsSkuMap) && goodsSkuMap.containsKey(categoryId) && CollectionUtils.isNotEmpty(goodsSkuMap.get(categoryId))) {
                        skus.addAll(goodsSkuMap.get(categoryId).stream().map(SkuInfoDto::getSku).collect(Collectors.toList()));
                    }
                }
            }

            goodItemPO.setSku(skus);
            goodItemPO.setPackages(Collections.emptyList());
            skus = Lists.newArrayList();
            result.add(new UpdateGoodsIncludePO(item.getKey(), GsonUtil.toJson(goodItemPO)));
        }

        if (MapUtils.isEmpty(context.getCouponGoods())) {
            return result;
        }

        for (Map.Entry<Long, String> item : context.getCouponGoods().entrySet()) {
            result.add(new UpdateGoodsIncludePO(item.getKey(), item.getValue()));
        }

        return result;
    }


    /**
     * 获取品类下的商品信息
     *
     * @param request        请求参数
     * @param goodsSkuMap    商品券品类商品信息
     * @param postFreeSkuMap 运费券品类商品信息
     * @throws Exception
     */
    private void getGoodsInfo(RefreshCouponGoodsContext request, Map<Integer, List<SkuInfoDto>> goodsSkuMap, Map<Integer, List<SkuInfoDto>> postFreeSkuMap) throws Exception {

        Map<Integer, List<Long>> couponTypeMap = new HashMap<>();
        for (Map.Entry<Long, Integer> item : request.getCouponTypeMap().entrySet()) {

            if (!couponTypeMap.containsKey(item.getValue())) {

                couponTypeMap.put(item.getValue(), Lists.newArrayList(item.getKey()));
                continue;
            }

            couponTypeMap.get(item.getValue()).add(item.getKey());
        }

        Set<Integer> goodsCategoryIds = new HashSet<>();
        Set<Integer> postFreeCategoryIds = new HashSet<>();

        if (couponTypeMap.containsKey(CouponTypeEnum.GOODS.getValue())) {
            couponTypeMap.get(CouponTypeEnum.GOODS.getValue()).forEach(x -> goodsCategoryIds.addAll(request.getCategoryIdMap().get(x)));
        }

        if (couponTypeMap.containsKey(CouponTypeEnum.SUBSIDY.getValue())) {
            couponTypeMap.get(CouponTypeEnum.SUBSIDY.getValue()).forEach(x -> goodsCategoryIds.addAll(request.getCategoryIdMap().get(x)));
        }

        if (couponTypeMap.containsKey(CouponTypeEnum.POSTFREE.getValue())) {
            couponTypeMap.get(CouponTypeEnum.POSTFREE.getValue()).forEach(x -> postFreeCategoryIds.addAll(request.getCategoryIdMap().get(x)));
        }
        // 品类券更新，只更新自营
        Map<Long, List<SkuInfoDto>> goodsSkuMapTemp = goodsService.getSkuMapByCategoryId(new ArrayList<>(goodsCategoryIds), Lists.newArrayList(CouponConstant.BUSINESS_TYPE_SELF), false);
        goodsSkuMapTemp.forEach((k, v) -> {
            List<SkuInfoDto> skuInfoDtos = v.stream().filter(x -> goodsService.filterSku(CouponTypeEnum.GOODS.getValue(), x, null)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(skuInfoDtos)) {
                goodsSkuMap.put(Integer.parseInt(String.valueOf(k)), skuInfoDtos);
            }
        });

        Map<Long, List<SkuInfoDto>> postFreeSkuMapTemp = goodsService.getSkuMapByCategoryId(new ArrayList<>(postFreeCategoryIds), Lists.newArrayList(CouponConstant.BUSINESS_TYPE_SELF), true);
        postFreeSkuMapTemp.forEach((k, v) -> {
            List<SkuInfoDto> skuInfoDtos = v.stream().filter(x -> goodsService.filterSku(CouponTypeEnum.POSTFREE.getValue(), x, null)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(skuInfoDtos)) {
                postFreeSkuMap.put(Integer.parseInt(String.valueOf(k)), skuInfoDtos);
            }
        });
    }


    /**
     * 获取需要刷新的品类券配置信息
     *
     * @return
     */
    public RefreshCouponGoodsContext buildSystemRefreshCouponGoodsContext() {

        // 分页获取数据
        int startSize = 0;
        int pageSize = 200;
        long nowTime = TimeUtil.getNowUnixSecond();
        List<CouponConfigPO> couponConfigPOS = new ArrayList<>();

        while (true) {
            List<CouponConfigPO> list = couponConfigRepository.getConfigForRefreshSchedule(nowTime, startSize, pageSize);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }

            couponConfigPOS.addAll(list);
            if (list.size() < pageSize) {
                break;
            }

            startSize += pageSize;
        }

        if (CollectionUtils.isEmpty(couponConfigPOS)) {
            return null;
        }

        Set<Long> configIds = new HashSet<>();
        Map<Long, Integer> couponTypeMap = new HashMap<>();
        Map<Long, Set<Integer>> categoryIdMap = new HashMap<>();

        for (CouponConfigPO po : couponConfigPOS) {
            configIds.add(po.getId());
            couponTypeMap.put(po.getId(), po.getCouponType());
            categoryIdMap.put(po.getId(), new HashSet<>(Arrays.stream(po.getCategoryIds().split(",")).map(Integer::parseInt).collect(Collectors.toList())));
        }

        return new RefreshCouponGoodsContext(configIds, categoryIdMap, couponTypeMap);
    }

    /**
     * 构建更新券新品参数
     *
     * @param configIds 请求参数
     */
    public RefreshCouponGoodsContext buildHandRefreshCouponGoodsContext(Set<Long> configIds) throws BizError {

        if (CollectionUtils.isEmpty(configIds)) {
            return null;
        }

        long limitCouponId = systemHelper.isTestSystem() ? 14776L : 50000L;
        Set<Long> oldCouponIds = new HashSet<>();
        configIds.forEach(x -> {
            if (x < limitCouponId) {
                oldCouponIds.add(x);
            }
        });

        Map<Long, String> oldCouponGoodsMap = getOldCouponGoods(oldCouponIds);

        configIds = CouponCollectionUtil.removeAllSet(configIds, oldCouponIds);
        List<CouponConfigPO> couponConfigPOS = new ArrayList<>(configIds.size());
        couponConfigPOS.addAll(CollectionUtils.isEmpty(configIds) ? Collections.emptyList() : couponConfigRepository.getConfigForRefresh(configIds));
        if (MapUtils.isEmpty(oldCouponGoodsMap) && CollectionUtils.isEmpty(couponConfigPOS)) {
            return null;
        }

        Set<Long> validConfigIds = new HashSet<>();
        Map<Long, Integer> couponTypeMap = new HashMap<>();
        Map<Long, Set<Integer>> categoryIdMap = new HashMap<>();

        for (CouponConfigPO po : couponConfigPOS) {
            validConfigIds.add(po.getId());
            couponTypeMap.put(po.getId(), po.getCouponType());
            categoryIdMap.put(po.getId(), new HashSet<>(Arrays.stream(po.getCategoryIds().split(",")).map(Integer::parseInt).collect(Collectors.toList())));
        }

        return new RefreshCouponGoodsContext(validConfigIds, categoryIdMap, couponTypeMap, oldCouponGoodsMap);
    }


    /**
     * 获取老券最新的商品信息
     *
     * @param oldCouponIds 券id
     * @return map
     */
    private Map<Long, String> getOldCouponGoods(Set<Long> oldCouponIds) throws BizError {

        if (CollectionUtils.isEmpty(oldCouponIds)) {
            return Collections.emptyMap();
        }

        List<CouponConfigPo> oldCouponConfigPos = oldCouponConfigMapper.getRefreshCoupons(oldCouponIds);
        if (CollectionUtils.isEmpty(oldCouponConfigPos)) {
            return Collections.emptyMap();
        }

        BaseData goodsData = makeCacheBaseData.getV3();

        Map<Long, String> result = new HashMap<>();
        for (CouponConfigPo couponConfigPo : oldCouponConfigPos) {
            result.put(couponConfigPo.getId(), couponMoveRepository.convertGoodsInclude(goodsData, couponConfigPo, true));
        }

        return result;
    }


}
