package com.xiaomi.nr.coupon.admin.infrastruture.repository;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.request.PermissionListRequest;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.CouponSceneMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.ScenePermissionMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.SceneListParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.ScenePermissionPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 优惠券场景
 * @Date: 2022.03.03 17:15
 */
@Component
public class CouponSceneRepository {

    @Autowired
    private CouponSceneMapper couponSceneMapper;

    @Autowired
    private ScenePermissionMapper scenePermissionMapper;


    /**
     * 根据场景编码查询场景
     */
    public CouponScenePO selectBySceneCode(String sceneCode){
        return couponSceneMapper.selectBySceneCode(sceneCode);
    }


    /**
     * 根据后台前端参数查询场景
     *
     * @param sceneListParam
     * @return
     */
    public List<CouponScenePO> searchSceneByListReq(SceneListParam sceneListParam) {
        int pageNo = sceneListParam.getPageNo();
        int pageSize = sceneListParam.getPageSize();
        return couponSceneMapper.searchSceneByListReq(sceneListParam, (pageNo - 1) * pageSize, pageSize);
    }

    /**
     * 根据后台前端列表参数查询数量
     *
     * @param sceneListParam
     * @return
     */
    public Integer searchSceneCountByListReq(SceneListParam sceneListParam) {
        return couponSceneMapper.searchSceneCountByListReq(sceneListParam);
    }

    /**
     * 根据id查询场景
     *
     * @param id
     * @return
     */
    public CouponScenePO searchSceneById(long id) {
        return couponSceneMapper.searchSceneById(id);
    }

    public CouponScenePO searchSceneByName(String name) {
        return couponSceneMapper.searchSceneByName(name);
    }

    public List<CouponScenePO> searchAllBriefPO(Boolean onlyOnline) {
        return couponSceneMapper.searchAllBriefScene(onlyOnline);
    }

    public List<CouponScenePO> searchAllBriefPO(boolean onlyOnline, Integer bizPlatform) {
        return this.searchAllBriefPO(onlyOnline, Lists.newArrayList(bizPlatform));
    }

    public List<CouponScenePO> searchAllBriefPO(boolean onlyOnline, List<Integer> bizPlatformList) {
        return couponSceneMapper.searchAllBriefSceneBiz(onlyOnline, bizPlatformList);
    }

    /**
     * 更新状态
     *
     * @param id
     * @param status
     * @return
     */
    public Integer updateStatusById(long id, int status) {
        return couponSceneMapper.updateStatusById(id, status);
    }

    public Integer insert(CouponScenePO couponScenePO) {
        return couponSceneMapper.insert(couponScenePO);
    }

    public Integer update(CouponScenePO couponScenePO) {
        return couponSceneMapper.update(couponScenePO);
    }

    /**
     * 根据后台前端参数授权列表
     *
     * @param request
     * @return
     */
    public List<ScenePermissionPO> searchPermissionByListReq(PermissionListRequest request) {
        int pageNo = request.getPageNo();
        int pageSize = request.getPageSize();
        return scenePermissionMapper.searchPermissionByListReq(request, (pageNo - 1) * pageSize, pageSize);
    }

    /**
     * 根据场景id查询授权列表
     *
     * @param sceneId
     * @return
     */
    public Integer searchPermissionCountBySceneId(Long sceneId) {
        return scenePermissionMapper.searchPermissionCountBySceneId(sceneId);
    }

    /**
     * 根据场景id和appId查询授权列表
     *
     * @param sceneId
     * @param appId
     * @return
     */
    public List<ScenePermissionPO> searchPermissionListBySceneIdAppId(Long sceneId, String appId) {
        return scenePermissionMapper.searchPermissionListBySceneIdAppId(sceneId, appId);
    }

    /**
     * 新增授权
     *
     * @param scenePermissionPO
     * @return
     */
    public Integer insertPermission(ScenePermissionPO scenePermissionPO) {
        return scenePermissionMapper.insert(scenePermissionPO);
    }

    /**
     * 根据id, 场景id, appid查询授权记录
     *
     * @param id
     * @param sceneId
     * @param appId
     * @return
     */
    public ScenePermissionPO searchPermissionByIdSceneIdAppId(Long id, Long sceneId, String appId) {
        return scenePermissionMapper.searchPermissionByIdSceneIdAppId(id, sceneId, appId);
    }

    /**
     * 更新授权状态
     * @param scenePermissionPO
     * @return
     */
    public Integer updatePermissionStatus(ScenePermissionPO scenePermissionPO) {
        return scenePermissionMapper.updateStatusById(scenePermissionPO.getId(), scenePermissionPO.getStatus(),
                scenePermissionPO.getModifier(), scenePermissionPO.getUpdateTime());
    }


    /**
     * 根据场景编码查询场景(批量)
     */
    public Map<String, String> selectBySceneCodes(List<String> sceneCode){
        Map<String, String> sceneMap = new HashMap<>();
        List<CouponScenePO> pos = couponSceneMapper.selectBySceneCodes(sceneCode);
        pos.forEach(po -> sceneMap.put(po.getSceneCode(), po.getName()));
        return sceneMap;
    }

    /**
     * 根据场景编码查询场景(批量)
     */
    public Map<String, CouponScenePO> selectPoBySceneCodes(List<String> sceneCode){
        Map<String, CouponScenePO> sceneMap = new HashMap<>();
        List<CouponScenePO> pos = couponSceneMapper.selectBySceneCodes(sceneCode);
        pos.forEach(po -> sceneMap.put(po.getSceneCode(), po));
        return sceneMap;
    }

    public CouponScenePO selectRelationSceneByCode(String code){
        return couponSceneMapper.selectRelationSceneByCode(code);
    }

    public List<CouponScenePO> selectBySceneCodes(String code) {
        List<String> codes = Arrays.asList(code.split(","));
        return couponSceneMapper.selectBySceneCodes(codes);
    }

    /**
     * 根据投放方式查询
     * @return
     */
    public List<CouponScenePO> selectSceneByAssignMode(Integer assignMode,Integer status){
        return couponSceneMapper.selectSceneByAssignMode(assignMode,status);
    }

    /**
     * 根据场景编码查询场景名称
     */
    public String selectNameBySceneCode(String sceneCode){
        return couponSceneMapper.searchNameSceneByCode(sceneCode);
    }


}
