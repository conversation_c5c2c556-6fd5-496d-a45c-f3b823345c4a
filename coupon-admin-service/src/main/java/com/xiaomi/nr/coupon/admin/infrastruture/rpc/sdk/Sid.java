/**
 * Autogenerated by Thrift Compiler (0.9.2)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.xiaomi.nr.coupon.admin.infrastruture.rpc.sdk;

import org.mi.thrift.EncodingUtils;
import org.mi.thrift.TException;
import org.mi.thrift.async.AsyncMethodCallback;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;
import org.mi.thrift.server.AbstractNonblockingServer.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Generated;
import java.util.*;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2021-11-15")
public class Sid {

    public interface Iface {

        public Result Get(int number, String appId, String xnamespace, String password) throws org.mi.thrift.TException;

    }

    public interface AsyncIface {

        public void Get(int number, String appId, String xnamespace, String password, org.mi.thrift.async.AsyncMethodCallback resultHandler) throws org.mi.thrift.TException;

    }

    public static class Client extends org.mi.thrift.TServiceClient implements Iface {
        private org.mi.thrift.transport.XContext context_ = null;
        public static class Factory implements org.mi.thrift.TServiceClientFactory<Client> {
            public Factory() {}
            public Client getClient(org.mi.thrift.protocol.TProtocol prot) {
                return new Client(prot);
            }
            public Client getClient(org.mi.thrift.protocol.TProtocol iprot, org.mi.thrift.protocol.TProtocol oprot) {
                return new Client(iprot, oprot);
            }
        }

        public void setContext(org.mi.thrift.transport.XContext context) {
            context_ = context;
        }
        public Client(org.mi.thrift.protocol.TProtocol prot)
        {
            super(prot, prot);
        }

        public Client(org.mi.thrift.protocol.TProtocol iprot, org.mi.thrift.protocol.TProtocol oprot) {
          super(iprot, oprot);
        }

        public Result Get(int number, String appId, String xnamespace, String password) throws org.mi.thrift.TException
        {
            org.mi.thrift.protocol.TProtocol proto = super.getOutputProtocol();
            org.mi.thrift.transport.TTransport trans = proto.getTransport();
            try            {
                org.mi.thrift.transport.TXmHeaderTransport xmHeader = (org.mi.thrift.transport.TXmHeaderTransport)trans;
                if (context_ != null)                 {
                    xmHeader.setAppId(context_.getAppId());
                    xmHeader.setLogId(context_.getLogId());
                    xmHeader.setRpcId(context_.getRpcId());
                }
                } catch(Exception e) {
            }
            send_Get(number, appId, xnamespace, password);
            return recv_Get();
        }

        public void send_Get(int number, String appId, String xnamespace, String password) throws org.mi.thrift.TException
        {
            Get_args args = new Get_args();
            args.setNumber(number);
            args.setAppId(appId);
            args.setXnamespace(xnamespace);
            args.setPassword(password);
            sendBase("Get", args);
        }

        public Result recv_Get() throws org.mi.thrift.TException
        {
            Get_result result = new Get_result();
            receiveBase(result, "Get");
            if (result.isSetSuccess()) {
              return result.success;
            }
            throw new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.MISSING_RESULT, "Get failed: unknown result");
        }

    }
public static class AsyncClient extends org.mi.thrift.async.TAsyncClient implements AsyncIface {
public static class Factory implements org.mi.thrift.async.TAsyncClientFactory<AsyncClient> {
  private org.mi.thrift.async.TAsyncClientManager clientManager;
  private org.mi.thrift.protocol.TProtocolFactory protocolFactory;
  public Factory(org.mi.thrift.async.TAsyncClientManager clientManager, org.mi.thrift.protocol.TProtocolFactory protocolFactory) {
    this.clientManager = clientManager;
    this.protocolFactory = protocolFactory;
  }
  public AsyncClient getAsyncClient(org.mi.thrift.transport.TNonblockingTransport transport) {
    return new AsyncClient(protocolFactory, clientManager, transport);
  }
}

public AsyncClient(org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.async.TAsyncClientManager clientManager, org.mi.thrift.transport.TNonblockingTransport transport) {
  super(protocolFactory, clientManager, transport);
}

public void Get(int number, String appId, String xnamespace, String password, org.mi.thrift.async.AsyncMethodCallback resultHandler) throws org.mi.thrift.TException {
  checkReady();
  Get_call method_call = new Get_call(number, appId, xnamespace, password, resultHandler, this, ___protocolFactory, ___transport);
  this.___currentMethod = method_call;
  ___manager.call(method_call);
}

public static class Get_call extends org.mi.thrift.async.TAsyncMethodCall {
    private int number;
    private String appId;
    private String xnamespace;
    private String password;
    public Get_call(int number, String appId, String xnamespace, String password, org.mi.thrift.async.AsyncMethodCallback resultHandler, org.mi.thrift.async.TAsyncClient client, org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.transport.TNonblockingTransport transport) throws org.mi.thrift.TException {
      super(client, protocolFactory, transport, resultHandler, false);
      this.number = number;
      this.appId = appId;
      this.xnamespace = xnamespace;
      this.password = password;
    }

    public void write_args(org.mi.thrift.protocol.TProtocol prot) throws org.mi.thrift.TException {
        prot.writeMessageBegin(new org.mi.thrift.protocol.TMessage("Get", org.mi.thrift.protocol.TMessageType.CALL, 0));
        Get_args args = new Get_args();
        args.setNumber(number);
        args.setAppId(appId);
        args.setXnamespace(xnamespace);
        args.setPassword(password);
        args.write(prot);
        prot.writeMessageEnd();
    }

    public Result getResult() throws org.mi.thrift.TException {
        if (getState() != org.mi.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.mi.thrift.transport.TMemoryInputTransport memoryTransport = new org.mi.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.mi.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_Get();
    }
}

}

public static class Processor<I extends Iface> extends org.mi.thrift.TBaseProcessor<I> implements org.mi.thrift.TProcessor {
private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
public Processor(I iface) {
  super(iface, getProcessMap(new HashMap<String, org.mi.thrift.ProcessFunction<I, ? extends org.mi.thrift.TBase>>()));
}

protected Processor(I iface, Map<String,  org.mi.thrift.ProcessFunction<I, ? extends  org.mi.thrift.TBase>> processMap) {
  super(iface, getProcessMap(processMap));
}

private static <I extends Iface> Map<String,  org.mi.thrift.ProcessFunction<I, ? extends  org.mi.thrift.TBase>> getProcessMap(Map<String,  org.mi.thrift.ProcessFunction<I, ? extends  org.mi.thrift.TBase>> processMap) {
    processMap.put("Get", new Get());
    return processMap;
}

public static class Get<I extends Iface> extends org.mi.thrift.ProcessFunction<I, Get_args> {
    public Get() {
      super("Get");
    }

    public Get_args getEmptyArgsInstance() {
      return new Get_args();
    }

    protected boolean isOneway() {
      return false;
    }

    public Get_result getResult(I iface, Get_args args) throws org.mi.thrift.TException {
        Get_result result = new Get_result();
        result.success = iface.Get(args.number, args.appId, args.xnamespace, args.password);
        return result;
    }
}

}

public static class AsyncProcessor<I extends AsyncIface> extends org.mi.thrift.TBaseAsyncProcessor<I> {
private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
public AsyncProcessor(I iface) {
  super(iface, getProcessMap(new HashMap<String, org.mi.thrift.AsyncProcessFunction<I, ? extends org.mi.thrift.TBase, ?>>()));
}

protected AsyncProcessor(I iface, Map<String,  org.mi.thrift.AsyncProcessFunction<I, ? extends  org.mi.thrift.TBase, ?>> processMap) {
  super(iface, getProcessMap(processMap));
}

private static <I extends AsyncIface> Map<String,  org.mi.thrift.AsyncProcessFunction<I, ? extends  org.mi.thrift.TBase,?>> getProcessMap(Map<String,  org.mi.thrift.AsyncProcessFunction<I, ? extends  org.mi.thrift.TBase, ?>> processMap) {
    processMap.put("Get", new Get());
    return processMap;
}

public static class Get<I extends AsyncIface> extends org.mi.thrift.AsyncProcessFunction<I, Get_args, Result> {
    public Get() {
      super("Get");
    }

    public Get_args getEmptyArgsInstance() {
      return new Get_args();
    }

    public AsyncMethodCallback<Result> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.mi.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<Result>() { 
            public void onComplete(Result o) {
                Get_result result = new Get_result();
                result.success = o;
                try {
                  fcall.sendResponse(fb,result, org.mi.thrift.protocol.TMessageType.REPLY,seqid);
                  return;
                } catch (Exception e) {
                  LOGGER.error("Exception writing to internal frame buffer", e);
                }
                fb.close();
            }
            public void onError(Exception e) {
                byte msgType = org.mi.thrift.protocol.TMessageType.REPLY;
                org.mi.thrift.TBase msg;
                Get_result result = new Get_result();
                {
                    msgType = org.mi.thrift.protocol.TMessageType.EXCEPTION;
                    msg = (org.mi.thrift.TBase)new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
                }
                try {
                  fcall.sendResponse(fb,msg,msgType,seqid);
                  return;
                } catch (Exception ex) {
                  LOGGER.error("Exception writing to internal frame buffer", ex);
                }
                fb.close();
            }
        };
    }

    protected boolean isOneway() {
      return false;
    }

    public void start(I iface, Get_args args, org.mi.thrift.async.AsyncMethodCallback<Result> resultHandler) throws TException {
        iface.Get(args.number, args.appId, args.xnamespace, args.password,resultHandler);
    }
}

}

public static class Get_args implements org.mi.thrift.TBase<Get_args, Get_args._Fields>, java.io.Serializable, Cloneable, Comparable<Get_args> {
private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("Get_args");

private static final org.mi.thrift.protocol.TField NUMBER_FIELD_DESC = new org.mi.thrift.protocol.TField("number", org.mi.thrift.protocol.TType.I32, (short)1);
private static final org.mi.thrift.protocol.TField APP_ID_FIELD_DESC = new org.mi.thrift.protocol.TField("appId", org.mi.thrift.protocol.TType.STRING, (short)2);
private static final org.mi.thrift.protocol.TField XNAMESPACE_FIELD_DESC = new org.mi.thrift.protocol.TField("xnamespace", org.mi.thrift.protocol.TType.STRING, (short)3);
private static final org.mi.thrift.protocol.TField PASSWORD_FIELD_DESC = new org.mi.thrift.protocol.TField("password", org.mi.thrift.protocol.TType.STRING, (short)4);

private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
static {
  schemes.put(StandardScheme.class, new Get_argsStandardSchemeFactory());
  schemes.put(TupleScheme.class, new Get_argsTupleSchemeFactory());
}

public int number; // required
public String appId; // required
public String xnamespace; // required
public String password; // required

/** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
public enum _Fields implements org.mi.thrift.TFieldIdEnum {
    NUMBER((short)1, "number"),
    APP_ID((short)2, "appId"),
    XNAMESPACE((short)3, "xnamespace"),
    PASSWORD((short)4, "password");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
            case 1: // NUMBER
              return NUMBER;
            case 2: // APP_ID
              return APP_ID;
            case 3: // XNAMESPACE
              return XNAMESPACE;
            case 4: // PASSWORD
              return PASSWORD;
            default:
              return null;
        }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
}

// isset id assignments
private static final int __NUMBER_ISSET_ID = 0;
private byte __isset_bitfield = 0;
public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;
static {
    Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.NUMBER, new org.mi.thrift.meta_data.FieldMetaData("number", org.mi.thrift.TFieldRequirementType.DEFAULT, 
            new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.APP_ID, new org.mi.thrift.meta_data.FieldMetaData("appId", org.mi.thrift.TFieldRequirementType.DEFAULT, 
            new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.XNAMESPACE, new org.mi.thrift.meta_data.FieldMetaData("xnamespace", org.mi.thrift.TFieldRequirementType.DEFAULT, 
            new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PASSWORD, new org.mi.thrift.meta_data.FieldMetaData("password", org.mi.thrift.TFieldRequirementType.DEFAULT, 
            new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(Get_args.class, metaDataMap);
}

public Get_args() {
}

public Get_args(
    int number,
    String appId,
    String xnamespace,
    String password)
{
    this();
    this.number = number;
    setNumberIsSet(true);
    this.appId = appId;
    this.xnamespace = xnamespace;
    this.password = password;
}

/**
 * Performs a deep copy on <i>other</i>.
 */
public Get_args(Get_args other) {
    __isset_bitfield = other.__isset_bitfield;
    this.number = other.number;
    if (other.isSetAppId()) {
        this.appId = other.appId;
    }
    if (other.isSetXnamespace()) {
        this.xnamespace = other.xnamespace;
    }
    if (other.isSetPassword()) {
        this.password = other.password;
    }
}

public Get_args deepCopy() {
  return new Get_args(this);
}

@Override
public void clear() {
    setNumberIsSet(false);
    this.number = 0;
    this.appId = null;
    this.xnamespace = null;
    this.password = null;
}

public int getNumber() {
    return this.number;
}

public Get_args setNumber(int number) {
    this.number = number;
    setNumberIsSet(true);
    return this;
}

public void unsetNumber() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __NUMBER_ISSET_ID);
}

/** Returns true if field number is set (has been assigned a value) and false otherwise */
public boolean isSetNumber() {
    return EncodingUtils.testBit(__isset_bitfield, __NUMBER_ISSET_ID);
}

public void setNumberIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __NUMBER_ISSET_ID, value);
}

public String getAppId() {
    return this.appId;
}

public Get_args setAppId(String appId) {
    this.appId = appId;
    return this;
}

public void unsetAppId() {
    this.appId = null;
}

/** Returns true if field appId is set (has been assigned a value) and false otherwise */
public boolean isSetAppId() {
    return this.appId != null;
}

public void setAppIdIsSet(boolean value) {
    if (!value) {
      this.appId = null;
    }
}

public String getXnamespace() {
    return this.xnamespace;
}

public Get_args setXnamespace(String xnamespace) {
    this.xnamespace = xnamespace;
    return this;
}

public void unsetXnamespace() {
    this.xnamespace = null;
}

/** Returns true if field xnamespace is set (has been assigned a value) and false otherwise */
public boolean isSetXnamespace() {
    return this.xnamespace != null;
}

public void setXnamespaceIsSet(boolean value) {
    if (!value) {
      this.xnamespace = null;
    }
}

public String getPassword() {
    return this.password;
}

public Get_args setPassword(String password) {
    this.password = password;
    return this;
}

public void unsetPassword() {
    this.password = null;
}

/** Returns true if field password is set (has been assigned a value) and false otherwise */
public boolean isSetPassword() {
    return this.password != null;
}

public void setPasswordIsSet(boolean value) {
    if (!value) {
      this.password = null;
    }
}

public void setFieldValue(_Fields field, Object value) {
  switch (field) {
    case NUMBER:
        if (value == null) {
          unsetNumber();
        } else {
          setNumber((Integer)value);
        }
        break;

    case APP_ID:
        if (value == null) {
          unsetAppId();
        } else {
          setAppId((String)value);
        }
        break;

    case XNAMESPACE:
        if (value == null) {
          unsetXnamespace();
        } else {
          setXnamespace((String)value);
        }
        break;

    case PASSWORD:
        if (value == null) {
          unsetPassword();
        } else {
          setPassword((String)value);
        }
        break;

  }
}

public Object getFieldValue(_Fields field) {
    switch (field) {
    case NUMBER:
        return Integer.valueOf(getNumber());

    case APP_ID:
        return getAppId();

    case XNAMESPACE:
        return getXnamespace();

    case PASSWORD:
        return getPassword();

    }
    throw new IllegalStateException();
}

/** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case NUMBER:
        return isSetNumber();
    case APP_ID:
        return isSetAppId();
    case XNAMESPACE:
        return isSetXnamespace();
    case PASSWORD:
        return isSetPassword();
    }
    throw new IllegalStateException();
}

@Override
public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof Get_args)
      return this.equals((Get_args)that);
    return false;
}

public boolean equals(Get_args that) {
    if (that == null)
      return false;

    boolean this_present_number = true;
    boolean that_present_number = true;
    if (this_present_number || that_present_number) {
        if (!(this_present_number && that_present_number))
          return false;
        if (this.number != that.number)
          return false;
    }

    boolean this_present_appId = true && this.isSetAppId();
    boolean that_present_appId = true && that.isSetAppId();
    if (this_present_appId || that_present_appId) {
        if (!(this_present_appId && that_present_appId))
          return false;
        if (!this.appId.equals(that.appId))
          return false;
    }

    boolean this_present_xnamespace = true && this.isSetXnamespace();
    boolean that_present_xnamespace = true && that.isSetXnamespace();
    if (this_present_xnamespace || that_present_xnamespace) {
        if (!(this_present_xnamespace && that_present_xnamespace))
          return false;
        if (!this.xnamespace.equals(that.xnamespace))
          return false;
    }

    boolean this_present_password = true && this.isSetPassword();
    boolean that_present_password = true && that.isSetPassword();
    if (this_present_password || that_present_password) {
        if (!(this_present_password && that_present_password))
          return false;
        if (!this.password.equals(that.password))
          return false;
    }

    return true;
}

@Override
public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_number = true;
    list.add(present_number);
    if (present_number)
      list.add(number);

    boolean present_appId = true && (isSetAppId());
    list.add(present_appId);
    if (present_appId)
      list.add(appId);

    boolean present_xnamespace = true && (isSetXnamespace());
    list.add(present_xnamespace);
    if (present_xnamespace)
      list.add(xnamespace);

    boolean present_password = true && (isSetPassword());
    list.add(present_password);
    if (present_password)
      list.add(password);

    return list.hashCode();
}

@Override
public int compareTo(Get_args other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetNumber()).compareTo(other.isSetNumber());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNumber()) {
      lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.number, other.number);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppId()).compareTo(other.isSetAppId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppId()) {
      lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.appId, other.appId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetXnamespace()).compareTo(other.isSetXnamespace());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetXnamespace()) {
      lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.xnamespace, other.xnamespace);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPassword()).compareTo(other.isSetPassword());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPassword()) {
      lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.password, other.password);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
}

public _Fields fieldForId(int fieldId) {
  return _Fields.findByThriftId(fieldId);
}

public void read(org.mi.thrift.protocol.TProtocol iprot) throws org.mi.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
}

public void write(org.mi.thrift.protocol.TProtocol oprot) throws org.mi.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
}

@Override
public String toString() {
    StringBuilder sb = new StringBuilder("Get_args(");
    boolean first = true;

    sb.append("number:");
    sb.append(this.number);
    first = false;
    if (!first) sb.append(", ");
    sb.append("appId:");
    if (this.appId == null) {
      sb.append("null");
    } else {
        sb.append(this.appId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("xnamespace:");
    if (this.xnamespace == null) {
      sb.append("null");
    } else {
        sb.append(this.xnamespace);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("password:");
    if (this.password == null) {
      sb.append("null");
    } else {
        sb.append(this.password);
    }
    first = false;
    sb.append(")");
    return sb.toString();
}

public void validate() throws org.mi.thrift.TException {
    // check for required fields
    // check for sub-struct validity
}

private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
  try {
    write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
  } catch (org.mi.thrift.TException te) {
    throw new java.io.IOException(te);
  }
}

private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
  try {
    // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
    __isset_bitfield = 0;
    read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
  } catch (org.mi.thrift.TException te) {
    throw new java.io.IOException(te);
  }
}

private static class Get_argsStandardSchemeFactory implements SchemeFactory {
    public Get_argsStandardScheme getScheme() {
        return new Get_argsStandardScheme();
    }
}

private static class Get_argsStandardScheme extends StandardScheme<Get_args> {

    public void read(org.mi.thrift.protocol.TProtocol iprot, Get_args struct) throws org.mi.thrift.TException {
        org.mi.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
            schemeField = iprot.readFieldBegin();
            if (schemeField.type == org.mi.thrift.protocol.TType.STOP) { 
                break;
            }
            switch (schemeField.id) {
                case 1: // NUMBER
                    if (schemeField.type == org.mi.thrift.protocol.TType.I32) {
                        struct.number = iprot.readI32();
                        struct.setNumberIsSet(true);
                    } else { 
                      org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    break;
                case 2: // APP_ID
                    if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                        struct.appId = iprot.readString();
                        struct.setAppIdIsSet(true);
                    } else { 
                      org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    break;
                case 3: // XNAMESPACE
                    if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                        struct.xnamespace = iprot.readString();
                        struct.setXnamespaceIsSet(true);
                    } else { 
                      org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    break;
                case 4: // PASSWORD
                    if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                        struct.password = iprot.readString();
                        struct.setPasswordIsSet(true);
                    } else { 
                      org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    break;
                default:
                  org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot, Get_args struct) throws org.mi.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(NUMBER_FIELD_DESC);
        oprot.writeI32(struct.number);
        oprot.writeFieldEnd();
        if (struct.appId != null) {
            oprot.writeFieldBegin(APP_ID_FIELD_DESC);
            oprot.writeString(struct.appId);
            oprot.writeFieldEnd();
        }
        if (struct.xnamespace != null) {
            oprot.writeFieldBegin(XNAMESPACE_FIELD_DESC);
            oprot.writeString(struct.xnamespace);
            oprot.writeFieldEnd();
        }
        if (struct.password != null) {
            oprot.writeFieldBegin(PASSWORD_FIELD_DESC);
            oprot.writeString(struct.password);
            oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
    }

}

private static class Get_argsTupleSchemeFactory implements SchemeFactory {
    public Get_argsTupleScheme getScheme() {
        return new Get_argsTupleScheme();
    }
}

private static class Get_argsTupleScheme extends TupleScheme<Get_args> {

    @Override
    public void write(org.mi.thrift.protocol.TProtocol prot, Get_args struct) throws org.mi.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetNumber()) {
            optionals.set(0);
        }
        if (struct.isSetAppId()) {
            optionals.set(1);
        }
        if (struct.isSetXnamespace()) {
            optionals.set(2);
        }
        if (struct.isSetPassword()) {
            optionals.set(3);
        }
        oprot.writeBitSet(optionals, 4);
        if (struct.isSetNumber()) {
            oprot.writeI32(struct.number);
        }
        if (struct.isSetAppId()) {
            oprot.writeString(struct.appId);
        }
        if (struct.isSetXnamespace()) {
            oprot.writeString(struct.xnamespace);
        }
        if (struct.isSetPassword()) {
            oprot.writeString(struct.password);
        }
    }

    @Override
    public void read(org.mi.thrift.protocol.TProtocol prot, Get_args struct) throws org.mi.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(4);
        if (incoming.get(0)) {
            struct.number = iprot.readI32();
            struct.setNumberIsSet(true);
        }
        if (incoming.get(1)) {
            struct.appId = iprot.readString();
            struct.setAppIdIsSet(true);
        }
        if (incoming.get(2)) {
            struct.xnamespace = iprot.readString();
            struct.setXnamespaceIsSet(true);
        }
        if (incoming.get(3)) {
            struct.password = iprot.readString();
            struct.setPasswordIsSet(true);
        }
    }
}

}

public static class Get_result implements org.mi.thrift.TBase<Get_result, Get_result._Fields>, java.io.Serializable, Cloneable, Comparable<Get_result> {
private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("Get_result");

private static final org.mi.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.mi.thrift.protocol.TField("success", org.mi.thrift.protocol.TType.STRUCT, (short)0);

private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
static {
  schemes.put(StandardScheme.class, new Get_resultStandardSchemeFactory());
  schemes.put(TupleScheme.class, new Get_resultTupleSchemeFactory());
}

public Result success; // required

/** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
public enum _Fields implements org.mi.thrift.TFieldIdEnum {
    SUCCESS((short)0, "success");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
            case 0: // SUCCESS
              return SUCCESS;
            default:
              return null;
        }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
}

// isset id assignments
public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;
static {
    Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SUCCESS, new org.mi.thrift.meta_data.FieldMetaData("success", org.mi.thrift.TFieldRequirementType.DEFAULT, 
            new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, Result.class)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(Get_result.class, metaDataMap);
}

public Get_result() {
}

public Get_result(
    Result success)
{
    this();
    this.success = success;
}

/**
 * Performs a deep copy on <i>other</i>.
 */
public Get_result(Get_result other) {
    if (other.isSetSuccess()) {
        this.success = new Result(other.success);
    }
}

public Get_result deepCopy() {
  return new Get_result(this);
}

@Override
public void clear() {
    this.success = null;
}

public Result getSuccess() {
    return this.success;
}

public Get_result setSuccess(Result success) {
    this.success = success;
    return this;
}

public void unsetSuccess() {
    this.success = null;
}

/** Returns true if field success is set (has been assigned a value) and false otherwise */
public boolean isSetSuccess() {
    return this.success != null;
}

public void setSuccessIsSet(boolean value) {
    if (!value) {
      this.success = null;
    }
}

public void setFieldValue(_Fields field, Object value) {
  switch (field) {
    case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((Result)value);
        }
        break;

  }
}

public Object getFieldValue(_Fields field) {
    switch (field) {
    case SUCCESS:
        return getSuccess();

    }
    throw new IllegalStateException();
}

/** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case SUCCESS:
        return isSetSuccess();
    }
    throw new IllegalStateException();
}

@Override
public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof Get_result)
      return this.equals((Get_result)that);
    return false;
}

public boolean equals(Get_result that) {
    if (that == null)
      return false;

    boolean this_present_success = true && this.isSetSuccess();
    boolean that_present_success = true && that.isSetSuccess();
    if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
    }

    return true;
}

@Override
public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_success = true && (isSetSuccess());
    list.add(present_success);
    if (present_success)
      list.add(success);

    return list.hashCode();
}

@Override
public int compareTo(Get_result other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSuccess()) {
      lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.success, other.success);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
}

public _Fields fieldForId(int fieldId) {
  return _Fields.findByThriftId(fieldId);
}

public void read(org.mi.thrift.protocol.TProtocol iprot) throws org.mi.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
}

public void write(org.mi.thrift.protocol.TProtocol oprot) throws org.mi.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

@Override
public String toString() {
    StringBuilder sb = new StringBuilder("Get_result(");
    boolean first = true;

    sb.append("success:");
    if (this.success == null) {
      sb.append("null");
    } else {
        sb.append(this.success);
    }
    first = false;
    sb.append(")");
    return sb.toString();
}

public void validate() throws org.mi.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (success != null) {
      success.validate();
    }
}

private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
  try {
    write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
  } catch (org.mi.thrift.TException te) {
    throw new java.io.IOException(te);
  }
}

private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
  try {
    read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
  } catch (org.mi.thrift.TException te) {
    throw new java.io.IOException(te);
  }
}

private static class Get_resultStandardSchemeFactory implements SchemeFactory {
    public Get_resultStandardScheme getScheme() {
        return new Get_resultStandardScheme();
    }
}

private static class Get_resultStandardScheme extends StandardScheme<Get_result> {

    public void read(org.mi.thrift.protocol.TProtocol iprot, Get_result struct) throws org.mi.thrift.TException {
        org.mi.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
            schemeField = iprot.readFieldBegin();
            if (schemeField.type == org.mi.thrift.protocol.TType.STOP) { 
                break;
            }
            switch (schemeField.id) {
                case 0: // SUCCESS
                    if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                        struct.success = new Result();
                        struct.success.read(iprot);
                        struct.setSuccessIsSet(true);
                    } else { 
                      org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    break;
                default:
                  org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot, Get_result struct) throws org.mi.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
            oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
            struct.success.write(oprot);
            oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
    }

}

private static class Get_resultTupleSchemeFactory implements SchemeFactory {
    public Get_resultTupleScheme getScheme() {
        return new Get_resultTupleScheme();
    }
}

private static class Get_resultTupleScheme extends TupleScheme<Get_result> {

    @Override
    public void write(org.mi.thrift.protocol.TProtocol prot, Get_result struct) throws org.mi.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
            optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
            struct.success.write(oprot);
        }
    }

    @Override
    public void read(org.mi.thrift.protocol.TProtocol prot, Get_result struct) throws org.mi.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
            struct.success = new Result();
            struct.success.read(iprot);
            struct.setSuccessIsSet(true);
        }
    }
}

}

}
