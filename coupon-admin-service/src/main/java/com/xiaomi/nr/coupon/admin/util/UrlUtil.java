package com.xiaomi.nr.coupon.admin.util;

import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.util.Assert;

import javax.validation.constraints.NotNull;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * <AUTHOR>
 * @date 2024-09-19 16:14
*/
public class UrlUtil {

    private final static String XIAOMI_FDS_HOST_ONLINE = "cnbj1-new.fds.api.xiaomi.com";
    private final static String XIAOMI_FDS_HOST_STAGING = "staging-cnbj2-fds.api.xiaomi.net";

    @NotNull
    public static Boolean isSafety(@NotNull String url) throws BizError {
        Assert.notNull(url, "url不能为空");

        if (!url.contains(XIAOMI_FDS_HOST_ONLINE) && !url.contains(XIAOMI_FDS_HOST_STAGING)) {
            return false;
        }
        try {
            URL urlObj = new URL(url);
            String host = urlObj.getHost();
            if (!host.equals(XIAOMI_FDS_HOST_ONLINE) && !host.equals(XIAOMI_FDS_HOST_STAGING)) {
                return false;
            }
        } catch (MalformedURLException e) {
            throw ExceptionHelper.create(GeneralCodes.InternalError, "url解析异常");
        }
        return true;
    }
}
