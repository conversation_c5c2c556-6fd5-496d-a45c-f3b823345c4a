package com.xiaomi.nr.coupon.admin.util.beancopy;


import ma.glasnost.orika.MapperFacade;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.converter.ConverterFactory;
import ma.glasnost.orika.impl.DefaultMapperFactory;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;


/**
 * 实现深度的BeanOfClasssA<->BeanOfClassB复制
 */
public class BeanMapper {

    private static MapperFactory mapperFactory = new DefaultMapperFactory.Builder().build();

    static {
        ConverterFactory converterFactory = mapperFactory.getConverterFactory();
        converterFactory.registerConverter(new ListInteger2StringCustomerConverter());
        converterFactory.registerConverter(new ListLong2StringCustomerConverter());
        converterFactory.registerConverter(new ListString2StringCustomerConverter());

    }

    /**
     * 复制对象
     */
    public static void copy(Object source, Object destinationObject) {
        MapperFacade mapper = mapperFactory.getMapperFacade();
        mapper.map(source, destinationObject);
    }


    public static <T> List<T> mapList(Collection sourceList, Class<T> destinationClass) {
        if(CollectionUtils.isEmpty(sourceList)){
            return Collections.EMPTY_LIST;
        }
        MapperFacade mapper = mapperFactory.getMapperFacade();
        List<T> destinationList = mapper.mapAsList(sourceList,destinationClass);
        return destinationList;
    }




}
