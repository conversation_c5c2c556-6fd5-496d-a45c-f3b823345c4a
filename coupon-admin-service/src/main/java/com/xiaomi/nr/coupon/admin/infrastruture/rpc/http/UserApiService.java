package com.xiaomi.nr.coupon.admin.infrastruture.rpc.http;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaomi.common.perfcounter.annotation.PerfMethod;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.http.entrty.UserBatchData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 人群服务
 */
@Slf4j
@Component

public class UserApiService {

    @Autowired
    private UserCall userCall;
    private static ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
    }

    @PerfMethod
    public UserBatchData userBatchData(String batchId) {
        UserBatchData userBatchData;
        try {
            Object obj = userCall.call("/userBatch/meta", batchId);
            userBatchData = mapper.convertValue(obj, new TypeReference<UserBatchData>() {});
            if (userBatchData == null) {
                return null;
            }
            return userBatchData;
        } catch (Exception e) {
            log.error("userBatch error. batchId:{},err:", batchId, e);
            return null;
        }
    }


}
