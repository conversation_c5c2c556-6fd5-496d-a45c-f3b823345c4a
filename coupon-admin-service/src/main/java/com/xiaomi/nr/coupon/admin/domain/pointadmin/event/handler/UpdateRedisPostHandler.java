package com.xiaomi.nr.coupon.admin.domain.pointadmin.event.handler;

import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.PointConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/12/6 15:49
 */
@Component
public class UpdateRedisPostHandler extends BasePointBatchPostHandler{
    @Autowired
    private PointConfigRepository pointConfigRepository;

    @Override
    public void createPost(PointBatchConfigCreateEvent event) throws BizError {
        CarPointsBatchConfigPo batchConfigPo = event.getData();


    }

    @Override
    public void updatePost(PointBatchConfigUpdateEvent event) {

    }

    @Override
    public void updateStatus(PointBatchConfigUpdateStatusEvent event) throws BizError {
        CarPointsBatchConfigPo batchConfigPo = event.getData();

        // 更新批次配置缓存
        pointConfigRepository.updatePointBatchConfigCache(batchConfigPo);
    }
}
