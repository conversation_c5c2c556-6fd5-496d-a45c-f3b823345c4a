package com.xiaomi.nr.coupon.admin.application.dubbo.pointadmin;

import cn.hutool.core.util.PageUtil;
import com.google.common.base.Stopwatch;
import com.mi.oa.infra.oaucf.ems.enums.BudgetApplyTypeEnum;
import com.mi.oa.infra.oaucf.ems.enums.SystemSourceEnum;
import com.mi.oa.infra.oaucf.ems.req.br.BudgetApplyReq;
import com.mi.oa.infra.oaucf.ems.req.br.BudgetApplyTypeInfo;
import com.mi.oa.infra.oaucf.ems.req.br.BudgetExtendInfo;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.PageInfo;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.*;
import com.xiaomi.nr.coupon.admin.api.enums.EquityTypeCodeEnum;
import com.xiaomi.nr.coupon.admin.api.service.pointadmin.DubboPointAdminService;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.PointBatchConvert;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.PointBatchBasicConfigurationService;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.PointBatchConfigService;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.PointBatchPeriodStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.PointBatchStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.PointConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CarPointSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsScenePo;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.BrProxy;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.Service;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/6 15:20
 */
@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
@Component
@Slf4j
public class DubboPointAdminServiceImpl implements DubboPointAdminService {
    @Autowired
    private PointBatchConfigService pointBatchConfigService;

    @Autowired
    private PointBatchConvert pointBatchConvert;

    @Autowired
    private PointBatchBasicConfigurationService pointBatchBasicConfigurationService;

    @Autowired
    private PointConfigRepository pointConfigRepository;

    @Autowired
    private CarPointSceneRepository pointSceneRepository;

    @Autowired
    private BrProxy brProxy;
    /**
     * 保存积分批次配置信息
     *
     * @param request request
     * @return Result<SavePointBatchResponse>
     */
    @Override
    public Result<SavePointBatchResponse> savePointBatch(@Valid SavePointBatchRequest request) {
        log.info("DubboPointAdminServiceImpl.savePointBatch begin, request = {}", request);

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 1、入参校验
            pointBatchConfigService.checkSavePointBatchRequest(request, true);

            // 2、convert
            CarPointsBatchConfigPo batchConfigPo = pointBatchConvert.toPointBatchConfigPo(request);
            batchConfigPo.setCreator(request.getOperator());

            // 3、保存积分批次配置
            long batchId = pointBatchConfigService.insertPointBatch(batchConfigPo);

            // 4、构造返回出参
            SavePointBatchResponse response = new SavePointBatchResponse();
            response.setBatchId(batchId);

            log.info("DubboPointAdminServiceImpl.savePointBatch finished, response = {}, costTime = {}ms", response, stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboPointAdminServiceImpl.savePointBatch request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 更新积分批次配置信息
     *
     * @param request request
     * @return Result<Void>
     */
    @Override
    public Result<SavePointBatchResponse> updatePointBatch(SavePointBatchRequest request) {
        log.info("DubboPointAdminServiceImpl.updatePointBatch begin, request = {}", request);

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 1、入参校验
            pointBatchConfigService.checkUpdatePointBatchRequest(request);

            // 2、convert
            CarPointsBatchConfigPo batchConfigPo = pointBatchConvert.toPointBatchConfigPo(request);

            // 3、保存积分批次配置
            long batchId = pointBatchConfigService.updatePointBatch(batchConfigPo, request.getOperator());

            // 4、构造出参
            SavePointBatchResponse response = new SavePointBatchResponse();
            response.setBatchId(batchId);

            log.info("DubboPointAdminServiceImpl.updatePointBatch finished, request = {}, response = {}, costTime = {}ms", request, response, stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboPointAdminServiceImpl.updatePointBatch request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 通用黑名单列表
     *
     * @param request request
     * @return Result<BasePageResponse < SsuBlacklistDto>>
     */
    @Override
    public Result<BasePageResponse<SsuBlacklistDto>> ssuBlacklist(SsuBlacklistRequest request) {
        log.info("DubboPointAdminServiceImpl.ssuBlacklist begin, request = {}", request);

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 1、入参校验
            if (Objects.isNull(request)) {
                log.error("DubboPointAdminServiceImpl.ssuBlacklist request为空");
                throw ExceptionHelper.create(ErrCode.POINT, "request为空");
            }

            // 2、查询黑名单列表
            BasePageResponse<SsuBlacklistDto> response = pointBatchBasicConfigurationService.ssuBlacklist(request);

            log.info("DubboPointAdminServiceImpl.ssuBlacklist finished, request = {}, response = {}, costTime = {}ms", GsonUtil.toJson(request), GsonUtil.toJson(response), stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboPointAdminServiceImpl.ssuBlacklist request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 黑名单删除ssu
     *
     * @param request request
     * @return Result<Void>
     */
    @Override
    public Result<Void> deleteSsuBlacklist(DeleteSsuBlacklistRequest request) {
        log.info("DubboPointAdminServiceImpl.deleteSsuBlacklist begin, request = {}", request);

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 1、入参校验
            if (Objects.isNull(request)) {
                log.error("DubboPointAdminServiceImpl.deleteSsuBlacklist request为空");
                throw ExceptionHelper.create(ErrCode.POINT, "request为空");
            }

            // 2、黑名单中删除ssu
            pointBatchBasicConfigurationService.deleteSsuBlacklist(request.getSsuId());

            log.info("DubboPointAdminServiceImpl.deleteSsuBlacklist finished, request = {}, costTime = {}ms", request, stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboPointAdminServiceImpl.deleteSsuBlacklist request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 黑名单添加ssu
     *
     * @param request request
     * @return Result<Void>
     */
    @Override
    public Result<Void> addSsuBlacklist(AddSsuBlacklistRequest request) {
        log.info("DubboPointAdminServiceImpl.addSsuBlacklist begin, request = {}", request);

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 1、校验用户登录信息
            String account = RpcContext.getContext().getAttachment("$upc_account");
            if (StringUtils.isBlank(account)) {
                throw ExceptionHelper.create(GeneralCodes.NotAuthorized, "未找到登录用户信息");
            }

            // 2、入参校验
            if (Objects.isNull(request)) {
                log.error("DubboPointAdminServiceImpl.addSsuBlacklist request为空");
                throw ExceptionHelper.create(ErrCode.POINT, "request为空");
            }

            // 3、黑名单中添加ssu
            List<Long> ssuIdList = request.getSsuIdList().stream().distinct().collect(Collectors.toList());
            pointBatchBasicConfigurationService.addSsuBlacklist(ssuIdList, account);

            log.info("DubboPointAdminServiceImpl.addSsuBlacklist finished, request = {}, costTime = {}ms", request, stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboPointAdminServiceImpl.addSsuBlacklist request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 获取积分批次配置列表
     *
     * @param req request
     * @return Result<Void>
     */
    @Override
    public Result<List<PointBatchConfigData>> pointBatchList(PointBatchListRequest req) {

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 1、入参校验
            checkPointBatchListRequest(req);

            // 2、查询条件入参构造
            String keyWord = req.getKeyWord();
            String name = null;
            Long id = null;
            if (StringUtils.isNotBlank(keyWord)) {
                // 全数字按照id查询
                if (StringUtils.isNumeric(keyWord)) {
                    id = Long.parseLong(keyWord);
                }
                // 否则按照名称模糊匹配
                else {
                    name = keyWord.trim();
                }
            }
            Long queryTime = System.currentTimeMillis() / 1000;

            // 3、查询数据
            List<CarPointsBatchConfigPo> pointsBatchConfigPoList = pointConfigRepository.selectByPeriodStatus(req.getPeriodStatus(), queryTime, id, name);

            // 4、查询场景
            List<CarPointsScenePo> sceneList = pointSceneRepository.getAllSceneList(false);
            Map<String, String> sceneMap = sceneList.stream().collect(Collectors.toMap(CarPointsScenePo::getSceneCode, CarPointsScenePo::getName));

            // 5、查询使用量
            List<Long> curBatchIdList = pointsBatchConfigPoList.stream().map(CarPointsBatchConfigPo::getId).distinct().collect(Collectors.toList());
            Map<Long, Long> sendCountMap = pointConfigRepository.getPointBatchDistributeCache(curBatchIdList);

            // 6、结果转化
            List<PointBatchConfigData> pointBatchConfigDataList = pointsBatchConfigPoList.stream()
                    .map(po -> toPointBatchConfigData(po, sceneMap, sendCountMap))
                    .collect(Collectors.toList());

            log.info("DubboPointAdminServiceImpl.pointBatchList finished, req:{} resp:{} costTime = {}ms", GsonUtil.toJson(req), GsonUtil.toJson(pointBatchConfigDataList), stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return Result.success(pointBatchConfigDataList);
        } catch (Exception e) {
            log.error("DubboPointAdminServiceImpl.pointBatchList req:{}, error: ", GsonUtil.toJson(req), e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 批次状态变更
     *
     * @param req req
     * @return void
     */
    @Override
    public Result<Void> changePointBatchStatus(ChangePointBatchStatusRequest req) {

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {

            // 1、入参校验
            Integer optType = req.getOptType();
            if (optType < PointBatchStatusEnum.ONLINE.getCode() || optType > PointBatchStatusEnum.OFFLINE.getCode()) {
                throw ExceptionHelper.create(ErrCode.POINT, "操作类型非法");
            }

            // 2、校验用户登录信息
            String account = RpcContext.getContext().getAttachment("$upc_account");
            if (StringUtils.isBlank(account)) {
                throw ExceptionHelper.create(GeneralCodes.NotAuthorized, "未找到登录用户信息");
            }
            req.setOperator(account);

            // 2、更新
            pointBatchConfigService.updateStatus(req);

            log.info("DubboPointAdminServiceImpl.changePointBatchStatus finished, req:{} costTime = {}ms", GsonUtil.toJson(req), stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboPointAdminServiceImpl.changePointBatchStatus req:{}, error: ", GsonUtil.toJson(req), e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 更新积分批次配置信息
     *
     * @return Result<Void>
     */
    @Override
    public Result<Void> renewPointBatchConfig() {
        log.info("DubboPointAdminServiceImpl.renewPointBatchConfig begin");

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            pointBatchConfigService.renewPointBatchConfig();

            log.info("DubboPointAdminServiceImpl.renewPointBatchConfig finished, costTime = {}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboPointAdminServiceImpl.renewPointBatchConfig error: ", e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 积分批次配置详情
     *
     * @param request request
     * @return Result<PointBatchDetailDto>
     */
    @Override
    public Result<PointBatchDetailDto> pointBatchDetail(PointBatchDetailRequest request) {
        log.info("DubboPointAdminServiceImpl.pointBatchDetail begin, request = {}", request);

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            if (Objects.isNull(request)) {
                log.error("DubboPointAdminServiceImpl.pointBatchDetail request is null");
                throw ExceptionHelper.create(ErrCode.POINT, "request不能为空");
            }

            PointBatchDetailDto pointBatchDetailDto = pointBatchConfigService.pointBatchDetail(request);

            log.info("DubboPointAdminServiceImpl.pointBatchDetail finished, request = {}, costTime = {}ms", request, stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return Result.success(pointBatchDetailDto);
        } catch (Exception e) {
            log.error("DubboPointAdminServiceImpl.pointBatchDetail request = {}, error = ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 查询预算池列表
     *
     * @param
     * @return
     */
    @Override
    public Result<PageInfo<BudgetInfoDto>> queryBudgetList(PointQueryBudgetListRequest request) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            Pair<Boolean, String> checkParam = request.checkParam();
            if (!checkParam.getLeft()) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, checkParam.getRight());
            }
            PageInfo<BudgetInfoDto> info = queryBudgetListByBr(request);
            log.info("DubboPointAdminServiceImpl.queryBudgetList is success, request is {}, response is {}", GsonUtil.toJson(request), GsonUtil.toJson(info));
            return Result.success(info);
        } catch (Exception e) {
            log.error("DubboPointAdminServiceImpl.queryBudgetList request:{}. error:", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 通过br查询预算池列表
     *
     * @param
     * @return
     */
    public PageInfo<BudgetInfoDto> queryBudgetListByBr(PointQueryBudgetListRequest request) throws BizError {
        log.info("DubboPointAdminServiceImpl.queryBudgetList begin, request = {}", request);
        try {
            BudgetApplyReq req = new BudgetApplyReq();
            req.setPageNum(request.getPageNum());
            req.setPageSize(20);
            req.setSystemSource(SystemSourceEnum.EQUITY_CENTER);
            req.setKeyword(request.getKeyword());

            BudgetApplyTypeInfo budgetApplyTypeInfo = new BudgetApplyTypeInfo();
            budgetApplyTypeInfo.setBudgetApplyType(BudgetApplyTypeEnum.EQUITY);
            budgetApplyTypeInfo.setFeeTypeList(com.google.common.collect.Lists.newArrayList(EquityTypeCodeEnum.POINT.getCode()));

            BudgetExtendInfo budgetExtendInfo = new BudgetExtendInfo();
            budgetExtendInfo.setGroupCode(BudgetApplyTypeEnum.EQUITY.getCode());
            budgetExtendInfo.setFieldCode("integralScenarioCode");
            budgetExtendInfo.setFieldValueList(com.google.common.collect.Lists.newArrayList(request.getSendScene()));
            budgetApplyTypeInfo.setExtendFieldList(com.google.common.collect.Lists.newArrayList(budgetExtendInfo));
            req.setBudgetApplyTypeList(com.google.common.collect.Lists.newArrayList(budgetApplyTypeInfo));
            return brProxy.queryBudgetList(req);
        } catch (Exception e) {
            log.error("DubboPointAdminServiceImpl.queryBudgetList error request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
    }

    /**
     * 入参校验
     *
     * @param req 入参
     * @throws BizError 业务异常
     */
    private void checkPointBatchListRequest(PointBatchListRequest req) throws BizError {
        Integer periodStatus = req.getPeriodStatus();
        if (periodStatus != null && (periodStatus < PointBatchPeriodStatusEnum.IN_PROGRESS.getCode() || periodStatus > PointBatchPeriodStatusEnum.COMPLETED.getCode())) {
            throw ExceptionHelper.create(ErrCode.POINT, "积分批次周期状态非法");
        }
    }

    private PointBatchConfigData toPointBatchConfigData(CarPointsBatchConfigPo batchConfigPo,
                                                        Map<String, String> sceneMap,
                                                        Map<Long, Long> sendCountMap) {
        PointBatchConfigData pointBatchConfigData = new PointBatchConfigData();
        BeanUtils.copyProperties(batchConfigPo, pointBatchConfigData);
        pointBatchConfigData.setBatchId(batchConfigPo.getId());
        pointBatchConfigData.setBatchName(batchConfigPo.getName());
        Long sendCount = sendCountMap.getOrDefault(batchConfigPo.getId(), batchConfigPo.getSendCount());
        pointBatchConfigData.setBalanceCount(pointBatchConfigData.getApplyCount() - sendCount);
        pointBatchConfigData.setSendScene(sceneMap.get(batchConfigPo.getSendScene()));
        return pointBatchConfigData;
    }

}
