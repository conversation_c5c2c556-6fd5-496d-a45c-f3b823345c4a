package com.xiaomi.nr.coupon.admin.infrastruture.staticdata;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.UseChannelClientRelationDo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelType;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @description: 使用渠道和client对应关系数据
 * @author: hejiapeng
 * @Date 2022/3/12 5:08 下午
 * @Version: 1.0
 **/
@Component
public class UseChannelClientRel {

    private static Map<String, UseChannelClientRelationDo> result = new HashMap<>();

    static{
        //小米商城
        UseChannelClientRelationDo miShop = new UseChannelClientRelationDo();
        miShop.setName(UseChannelEnum.MiShop.getName());
        miShop.setChannel(UseChannelEnum.MiShop.getValue());
        miShop.setChannelType(UseChannelType.Online.getRedisValue());
        Set<Long> miShopClientId = new HashSet<>();
        miShopClientId.add(180100041114L);
        miShopClientId.add(180100041090L);
        miShopClientId.add(180100041088L);
        miShopClientId.add(180100041081L);
        miShopClientId.add(180100041080L);
        miShopClientId.add(180100041079L);
        miShopClientId.add(180100041078L);
        miShopClientId.add(180100041074L);
        miShopClientId.add(180100031055L);
        miShopClientId.add(180100031022L);
        miShopClientId.add(180100031052L);
        miShopClientId.add(180100031036L);
        miShopClientId.add(180100031013L);
        miShopClientId.add(180100031058L);
        miShopClientId.add(180100041061L);
        miShopClientId.add(180100031051L);
        miShopClientId.add(180100031021L);
        miShopClientId.add(180100031024L);
        miShopClientId.add(180100041086L);
        miShopClientId.add(180100041150L);
        miShopClientId.add(180100041089L);
        miShopClientId.add(180100041171L);
        miShopClientId.add(180100041194L);
        miShopClientId.add(180100041065L);
        miShopClientId.add(180100031056L);
        miShopClientId.add(180100041083L);
        miShopClientId.add(180100031039L);
        miShopClientId.add(180100031016L);
        miShopClientId.add(180100031037L);
        miShopClientId.add(180100041099L);
        miShopClientId.add(180100041104L);
        miShopClientId.add(180100031027L);
        miShopClientId.add(180100031032L);
        miShopClientId.add(180100041200L);
        miShop.setClientIds(miShopClientId);
        result.put(miShop.getChannel(), miShop);

        //小米之家
        UseChannelClientRelationDo miHome = new UseChannelClientRelationDo();
        miHome.setName(UseChannelEnum.MiHome.getName());
        miHome.setChannel(UseChannelEnum.MiHome.getValue());
        miHome.setChannelType(UseChannelType.Offline.getRedisValue());
        Set<Long> miHomeClientId = new HashSet<>();
        miHomeClientId.add(180100041075L);
        miHome.setClientIds(miHomeClientId);
        result.put(miHome.getChannel(), miHome);

        //授权店
        UseChannelClientRelationDo miAuthorized = new UseChannelClientRelationDo();
        miAuthorized.setName(UseChannelEnum.MiAuthorized.getName());
        miAuthorized.setChannel(UseChannelEnum.MiAuthorized.getValue());
        miAuthorized.setChannelType(UseChannelType.Offline.getRedisValue());
        Set<Long> miAuthorizedClientId = new HashSet<>();
        miAuthorizedClientId.add(180100041157L);
        miAuthorized.setClientIds(miAuthorizedClientId);
        result.put(miAuthorized.getChannel(), miAuthorized);
    }
    /**
     * 获取使用渠道与client_id关系
     *
     * @return Map<String, UseChannelClientRelationDo>
     */
    public Map<String, UseChannelClientRelationDo> getUseChannelClientRelation() {
        return result;
    }
}
