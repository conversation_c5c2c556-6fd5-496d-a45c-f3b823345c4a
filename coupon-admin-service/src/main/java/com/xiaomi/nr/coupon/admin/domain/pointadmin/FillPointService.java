package com.xiaomi.nr.coupon.admin.domain.pointadmin;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.mi.tools.StringUtils;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task.CreateFillPointTaskRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task.PointTaskListVO;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task.PointTaskVO;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task.ReStartPointTaskRequest;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.PointBatchStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.task.DownloadTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.task.PointFillSendTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.point.PointConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.point.PointFillPathConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponTaskRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.PointConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.PointFillDetailPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.PointFillPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.fds.impl.FileServiceImpl;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.hdfs.HdfsHelper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.FillCouponTaskPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.Param;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.ResultOutPut;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.SearchTaskParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URI;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 灌积分服务
 * @date 2024-08-14 11:02
 */

@Service
@Slf4j
public class FillPointService {

    @Autowired
    private PointConfigRepository pointConfigRepository;

    @Autowired
    private CouponTaskRepository fillCouponTaskRepository;

    @Autowired
    private FillPointConvert fillPointConvert;

    @Autowired
    private PointDownLoadHelper pointDownLoadHelper;

    @Autowired
    private HdfsHelper hdfsHelper;

    @Autowired
    private FileServiceImpl fileService;

    /**
     * 获取灌积分任务列表
     *
     * @param searchParam 搜索参数
     * @param response    分页响应
     * @return 任务列表
     */
    public List<PointTaskListVO> searchFillPoint(SearchTaskParam searchParam, BasePageResponse<PointTaskListVO> response) {

        // 判断是否需要查询积分批次id
        String pointBatchName = searchParam.getCouponName();
        if (StringUtils.isNotEmpty(pointBatchName)) {
            List<Long> pointBatchIds = pointConfigRepository.getBatchConfigIdByName(pointBatchName);
            if (CollectionUtils.isNotEmpty(searchParam.getConfigIds())) {
                pointBatchIds = (List<Long>) CollectionUtils.intersection(searchParam.getConfigIds(), pointBatchIds);
            }
            if (CollectionUtils.isEmpty(pointBatchIds)) {
                return Collections.emptyList();
            }
            searchParam.setConfigIds(pointBatchIds);
        }

        PageHelper.startPage(searchParam.getPageNo(), searchParam.getPageSize());
        List<FillCouponTaskPO> parentTasks = fillCouponTaskRepository.searchTaskList(searchParam);
        if (CollectionUtils.isEmpty(parentTasks)) {
            log.info("searchFillPoint parentIds is empty. request:{}", searchParam);
            return Collections.emptyList();
        }

        List<Long> parentIds = parentTasks.stream().map(FillCouponTaskPO::getTaskId).collect(Collectors.toList());
        List<Long> configIds = parentTasks.stream().map(FillCouponTaskPO::getConfigId).collect(Collectors.toList());

        // 获取子任务
        List<FillCouponTaskPO> childTasks = fillCouponTaskRepository.searchTaskByParentIds(parentIds);
        Map<Long, FillCouponTaskPO> idToChildTaskMap = childTasks.stream().collect(Collectors.toMap(FillCouponTaskPO::getTaskId, Function.identity(), (before, after) -> before));

        // 构建父任务与子任务映射关系
        Map<Long, List<Long>> parentIdToChildTaskMap = new HashMap<>();
        for (FillCouponTaskPO item : idToChildTaskMap.values()) {
            long parentId = item.getParentId();
            long childId = item.getTaskId();

            if (!parentIdToChildTaskMap.containsKey(parentId)) {
                parentIdToChildTaskMap.put(parentId, Lists.newArrayList(childId));
            }
            parentIdToChildTaskMap.get(parentId).add(childId);
        }

        // 获取积分批次配置信息
        List<CarPointsBatchConfigPo> pointConfigs = pointConfigRepository.getBathcConfigByIds(configIds);
        Map<Long, CarPointsBatchConfigPo> idToPointConfigMap = pointConfigs.stream().collect(Collectors.toMap(CarPointsBatchConfigPo::getId, Function.identity(), (before, after) -> before));

        PageInfo<FillCouponTaskPO> pageInfo = new PageInfo<>(parentTasks);
        response.setTotalCount(pageInfo.getTotal());
        response.setTotalPage(pageInfo.getPages());

        // 封装返回值
        return fillPointConvert.buildTaskVOs(parentTasks, idToChildTaskMap, parentIdToChildTaskMap, idToPointConfigMap);
    }

    /**
     * 创建灌积分任务流程
     *
     * @param request 任务参数
     * @return 任务id
     * @throws BizError 业务异常
     */
    public Long createFillPointTask(CreateFillPointTaskRequest request) {

        if (ObjectUtils.isEmpty(request.getBizType()) || request.getBizType() == 0) {
            request.setBizType(BizPlatformEnum.CAR_SHOP.getCode());
        }

        FillCouponTaskPO taskPO = fillPointConvert.convertFillCouponTaskPO(request);

        fillCouponTaskRepository.insert(taskPO);

        log.info("createFillPointTask end. marketTask:{}", taskPO);

        return taskPO.getTaskId();
    }

    /**
     * 获取任务详情
     *
     * @param taskId 任务id
     * @return 任务详情
     * @throws Exception 异常
     */
    public PointTaskVO taskDetail(long taskId) throws Exception {
        FillCouponTaskPO taskPO = fillCouponTaskRepository.getDetailTaskById(taskId);
        if (ObjectUtils.isEmpty(taskPO)) {
            throw ExceptionHelper.create(ErrCode.POINT, "灌积分任务不存在");
        }
        // 获取子任务
        List<FillCouponTaskPO> childTasks = fillCouponTaskRepository.searchTaskByParentIds(Collections.singletonList(taskId));
        // 获取积分批次配置信息
        CarPointsBatchConfigPo configPo = pointConfigRepository.findById(taskPO.getConfigId());

        return fillPointConvert.convertPointTaskVO(taskPO, configPo.getName(), childTasks);

    }

    /**
     * 灌积分任务重试
     *
     * @param request 重试参数
     * @throws Exception biz & io
     */
    public void retryFillPointTask(ReStartPointTaskRequest request) throws Exception {
        FillCouponTaskPO parentTask = fillCouponTaskRepository.getDetailTaskById(request.getTaskId());
        if (ObjectUtils.isEmpty(parentTask)) {
            throw ExceptionHelper.create(ErrCode.POINT, "灌积分任务不存在");
        }

        Param param = GsonUtil.fromJson(parentTask.getParams(), Param.class);
        ResultOutPut resultOutPut = GsonUtil.fromJson(parentTask.getResult(), ResultOutPut.class);
        long totalSendPoint = Objects.isNull(param) ? CommonConstant.ZERO_LONG : param.getApplyPointCount();
        checkPointBatchConfig(parentTask.getConfigId(), totalSendPoint);

        if (resultOutPut != null && param != null && param.getApplyCount() > param.getCount() && param.getCount() == resultOutPut.getSuccessCount()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "当前成功率低于100%原因为任务申请发放数量大于用户数量，当前用户已全部灌积分成功，无需重试");
        }

        List<FillCouponTaskPO> childTasks = fillCouponTaskRepository.getCouponTaskByParentId(request.getTaskId());
        long lastChildTaskId = parentTask.getTaskId();
        if (CollectionUtils.isNotEmpty(childTasks)) {
            // 重试次数限制
            if (PointConstant.FILL_POINT_RETRY_TIMES < childTasks.size()) {
                log.warn("retryFillPointTask fail. retry times is more than {}, request:{}", PointConstant.FILL_POINT_RETRY_TIMES, request);
                throw ExceptionHelper.create(ErrCode.POINT, "灌积分任务重试次数超过限制");
            }
            lastChildTaskId = childTasks.get(CommonConstant.ZERO_INT).getTaskId();
        }

        //包装子任务
        FillCouponTaskPO childTask = fillPointConvert.buildChildTask(parentTask, lastChildTaskId, request.getOperator());

        //插入子任务
        fillCouponTaskRepository.insert(childTask);

        // 删除父任务灌积分失败详情
        String hdfsPath = PointFillPathConstant.FILL_POINT_DETAIL_PATH + request.getTaskId();
        URI fileUri = hdfsHelper.getFileUri(hdfsPath);
        hdfsHelper.removeHDFSFile(fileUri, hdfsPath);
    }

    /**
     * 校验积分可用性
     *
     * @param pointBatchId 积分批次id
     * @return 积分批次配置信息
     * @throws BizError 业务异常
     */
    public CarPointsBatchConfigPo checkPointBatchConfig(Long pointBatchId, Long planCount) throws BizError {
        if (ObjectUtils.isEmpty(pointBatchId) || ObjectUtils.isEmpty(planCount)) {
            throw ExceptionHelper.create(ErrCode.POINT, "积分批次ID和计划发放数量不能为空");
        }

        if (planCount <= CommonConstant.ZERO_LONG) {
            throw ExceptionHelper.create(ErrCode.POINT, "计划发放数量必须大于0");
        }

        // 获取积分批次信息
        CarPointsBatchConfigPo configPo = pointConfigRepository.findById(pointBatchId);

        if (ObjectUtils.isEmpty(configPo)) {
            throw ExceptionHelper.create(ErrCode.POINT, "积分批次ID无效");
        }

        if (!Objects.equals(configPo.getStatus(), PointBatchStatusEnum.ONLINE.getCode())) {
            throw ExceptionHelper.create(ErrCode.POINT, "积分批次ID无效");
        }

        if (configPo.getEndTime() < TimeUtil.getNowUnixSecond()) {
            throw ExceptionHelper.create(ErrCode.POINT, "积分批次已过期");
        }

        if (configPo.getStartTime() > TimeUtil.getNowUnixSecond()) {
            throw ExceptionHelper.create(ErrCode.POINT, "积分批次ID无效");
        }

        // 判断积分库存
        Long sendCount = pointConfigRepository.getPointBatchDistributeCache(pointBatchId);
        sendCount = ObjectUtils.isEmpty(sendCount) ? CommonConstant.ZERO_LONG : sendCount;
        if (sendCount + planCount > configPo.getApplyCount()) {
            throw ExceptionHelper.create(ErrCode.POINT, "维护的积分批次积分值不足，请检查！");
        }

        return configPo;
    }

    /**
     * 根据fds地址将数据文件上传值hdfs
     *
     * @param fdsAddress fds地址
     * @return 文件保存路径
     * @throws BizError 异常
     */
    public String uploadFdsToHdfs(String fdsAddress) throws BizError {
        List<PointFillPO> pointFillList = pointDownLoadHelper.getPointFillDataByFdsPath(fdsAddress);
        if (CollectionUtils.isEmpty(pointFillList)) {
            throw ExceptionHelper.create(ErrCode.POINT, "灌积分数据为空");
        }

        String fileStr = fdsAddress.substring(fdsAddress.lastIndexOf("/") + 1);
        fileStr = fileStr.substring(0, fileStr.lastIndexOf("."));

        String fileName = PointFillSendTypeEnum.UPLOAD_FILE.getPrefix() + fileStr + "_" + TimeUtil.getNowUnixMillis();
        String destPath = PointFillPathConstant.FILL_POINT_UPLOAD_PATH;

        pointDownLoadHelper.uploadPointFillDataToHdfsAsync(pointFillList, fileName, destPath);

        return destPath + fileName;
    }

    /**
     * 下载灌积分任务明细
     *
     * @param taskId 任务id
     * @return 下载链接
     */
    public String downloadPointFillDetail(Long taskId) throws Exception {
        List<PointFillPO> allEntities = getFillPointEntityList(taskId);
        List<PointFillDetailPO> failEntityDetails = pointDownLoadHelper.getFailPointFillDetail(taskId, DownloadTypeEnum.TASK_DETAIL);

        List<PointFillDetailPO> list = fillPointConvert.convertToPointFillDetail(allEntities, failEntityDetails);

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        EasyExcel.write(byteArrayOutputStream, PointFillDetailPO.class)
                .sheet("灌积分任务明细")
                .doWrite(list);

        ByteArrayInputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
        String fileName = "灌积分任务明细_" + taskId + ".xlsx";
        return fileService.uploadFileToFds(fileName, inputStream, true);
    }

    /**
     * 下载灌积分数据
     *
     * @param hdfsAddress
     * @return
     * @throws Exception
     */
    public String downloadPointFillData(String hdfsAddress) throws Exception {
        return pointDownLoadHelper.downloadPointFillDataByHdfsAddress(hdfsAddress);
    }

    private List<PointFillPO> getFillPointEntityList(long taskId) throws Exception {
        String paramStr = fillCouponTaskRepository.getTaskParamById(taskId);
        Param param = GsonUtil.fromJson(paramStr, Param.class);

        if (Objects.isNull(param) || StringUtils.isEmpty(param.getAddress())) {
            throw ExceptionHelper.create(ErrCode.POINT, "灌积分用户集不存在,taskId=" + taskId);
        }

        if (param.getCount() > 20000) {
            throw ExceptionHelper.create(ErrCode.POINT, "灌券用户集大于20000,taskId=" + taskId);
        }

        try {
            return pointDownLoadHelper.downloadEntityList(param.getAddress());
        } catch (Exception e) {
            log.error("FillPointService getFillPointEntityList error,taskId={}", taskId, e);
            throw e;
        }
    }

}
