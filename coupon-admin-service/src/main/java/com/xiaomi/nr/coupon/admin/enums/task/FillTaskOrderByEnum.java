package com.xiaomi.nr.coupon.admin.enums.task;

import lombok.Data;
import lombok.Getter;

/**
 * @description 灌积分任务排序字段枚举
 * <AUTHOR>
 * @date 2024-08-16 18:22
*/
public enum FillTaskOrderByEnum {

    TASK_ID("taskId", "task_id"),
    CONFIG_ID("pointBatchId", "config_id"),
    TASK_NAME("taskName", "task_name"),
    START_TIME("startTime", "start_time"),
    END_TIME("endTime", "end_time"),
    CREATE_TIME("createTime", "create_time"),
    ;
    @Getter
    private String field;
    @Getter
    private String column;

    FillTaskOrderByEnum(String field, String column) {
        this.field = field;
        this.column = column;
    }

    public static FillTaskOrderByEnum findByField(String field) {
        for (FillTaskOrderByEnum value : FillTaskOrderByEnum.values()) {
            if (value.getField().equals(field)) {
                return value;
            }
        }
        return null;
    }

}
