package com.xiaomi.nr.coupon.admin.application.dubbo;

import com.google.common.base.Stopwatch;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.EcardDto;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.EcardLogDto;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.GetEcardInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.GetEcardLogRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.GetEcardInfoResponse;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.GetEcardLogResponse;
import com.xiaomi.nr.coupon.admin.infrastruture.annotation.MethodLimit;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.ListEcardIdRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.ListEcardDescResponse;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.ListEcardStatResponse;
import com.xiaomi.nr.coupon.admin.api.service.DubboEcardService;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.ecard.EcardConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrInfo;
import com.xiaomi.nr.coupon.admin.domain.ecard.EcardService;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
public class DubboEcardServiceImpl implements DubboEcardService {

    @Autowired
    private EcardService ecardService;

    /**
     * 礼品卡状态查询接口
     *
     * @param request ListEcardIdRequest
     * @return List<ListEcardResponse>
     */
    @Override
    @MethodLimit(name = "DubboEcardService.listEcardStat", frequency = 100.0)
    public Result<List<ListEcardStatResponse>> listEcardStat(ListEcardIdRequest request) {
        //通过礼品卡id列表获取礼品卡状态列表
        List<ListEcardStatResponse> responseList;
        List<Long> cardIdList = request.getEcardIdList();

        try {
            if (CollectionUtils.isEmpty(cardIdList)) {
                log.warn("coupon.admin.DubboEcardService, batch listEcardStat fail ParamError, cardIdList=null");
                throw ExceptionHelper.create(ErrCode.ECARD, "礼品卡状态查询所传参数为空，请核实后重试");
            }

            if (cardIdList.size() > EcardConstant.GET_ECARD_BY_CARDIDS_MAX_SIZE) {
                log.warn("coupon.admin.DubboEcardService, batch listEcardStat fail, size={}, cardIdList={}", cardIdList.size(), cardIdList);
                return Result.fromException(ErrInfo.ERR_ECARD_ID_LENGTH);
            }

            responseList = ecardService.listEcardStat(cardIdList);
        } catch (BizError e) {
            log.error("coupon.admin.DubboEcardService, listEcardStat BizError, cardIdList={}", cardIdList,e);
            return Result.fromException(e);
        } catch (Exception e) {
            log.error("coupon.admin.DubboEcardService, listEcardStat Exception, cardIdList={}", cardIdList,e);
            return Result.fromException(e);
        }

        return Result.success(responseList);
    }

    /**
     * 礼品卡信息查询接口
     *
     * @param request ListEcardIdRequest
     * @return List<ListEcardResponse>
     */
    @Override
    @MethodLimit(name = "DubboEcardService.listEcardDesc", frequency = 100.0)
    public Result<List<ListEcardDescResponse>> listEcardDesc(ListEcardIdRequest request) {
        List<Long> cardIdList = request.getEcardIdList();

        if (CollectionUtils.isEmpty(cardIdList)) {
            log.warn("coupon.admin.DubboEcardService, batch listEcardDesc fail ParamError, cardIdList={}", cardIdList);
            return Result.fail(ErrCode.ECARD, "礼品卡状态查询所传参数为空，请核实后重试");
        }

        if (cardIdList.size() > EcardConstant.GET_ECARD_BY_CARDIDS_MAX_SIZE) {
            log.warn("coupon.admin.DubboEcardService.listEcardDesc, batch listEcardDesc fail, size={}, cardIdList={}", cardIdList.size(), cardIdList);
            return Result.fromException(ErrInfo.ERR_ECARD_ID_LENGTH);
        }

        List<ListEcardDescResponse> responseList;
        try {
            responseList = ecardService.listEcardDesc(cardIdList);
        } catch (BizError e) {
            log.warn("coupon.admin.DubboEcardService, listEcardDesc fail,cardIdList={}", cardIdList,e);
            return Result.fromException(e);
        } catch (Exception e) {
            log.error("coupon.admin.DubboEcardService, listEcardDesc error,cardIdList={}", cardIdList,e);
            return Result.fromException(e);
        }

        return Result.success(responseList);
    }

    /**
     * 查询ecard日志（财务系统使用）
     *
     * @param request 获取电子卡日志的请求对象
     * @return 包含电子卡日志的响应结果
     */
    @Override
    public Result<GetEcardLogResponse> getEcardLog(@Valid GetEcardLogRequest request) {
        log.info("DubboEcardServiceImpl.getEcardLog begin, request = {}", GsonUtil.toJson(request));
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {

            List<EcardLogDto> ecardLogDtoList = ecardService.getEcardLog(request);

            GetEcardLogResponse response = new GetEcardLogResponse();
            response.setEcardLogDtoList(ecardLogDtoList);

            // 记录方法结束日志，打印请求参数、响应参数和耗时
            log.info("DubboEcardServiceImpl.getEcardLog finished, request = {}, response = {}, costTime = {}ms", GsonUtil.toJson(request), GsonUtil.toJson(response), stopwatch.elapsed(TimeUnit.MILLISECONDS));

            // 返回成功结果
            return Result.success(response);
        } catch (BizError e) {
            // 记录错误日志，打印请求参数和异常信息
            log.error("DubboEcardServiceImpl.getEcardLog has BizError, request = {}, e = ", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        } catch (Exception e) {
            // 记录错误日志，打印请求参数和异常信息
            log.error("DubboEcardServiceImpl.getEcardLog has Error, request = {}, e = ", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
        
    }

    /**
     * 查询ecard信息（财务系统使用）
     *
     * @param request 获取电子卡信息的请求对象
     * @return 包含电子卡信息的响应结果
     */
    @Override
    public Result<GetEcardInfoResponse> getEcardInfo(@Valid GetEcardInfoRequest request) {
        log.info("DubboEcardServiceImpl.getEcardInfo begin, request = {}", GsonUtil.toJson(request));
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 调用服务获取电子卡信息
            List<EcardDto> ecardDtoList = ecardService.getEcardInfo(request);

            // 构建响应对象
            GetEcardInfoResponse response = new GetEcardInfoResponse();
            response.setEcardDtoList(ecardDtoList);

            // 记录方法结束日志，打印请求参数、响应参数和耗时
            log.info("DubboEcardServiceImpl.getEcardInfo finished, request = {}, response = {}, costTime = {}ms", GsonUtil.toJson(request), GsonUtil.toJson(response), stopwatch.elapsed(TimeUnit.MILLISECONDS));

            // 返回成功结果
            return Result.success(response);
        } catch (BizError e) {
            // 记录错误日志，打印请求参数和异常信息
            log.error("DubboEcardServiceImpl.getEcardInfo has BizError, request = {}, e = ", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        } catch (Exception e) {
            // 记录错误日志，打印请求参数和异常信息
            log.error("DubboEcardServiceImpl.getEcardInfo has Error, request = {}, e = ", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }
}
