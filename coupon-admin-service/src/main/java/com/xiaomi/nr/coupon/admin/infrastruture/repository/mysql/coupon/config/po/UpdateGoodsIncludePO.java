package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po;

import lombok.Data;

import java.io.Serializable;

@Data
public class UpdateGoodsIncludePO implements Serializable {
    private static final long serialVersionUID = -3652701389726857302L;

    /**
     * 优惠券编号
     */
    private long id;

    /**
     * 券可用商品
     * json {"sku":"s1,s2","package":"p1,p2"}
     */
    private String goodsInclude;

    public UpdateGoodsIncludePO(){}
    public UpdateGoodsIncludePO(long id, String goodsInclude){
        this.id = id;
        this.goodsInclude = goodsInclude;
    }
}
