package com.xiaomi.nr.coupon.admin.infrastruture.staticdata;

import org.apache.commons.lang.StringUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * @description: 图片文件后缀集合
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/7/8 2:27 下午
 * @Version: 1.0
 **/

public class PictureFileSuffeix {

    public static Set<String> pictureSuffiex = new HashSet<>();

    static {
        pictureSuffiex.add("BMP");
        pictureSuffiex.add("DIB");
        pictureSuffiex.add("PCP");
        pictureSuffiex.add("DIF");
        pictureSuffiex.add("WMF");
        pictureSuffiex.add("GIF");
        pictureSuffiex.add("JPG");
        pictureSuffiex.add("TIF");
        pictureSuffiex.add("EPS");
        pictureSuffiex.add("PSD");
        pictureSuffiex.add("CDR");
        pictureSuffiex.add("IFF");
        pictureSuffiex.add("TGA");
        pictureSuffiex.add("PCD");
        pictureSuffiex.add("MPT");
        pictureSuffiex.add("PNG");
    }

}
