package com.xiaomi.nr.coupon.admin.enums;

/**
 * 订单状态枚举
 */
public enum OrderStatusNodeEnum {

    COMMIT_ORDER(1, "提交订单"),
    WAIT_ORDER(3, "提交订单"),
    PAID_ALREADY(4, "已支付"),
    DOWN(18, "已关闭"),
    REFUND(39, "退款完成"),
    SEND_OUT(50, "已通知仓库发货");

    private final Integer id;
    private final String description;

    private OrderStatusNodeEnum(Integer id, String description) {
        this.description = description;
        this.id = id;
    }

    public static OrderStatusNodeEnum getEnum(int id) {
        OrderStatusNodeEnum[] enums = values();
        OrderStatusNodeEnum[] var2 = enums;
        int var3 = enums.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            OrderStatusNodeEnum e = var2[var4];
            if (e.id == id) {
                return e;
            }
        }

        return null;
    }

    public Integer getId() {
        return this.id;
    }

    public String getDescription() {
        return this.description;
    }

    public static String findByDesc(Integer id) {
        OrderStatusNodeEnum[] values = OrderStatusNodeEnum.values();
        for (OrderStatusNodeEnum item : values) {
            if (item.getId().equals(id)) {
                return item.getDescription();
            }
        }
        return null;
    }

    public static String findByDescText(Integer id) {
        OrderStatusNodeEnum[] values = OrderStatusNodeEnum.values();
        for (OrderStatusNodeEnum item : values) {
            if (item.getId().equals(id)) {
                return item.getDescription() + "("+id+")";
            }
        }
        return null;
    }


}

