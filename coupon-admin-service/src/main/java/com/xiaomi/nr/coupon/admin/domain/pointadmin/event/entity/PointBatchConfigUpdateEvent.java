package com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/12/7 15:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PointBatchConfigUpdateEvent  extends PointBaseEvent<CarPointsBatchConfigPo> {
    private static final long serialVersionUID = -3538947948899106521L;

    /**
     * 修改前po
     */
    private CarPointsBatchConfigPo oldPo;

    /**
     * 操作人邮箱前缀
     */
    private String operator;

}
