package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;

/**
 * <AUTHOR>
 * @date 2024/12/12 17:27
 */
public abstract class GiftBuyCheckAction extends CouponConfigBaseCheck{

    /**
     * 券创建基础校验
     */
    @Override
    public void createCommonCheck(CouponBaseInfo info) throws BizError {
        // 券创建、修改公共校验
        commonCheck(info);

        // 商品券类型校验
        couponTypeCheck(info);

        if (info.getId() > 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券id必须为空");
        }

        if (info.getPromotionValue() != 0L) {
            throw ExceptionHelper.create(ErrCode.COUPON, "商品金额应直减至0元");
        }
    }

    /**
     * 券优惠类型
     */
    @Override
    public PromotionTypeEnum getPromotionType() {
        return PromotionTypeEnum.GIFT;
    }
}
