package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponmission.impl;

import com.google.gson.reflect.TypeToken;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponmission.MissionCacheRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponmission.po.MissionCacheItemPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponmission.po.MissionMapType;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class MissionCacheRedisDaoImpl implements MissionCacheRedisDao {

    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisTemplate;

    /**
     * 发放任务缓存信息对应key
     */
    private static final String KEY_COUPON_MISSION_CACHE = "nr:coupon:mission:{id}";
    /**
     * 发放任务id与券配置对应关系缓存对应key
     */
    private static final String KEY_COUPON_MISSIONID_CACHE = "nr_coupon_valid_mission_list";

    private static final String NEW_KEY_COUPON_MISSIONID_CACHE = "nr:coupon:valid:mission:{id}";

    /**
     * 每次写入100个缓存
     */
    private static final int LIMIT_REDIS_SET_COUNT = 100;
    /**
     * 批量每次读取100个缓存
     */
    private static final int LIMIT_REDIS_GET_COUNT = 100;

    /**
     * 券发放任务id和券id映射关系分片去存
     */
    private static final long COMPLEMENT_NUMBER = 16L;

    /**
     * 发放任务id与券配置对应关系缓存对应key
     */
    private static final String KEY_COUPON_MISSIONID_CACHE_SHARD = "nr_coupon_missionconfig_shard_{key}";



    /**
     * 写redis
     * 将券发放任务基础信息写入缓存
     * @param missionCacheList
     */
    @Override
    public void setMissionDesc(List<MissionCacheItemPo> missionCacheList) {

        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        Map<Long, MissionCacheItemPo> initMap = new HashMap<>(LIMIT_REDIS_SET_COUNT);
        for(MissionCacheItemPo missionCache : missionCacheList){
            if(initMap.size() >= LIMIT_REDIS_SET_COUNT){
                operations.multiSet(convertMapString(initMap));

                initMap = new HashMap<>(LIMIT_REDIS_SET_COUNT);
            }
            initMap.put(missionCache.getId(),missionCache);
        }
        if(!CollectionUtils.isEmpty(initMap)){
            operations.multiSet(convertMapString(initMap));
        }

    }



    /**
     * 批量写入redis操作类
     * @param map  map原始数据
     * @return     转换后的数据
     */
    private Map<String,String> convertMapString(Map<Long, MissionCacheItemPo> map){
        Map<String,String> dataMap = new HashMap<>(LIMIT_REDIS_SET_COUNT);
        map.forEach((key,value) -> {
            dataMap.put(StringUtil.formatContent(KEY_COUPON_MISSION_CACHE, key.toString()), GsonUtil.toJson(value));
        });
        return dataMap;
    }



    /**
     * 写redis
     * 将发券任务id写入缓存
     * @param missionMapTypeList
     */
    @Override
    public void setMissionIdList(List<MissionMapType> missionMapTypeList) {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        if(!CollectionUtils.isEmpty(missionMapTypeList)){
            operations.set(KEY_COUPON_MISSIONID_CACHE, Objects.requireNonNull(GsonUtil.toJson(missionMapTypeList)));
        }
    }


    /**
     * 批量设置写redis
     * 将发券任务id写入缓存
     * @param missionMapTypeList
     */
    @Override
    public void setMissionIdListV2(List<MissionMapType> missionMapTypeList) {
        if(CollectionUtils.isEmpty(missionMapTypeList)){
            return;
        }
        Map<Long,List<Long>> configIdMissionMap = new HashMap<>();
        for (MissionMapType missionMapType : missionMapTypeList) {
            Long configId = missionMapType.getCouponConfigId();
            if(configIdMissionMap.containsKey(configId)){
                configIdMissionMap.get(configId).add(missionMapType.getMissionId());
                continue;
            }
            configIdMissionMap.put(configId, Lists.newArrayList(missionMapType.getMissionId()));
        }

        ValueOperations<String, String> operations = redisTemplate.opsForValue();

        for (Map.Entry<Long,List<Long>> item: configIdMissionMap.entrySet()) {
            operations.set(StringUtil.formatContent(NEW_KEY_COUPON_MISSIONID_CACHE, String.valueOf(item.getKey())), StringUtils.join(item.getValue(), ","), 3600, TimeUnit.SECONDS);
        }
    }


    /**
     * 写redis
     * 获取发放任务缓存id和券配置id关系列表
     * @return List<MissionMapType>
     */
    @Override
    public List<MissionMapType> getMissionIds() {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String missionIdStr = operations.get(KEY_COUPON_MISSIONID_CACHE);
        return GsonUtil.fromListJson(missionIdStr,MissionMapType.class);
    }



    /**
     * 读redis
     * 获取单个发放任务缓存信息
     * @param missionId 发放任务id
     * @return SingleMissionCache
     */
    @Override
    public MissionCacheItemPo getMissionCacheById(long missionId){
        if(missionId < 0){
            return null;
        }

        String key = StringUtil.formatContent(KEY_COUPON_MISSION_CACHE,String.valueOf(missionId));
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String missionCacheStr = operations.get(key);
        if(StringUtils.isEmpty(missionCacheStr)){
            log.warn("couponMission.cache, 从redis里取到的数据为null, missionId={}",missionId);
            return null;
        }

        return GsonUtil.fromJson(missionCacheStr, MissionCacheItemPo.class);
    }



    /**
     * 读redis
     * 批量获取券发放任务信息
     * @param missionIds 发放任务id列表
     * @return List<SingleMissionCache>
     */
    @Override
    public List<MissionCacheItemPo> getMissionCacheList(List<Long> missionIds){

        if(CollectionUtils.isEmpty(missionIds)){
            return Collections.emptyList();
        }

        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        List<MissionCacheItemPo> result = new ArrayList<>();
        List<String> keyList = new ArrayList<>(LIMIT_REDIS_GET_COUNT);

        for(Long id : missionIds){
            if(StringUtils.isEmpty(id.toString())){
                continue;
            }

            keyList.add(StringUtil.formatContent(KEY_COUPON_MISSION_CACHE,String.valueOf(id)));

            if (keyList.size() >= LIMIT_REDIS_GET_COUNT) {
                List<String> jsonStrList = operations.multiGet(keyList);
                result.addAll(decodeBaseInfo(keyList, jsonStrList));
                keyList.clear();
            }
        }

        if(!CollectionUtils.isEmpty(keyList)){
            List<String> jsonStrList = operations.multiGet(keyList);
            result.addAll(decodeBaseInfo(keyList, jsonStrList));
        }

        return result;
    }



    /**
     * 解析券缓存基本信息
     *
     * @param keyList List<String>
     * @param dataList List<String>
     * @return List<ConfigCacheItemPo>
     */
    private List<MissionCacheItemPo> decodeBaseInfo(List<String> keyList, List<String> dataList) {
        List<MissionCacheItemPo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("mission.cache, 从redis里取到的数据为null, keyList={}", keyList);
            return result;
        }

        for (String resJson : dataList) {
            if (StringUtils.isEmpty(resJson)) {
                log.warn("mission.cache, 从redis里取到的数据为空, keyList={}, resJson={}", keyList, resJson);
                continue;
            }

            MissionCacheItemPo info = GsonUtil.fromJson(resJson, MissionCacheItemPo.class);
            if (Objects.isNull(info) || info.getId() <= 0) {
                log.warn("mission.cache, 从redis里取到的数据解析后发现不符合要求, keyList={}, resJson={}", keyList, resJson);
                continue;
            }

            result.add(info);
        }

        return result;
    }



    /**
     * 券任务id和券配置id的映射关系分片写入redis
     * @param map 券任务id和券id映射关系
     */
    @Override
    public void setMissionIdMap(Map<Long, Long> map) {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        Map<Long, Map<Long, Long>> shardMapTmp = new HashMap<>();
        for(Map.Entry<Long, Long> item : map.entrySet()){
            long missionId = item.getKey();
            long configId = item.getValue();
            long shardKey = missionId % COMPLEMENT_NUMBER;
            if(shardMapTmp.containsKey(shardKey)){
                shardMapTmp.get(shardKey).put(missionId, configId);
                continue;
            }

            Map<Long, Long> tmpMap = new HashMap<>(16);
            tmpMap.put(missionId, configId);
            shardMapTmp.put(shardKey, tmpMap);

        }

        Map<String, String> shardMap = new HashMap<>(16);
        for(Map.Entry<Long, Map<Long, Long>> item : shardMapTmp.entrySet()){
            shardMap.put(StringUtil.formatContent(KEY_COUPON_MISSIONID_CACHE_SHARD, String.valueOf(item.getKey())), GsonUtil.toJson(item.getValue()));
        }

        operations.multiSet(shardMap);
    }



    /**
     * 获取券任务id和券配置映射关系
     *
     * @return 券任务id和券配置映射关系列表
     */
    public List<MissionMapType> getMIssionIdMap() {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        Map<Long, Long> missionMap = new HashMap<>();
        for (int i = 0; i < 16; i++) {
            String mapStr = operations.get(StringUtil.formatContent(KEY_COUPON_MISSIONID_CACHE_SHARD, String.valueOf(i)));
            Map<Long, Long> map = GsonUtil.fromMapJson(mapStr, new TypeToken<Map<Long, Long>>() {}.getType());
            if(CollectionUtils.isEmpty(map)){
                continue;
            }
            missionMap.putAll(map);
        }

        List<MissionMapType> missionMapTypeList = new ArrayList<>();
        for(Map.Entry<Long, Long> item : missionMap.entrySet()){
            missionMapTypeList.add(new MissionMapType(item.getKey(), item.getValue()));
        }

        return missionMapTypeList;
    }

}
