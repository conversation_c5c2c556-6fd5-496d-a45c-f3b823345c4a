package com.xiaomi.nr.coupon.admin.infrastruture.repository.excel;

import lombok.Data;


@Data
public class GoodsIdListPO {

    public static final int HEAD_ROW_NUMBER=0;


    public static final String headSku1= "sku";
    public static final String headSku2= "必填，sku为五位数字\n" +
            "请勿删除本行提示\n" +
            "（如：14318）";


    public static final  String headPackage1= "套装id";
    public static final  String headPackage2= "必填，套装id为1开头的十位数字\n" +
            "请勿删除本行提示\n" +
            "（如：1431898789）";


    public static final  String headSuit1= "套装id";
    public static final  String headSuit2= "必填，套装id为6开头的九位数字\n" +
            "请勿删除本行提示\n" +
            "（如：643189878）";



    /**
     * sku 或 套装id
     */
    private String goodsId;


}
