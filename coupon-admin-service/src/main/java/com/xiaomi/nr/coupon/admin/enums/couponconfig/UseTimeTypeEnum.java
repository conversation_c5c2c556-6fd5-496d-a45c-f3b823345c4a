package com.xiaomi.nr.coupon.admin.enums.couponconfig;

import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Getter
public enum UseTimeTypeEnum {

    ABSOLUTE(1, "固定有效", "Fixed"),
    RELATIVE(2, "相对有效期", "Unfixed"),
    CUSTOM(3, "自定义", "");

    private int value;
    private String name;
    private String watermelonDesc;

    UseTimeTypeEnum(int value, String name, String watermelonDesc) {
        this.value = value;
        this.name = name;
        this.watermelonDesc = watermelonDesc;
    }


    public static UseTimeTypeEnum getByValue(int value) {
        UseTimeTypeEnum[] values = UseTimeTypeEnum.values();
        for (UseTimeTypeEnum item : values) {
            if (item.getValue()==value) {
                return item;
            }
        }
        return null;
    }


    public static String getDesc(UseTimeTypeEnum useTimeTypeEnum,UseTimeGranularityEnum useTimeGranularityEnum,Date startUseTime, Date endUseTime, int useDuration){
       switch (useTimeTypeEnum){
           case ABSOLUTE:
               return TimeUtil.formatDate(startUseTime)+"至"+TimeUtil.formatDate(endUseTime);
           case RELATIVE:
               return UseTimeGranularityEnum.getDesc(useTimeGranularityEnum,useDuration);
           case CUSTOM:
               return "不支持配置，由发券业务系统指定券使用时间";
       }
       return null;
    }

    public static String getWatermelonDesc(Integer value) {
        UseTimeTypeEnum[] values = UseTimeTypeEnum.values();
        for (UseTimeTypeEnum item : values) {
            if (item.getValue()==value) {
                return item.getWatermelonDesc();
            }
        }
        return StringUtils.EMPTY;
    }



}
