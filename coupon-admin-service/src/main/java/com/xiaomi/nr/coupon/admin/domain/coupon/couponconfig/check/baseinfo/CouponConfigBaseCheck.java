package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.BudgetFeeTypeEnum;
import com.xiaomi.nr.coupon.admin.api.enums.FetchLimitTypeEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneSendModeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.BrProxy;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 建券校验
 */
public abstract class CouponConfigBaseCheck {

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Autowired
    private BrProxy brProxy;

    /**
     * 初始化（用来工厂和策略模式的注册）
     */
    public abstract void init();

    /**
     * 券创建基础校验
     */
    public abstract void createCommonCheck(CouponBaseInfo info) throws BizError;

    /**
     * 券创建特殊校验
     */
    public abstract void createSpecialCheck(CouponBaseInfo info) throws BizError;

    /**
     * 商品校验
     */
    public abstract void goodsCheck(CouponConfigItem configItem) throws Exception;

    /**
     * 券修改特殊校验
     */
    public abstract void updateSpecialCheck(CouponBaseInfo info) throws BizError;

    /**
     * 券优惠类型
     */
    public abstract PromotionTypeEnum getPromotionType();

    /**
     * 业务平台类型
     */
    public abstract BizPlatformEnum getBizPlatformEnum();

    /**
     * 券创建校验
     */
    public void createCheck(CouponConfigItem configItem) throws Exception {
        CouponBaseInfo info = configItem.getCouponBaseInfo();

        // 通用校验
        createCommonCheck(info);

        // 特殊校验
        createSpecialCheck(info);

        // 商品校验
        goodsCheck(configItem);
    }

    /**
     * 修改券基础校验
     */
    public void updateCheck(CouponConfigItem configItem) throws Exception {
        CouponBaseInfo info = configItem.getCouponBaseInfo();

        // 券创建、修改公共校验
        commonCheck(info);

        // 券类型校验
        couponTypeCheck(info);

        // 券修改基础校验
        updateBaseCheck(info);

        // 券修改特殊校验
        updateSpecialCheck(info);

        // 商品校验
        goodsCheck(configItem);
    }

    /**
     * 运费券和商品券的共同校验
     */
    protected void commonCheck(CouponBaseInfo info) throws BizError {
        if (StringUtils.isBlank(info.getName())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券名称不能为空");
        }
        if (StringUtils.isBlank(info.getCouponDesc())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用说明不能为空");
        }
        if (StringUtils.isBlank(info.getSendScene())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "投放场景不能为空");
        }
        if (info.getStartFetchTime() == null || info.getEndFetchTime() == null ||
                (!info.getStartFetchTime().before(info.getEndFetchTime())) || info.getEndFetchTime().before(new Date())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "领取时间不能为空，且开始领取时间不能大于结束领取时间，结束领取时间不能小于当前时间");
        }

        UseTimeTypeEnum useTimeType = UseTimeTypeEnum.getByValue(info.getUseTimeType());
        if (Objects.isNull(useTimeType)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用有效期类型异常");
        }
        if (UseTimeTypeEnum.ABSOLUTE.equals(useTimeType)) {
            if (info.getStartUseTime() == null || info.getEndUseTime() == null) {
                throw ExceptionHelper.create(ErrCode.COUPON, "固定时间有效期，开始使用时间和结束使用时间不能为空");
            }
        } else if (UseTimeTypeEnum.RELATIVE.equals(useTimeType)) {
            if (info.getUseDuration() <= 0) {
                throw ExceptionHelper.create(ErrCode.COUPON, "相对时间有效期，有效时长不能为空");
            }
        }

        if (info.getPromotionType() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠类型不能为空");
        }

        // 发放场景
        List<String> sendSceneList = Arrays.asList(info.getSendScene().split(","));
        Map<String, CouponScenePO> scenePoMap = couponSceneRepository.selectPoBySceneCodes(sendSceneList);
        for (String sceneCode : sendSceneList) {
            CouponScenePO scenePO = scenePoMap.getOrDefault(sceneCode, null);
            if (Objects.isNull(scenePO)) {
                throw ExceptionHelper.create(ErrCode.COUPON, "投放场景信息不存在, sceneCode = " + sceneCode);
            }

            // 限领校验
            if (FetchLimitTypeEnum.LIMIT.getCode().equals(info.getFetchLimitType())) {
                if (SceneSendModeEnum.COUPON.getCode() == scenePO.getSendMode() && (info.getApplyCount() <= 0 || info.getFetchLimit() <= 0 || info.getApplyCount() < info.getFetchLimit())) {
                    throw ExceptionHelper.create(ErrCode.COUPON, "发放总量必须大于等于每人限领");
                }
            }
        }

        // 不限领
        if (FetchLimitTypeEnum.NO_LIMIT.getCode().equals(info.getFetchLimitType())) {
            if (info.getFetchLimit() != 0) {
                throw ExceptionHelper.create(ErrCode.COUPON, "不限领情况下每人限领数量异常");
            }
        }

        // TODO: 特殊规则跳过
        if (info.getExtProp().getArea() == GivenAreaEnum.YES.getType()) {
            if (CollectionUtils.isEmpty(info.getAreaIds())) {
                throw ExceptionHelper.create(ErrCode.COUPON, "指定地区码不可为空");
            }
        }

        // 车商城预算信息校验
        BizPlatformEnum bizPlatform = BizPlatformEnum.valueOf(info.getBizPlatform());
        if (BizPlatformEnum.CAR_SHOP.equals(bizPlatform)) {
            if (Objects.nonNull(info.getBudgetApplyNo()) && Objects.nonNull(info.getLineNum())) {
                brProxy.queryBrInfo(info.getBudgetApplyNo(), info.getLineNum(), BudgetFeeTypeEnum.CARSHOP1.getCode());
            }
        }

        // 目前整车、售后服务领域的useChannel为默认直营店，此处忽略渠道业务领域匹配校验
        if (BizPlatformEnum.RETAIL.equals(bizPlatform)
                || BizPlatformEnum.CAR_SHOP.equals(bizPlatform)) {
            // channel 2 biz check
            for (Integer channel : info.getUseChannel().keySet()) {
                if (Objects.isNull(channel)) continue;

                BizPlatformEnum bizByChannel = UseChannelsEnum.getBizByChannel(channel);
                if (!bizPlatform.equals(bizByChannel)) {
                    throw ExceptionHelper.create(ErrCode.COUPON, "渠道和业务场景不匹配");
                }
            }
        }
    }

    /**
     * 商品券类型校验
     */
    protected void couponTypeCheck(CouponBaseInfo info) throws BizError {
        if (Objects.equals(CouponTypeEnum.POSTFREE.getValue(), info.getCouponType())) {
            checkPostFreeCoupon(info);
        }

        if (Objects.equals(CouponTypeEnum.GOODS.getValue(), info.getCouponType())
                || Objects.equals(CouponTypeEnum.SUBSIDY.getValue(), info.getCouponType())) {
            checkGoodsCoupon(info);
        }
    }

    /**
     * 校验商品券
     */
    private void checkGoodsCoupon(CouponBaseInfo info) throws BizError {
        if (MapUtils.isEmpty(info.getCostShare())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "成本分摊不可为空");
        }
    }

    /**
     * 校验运费券规则
     * <note>多为临时固定规则</>
     *
     * @param info
     * @throws BizError
     */
    private void checkPostFreeCoupon(CouponBaseInfo info) throws BizError {
        // TODO 使用时间相对时间
        // 使用渠道
        for (Integer useChanel : info.getUseChannel().keySet()) {
            if (useChanel != UseChannelsEnum.DIRECTSALE_STORE.getValue() &&
                    useChanel != UseChannelsEnum.EXCLUSIVE_SHOP.getValue()) {
                throw ExceptionHelper.create(ErrCode.COUPON, "运费券目前只支持直营店和专卖店！");
            }
        }
        // 履约方式  TODO 换成常量或枚举
        if (info.getShipmentId() != 139) {
            throw ExceptionHelper.create(ErrCode.COUPON, "运费券履约方式只支持门店闪送！");
        }
        if (info.getPromotionType() != PromotionTypeEnum.DirectReduce.getValue() || info.getPromotionValue() != 1000) {
            throw ExceptionHelper.create(ErrCode.COUPON, "运费券当前只支持配置立减10元的优惠类型！");
        }
        // 特殊规则
        if (info.getExtProp().getPostFree() == Integer.parseInt(IsPostFreeEnum.Yes.getMysqlValue()) ||
                info.getExtProp().getArea() == GivenAreaEnum.YES.getType()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "运费券特殊规则只支持配置是否转赠！");
        }
    }

    /**
     * 券修改基础校验
     */
    protected void updateBaseCheck(CouponBaseInfo info) throws BizError {
        if (info.getId() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券id不能为空");
        }

        CouponConfigPO couponConfigPO = couponConfigRepository.searchCouponById(info.getId());
        if (couponConfigPO == null) {
            throw ExceptionHelper.create(ErrCode.COUPON, "未找到券信息");
        }
        if (!couponConfigPO.getSendScene().equals(info.getSendScene())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "投放场景不可修改");
        }
        if (couponConfigPO.getUseTimeType() != info.getUseTimeType()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用时间类型不可修改");
        }
        if (couponConfigPO.getPromotionType() != info.getPromotionType()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠类型不可修改");
        }
        if (couponConfigPO.getBottomType() != info.getBottomType()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "门槛类型不可修改");
        }
        if (!Objects.equals(couponConfigPO.getBizPlatform(), info.getBizPlatform())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "业务类型不可修改");
        }
//        if (info.getApplyCount() < couponConfigPO.getApplyCount()) {
//            throw ExceptionHelper.create(ErrCode.COUPON, "发放数量只能增加");
//        }
    }

    protected void check(CouponBaseInfo info) throws BizError {
        commonCheck(info);
        // 运费券一些特殊校验逻辑统一校验，此处校验规则多为临时规则（优惠方式固定、履约方式固定...），后续可能删除
        if (Objects.equals(CouponTypeEnum.POSTFREE.getValue(), info.getCouponType())) {
            checkPostFreeCoupon(info);
        } else {
            checkGoodsCoupon(info);
        }
    }
}
