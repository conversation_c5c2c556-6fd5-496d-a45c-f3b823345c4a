package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.bdmall;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.bdmall.po.ActivityMarketCouponPO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

/**
 * @Description: 优惠吗
 * @Date: 2022.03.11 0:24
 */
@Mapper
@Component
public interface ActivityMarketCouponMapper {

    String COUPON_CONFIG_TABLE = "mall_activity_market_coupon";

    /**
     * 获取券配置id
     * @param configId
     * @return
     */
    @Select("select coupon_id from " + COUPON_CONFIG_TABLE + " where coupon_id = #{configId}")
    Long getMarketCouponIdByConfigId(@Param("configId") long configId);

    /**
     * 老券写入新表
     * @return
     */
    @Insert("insert into " + COUPON_CONFIG_TABLE + " (coupon_id,coupon_type,coupon_name,coupon_desc,start_date,end_date,send_limit,is_code,policy,deduct_type,select_type,all_goods," +
            "goods_include,goods_include_ids,client,postfree,is_share,check_price,check_package,add_time,add_user,update_time,update_user,send_channel,use_channel,audit_status," +
            "class_ids,finance_allocate,class_ids_version)"+
            " values " +
            "(#{couponId},#{couponType},#{couponName},#{couponDesc},#{startTime},#{endTime},#{sendLimit},#{isCode},#{policyText},#{deductType},#{selectType},#{allGoods}," +
            "#{goodsInclude},#{getGoodsIncludeIds},#{client},#{postFree},#{isShare},#{checkPrice},#{checkPackage},#{addTime},#{addUser},#{updateTime},#{updateUser},#{sendChannel},#{useChannel},#{auditStatus}," +
            "'','','')")
    Long insertMarketCoupon(ActivityMarketCouponPO po);

    /**
     * 更新优惠券
     * @param po
     * @return
     */
    @Update("<script>update " + COUPON_CONFIG_TABLE + " <set>" +
            "<if test='couponId!=null'>coupon_id=#{couponId},</if>"+
            "<if test='couponType!=null'>coupon_type=#{couponType},</if>"+
            "<if test='couponName!=null'>coupon_name=#{couponName},</if>"+
            "<if test='couponDesc!=null'>coupon_desc=#{couponDesc},</if>"+
            "<if test='startTime!=null'>start_date=#{startTime},</if>"+
            "<if test='endTime!=null'>end_date=#{endTime},</if>"+
            "<if test='sendLimit!=null'>send_limit=#{sendLimit},</if>"+
            "<if test='isCode!=null'>is_code=#{isCode},</if>"+
            "<if test='policyText!=null'>policy=#{policyText},</if>"+
            "<if test='deductType!=null'>deduct_type=#{deductType},</if>"+
            "<if test='selectType!=null'>select_type=#{selectType},</if>"+
            "<if test='allGoods!=null'>all_goods=#{allGoods},</if>"+
            "<if test='goodsInclude!=null'>goods_include=#{goodsInclude},</if>"+
            "<if test='getGoodsIncludeIds!=null'>goods_include_ids=#{getGoodsIncludeIds},</if>"+
            "<if test='client!=null'>client=#{client},</if>"+
            "<if test='postFree!=null'>postfree=#{postFree},</if>"+
            "<if test='isShare!=null'>is_share=#{isShare},</if>"+
            "<if test='checkPrice!=null'>check_price=#{checkPrice},</if>"+
            "<if test='checkPackage!=null'>check_package=#{checkPackage},</if>"+
            "<if test='addTime!=null'>add_time=#{addTime},</if>"+
            "<if test='addUser!=null'>add_user=#{addUser},</if>"+
            "<if test='updateTime!=null'>update_time=#{updateTime},</if>"+
            "<if test='updateUser!=null'>update_user=#{updateUser},</if>"+
            "<if test='sendChannel!=null'>send_channel=#{sendChannel},</if>"+
            "<if test='useChannel!=null'>use_channel=#{useChannel},</if>"+
            "<if test='auditStatus!=null'>audit_status=#{auditStatus},</if>"+
            "<if test='updateTime!=null'>update_time=#{updateTime}</if>"+
            "</set><where> coupon_id=#{couponId}</where></script>")
    Long updateMarketCoupon(ActivityMarketCouponPO po);

    /**
     * 更新券状态
     * @param auditStatus
     * @param couponId
     * @return
     */
    @Update("update " + COUPON_CONFIG_TABLE + " set audit_status= #{auditStatus}, update_time = now() where coupon_id=#{couponId} ")
    Long updateMarketCouponStatus(@Param("auditStatus") String auditStatus, @Param("couponId") long couponId);
}
