package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale.tools;

import com.xiaomi.nr.coupon.admin.api.enums.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale.ServiceCouponCheckTools;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale.ServiceCouponCheckToolsFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.TimesLimitEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;

/**
 * @description 汽车售后-耗材券-校验工具
 * <AUTHOR>
 * @date 2024-12-30 10:50
*/
@Component
public class ConsumablesCouponCheckTools extends ServiceCouponCheckTools {

    @PostConstruct
    public void init() {
        ServiceCouponCheckToolsFactory.register(CouponServiceTypeEnum.CONSUMABLES, this);
    }

    @Override
    public void goodsCheck(Map<Long, Integer> labourHourSsu, Map<Long, Integer> partsSsu) throws BizError {
        // 工时和配件不能全为空
        if (MapUtils.isEmpty(partsSsu) && MapUtils.isEmpty(labourHourSsu)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "工时和配件不能全部为空");
        }
        if (MapUtils.isNotEmpty(labourHourSsu)) {
            // 工时ssu数量只能为1
            boolean labourHourValid = labourHourSsu.values().stream().allMatch(value -> value == 1);
            if (!labourHourValid) {
                throw ExceptionHelper.create(ErrCode.COUPON, "工时ssu数量只能为1");
            }
        }

        if (MapUtils.isNotEmpty(partsSsu)) {
            // 配件ssu数量需大于0
            boolean partsValid = partsSsu.values().stream().allMatch(value -> value > 0);
            if (!partsValid) {
                throw ExceptionHelper.create(ErrCode.COUPON, "配件ssu数量需大于0");
            }
        }
    }

    @Override
    public void createSpecialCheck(CouponBaseInfo info) throws BizError {
        // 券类型校验
        couponTypeCheck(info);

        // 使用次数限制
        timesLimitCheck(info);
    }

    @Override
    public void updateSpecialCheck(CouponBaseInfo info) throws BizError {
        // 券类型校验
        couponTypeCheck(info);

        // 使用次数限制
        timesLimitCheck(info);
    }

    /**
     * 券类型校验
     *
     * @param info 券配置基础信息
     */
    public void couponTypeCheck(CouponBaseInfo info) throws BizError {
        CouponTypeEnum couponTypeEnum = CouponTypeEnum.getByValue(info.getCouponType());

        if (Objects.isNull(couponTypeEnum)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券类型不存在");
        }

        // 耗材券，券类型必须为抵扣券
        if (!Objects.equals(couponTypeEnum, CouponTypeEnum.DEDUCTION)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券类型必须为抵扣券");
        }
    }

    /**
     * 使用次数校验
     *
     * @param info 券配置基础信息
     */
    public void timesLimitCheck(CouponBaseInfo info) throws BizError {
        TimesLimitEnum timesLimitEnum = TimesLimitEnum.valueOf(info.getTimesLimit());

        if (Objects.isNull(timesLimitEnum)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用次数限制参数异常");
        }

        // 耗材券，使用次数限制必须为限制
        if (!Objects.equals(timesLimitEnum, TimesLimitEnum.LIMIT)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用次数限制必须为有限制");
        }
    }
}
