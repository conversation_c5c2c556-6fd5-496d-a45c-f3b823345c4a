package com.xiaomi.nr.coupon.admin.application.dubbo.convert;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.UserEcardLogVO;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.UserEcardVO;
import com.xiaomi.nr.coupon.admin.enums.ecard.EcardStatEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.EcardConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecardconfig.po.EcardLogPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.ecardconfig.po.EcardPo;
import com.xiaomi.nr.coupon.admin.util.NumberUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class UserEcardConvert {

    @Autowired
    private EcardConfigRepository ecardConfigRepository;

    /**
     * 用户礼品卡信息转换
     *
     * @param ecardPoList po
     * @return vo
     */
    public List<UserEcardVO> convertEcardPoToVo(List<EcardPo> ecardPoList) {

        if (CollectionUtils.isEmpty(ecardPoList)) {
            return Collections.emptyList();
        }

        List<Integer> cardTypeIds = ecardPoList.stream().map(EcardPo::getTypeId).distinct().collect(Collectors.toList());
        Map<Integer, String> cardTypeMap = ecardConfigRepository.getEcardTypeNameById(cardTypeIds);

        UserEcardVO ecardVO;
        List<UserEcardVO> ecardVOList = Lists.newArrayList();

        for (EcardPo po : ecardPoList) {
            int stat = po.getStat();
            int typeId = po.getTypeId();
            ecardVO = new UserEcardVO();
            ecardVO.setCardId(po.getCardId());
            ecardVO.setUserId(po.getUserId());
            ecardVO.setCardType(typeId);
            ecardVO.setCardTypeDesc(cardTypeMap.get(typeId));
            ecardVO.setStartTime(Long.parseLong(po.getStartTime().trim()));
            ecardVO.setEndTime(Long.parseLong(po.getEndTime().trim()));
            ecardVO.setStatus(stat);
            ecardVO.setStatusDesc(EcardStatEnum.findNameByValue(stat));
            ecardVO.setVirtual(po.getIsVirtual());
            ecardVO.setFromOrderId(po.getFromOrderId());
            ecardVOList.add(ecardVO);
        }

        return ecardVOList;
    }


    /**
     * 用户礼品卡使用日志转换
     *
     * @param logPoList po
     * @return vo
     */
    public List<UserEcardLogVO> convertEcardLogPoToVo(List<EcardLogPo> logPoList) {
        if (CollectionUtils.isEmpty(logPoList)) {
            return Collections.emptyList();
        }

        UserEcardLogVO vo;
        List<UserEcardLogVO> logVOList = Lists.newLinkedList();

        for (EcardLogPo po : logPoList) {
            vo = new UserEcardLogVO();
            vo.setUserId(po.getUserId());
            vo.setOrderId(po.getOrderId());
            vo.setIncome(NumberUtil.format2DecimalPlaces(po.getIncome(),true));
            vo.setOldBalance(NumberUtil.format2DecimalPlaces(po.getOldBalance(),false));
            vo.setNewBalance(NumberUtil.format2DecimalPlaces(po.getNewBalance(),false));
            vo.setUpdateTime(po.getAddTime());
            vo.setDescription(po.getDescription());
            logVOList.add(vo);
        }

        return logVOList;
    }


    /**
     * 构建礼品卡延期日志
     *
     * @param ecardPo   ecardPo
     * @param delayTime delayTime
     * @return 日志实体
     */
    public EcardLogPo buildEcardLogPo(EcardPo ecardPo, Long delayTime, String operator) {

        EcardLogPo logPo = new EcardLogPo();
        logPo.setCardId(ecardPo.getCardId());
        logPo.setUserId(ecardPo.getUserId());
        logPo.setOrderId(StringUtils.EMPTY);
        logPo.setRefundNo(CommonConstant.ZERO_LONG);
        logPo.setLogType(CommonConstant.ZERO_INT);
        logPo.setIncome(new BigDecimal("0.00"));
        logPo.setOldBalance(ecardPo.getBalance());
        logPo.setNewBalance(ecardPo.getBalance());
        logPo.setOperatorId(CommonConstant.ZERO_LONG);
        logPo.setAddTime(TimeUtil.getNowUnixSecond());
        String desc = operator +"延期: 由["
                + TimeUtil.formatSecond(Long.parseLong(ecardPo.getEndTime().trim()))
                + "]延期至["
                + TimeUtil.formatSecond(delayTime)
                + "]";
        logPo.setDescription(desc);

        String hashCode = DigestUtils.md5Hex(String.format("%d%d%s%s%d", ecardPo.getCardId(), ecardPo.getUserId(),
                ecardPo.getFromOrderId(), "延期", System.nanoTime()));
        logPo.setHashCode(hashCode);

        return logPo;
    }

}
