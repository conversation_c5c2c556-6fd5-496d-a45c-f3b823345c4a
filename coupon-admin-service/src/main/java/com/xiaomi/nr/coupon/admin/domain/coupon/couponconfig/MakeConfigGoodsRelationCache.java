package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.goods.po.GoodsPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.goods.po.PackagePo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigCacheItemPo;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.GoodsConfigRelationItem;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.GoodsConfigRelationPo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.ModeTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.StatusEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 构建优惠券可用商品缓存
 *
 * <AUTHOR>
 */

@Slf4j
@Component
@Deprecated
public class MakeConfigGoodsRelationCache {

    /**
     * 构建货品或套装对应可用的优惠券配置列表缓存
     *
     * @param configs    List<ConfigCacheItemPo>
     * @param goodsMap   Map<String, GoodsPo>
     * @param packageMap Map<String, PackagePo>
     * @return List<GoodsConfigRelationPo>
     */

    public List<GoodsConfigRelationPo> run(List<ConfigCacheItemPo> configs, Map<Long, GoodsPo> goodsMap, Map<Long, PackagePo> packageMap) {
        long nowTime = TimeUtil.getNowUnixSecond();
        List<GoodsConfigRelationPo> result = new ArrayList<>();

        //构建货品对应券配置数据
        try{
            result.addAll(makeSkuCacheData(configs, goodsMap, nowTime));
        } catch (Exception e){
            log.error("coupon.MakeConfigGoodsRelationCache.run, 生成SKU对应可用的优惠券配置列表失败, Exception error=", e);
        }

        //构建套装对应券配置数据
        try{
            result.addAll(makePackageCacheData(configs, packageMap, nowTime));
        } catch (Exception e){
            log.error("coupon.MakeConfigGoodsRelationCache.run, 生成套装对应可用的优惠券配置列表失败, Exception error=", e);
        }
        return result;
    }



    /**
     * 构建货品可用券配置列表缓存数据
     *
     * @param configList 券配置缓存信息列表
     * @param goodsMap   货品信息
     * @param nowTime    当前时间
     * @return List<>    sku与券配置对应关系列表
     */

    private List<GoodsConfigRelationPo> makeSkuCacheData(List<ConfigCacheItemPo> configList, Map<Long, GoodsPo> goodsMap, long nowTime){
        Map<Long, GoodsConfigRelationPo> dataMap = new ConcurrentHashMap<>();
        //循环构建SKU对应的券配置
        configList.stream().forEach(config -> {
            //只处理无码券
            if (!isValidNoCodeCoupon(config, nowTime)) {
                return;
            }

            Long configId = config.getId();
            String configName = config.getName();
            List<String> goodsList = new ArrayList<>(config.getGoodsInclude().getGoods().keySet());

            //构建单张券中每个SKU对应的券配置信息
            goodsList.stream().forEach(goodsId -> {
                Long goodsIdLong = Long.parseLong(goodsId);
                //只构建有效的SKU数据(以GMS商品数据为准)
                if(!goodsMap.containsKey(goodsIdLong)){
                    return;
                }

                Long sku = goodsMap.get(goodsIdLong).getSku();
                //SKU数据非首次构建,直接追加券配置信息即可
                if (dataMap.containsKey(sku)) {
                    dataMap.get(sku).getList().add(new GoodsConfigRelationItem(configId, configName));
                } else {
                    dataMap.put(sku, createGoodsConfigRelation(configId, configName, sku, GoodsLevelEnum.Sku.getValue()));
                }
            });
        });

        //没匹配到的sku,将它的券配置信息置空
        goodsMap.values().forEach(goodsPo -> {
            if(!dataMap.containsKey(goodsPo.getSku())){
                dataMap.put(goodsPo.getSku(), createGoodsConfigRelation(null,null, goodsPo.getSku(), GoodsLevelEnum.Sku.getValue()));
            }

        });

        return new ArrayList<>(dataMap.values());
    }



    /**
     * 构建套装可用券配置列表缓存数据
     *
     * @param configList 券配置缓存信息列表
     * @param packageMap 套装信息
     * @param nowTime    当前时间
     * @return List<>    sku与券配置对应关系列表
     */

    private List<GoodsConfigRelationPo> makePackageCacheData(List<ConfigCacheItemPo> configList, Map<Long, PackagePo> packageMap, long nowTime){
        Map<Long, GoodsConfigRelationPo> dataMap = new ConcurrentHashMap<>();
        //循环构建套装对应的券配置
        configList.stream().forEach(config -> {
            //只处理无码券
            if (!isValidNoCodeCoupon(config, nowTime)) {
                return;
            }
            Long configId = config.getId();
            String configName = config.getName();
            List<String> packageList = new ArrayList<>(config.getGoodsInclude().getPackages().keySet());

            //构建单张券中每个套装对应的券配置信息
            packageList.stream().forEach(packageId -> {
                Long packageLong = Long.parseLong(packageId);
                //只构建有效的套装数据(以GMS套装数据为准)
                if(!packageMap.containsKey(packageLong)){
                    return;
                }

                //套装数据非首次构建,直接追加券配置信息即可
                if (dataMap.containsKey(packageLong)) {
                    dataMap.get(packageLong).getList().add(new GoodsConfigRelationItem(configId, configName));

                } else {
                    dataMap.put(packageLong, createGoodsConfigRelation(configId, configName, packageLong, GoodsLevelEnum.Package.getValue()));
                }
            });
        });


        //没匹配到的package,将它的券配置信息置空
        packageMap.values().forEach(packagePo -> {
            if(!dataMap.containsKey(packagePo.getPackageId())){
                dataMap.put(packagePo.getPackageId(), createGoodsConfigRelation(null,null, packagePo.getPackageId(), GoodsLevelEnum.Package.getValue()));

            }
        });

        return new ArrayList<>(dataMap.values());
    }



    /**
     * 创建单个SKU/套装对应的券配置的基础信息
     *
     * @param configId   券配置id
     * @param configName 券配置名称
     * @param id         SKU/套装id
     * @param level      sku/package
     * @return GoodsConfigRelationPo
     */

    private GoodsConfigRelationPo createGoodsConfigRelation(Long configId, String configName, Long id, String level){
        GoodsConfigRelationPo goodsConfigRelationPo = new GoodsConfigRelationPo();
        List<GoodsConfigRelationItem> configInfoList = new ArrayList<>();
        if(!Objects.isNull(configId)){
            configInfoList.add(new GoodsConfigRelationItem(configId, configName));
        }

        goodsConfigRelationPo.setId(id);
        goodsConfigRelationPo.setLevel(level);
        goodsConfigRelationPo.setCacheCreateTime(TimeUtil.getNowDateTime());
        goodsConfigRelationPo.setList(configInfoList);
        return goodsConfigRelationPo;
    }



    /**
     * 判断是否为有效券无码券
     *
     * @param configInfo ConfigCacheItemPo
     * @param nowTime    long
     * @return Boolean
     */
    private static Boolean isValidNoCodeCoupon(ConfigCacheItemPo configInfo, long nowTime) {
        //非无码券
        if (!ModeTypeEnum.NoCode.getRedisValue().equals(configInfo.getModeType())) {
            return false;
        }

        //已过期
        if (configInfo.getGlobalEndTime() <= nowTime) {
            return false;
        }

        //非审核通过状态
        if (!StatusEnum.Approved.getRedisValue().equals(configInfo.getStatus())) {
            return false;
        }
        return true;
    }

}