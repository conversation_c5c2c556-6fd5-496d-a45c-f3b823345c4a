package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.impl;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.OldCouponRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.oldcoupon.OldCouponInfo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 老redis数据写入
 * @author: hejiapeng
 * @Date 2022/3/8 6:33 下午
 * @Version: 1.0
 **/
@Component
@Slf4j
public class OldCouponRedisDaoImpl implements OldCouponRedisDao {

    /**
     * 新有码和无码优惠券信息
     * type: string
     * nr:coupon:oldInfo:{configId} (注：configId: 类型ID
     */
    public static final String KEY_COUPON_NEW_INFO = "nr:coupon:oldInfo:{configId}";

    public static final String KEY_COUPON_VALID_LIST = "nr:coupon:validconfigId:list";

    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringPulseTypeRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Override
    public void setOldCouponInfo(OldCouponInfo oldCouponCachePo) {
        String configInfoStr = GsonUtil.toJson(oldCouponCachePo);
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String key = StringUtil.formatContent(KEY_COUPON_NEW_INFO, String.valueOf(oldCouponCachePo.getBasetype().getId()));
        try {
            operations.set(key, configInfoStr);
        } catch (Exception e) {
            log.warn("OldCouponRedisDao.setOldCouponInfo, 初次写入redis券商品缓存失败, key:{}, err:", key, e);
            operations.set(key, configInfoStr);
        }
    }

    @Override
    public void setValidConfigIds(List<Long> validConfigIds) {
        String configIds = GsonUtil.toJson(validConfigIds);
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        try {
            operations.set(KEY_COUPON_VALID_LIST, configIds);
        } catch (Exception e) {
            log.warn("OldCouponRedisDao.setValidConfigIds, 初次写入redis有效券列表缓存失败, key:{}, err:", KEY_COUPON_VALID_LIST, e);
            operations.set(KEY_COUPON_VALID_LIST, configIds);
        }
    }

    @Override
    public void deleteOldCouponInfo(List<Long> configIds) {
        List<String> keys = new ArrayList<>(configIds.size());
        for (Long configId:configIds ) {
            String key = StringUtil.formatContent(KEY_COUPON_NEW_INFO, String.valueOf(configId));
            keys.add(key);
        }
        redisTemplate.delete(keys);
    }
}
