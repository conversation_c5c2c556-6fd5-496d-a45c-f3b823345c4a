package com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.carcoupon;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.SearchUserCouponListParam;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.usercoupon.po.UserCouponPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 汽车售后服务券mapper
 *
 * <AUTHOR>
 * @date 2024/2/29
 */
@Mapper
@Component
public interface CarCouponMapper {

    String SELECT_PARAMS = " id, vid, user_id, type_id, activity_id, start_time, end_time,days, stat, order_id, use_time, " +
            "expire_time, is_pass, admin_id, admin_name,add_time, send_type, from_order_id, replace_money, invalid_time," +
            "last_update_time, offline, reduce_express, parent_id, send_channel, biz_platform ";

    /**
     * 查询vid券总数
     */
    @Select("<script>select count(1) from tb_car_coupon where 1=1 " +
            "<if test='uid>0'> and user_id=#{uid}</if>" +
            "<if test='configIds!=null and configIds.size()>0'> and type_id in <foreach collection='configIds' item='configId' index='index' open='(' close=')' separator=','>#{configId}</foreach> </if>"+
            "<if test='couponStatus ==\"unused\"'> and stat='unused' and end_time &gt; unix_timestamp(now()) </if>" +
            "<if test='couponStatus == \"expired\"'> and stat in ('unused','expired') and end_time &lt;= unix_timestamp(now()) </if>" +
            "<if test='couponStatus != null and couponStatus != \"unused\" and couponStatus != \"expired\"'> and stat=#{couponStatus} </if>" +
            "<if test='couponId!=null and couponId>0'> and id=#{couponId} </if>" +
            "<if test='vid!=null and vid!=\"\"'> and vid=#{vid} </if>" +
            "</script>")
    Long selectCount(SearchUserCouponListParam param);

    /**
     * 查询vid券列表
     */
    @Select("<script>select "+ SELECT_PARAMS+ ", vid from tb_car_coupon where 1=1"  +
            "<if test='uid>0'> and user_id=#{uid}</if>" +
            "<if test='configIds!=null and configIds.size()>0'> and type_id in <foreach collection='configIds' item='configId' index='index' open='(' close=')' separator=','>#{configId}</foreach> </if>"+
            "<if test='couponStatus ==\"unused\"'> and stat='unused' and end_time &gt; unix_timestamp(now()) </if>" +
            "<if test='couponStatus == \"expired\"'> and stat in ('unused','expired') and end_time &lt;= unix_timestamp(now()) </if>" +
            "<if test='couponStatus != null and couponStatus != \"unused\" and couponStatus != \"expired\"'> and stat=#{couponStatus} </if>" +
            "<if test='couponId!=null and couponId>0'> and id=#{couponId} </if>" +
            "<if test='vid!=null and vid!=\"\"'> and vid=#{vid} </if>" +
            " order by  ${orderBy}  ${orderDirection} "+
            " limit #{offset}, #{limit}"+
            "</script>")
    List<UserCouponPO> selectList(SearchUserCouponListParam param);

    @Select("<script> select "+SELECT_PARAMS+" from tb_car_coupon where vid=#{vid} and id =#{couponId} " +
            "</script>")
    UserCouponPO selectByCouponId(@Param("vid") String vid, @Param("couponId") long couponId);

    @Update("<script> update tb_car_coupon set stat=#{newCouponStatus},invalid_time=#{invalidTime} where id =#{couponId} and vid=#{vid} and stat='unused' " +
            "</script>")
    Long destroyCoupon(@Param("vid") String vid, @Param("couponId") long couponId, @Param("newCouponStatus") String newCouponStatus, @Param("invalidTime") long invalidTime);

}
