package com.xiaomi.nr.coupon.admin.enums.couponconfig;

public enum CouponConfigStatusEnum {

    ONLINE(1, "上线"),
    OFFLINE(2, "下线"),
    STOP_FETCHING(3, "终止领取");

    public int code;
    public String name;

    public int getCode() {
        return this.code;
    }
    public String getName() {
        return name;
    }

    CouponConfigStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CouponConfigStatusEnum findByCode(int value) {
        CouponConfigStatusEnum[] values = CouponConfigStatusEnum.values();
        for (CouponConfigStatusEnum item : values) {
            if (item.getCode() == value) {
                return item;
            }
        }
        return null;
    }
}
