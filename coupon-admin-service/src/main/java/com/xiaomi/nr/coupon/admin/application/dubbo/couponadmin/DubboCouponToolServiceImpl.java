package com.xiaomi.nr.coupon.admin.application.dubbo.couponadmin;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xiaomi.miliao.zookeeper.ZKClient;
import com.xiaomi.miliao.zookeeper.ZKFacade;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.GoodConfigIdsRequest;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.GenAllConfigRedisCacheRequest;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponToolService;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponConfigConvert;
import com.xiaomi.nr.coupon.admin.application.schedule.constant.ScheduleConstant;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.CouponConfigRefreshService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.CouponConfigWarningService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.CouponDownLoadService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.GoodsCouponRefreshService;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.infrastruture.notify.robotmessage.RobotSendMessageUtil;
import com.xiaomi.nr.coupon.admin.infrastruture.notify.robotmessage.po.Card;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponLogRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponMoveRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.GoodsCouponPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hejiapeng
 * @Date 2022/3/3 12:51 下午
 * @Version: 1.0
 **/
@ApiModule(value = "券后台工具服务", apiInterface = DubboCouponToolService.class)
@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
@Component
@Slf4j
public class DubboCouponToolServiceImpl implements DubboCouponToolService {

    private final ZKClient zkClient = ZKFacade.getAbsolutePathClient();

    @Resource
    private CouponConfigRefreshService couponConfigRefreshService;

    @Resource
    private GoodsCouponRefreshService goodsCouponRefreshService;

    @Resource
    private CouponMoveRepository couponMoveRepository;

    @Autowired
    private CouponConfigConvert couponConfigConvert;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Autowired
    private CouponLogRepository couponLogRepository;

    @Autowired
    private ThreadPoolTaskExecutor asyncExecutor;

    @Autowired
    private CouponConfigWarningService couponConfigWarningService;

    @Autowired
    private RobotSendMessageUtil robotMessageUtil;

    @Autowired
    private CouponDownLoadService couponDownLoadService;


    /**
     * 查询商品可用券接口
     * caller:乾坤后台调用
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("查询商品可用券接口")
    public Result<BasePageResponse<CouponConfigListVO>> searchGoodConfigId(GoodConfigIdsRequest request) {
        BasePageResponse<CouponConfigListVO> response = new BasePageResponse<>(request.getPageNo(), request.getPageSize());
        try {

            if (StringUtils.isEmpty(request.getLevel())) {
                Result.fail(GeneralCodes.ParamError, "商品类别不能为空");
            }

            if (request.getItemId() == null || request.getItemId() <= 0) {
                Result.fail(GeneralCodes.ParamError, "商品Id不能为空");
            }

            if (request.getUseAbleType() == null) {
                Result.fail(GeneralCodes.ParamError, "有效类型不能为空");
            }

            PageHelper.startPage(request.getPageNo(), request.getPageSize());

            List<CouponConfigPO> configPOList = couponConfigRepository.getGoodCouponConfigs(request);

            if (CollectionUtils.isEmpty(configPOList)) {
                return Result.success(response);
            }

            List<String> sendScene = configPOList.stream().map(CouponConfigPO::getSendScene).collect(Collectors.toList());
            Map<String, String> sceneMap = couponSceneRepository.selectBySceneCodes(sendScene);

            List<CouponConfigListVO> couponConfigListVOS = couponConfigConvert.convertToCouponConfigListVO(configPOList, sceneMap);

            PageInfo<CouponConfigPO> pageInfo = new PageInfo<>(configPOList);
            response.setList(couponConfigListVOS);
            response.setTotalCount(pageInfo.getTotal());
            response.setTotalPage(pageInfo.getPages());

            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponToolService.updateCouponGoods request={}, error: ", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 全量更新缓存券配置信息
     * caller: dayu定时任务配置
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("全量更新缓存券配置信息")
    public Result<Boolean> loadAllCouponConfig(GenAllConfigRedisCacheRequest request) {
        long runStartTime = TimeUtil.getNowUnixMillis();
        asyncExecutor.submit(() -> {
            try {
                log.info("DubboCouponToolService.loadAllCouponConfig, 全量生成优惠券缓存任务开始");
                couponConfigRefreshService.updateAllCouponToRedis(request.getItemIds(), request.getFull());
                log.info("DubboCouponToolService.loadAllCouponConfig, 全量生成优惠券缓存任务结束, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));
            } catch (Exception e) {
                log.error("DubboCouponToolService.loadAllCouponConfig, 全量生成优惠券缓存任务失败, runTime={}毫秒, {}", TimeUtil.sinceMillis(runStartTime), e);
            }
        });
        return Result.success(true);
    }

    /**
     * 全量刷新商品券配置关系
     * caller: dayu定时任务配置
     *
     * @return
     */
    @Override
    @ApiDoc("全量刷新商品券配置关系")
    public Result<Boolean> loadAllGoodsCoupon() {
        long runStartTime = TimeUtil.getNowUnixMillis();
        asyncExecutor.submit(() -> {
            try {
                log.info("DubboCouponToolService.loadAllGoodsCoupon, 全量生成商品可用优惠券缓存任务开始");
                Timestamp maxUpdateTime = goodsCouponRefreshService.updateRedisGoodsCouponRel(true, "0");
                zkClient.updatePersistent(ScheduleConstant.COUPON_CONFIG_CHANGE_RECORD_PATH, maxUpdateTime.toString());
                log.info("DubboCouponToolService.loadAllGoodsCoupon, 全量生成商品可用优惠券缓存任务结束, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));
            } catch (Exception e) {
                log.error("DubboCouponToolService.loadAllGoodsCoupon, 全量生商品可用优惠券缓存任务失败, runTime={}毫秒, {}", TimeUtil.sinceMillis(runStartTime), e);
            }
        });
        return Result.success(true);
    }

    /**
     * 老数据迁移
     *
     * @param request
     * @return
     */
    @Override
    public Result<Boolean> moveOldCouponByIds(GenAllConfigRedisCacheRequest request) {
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            log.info("DubboCouponToolService.moveOldCouponByIds, 迁移老优惠券任务开始");
            couponMoveRepository.moveOldCoupon(request.getFull(), request.getItemIds());
            log.info("DubboCouponToolService.moveOldCouponByIds, 迁移老优惠券任务结束, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));
            return Result.success(true);
        } catch (Exception e) {
            log.error("DubboCouponToolService.loadAllGoodsCoupon, 迁移老优惠券任务是爱, runTime={}毫秒, {}", TimeUtil.sinceMillis(runStartTime), e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<Boolean> couponEarlyWarning() {
        asyncExecutor.submit(() -> {
            try {
                long runStartTime = TimeUtil.getNowUnixMillis();

                log.info("DubboCouponToolService.couponEarlyWarning, 优惠券预警任务开始");

                Map<Long, CouponConfigPO> couponConfigPOMap = couponConfigRepository.getWarningCouponConfigs();
                if (MapUtils.isEmpty(couponConfigPOMap)) {
                    return;
                }

                Map<String, List<Long>> operatorMap = couponLogRepository.getLatestModifyConfigIdMap(couponConfigPOMap.keySet());

                for (Map.Entry<String, List<Long>> operatorItem : operatorMap.entrySet()) {
                    List<CouponConfigPO> couponConfigPOS = Lists.newArrayList();
                    operatorItem.getValue().stream().map(couponConfigPOMap::get).filter(Objects::nonNull).forEach(couponConfigPOS::add);

                    Map<Integer, List<Long>> warningCheckResult = couponConfigWarningService.couponWarningCheck(couponConfigPOS);

                    if (MapUtils.isEmpty(warningCheckResult)) {
                        continue;
                    }

                    Card card = Card.getCouponWarningCard(warningCheckResult, operatorItem.getKey());

                    robotMessageUtil.sendPrivateCard(card, operatorItem.getKey());
                }
                log.info("DubboCouponToolService.couponEarlyWarning, 优惠券预警任务结束, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));
            } catch (Exception e) {
                log.error("DubboCouponToolService.couponEarlyWarning, 优惠券预警任务失败", e);
            }
        });
        return Result.success(true);
    }

    @Override
    public Result<Integer> exportGoodsCoupon(GoodConfigIdsRequest request) {

        try {
            log.info("DubboCouponToolService.exportGoodsCoupon start, request:{}", request);

            String account = UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix();

            // 获取可用券信息
            List<CouponConfigPO> configPOList = couponConfigRepository.getGoodCouponConfigs(request);
            if (CollectionUtils.isEmpty(configPOList)) {
                return Result.fail(GeneralCodes.NotFound, "下载文件为空，请确认");
            }

            // 下载逻辑异步执行
            asyncExecutor.submit(() -> {
                try {

                    List<String> sendScene = configPOList.stream().map(CouponConfigPO::getSendScene).collect(Collectors.toList());

                    Map<String, String> sceneMap = couponSceneRepository.selectBySceneCodes(sendScene);

                    List<GoodsCouponPO> goodsCouponPOS = couponConfigConvert.convertToGoodsCouponPO(configPOList, sceneMap);

                    String fileUrl = couponDownLoadService.uploadGoodsCoupon(goodsCouponPOS, request.getItemId());

                    robotMessageUtil.sendPrivateCard(Card.getGoodsCouponCard(request.getItemId(), fileUrl), account);

                    log.info("DubboCouponToolService.exportGoodsCoupon async execute success. request:{}", request);

                } catch (Exception e) {
                    log.error("DubboCouponToolService.exportGoodsCoupon async execute error. request:{}, error:{}", request, e);
                }
            });

            return Result.success(CommonConstant.ZERO_INT);
        } catch (Exception e) {
            log.error("DubboCouponToolService.exportGoodsCoupon exception, request:{}", request, e);
            return Result.fromException(e);
        }
    }

    public Result<Boolean> forceMergeIndex() {
        try {
            log.info("DubboCouponToolService.forceMergeIndex start");
            couponConfigRepository.forceMergeIndex();
        } catch (Exception e) {
            log.error("DubboCouponToolService.forceMergeIndex, 优惠券索引forceMergeIndex任务失败", e);
        }
        return Result.success(true);
    }
}