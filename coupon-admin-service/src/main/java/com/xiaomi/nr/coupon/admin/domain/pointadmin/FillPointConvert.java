package com.xiaomi.nr.coupon.admin.domain.pointadmin;

import com.google.common.collect.Lists;
import com.mi.tools.CollectionUtils;
import com.mi.tools.StringUtils;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task.CreateFillPointTaskRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task.PointTaskListVO;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task.PointTaskVO;
import com.xiaomi.nr.coupon.admin.enums.task.CouponTaskTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.task.TaskStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.task.UserGroupTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.point.PointFillPathConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponTaskRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.PointFillDetailPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.PointFillPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.FillCouponTaskPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.Param;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.ResultOutPut;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 灌积分数据包装
 * @date 2024-08-15 10:38
 */
@Component
@Slf4j
public class FillPointConvert {

    @Autowired
    private CouponTaskRepository fillCouponTaskRepository;

    /**
     * 包装任务信息返回值
     *
     * @param parentTasks         父任务信息列表
     * @param childTasks          子任务信息列表
     * @param parentIdToChildTask 父任务id对应子任务id
     * @param idToPointConfig     积分配置id对应积分配置信息
     * @return 任务列表返回值
     */
    public List<PointTaskListVO> buildTaskVOs(List<FillCouponTaskPO> parentTasks, Map<Long, FillCouponTaskPO> childTasks, Map<Long, List<Long>> parentIdToChildTask, Map<Long, CarPointsBatchConfigPo> idToPointConfig) {
        List<PointTaskListVO> taskVOList = new ArrayList<>();
        for (FillCouponTaskPO taskPO : parentTasks) {
            PointTaskListVO taskVO = new PointTaskListVO();
            long configId = taskPO.getConfigId();
            taskVO.setTaskId(taskPO.getTaskId());
            taskVO.setTaskName(taskPO.getTaskName());
            taskVO.setPointBatchId(configId);
            taskVO.setCreateTime(new Date(taskPO.getCreateTime()));
            taskVO.setPointBatchName(idToPointConfig.containsKey(configId) ? idToPointConfig.get(configId).getName() : "--");
            if (taskPO.getStartTime() > CommonConstant.ZERO_LONG) {
                taskVO.setStartTime(TimeUtil.convertLongToDate(taskPO.getStartTime()));
            }
            taskVO.setCreator(StringUtils.isNotEmpty(taskPO.getCreator()) ? taskPO.getCreator() : "--");

            if (StringUtils.isNotEmpty(taskPO.getParams())) {
                Param param = GsonUtil.fromJson(taskPO.getParams(), Param.class);
                taskVO.setAddress(Objects.isNull(param) ? null : param.getAddress());
            }

            //申请总数
            Param param = GsonUtil.fromJson(taskPO.getParams(), Param.class);
            taskVO.setTotalCount(param.getApplyCount());

            //成功数
            long parentSuccessCount = CommonConstant.ZERO_LONG;
            if (StringUtils.isNotEmpty(taskPO.getResult())) {
                ResultOutPut resultOutPut = GsonUtil.fromJson(taskPO.getResult(), ResultOutPut.class);
                parentSuccessCount = Objects.isNull(resultOutPut) ? CommonConstant.ZERO_LONG : resultOutPut.getSuccessCount();
            }

            long parentId = taskPO.getTaskId();
            if (parentIdToChildTask.containsKey(parentId)) {
                // 计算成功总数（包括子任务）
                long successCount = parentIdToChildTask.get(parentId).stream().mapToLong(childId -> {
                    if (childTasks.containsKey(childId) && org.apache.commons.lang.StringUtils.isNotEmpty(childTasks.get(childId).getResult())) {
                        ResultOutPut resultOutPut = GsonUtil.fromJson(childTasks.get(childId).getResult(), ResultOutPut.class);
                        return Objects.isNull(resultOutPut) ? CommonConstant.ZERO_LONG : resultOutPut.getSuccessCount();
                    }
                    return CommonConstant.ZERO_LONG;
                }).sum();
                taskVO.setSuccessCount(successCount + parentSuccessCount);

                // 获取最新的子任务，填充展示信息
                long lastTaskId = parentIdToChildTask.get(parentId).stream().mapToLong(childId -> childId).max().orElse(-1);
                if (childTasks.containsKey(lastTaskId)) {
                    FillCouponTaskPO childTask = childTasks.get(lastTaskId);
                    if (childTask.getFinishTime() > 0) {
                        taskVO.setEndTime(TimeUtil.convertLongToDate(childTask.getFinishTime()));
                    }
                    taskVO.setProcessRate(childTask.getProcessRate());
                    taskVO.setStatus(convertStatus(taskPO));
                }

            } else {
                // 不包含子任务
                if (taskPO.getFinishTime() > 0) {
                    taskVO.setEndTime(TimeUtil.convertLongToDate(taskPO.getFinishTime()));
                }
                taskVO.setProcessRate(taskPO.getProcessRate());
                taskVO.setStatus(convertStatus(taskPO));
                taskVO.setSuccessCount(parentSuccessCount);
            }

            if (taskVO.getSuccessCount() >= taskVO.getTotalCount()) {
                taskVO.setProcessRate(100L);
            }
            taskVOList.add(taskVO);
        }
        return taskVOList;
    }

    /**
     * 灌积分任务状态转换
     *
     * @param taskPO 任务实体
     * @return 灌积分状态值
     */
    public int convertStatus(FillCouponTaskPO taskPO) {
        int status = taskPO.getStatus();

        //进行中，运行状态或者失败状态，并且重试次数没有超过三次，前端显示进行中
        if (status == TaskStatusEnum.RUNNING.getCode() || status == TaskStatusEnum.AWAIT.getCode()
                || status == TaskStatusEnum.READY.getCode() || status == TaskStatusEnum.PRE_AWAIT.getCode()
                || (status == TaskStatusEnum.FAIL.getCode() && taskPO.getRetryTimes() < CommonConstant.THREE_INT)) {
            return TaskStatusEnum.RUNNING.getCode();
        }

        //失败
        if (status == TaskStatusEnum.ERROR.getCode() || status == TaskStatusEnum.FAIL.getCode()) {
            return TaskStatusEnum.FAIL.getCode();
        }

        //已完成
        return TaskStatusEnum.SUCCESS.getCode();
    }

    /**
     * 封装灌积分任务实体
     *
     * @param request 创建灌积分任务参数
     * @return 灌积分任务实体
     */
    public FillCouponTaskPO convertFillCouponTaskPO(CreateFillPointTaskRequest request) {
        if (ObjectUtils.isEmpty(request)) {
            return null;
        }

        FillCouponTaskPO taskPO = new FillCouponTaskPO();
        taskPO.setTaskName(request.getTaskName());
        taskPO.setConfigId(request.getPointBatchId());
        taskPO.setCreateTime(TimeUtil.getNowUnixMillis());
        taskPO.setType(CouponTaskTypeEnum.POINT_FILL.getCode());
        taskPO.setCreator(request.getCreator());
        taskPO.setStatus(TaskStatusEnum.AWAIT.getCode());
        taskPO.setDepartmentId(ObjectUtils.isEmpty(request.getDepartmentId()) ? String.valueOf(CommonConstant.ZERO_INT) : request.getDepartmentId());
        taskPO.setSource("2");
        taskPO.setBizPlatform(request.getBizType());

        Param param = new Param();
        param.setApplyCount(request.getApplyCount());
        param.setAddress(request.getHdfsAddress());
        param.setCustomizeGroup(request.getSendType());
        param.setDataType(UserGroupTypeEnum.CUSTOMIZE_USER_GROUP.getCode());
        param.setSendScene(request.getSendScene());
        param.setApplyPointCount(request.getApplyPointCount());

        // 灌积分中不去重
        param.setDistinct(CommonConstant.ONE_INT);
        param.setCount(request.getApplyCount());

        taskPO.setParams(GsonUtil.toJson(param));
        return taskPO;
    }

    /**
     * 封装灌积分任务详情信息
     *
     * @param taskPO 灌积分任务实体
     * @param pointBatchName 积分批次名称
     * @param childTasks 子任务列表
     * @return 灌积分任务详情信息
     * @throws BizError 异常
     */
    public PointTaskVO convertPointTaskVO(FillCouponTaskPO taskPO, String pointBatchName, List<FillCouponTaskPO> childTasks) throws BizError {
        if (ObjectUtils.isEmpty(taskPO)) {
            throw ExceptionHelper.create(ErrCode.POINT, "灌积分任务不存在");
        }

        PointTaskVO taskVO = new PointTaskVO();
        taskVO.setName(taskPO.getTaskId() + "_" + taskPO.getTaskName());
        taskVO.setPointBatchName(taskPO.getConfigId() + "_" + pointBatchName);

        Param param = GsonUtil.fromJson(taskPO.getParams(), Param.class);
        taskVO.setPlanCount(Objects.isNull(param) ? null : param.getApplyCount());
        taskVO.setHdfsAddr(Objects.isNull(param) ? null : param.getAddress());
        taskVO.setSendType(Objects.isNull(param) ? null : param.getCustomizeGroup());

        if (CollectionUtils.isNotEmpty(childTasks)){
            FillCouponTaskPO lastTask = childTasks.stream().max(Comparator.comparing(FillCouponTaskPO::getTaskId)).get();
            taskVO.setProcessRate(lastTask.getProcessRate());
            taskVO.setStatus(convertStatus(lastTask));
        } else {
            taskVO.setProcessRate(taskPO.getProcessRate());
            taskVO.setStatus(convertStatus(taskPO));
        }
        return taskVO;
    }

    /**
     * 灌积分任务重试构造子任务
     *
     * @param parentTask 父任务
     * @param lastChildTaskId 最近一次的子任务id
     * @param creator 创建人
     * @return FillCouponTaskPO
     */
    public FillCouponTaskPO buildChildTask(FillCouponTaskPO parentTask, long lastChildTaskId, String creator) {
        FillCouponTaskPO childTask = new FillCouponTaskPO();
        childTask.setConfigId(parentTask.getConfigId());
        childTask.setStatus(TaskStatusEnum.AWAIT.getCode());
        childTask.setType(CouponTaskTypeEnum.POINT_FILL.getCode());
        childTask.setParentId(parentTask.getTaskId());

        Param parentParam = GsonUtil.fromJson(parentTask.getParams(), Param.class);
        Param param = new Param();
        param.setApplyCount(Objects.isNull(parentParam) ? CommonConstant.ZERO_LONG : parentParam.getApplyCount());
        param.setAddress(PointFillPathConstant.FILL_POINT_FAIL_UID_DATA_PATH + parentTask.getTaskId() + "_" + lastChildTaskId + ".txt");
        param.setApplyPointCount(Objects.isNull(parentParam) ? 0 : parentParam.getApplyPointCount());

        childTask.setParams(GsonUtil.toJson(param));
        childTask.setTaskName(parentTask.getTaskName());
        childTask.setCreateTime(TimeUtil.getNowUnixSecond());
        childTask.setCreator(StringUtils.isNotEmpty(creator) ? creator : "bug");
        childTask.setDepartmentId(String.valueOf(CommonConstant.ZERO_INT));
        childTask.setSource("2");
        childTask.setBizPlatform(parentTask.getBizPlatform());
        return childTask;
    }

    public List<PointFillDetailPO> convertToPointFillDetail(List<PointFillPO> allEntities, List<PointFillDetailPO> failEntityDetails) throws BizError {
        if (CollectionUtils.isNotEmpty(allEntities) && allEntities.size() > 20000) {
            throw ExceptionHelper.create(ErrCode.POINT, "灌积分实体集大于20000");
        }

        Map<PointFillPO, Integer> failEntityMap = new HashMap<>();
        Map<PointFillPO, LinkedList<String>> failUserDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(failEntityDetails)) {
            for (PointFillDetailPO item : failEntityDetails) {
                PointFillPO pointFillPO = new PointFillPO(item.getMid(), item.getPointCount());
                if(failEntityMap.containsKey(pointFillPO)) {
                    failEntityMap.put(pointFillPO, failEntityMap.get(pointFillPO) + CommonConstant.ONE_INT);
                    failUserDetailMap.get(pointFillPO).add(item.getFailReason());
                    continue;
                }

                failEntityMap.put(pointFillPO, CommonConstant.ONE_INT);
                failUserDetailMap.put(pointFillPO, Lists.newLinkedList(Collections.singletonList(item.getFailReason())));
            }
        }

        List<PointFillDetailPO> result = Lists.newLinkedList();
        for(PointFillPO entity : allEntities) {
            if (failEntityMap.containsKey(entity) && failEntityMap.get(entity) > CommonConstant.ZERO_INT) {
                result.add(new PointFillDetailPO(entity.getMid(), entity.getPointCount(), failUserDetailMap.get(entity).getLast()));
                failEntityMap.put(entity, failEntityMap.get(entity) - CommonConstant.ONE_INT);
                failUserDetailMap.get(entity).removeLast();
                continue;
            }
            result.add(new PointFillDetailPO(entity.getMid(), entity.getPointCount(), ""));
        }

        return result;
    }
}
