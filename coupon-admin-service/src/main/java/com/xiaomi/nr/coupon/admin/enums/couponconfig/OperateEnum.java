package com.xiaomi.nr.coupon.admin.enums.couponconfig;

/**
 * 优惠券配置操作 枚举
 *
 * <AUTHOR>
 */
public enum OperateEnum {

    /**
     * 创建
     */
    Create("create", "创建"),

    /**
     * 更新
     */
    Update("update", "更新"),

    /**
     * 终止
     */
    Cancel("cancel", "终止");

    private final String value;
    private final String name;

    OperateEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static String findNameByValue(String value) {
        OperateEnum[] values = OperateEnum.values();
        for (OperateEnum item : values) {
            if (item.getValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }
}

