package com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.oldcoupon;

import com.google.gson.reflect.TypeToken;
import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.UseChannelVO;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.UseChannelClientRelationDo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.ExtPropPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.GoodItemPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.GoodsItemPo;
import com.xiaomi.nr.coupon.admin.infrastruture.staticdata.UseChannelClientRel;
import com.xiaomi.nr.coupon.admin.util.CouponCollectionUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 构建券配置缓存
 *
 */
@Slf4j
@Component
public class OldCouponConvert {

    @Autowired
    private UseChannelClientRel useChannelClientRel;

    /**
     * 构建老缓存结构
     * @param couponConfigPO
     * @return
     */
    public OldCouponInfo serializeOldCouponInfo(CouponConfigPO couponConfigPO){

        OldCouponInfo oldCouponInfo = new OldCouponInfo();

        oldCouponInfo.setBasetype(serializeTypeBase(couponConfigPO));

        oldCouponInfo.setCondition(serializeCondition(couponConfigPO));

        oldCouponInfo.setPolicy(serializePolicy(couponConfigPO));

        return oldCouponInfo;
    }

    /**
     * 构建基本信息
     * @param couponConfigPO
     * @return
     */
    private TypeBase serializeTypeBase(CouponConfigPO couponConfigPO){
        TypeBase typeBase = new TypeBase();
        typeBase.setId(couponConfigPO.getId());
        typeBase.setType(convertUseType(couponConfigPO.getPromotionType()).getValue());
        typeBase.setDeductType(convertDeductType(couponConfigPO));
        if(CouponConfigStatusEnum.ONLINE.getCode() == couponConfigPO.getStatus()){
            typeBase.setStat(StatusEnum.Approved.getRedisValue());
        }else{
            typeBase.setStat(StatusEnum.Cancel.getRedisValue());
        }
        typeBase.setName(couponConfigPO.getName());
        typeBase.setDesc(StringUtils.EMPTY);
        typeBase.setRangeDesc(couponConfigPO.getCouponDesc());
        typeBase.setRangeLongDesc(StringUtils.EMPTY);
        typeBase.setValueDesc(StringUtils.EMPTY);
        ExtPropPO extPropPO = GsonUtil.fromJson(couponConfigPO.getExtProp(), ExtPropPO.class);
        typeBase.setPostFree(convertPostFree(extPropPO));
        typeBase.setIsShare(convertShare(extPropPO));
        typeBase.setProMember(convertProMember(extPropPO));
        typeBase.setSpecialStore(extPropPO.getSpecialStore() == CommonConstant.ONE_INT ? 1 : 2);
        typeBase.setShowTitle(getShowTitle(couponConfigPO));
        typeBase.setShowUnit(getShowUnit(couponConfigPO));

        List<Integer> useChannel = Arrays.stream(StringUtils.split(couponConfigPO.getUseChannel(), ",")).map(Integer::parseInt).collect(Collectors.toList());

        typeBase.setOffline(Integer.parseInt(convertUseChannelType(useChannel)));
        typeBase.setCode(couponConfigPO.getCode());
        typeBase.setTypeCode(convertCodeType(couponConfigPO.getPromotionType()));
        typeBase.setGlobalEndTme(couponConfigPO.getEndFetchTime());
        typeBase.setGlobalStartTime(couponConfigPO.getStartFetchTime());
        //针对兼容有码券，且只会配置固定期限的时间
        typeBase.setGlobalUseStartTime(couponConfigPO.getStartUseTime());
        typeBase.setGlobalUseEndTme(couponConfigPO.getEndUseTime());
        typeBase.setCouponType(couponConfigPO.getCouponType());
        typeBase.setShipmentId(couponConfigPO.getShipmentId());

        // 业务平台
        typeBase.setBizPlatform(couponConfigPO.getBizPlatform());

        return typeBase;
    }

    /**
     * 构建使用条件
     * @param couponConfigPO
     * @return
     */
    private Condition serializeCondition(CouponConfigPO couponConfigPO){
        Long updateTime = TimeUtil.getNowUnixSecond();
        Condition condition = new Condition();
        condition.setId(couponConfigPO.getId());
        condition.setType(convertUseType(couponConfigPO.getPromotionType()).getValue());
        condition.setAreaId(2L);
        condition.setQuota(convertQuota(couponConfigPO));
        condition.setGoodsInclude(Lists.newArrayList(convertGoodsInclude(couponConfigPO,updateTime)));
        CompareItem compareItem = convertCompareItem(couponConfigPO.getGoodsExclude(),updateTime);
        compareItem.setAll(-1); // pulse_v3 赋值为-1
        condition.setGoodsInexclude(compareItem);

        //todo 是否无用
        //condition.setGoodsExclude(convertCompareItem(couponConfigPO.getGoodsExclude(),updateTime));

        ExtPropPO extPropPO = GsonUtil.fromJson(couponConfigPO.getExtProp(), ExtPropPO.class);
        condition.setCheckPackage(convertCheckPackage(extPropPO));
        condition.setCheckPrice(convertCheckPrice(extPropPO));

        List<Integer> useChannel = Arrays.stream(StringUtils.split(couponConfigPO.getUseChannel(), ",")).map(Integer::parseInt).collect(Collectors.toList());
        condition.setOffline(Integer.parseInt(convertUseChannelType(useChannel)));

        // TODO: 汽车优惠券使用渠道暂时为空
        if (!BizPlatformEnum.CAR.getCode().equals(couponConfigPO.getBizPlatform())) {
           List<String> oldUseChannel = convertUseChannel(useChannel);
           condition.setUseChannel(StringUtils.join(oldUseChannel, ","));
           condition.setClient(convertClients(oldUseChannel));
           condition.setUseChannelStore(convertUseChannel(couponConfigPO));
       }

        String areaIds = couponConfigPO.getAreaIds();
        if(StringUtils.isNotBlank(areaIds)){
            condition.setAssignArea(extPropPO.getArea() == 1);
            List<Integer> areaList = Arrays.stream(StringUtils.split(areaIds, ",")).map(Integer::parseInt).collect(Collectors.toList());
            Map<Integer, List<Integer>> assignAreaConfig = new HashMap<>();
            assignAreaConfig.put(2, areaList);
            condition.setAssignAreaConfig(assignAreaConfig);
        }
        return condition;
    }

    /**
     * 构建使用政策
     * @param couponConfigPO
     * @return
     */
    private Policy serializePolicy(CouponConfigPO couponConfigPO){
        Policy policy = new Policy();
        policy.setType(convertUseType(couponConfigPO.getPromotionType()).getValue());
        policy.setPolicies(Lists.newArrayList(convertPolicyLevel(couponConfigPO)));
        return policy;
    }

    /**
     * 转换抵扣类型
     * @param couponConfigPO
     * @return
     */
    private Integer convertDeductType(CouponConfigPO couponConfigPO) {
        Integer deductType = DeductTypeEnum.Zero.getMysqlValue();
        if(couponConfigPO.getPromotionType() == PromotionTypeEnum.NyuanBuy.getValue()) {
            if(couponConfigPO.getPromotionValue() > 0){
                deductType = DeductTypeEnum.OneCent.getMysqlValue();
            }
        }
        return deductType;
    }

    /**
     * 转换使用渠道
     * @param couponConfig
     * @return
     */
    private Map<Integer, UseChannelStoreItem> convertUseChannel(CouponConfigPO couponConfig){
        String useChannelStr = couponConfig.getUseChannel();
        if(StringUtils.isEmpty(useChannelStr)) {
            throw new BaseException(-1, "使用渠道基本配置存在null的情况");
        }

        Map<Integer, UseChannelStoreItem> useChannelMap = new HashMap<>();

        List<Integer> useChannelList = Arrays.stream(useChannelStr.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        Map<String, UseChannelVO> useStorePOMap = GsonUtil.fromJson(couponConfig.getUseStore(), new TypeToken<Map<String, UseChannelVO>>(){}.getType());

        Collections.sort(useChannelList);

        for (Integer useChannelId : useChannelList) {
            UseChannelVO useChannel = useStorePOMap.get(String.valueOf(useChannelId));
            if (useChannel == null) {
                continue;
            }

            if (UseChannelsEnum.DIRECTSALE_STORE.getValue() == useChannelId || UseChannelsEnum.EXCLUSIVE_SHOP.getValue() == useChannelId) {
                UseChannelStoreItem useChannelStoreItem = useChannelMap.get(UseChannelsEnum.DIRECTSALE_STORE.getValue());
                if(useChannelStoreItem != null && useChannelStoreItem.getScope() == 2) {
                    useChannelStoreItem.getStoreIds().addAll(useChannel.getLimitIds());
                    continue;
                }
            }

            UseChannelStoreItem useChannelStoreItem = new UseChannelStoreItem();
            useChannelStoreItem.setScope(useChannel.isAll() ? 1 : 2);
            useChannelStoreItem.setStoreIds(useChannel.getLimitIds());

            if (UseChannelsEnum.AUTHORIZED_STORE.getValue() == useChannelId) {
                useChannelMap.put(3, useChannelStoreItem);
            } else {
                useChannelMap.put(useChannelId, useChannelStoreItem);
            }
        }

        return useChannelMap;
    }

    /**
     * 转换使用政策
     * @param couponConfigPO
     * @return
     */
    private PolicyLevel convertPolicyLevel(CouponConfigPO couponConfigPO){
        PolicyLevel policyLevel = new PolicyLevel();
        policyLevel.setQuota(convertQuota(couponConfigPO));
        policyLevel.setRule(ConvertRuleEle(couponConfigPO));

        return  policyLevel;
    }


    /**
     * 扣减规则转换
     * @param couponConfig
     * @return
     */
    private RuleEle ConvertRuleEle(CouponConfigPO couponConfig){
        RuleEle rule = new RuleEle();
        PromotionTypeEnum promotionType = PromotionTypeEnum.getByValue(couponConfig.getPromotionType());
        switch (promotionType) {
            case ConditionReduce:
            case DirectReduce:
                rule.setReduceMoney(couponConfig.getPromotionValue());
                break;
            case ConditionDiscount:
                rule.setReduceDiscount(couponConfig.getPromotionValue() / 10);
                rule.setMaxPrice(couponConfig.getMaxReduce().longValue());
                break;
            case NyuanBuy:
                rule.setNYuanPrice(couponConfig.getPromotionValue());
                GoodItemPO goodsItemPo = GsonUtil.fromJson(couponConfig.getGoodsInclude(), GoodItemPO.class);
                if(goodsItemPo == null){
                    throw new BaseException(-1, "兑换商品配置存在null的情况");
                }
                rule.setDeductedGoods(geDeductGoodsMap(goodsItemPo));
                break;
        }

        ExtPropPO extPropPO = GsonUtil.fromJson(couponConfig.getExtProp(), ExtPropPO.class);
        rule.setPostFree(convertPostFree(extPropPO));

        return rule;
    }

    /**
     * 转换是否检查套装
     * @param extPropPO
     * @return
     */
    private Integer convertCheckPackage(ExtPropPO extPropPO) {
        return extPropPO.getCheckPackage() == CommonConstant.ONE_INT ? 1 : 2;
    }

    /**
     * 转换是否检查价格
     * @param extPropPO
     * @return
     */
    private Integer convertCheckPrice(ExtPropPO extPropPO) {
        return extPropPO.getCheckPrice() == CommonConstant.ONE_INT ? 1 : 2;
    }

    /**
     * 转换是否检查价格
     * @param extPropPO
     * @return
     */
    private Integer convertPostFree(ExtPropPO extPropPO) {
        return extPropPO.getPostFree() == CommonConstant.ONE_INT ? 1 : 2;
    }

    /**
     * 转换是否检查价格
     * @param extPropPO
     * @return
     */
    private Integer convertShare(ExtPropPO extPropPO) {
        return extPropPO.getShare() == CommonConstant.ONE_INT ? 1 : 2;
    }

    /**
     * 转换是否pro会员券
     * @param extPropPO
     * @return
     */
    private Integer convertProMember(ExtPropPO extPropPO) {
        return extPropPO.getProMember() == CommonConstant.ONE_INT ? 1 : 2;
    }

    /**
     * 获取抵扣商品
     * @param goodsItemPo
     * @return
     */
    private Map<String, String> geDeductGoodsMap(GoodItemPO goodsItemPo) {
        Map<String, String> deductGoods = new HashMap<>();

        if(CollectionUtils.isNotEmpty(goodsItemPo.getSku())){
            Map<String, String> deductSku = goodsItemPo.getSku().stream().
                    collect(Collectors.toMap(String::valueOf, String::valueOf));
            deductGoods.putAll(deductSku);
        }
        if(CollectionUtils.isNotEmpty(goodsItemPo.getPackages())){
            Map<String, String> deductPackage = goodsItemPo.getPackages().stream().
                    collect(Collectors.toMap(String::valueOf, String::valueOf));
            deductGoods.putAll(deductPackage);
        }
        return deductGoods;
    }

    /**
     * 展示面额文案转换
     * @param couponConfigPO
     * @return
     */
    private String getShowTitle(CouponConfigPO couponConfigPO){
        String showTitle;
        DecimalFormat df = new DecimalFormat("0.00");
        long promotionValue = couponConfigPO.getPromotionValue();
        if(couponConfigPO.getPromotionType() == PromotionTypeEnum.ConditionDiscount.getValue()) {
            showTitle = df.format((float) promotionValue / 100);
        } else if(couponConfigPO.getPromotionType() == PromotionTypeEnum.NyuanBuy.getValue()){
            showTitle = df.format((float) promotionValue / 100);
        } else {
            showTitle = df.format((float) promotionValue / 100);
        }

        return showTitle;
    }

    /**
     * 展示单位转换
     * @param couponConfigPO
     * @return
     */
    private String getShowUnit(CouponConfigPO couponConfigPO){
        String showUnit;
        if(couponConfigPO.getPromotionType() == PromotionTypeEnum.ConditionDiscount.getValue()) {
            showUnit = "折";
        } else if(couponConfigPO.getPromotionType() == PromotionTypeEnum.NyuanBuy.getValue()){
            showUnit = "";
        } else {
            showUnit = "元";
        }
        return showUnit;
    }

    /**
     * 使用门槛规则转换
     * @return
     */
    private List<QuotaEle> convertQuota(CouponConfigPO couponConfig) {
        QuotaEle quotaEle = new QuotaEle();
        PromotionTypeEnum promotionType = PromotionTypeEnum.getByValue(couponConfig.getPromotionType());
        switch (promotionType) {
            case ConditionReduce:
            case ConditionDiscount:
            case DirectReduce:
                if (couponConfig.getBottomType() == QuotaTypeEnum.Money.getCode()) {
                    // 满元
                    quotaEle.setType(0);
                    quotaEle.setMoney(couponConfig.getBottomPrice().longValue());
                } else if (couponConfig.getBottomType() == QuotaTypeEnum.Count.getCode()) {
                    // 满件
                    quotaEle.setType(1);
                    quotaEle.setCount(couponConfig.getBottomCount());
                } else if (couponConfig.getBottomType() == QuotaTypeEnum.MoneyCount.getCode()) {
                    // 满件且满元
                    quotaEle.setType(2);
                    quotaEle.setMoney(couponConfig.getBottomPrice().longValue());
                    quotaEle.setCount(couponConfig.getBottomCount());
                }else if (couponConfig.getBottomType() == QuotaTypeEnum.EveMoney.getCode()) {
                    // 每满元
                    quotaEle.setType(3);
                    quotaEle.setMoney(couponConfig.getBottomPrice().longValue());
                } else if (couponConfig.getBottomType() == QuotaTypeEnum.EveCount.getCode()){
                    // 每满件
                    quotaEle.setType(4);
                    quotaEle.setCount(couponConfig.getBottomCount());
                }
                break;
            case NyuanBuy:
                quotaEle.setType(1);
                quotaEle.setCount(1);
                break;
        }
        return Lists.newArrayList(quotaEle);
    }

    /**
     * 门槛金额转换
     * @param couponConfigPO
     * @return
     */
    private long getQuatoMoney(CouponConfigPO couponConfigPO) {
        BigDecimal money = new BigDecimal(couponConfigPO.getBottomPrice());
        BigDecimal moneyCent = money.multiply(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros();

        if (moneyCent.compareTo(new BigDecimal(0)) < 0) {
            throw new BaseException(-1, String.format("非法的券配置，配额的金额必须大于0，bottomPrice=%s", couponConfigPO.getBottomPrice()));
        }
        return moneyCent.longValue();
    }

    /**
     * 转换商品信息
     *
     * @param updateTime
     * @return
     */
    private CompareItem convertGoodsInclude(CouponConfigPO couponConfig, long updateTime){
        if (BizPlatformEnum.CAR.getCode().equals(couponConfig.getBizPlatform())) {
            return null;
        }

        if (StringUtils.isEmpty(couponConfig.getGoodsInclude())) {
            throw new BaseException(-1, String.format("非法的券配置，可用券的商品列表不能为空，configId=%s", couponConfig.getId()));
        }
        CompareItem compareItem = new CompareItem();
        compareItem.setModifyIndex(updateTime);
        GoodItemPO goodItemInclude = GsonUtil.fromJson(couponConfig.getGoodsInclude(), GoodItemPO.class);
        if(goodItemInclude == null) {
            log.error("CouponConfigPoConvert.serializeGoodsItemPo err goodItemInclude is null, configId:{}", couponConfig.getId());
            return compareItem;
        }
        if (checkNeedExceptGood(couponConfig)) {
            GoodItemPO goodItemExclude = GsonUtil.fromJson(couponConfig.getGoodsExclude(), GoodItemPO.class);
            compareItem.setSku(getGoodList(goodItemInclude.getSku(), goodItemExclude.getSku()));
            compareItem.setPackages(getGoodList(goodItemInclude.getPackages(), goodItemExclude.getPackages()));
        } else {
            compareItem.setSku(CollectionUtils.isNotEmpty(goodItemInclude.getSku()) ? goodItemInclude.getSku().stream().map(String::valueOf).collect(Collectors.toList()) : new ArrayList<>());
            compareItem.setPackages(CollectionUtils.isNotEmpty(goodItemInclude.getPackages()) ? goodItemInclude.getPackages().stream().map(String::valueOf).collect(Collectors.toList()) : new ArrayList<>());
            // 新套装
            compareItem.setSsu(CollectionUtils.isNotEmpty(goodItemInclude.getSuit()) ? goodItemInclude.getSuit().stream().map(String::valueOf).collect(Collectors.toList()) : new ArrayList<>());
        }
        return compareItem;

    }

    /**
     * 商品转换
     * @param goodList
     * @param updateTime
     * @return
     */
    private CompareItem convertCompareItem(final String goodList, long updateTime) {
        CompareItem compareItem = new CompareItem();
        compareItem.setModifyIndex(updateTime);
        GoodItemPO goodsItemPo = GsonUtil.fromJson(goodList, GoodItemPO.class);
        if(goodsItemPo == null){
            return compareItem;
        }

        if(CollectionUtils.isNotEmpty(goodsItemPo.getSku())){
            compareItem.setSku(goodsItemPo.getSku().stream().map(String::valueOf).collect(Collectors.toList()));
        }
        if(CollectionUtils.isNotEmpty(goodsItemPo.getGoods())){
            compareItem.setGoods(goodsItemPo.getGoods().stream().map(String::valueOf).collect(Collectors.toList()));
        }
        if(CollectionUtils.isNotEmpty(goodsItemPo.getPackages())){
            compareItem.setPackages(goodsItemPo.getPackages().stream().map(String::valueOf).collect(Collectors.toList()));
        }
        return compareItem;
    }

    /**
     * 是否要排除黑名单
     * @param couponConfig
     * @return
     */
    private boolean checkNeedExceptGood(CouponConfigPO couponConfig) {
        return couponConfig.getScopeType() == CouponScopeTypeEnum.Categories.getValue() && StringUtils.isNotEmpty(couponConfig.getGoodsExclude());
    }

    /**
     * 黑名单排除
     * @param includeGoods
     * @param excludeGoods
     * @return
     */
    private List<String> getGoodList(List<Long> includeGoods, List<Long> excludeGoods) {
        if (CollectionUtils.isEmpty(includeGoods)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isNotEmpty(excludeGoods)) {
            return CouponCollectionUtil.removeAll(includeGoods, excludeGoods).stream().map(String::valueOf).collect(Collectors.toList());
        } else {
            return includeGoods.stream().map(String::valueOf).collect(Collectors.toList());
        }
    }


    /**
     * 使用clients转换
     *
     * @return List<String>
     */
    private List<String> convertClients(List<String> oldUseChannel) {
        if (CollectionUtils.isEmpty(oldUseChannel)) {
            throw new BaseException(-1, String.format("非法的券配置，可用券的useChannel列表不能为空，oldUseChannel=%s", oldUseChannel));
        }

        Map<String, UseChannelClientRelationDo> clientsMap = useChannelClientRel.getUseChannelClientRelation();

        List<String> clientList = Lists.newArrayList();
        for (String channel : oldUseChannel) {
            UseChannelClientRelationDo useChannelClientRelationDo = clientsMap.get(channel);
            if (useChannelClientRelationDo != null) {
                List<String> clients = useChannelClientRelationDo.getClientIds().stream().map(String::valueOf).collect(Collectors.toList());
                clientList.addAll(clients);
            }
        }

        if (CollectionUtils.isEmpty(clientList)) {
            throw new BaseException(-1, String.format("非法的券配置，可用券的client列表无法正常解析，oldUseChannel=%s", oldUseChannel));
        }
        return clientList;
    }

    /**
     * 使用渠道类型转换
     * 线上 线下 线上线下
     * @param useChannel List<String>
     * @return String
     */
    private String convertUseChannelType(List<Integer> useChannel) {
        if (CollectionUtils.isEmpty(useChannel)) {
            return UseChannelType.OnlineOffline.getMysqlValue();
        }

        // 去重
        Set<Integer> useChannelSet = new HashSet<>(useChannel);

        //只有一种情况：线上可用　或　线下可用
        if (useChannelSet.size() == 1) {
            if (UseChannelsEnum.XIAOMI_SHOP.getValue() == useChannel.get(0)) {
                return UseChannelType.Online.getMysqlValue();
            } else {
                return UseChannelType.Offline.getMysqlValue();
            }
        } else {
            if (useChannelSet.contains(UseChannelsEnum.XIAOMI_SHOP.getValue())) {
                return UseChannelType.OnlineOffline.getMysqlValue();
            } else {
                return UseChannelType.Offline.getMysqlValue();
            }
        }
    }

    /**
     * 使用渠道转换
     * @param useChannels
     * @return
     */
    private List<String> convertUseChannel(List<Integer> useChannels) {

        Set<String> useChannelList = new HashSet<>();

        for (Integer useChannel : useChannels) {
            UseChannelEnum useChannelEnum = UseChannelEnum.findByCode(useChannel);
            if (UseChannelsEnum.EXCLUSIVE_SHOP.getValue() == useChannel) {
                useChannelEnum = UseChannelEnum.MiHome;
            }
            if (useChannelEnum == null) {
                continue;
            }
            useChannelList.add(useChannelEnum.getValue());
        }

        if (CollectionUtils.isEmpty(useChannelList)) {
            throw new BaseException(-1, String.format("非法的券配置，无使用渠道配置，useChannel=%s", useChannels));
        }
        return Lists.newArrayList(useChannelList);
    }

    /**
     * 券类型转换
     *
     * @param type Integer
     * @return String
     */
    private PromotionTypeEnum convertUseType(Integer type) {
        if (type == null || type <= 0) {
            throw new BaseException(-1, String.format("非法的券配置，使用类型不能为空，type=%d", type));
        }
        PromotionTypeEnum val = PromotionTypeEnum.getByValue(type);
        if (val == null) {
            throw new BaseException(-1, String.format("非法的券配置，使用类型超出已知范围，type=%d", type));
        }
        if (val == PromotionTypeEnum.DirectReduce) {
            val = PromotionTypeEnum.ConditionReduce;
        }
        return val;
    }

    /**
     * 券类型转换
     * @param type
     * @return
     */
    private String convertCodeType(Integer type) {
        if (type == null || type <= 0) {
            throw new BaseException(-1, String.format("非法的券配置，使用类型不能为空，type=%d", type));
        }
        String val = UseTypeEnum.findRedisValueByMysqlValue(type);
        if (StringUtils.isEmpty(val)) {
            throw new BaseException(-1, String.format("非法的券配置，使用类型超出已知范围，type=%d", type));
        }
        return val;
    }

}
