package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale.tools;

import com.xiaomi.nr.coupon.admin.api.enums.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale.ServiceCouponCheckToolsFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.caraftersale.ServiceCouponCheckTools;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.TimesLimitEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;

/**
 * 汽车售后服务券-补胎券 校验工具
 *
 * <AUTHOR>
 * @date 2024/5/10 10:06
 */
@Component
public class RepairTireCouponCheckTools extends ServiceCouponCheckTools {
    @PostConstruct
    public void init() {
        ServiceCouponCheckToolsFactory.register(CouponServiceTypeEnum.REPAIR_TAIR, this);
    }

    /**
     * 工时&配件ssu校验
     *
     * @param labourHourSsu 工时ssu
     * @param partsSsu      配件ssu
     */
    @Override
    public void goodsCheck(Map<Long, Integer> labourHourSsu, Map<Long, Integer> partsSsu) throws BizError {
        if (MapUtils.isEmpty(labourHourSsu)) {
            // 工时ssu不能为空
            throw ExceptionHelper.create(ErrCode.COUPON, "工时ssu不能为空");
        }

        // 工时ssu数量只能为1
        boolean labourHourValid = labourHourSsu.values().stream().allMatch(value -> value == 1);
        if (!labourHourValid) {
            throw ExceptionHelper.create(ErrCode.COUPON, "工时ssu数量只能为1");
        }
    }

    /**
     * 券创建校验
     *
     * @param info 券配置基础信息
     */
    @Override
    public void createSpecialCheck(CouponBaseInfo info) throws BizError {
        // 券类型校验
        couponTypeCheck(info);

        // 使用次数校验
        timesLimitCheck(info);
    }

    /**
     * 券修改校验
     *
     * @param info 券配置基础信息
     */
    @Override
    public void updateSpecialCheck(CouponBaseInfo info) throws BizError {
        // 券类型校验
        couponTypeCheck(info);

        // 使用次数限制
        timesLimitCheck(info);
    }

    /**
     * 券类型校验
     *
     * @param info 券配置基础信息
     */
    public void couponTypeCheck(CouponBaseInfo info) throws BizError {
        // 券类型校验
        CouponTypeEnum couponTypeEnum = CouponTypeEnum.getByValue(info.getCouponType());

        if (null == couponTypeEnum) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券类型不存在");
        }

        // 补胎券，券类型必须为不限次服务卡
        if (!Objects.equals(couponTypeEnum, CouponTypeEnum.UNLIMITED_SERVICE_CARD)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "券类型必须为不限次服务卡");
        }
    }

    /**
     * 使用次数校验
     *
     * @param info 券配置基础信息
     */
    public void timesLimitCheck(CouponBaseInfo info) throws BizError {
        TimesLimitEnum timesLimitEnum = TimesLimitEnum.valueOf(info.getTimesLimit());

        if (null == timesLimitEnum) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用次数限制参数异常");
        }

        // 补胎券，使用次数限制为不限次
        if (!Objects.equals(timesLimitEnum, TimesLimitEnum.NO_LIMIT)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "使用次数限制必须为不限制");
        }
    }
}
