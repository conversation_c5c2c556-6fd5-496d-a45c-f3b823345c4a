package com.xiaomi.nr.coupon.admin.application.dubbo.pointadmin.convert;

import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task.PointFillTaskListRequest;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.enums.task.CouponTaskTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.task.FillTaskOrderByEnum;
import com.xiaomi.nr.coupon.admin.enums.task.TaskStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.po.SearchTaskParam;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.ImmutableMap;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @description 灌积分参数转换
 * @date 2024-08-16 18:10
 */
@Slf4j
@Component
public class PointTaskConvert {

    /**
     * 查询参数转换
     *
     * @param req 查询入参
     * @return 搜索参数
     */
    public SearchTaskParam transferToSearchParameter(PointFillTaskListRequest req) {

        SearchTaskParam searchTaskParam = new SearchTaskParam();
        searchTaskParam.setTaskId(req.getTaskId());
        searchTaskParam.setCouponName(StringUtils.isBlank(req.getPointBatchName()) ? null : req.getPointBatchName());
        searchTaskParam.setOrderBy(StringUtils.isBlank(req.getOrderBy()) ? FillTaskOrderByEnum.TASK_ID.getColumn() : Objects.requireNonNull(FillTaskOrderByEnum.findByField(req.getOrderBy())).getColumn());
        searchTaskParam.setType(CouponTaskTypeEnum.POINT_FILL.getCode());
        searchTaskParam.setConfigIds(ObjectUtils.isEmpty(req.getPointBatchId()) ? null : new ArrayList<>(Collections.singletonList(req.getPointBatchId())));
        searchTaskParam.setPageNo(checkPage(req.getPageNo()) ? req.getPageNo() : 1);
        searchTaskParam.setPageSize(checkPage(req.getPageSize()) ? req.getPageSize() : 10);
        searchTaskParam.setStartTime(Objects.isNull(req.getStartTime()) ? null : TimeUtil.convertDateToLong(req.getStartTime()));
        searchTaskParam.setEndTime(Objects.isNull(req.getEndTime()) ? null : TimeUtil.convertDateToLong(req.getEndTime()));
        searchTaskParam.setCreator(StringUtils.isBlank(req.getCreator()) ? null : req.getCreator());
        if (!Objects.isNull(req.getStatus())) {
            switch (Objects.requireNonNull(TaskStatusEnum.findByCode(req.getStatus()))) {
                case RUNNING:
                    searchTaskParam.setStatusList(Arrays.asList(TaskStatusEnum.RUNNING.getCode(), TaskStatusEnum.READY.getCode(), TaskStatusEnum.AWAIT.getCode(), TaskStatusEnum.PRE_AWAIT.getCode()));
                    break;
                case SUCCESS:
                    searchTaskParam.setStatusList(Collections.singletonList(TaskStatusEnum.SUCCESS.getCode()));
                    break;
                case FAIL:
                    searchTaskParam.setStatusList(Arrays.asList(TaskStatusEnum.FAIL.getCode(), TaskStatusEnum.ERROR.getCode()));
                    break;
                default:
                    log.warn("灌积分任务状态不合法status:{}", req.getStatus());
                    break;
            }
        }
        Integer bizType = Optional.ofNullable(req.getBizType()).orElse(BizPlatformEnum.CAR_SHOP.getCode());
        searchTaskParam.setBizType(bizType.toString());
        return searchTaskParam;
    }

    private boolean checkPage(Integer Number) {
        if (Objects.isNull(Number) || Number <= 0) {
            return false;
        }
        return true;
    }
}
