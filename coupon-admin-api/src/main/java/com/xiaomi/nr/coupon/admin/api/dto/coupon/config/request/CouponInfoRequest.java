package com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request;

import com.xiaomi.nr.coupon.admin.api.dto.BaseRequest;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 优惠券详情参数
 */
@Data
public class CouponInfoRequest extends BaseRequest {
    private static final long serialVersionUID = 8507444395847703803L;

    /**
     * 优惠券配置id
     */
    @NotNull(message = "券配置id不能为空")
    @Min(value= 1, message = "券配置id不能为空")
    private Long id;
}
