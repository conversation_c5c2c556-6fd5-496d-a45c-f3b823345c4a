package com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request;

import com.xiaomi.nr.coupon.admin.api.dto.BaseRequest;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 创建灌券任务请求参数
 */

@Data
public class CreateFillCouponTaskRequest extends BaseRequest {
    private static final long serialVersionUID = -6630103634427222319L;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    /**
     * 优惠券ID
     */
    @NotNull(message = "优惠券Id不能为空")
    @Min(value = 1, message = "券配置Id有误，不能为0")
    private Long configId;

    /**
     * 发放数量
     */
    @Min(value = 1, message = "发放数量有误，不能小于1")
    private Long applyCount;

    /**
     * 人群包类型
     */
    @NotNull(message = "发放人群类型不能为空")
    @Range(min = 0, max = 1,message = "发放人群类型不在合法范围内")
    private Integer userGroupType;

    /**
     * 自定义人群包类型
     */
    @NotNull(message = "自定义人去包类型不能为空")
    @Range(min = 1, max = 2, message = "人群包类型不在合法范围内")
    private Integer customizeType;

    /**
     * 人群包ID
     */
    @Min(value = 1, message = "人群包Id有误")
    private Long batchId;

    /**
     * 人群包名称
     */
    private String batchName;

    /**
     * 人群包HDFS地址
     */
    @NotBlank(message = "数据集地址不能为空")
    private String hdfsAddr;

    /**
     * 数据集列表列表
     */
    @Size(min = 1, max = 10000, message = "数据集大小为1-10000")
    private List<String> dataList;

    /**
     * 用户集大小
     */
    @NotNull(message = "用户数据集大小不能为空")
    private Long userGroupSize;

    /**
     * 创建人
     */
    @NotBlank(message = "灌券任务创建人不能为空")
    private String creator;

    /**
     * 创建人部门
     */
    private String departmentId;

    /**
     * 人群包是否去重
     * 0：去重
     * 1：不去重
     */
    @NotNull
    @Range(min = 0, max = 1, message = "人群包去重传参范围有误")
    private Integer distinct;

    /**
     * 售后服务券灌券场景
     */
    private String sendScene;

    /**
     * 地区id
     */
    private String areaId;

    /**
     * 审批流ID
     */
    private Long workflowId;
}
