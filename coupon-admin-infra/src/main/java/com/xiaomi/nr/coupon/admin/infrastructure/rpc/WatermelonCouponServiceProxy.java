package com.xiaomi.nr.coupon.admin.infrastructure.rpc;

import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponConfigConvert;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.ResultValidator;
import com.xiaomi.nr.phoenix.api.dto.request.watermelon.WatermelonCodeCouponSyncReq;
import com.xiaomi.nr.phoenix.api.dto.request.watermelon.WatermelonCouponTemplateReq;
import com.xiaomi.nr.phoenix.api.dto.response.watermelon.WatermelonCouponSyncResp;
import com.xiaomi.nr.phoenix.api.service.DubboWatermelonCouponService;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/7/4 16:33
 */
@Slf4j
@Service
public class WatermelonCouponServiceProxy {
    /**
     * 西瓜集成phoenix
     */
    @Reference(check = false, interfaceClass = DubboWatermelonCouponService.class, group = "${dubbo.group}", version = "1.0", timeout = 5000)
    private DubboWatermelonCouponService watermelonCouponService;

    /**
     * 同步券码信息
     *
     * @param request request
     * @return WatermelonCouponSyncResp
     */
    public WatermelonCouponSyncResp syncCodeCoupon(WatermelonCodeCouponSyncReq request) throws BizError {
        log.info("WatermelonCouponServiceProxy.syncCodeCoupon begin, request = {}", GsonUtil.toJson(request));

        try {
            Result<WatermelonCouponSyncResp> response = watermelonCouponService.syncCodeCoupon(request);

            ResultValidator.validate(response, "调用三方服务同步券码信息失败");

            log.info("WatermelonCouponServiceProxy.syncCodeCoupon finished, request = {}, response = {}", GsonUtil.toJson(request), GsonUtil.toJson(response));

            return response.getData();
        } catch (BizError bizError) {
            log.error("WatermelonCouponServiceProxy.syncCodeCoupon bizError request = {}, bizError = ", GsonUtil.toJson(request), bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("WatermelonCouponServiceProxy.syncCodeCoupon error request = {}, bizError =", GsonUtil.toJson(request), e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "调用三方服务同步券码信息失败");
        }
    }

    /**
     * 同步券批次信息
     *
     * @param po CouponConfigPO
     * @return
     */
    public void syncCouponTemplate(CouponConfigPO po) throws BizError {
        WatermelonCouponTemplateReq request = null;
        try {
            request = CouponConfigConvert.WatermelonCouponConvert(po);
            log.info("WatermelonCouponServiceProxy.syncCouponTemplate begin, request = {}", GsonUtil.toJson(request));

            Result<Void> response = watermelonCouponService.syncCouponTemplate(request);
            ResultValidator.validate(response, "调用三方服务同步券批次信息失败");
            log.info("WatermelonCouponServiceProxy.syncCouponTemplate finished, request = {}, response = {}", GsonUtil.toJson(request), GsonUtil.toJson(response));
        } catch (BizError bizError) {
            log.error("WatermelonCouponServiceProxy.syncCouponTemplate bizError request = {}, bizError = ", GsonUtil.toJson(request), bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("WatermelonCouponServiceProxy.syncCouponTemplate error request = {}, bizError =", GsonUtil.toJson(request), e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "调用三方服务同步券批次信息失败");
        }
    }
}
