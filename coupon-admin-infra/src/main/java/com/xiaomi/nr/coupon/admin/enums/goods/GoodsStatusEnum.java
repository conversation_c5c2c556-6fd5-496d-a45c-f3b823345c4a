package com.xiaomi.nr.coupon.admin.enums.goods;

import java.io.Serializable;
import java.util.HashMap;

/**
 * 商品状态
 */
public enum GoodsStatusEnum implements Serializable {

    /**
     * 销售中
     */
    ONSALE_ONSHELF(1, "销售中"),

    /**
     * 未开售上架
     */
    DWONSALE_ONSHELF(2, "未开售上架"),

    /**
     * 未开售下架
     */
    DOWNSALE_DOWNSHELF(3, "下架")

    ;

    private static final HashMap<Integer, GoodsStatusEnum> MAPPING = new HashMap<>();

    /**
     * ID
     */
    private int id;

    /**
     * 状态名称
     */
    private String name;

    static {
        for (GoodsStatusEnum statusEnum : GoodsStatusEnum.values()) {
            MAPPING.put(statusEnum.getId(), statusEnum);
        }
    }

    GoodsStatusEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public static GoodsStatusEnum valueOf(Integer type) {
        return MAPPING.get(type);
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }
}

