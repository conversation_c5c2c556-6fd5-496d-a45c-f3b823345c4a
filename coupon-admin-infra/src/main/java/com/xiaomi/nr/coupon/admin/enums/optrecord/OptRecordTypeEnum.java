package com.xiaomi.nr.coupon.admin.enums.optrecord;

import lombok.Getter;

@Getter
public enum OptRecordTypeEnum {

    ADD_COUPON(1, "创建券"),
    UPDATE_COUPON(2, "修改券"),
    UPDATE_COUPON_STATUS(3, "修改券状态"),
    ;

    private final int value;
    private final String name;

    OptRecordTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }



    public static OptRecordTypeEnum findByValue(int value) {
        OptRecordTypeEnum[] values = OptRecordTypeEnum.values();
        for (OptRecordTypeEnum typeEnum : values) {
            if (value == typeEnum.value) {
                return typeEnum;
            }
        }
        return null;
    }

}
