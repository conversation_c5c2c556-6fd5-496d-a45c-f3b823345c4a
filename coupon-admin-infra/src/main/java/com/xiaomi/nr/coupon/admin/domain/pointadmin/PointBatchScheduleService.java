package com.xiaomi.nr.coupon.admin.domain.pointadmin;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.enums.mail.PostOfficeMsgChannelEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.notify.robotmessage.RobotSendMessageUtil;
import com.xiaomi.nr.coupon.admin.infrastructure.notify.robotmessage.po.Card;
import com.xiaomi.nr.coupon.admin.infrastructure.notify.rocketmq.PostMessageService;
import com.xiaomi.nr.coupon.admin.infrastructure.notify.rocketmq.model.PostOfficeMsg;
import com.xiaomi.nr.coupon.admin.infrastructure.notify.rocketmq.model.PostOfficeMsgContent;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CarPointsConfigLogRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.PointConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.xiaomi.nr.coupon.admin.infrastructure.constant.CommonConstant.SQL_BATCH_SIZE;
import static com.xiaomi.nr.coupon.admin.infrastructure.constant.point.PointConstant.POINT_ADMIN_URL;

/**
 * moon定时任务
 *
 * <AUTHOR>
 * @date 2023/12/12
 */
@Service
@Slf4j
public class PointBatchScheduleService {

    @Resource
    private PointConfigRepository pointConfigRepository;

    @Resource
    private CarPointsConfigLogRepository carPointsConfigLogRepository;

    @Resource
    private RobotSendMessageUtil robotMessageUtil;

    @Resource
    private PostMessageService postMessageService;

    @Value("${postoffice.pointLowStockNotifySceneId}")
    private int pointLowStockNotifySceneId;

    /**
     * 风险预警
     */
    public void riskWarning() {

        log.info("PointBatchScheduleService pointBatchRiskWarning start");

        long startId = 0L;
        int batchSize = SQL_BATCH_SIZE;
        List<CarPointsBatchConfigPo> curBatch = null;
        long queryTime = System.currentTimeMillis() / 1000;

        // 1、扫描
        do {
            curBatch = pointConfigRepository.selectOnlineInProgressBatch(startId, queryTime, batchSize);
            if (CollectionUtils.isEmpty(curBatch)) {
                break;
            }

            // 2、遍历记录
            for (CarPointsBatchConfigPo batchConfigPo : curBatch) {
                try {
                    // 3、获取发放数量
                    Long sendCount = pointConfigRepository.getPointBatchDistributeCache(batchConfigPo.getId());
                    if (Objects.isNull(sendCount)) {
                        sendCount = batchConfigPo.getSendCount();
                    }

                    // 4、判断单个
                    // 记录剩余比例是否超过阈值，超过的话，进行飞书和邮件报警
                    double remainRatio = 100 - (double) sendCount / batchConfigPo.getApplyCount() * 100;
                    if (remainRatio <= batchConfigPo.getWarningRatio()) {
                        Long batchId = batchConfigPo.getId();
                        String batchName = batchConfigPo.getName();
                        // 报警
                        String creator = batchConfigPo.getCreator();
                        String updater = carPointsConfigLogRepository.selectUpdater(batchId);

                        // creator 报警
                        Card card = Card.getPointWarningCard(batchId, batchName, creator, updater, remainRatio);
                        robotMessageUtil.sendPrivateCard(card, creator);

                        String emailReceivers = creator+ CommonConstant.EMAIL_SUFFIX;

                        // updater 报警
                        if (StringUtils.isNotBlank(updater) && !updater.equals(creator)) {
                            robotMessageUtil.sendPrivateCard(card, updater);
                            emailReceivers += ";" + updater + CommonConstant.EMAIL_SUFFIX;

                        }

                        PostOfficeMsg postOfficeMsg = getMsg(emailReceivers, batchId, batchName, remainRatio);
                        postMessageService.sendMesaage(postOfficeMsg, "point_low_stock_notify_" + batchId);

                    }
                } catch (Exception e) {
                    log.error("riskWarning failed. batchConfigPo {}, e ", GsonUtil.toJson(batchConfigPo), e);
                }
            }

            startId = curBatch.get(curBatch.size() - 1).getId();
        } while (curBatch.size() == batchSize);

        log.info("PointBatchScheduleService pointBatchRiskWarning finished");

    }

    /**
     * 预算释放
     */
    public void budgetRelease() {

        log.info("PointBatchScheduleService budgetRelease start");

        long startId = 0L;
        int batchSize = SQL_BATCH_SIZE;
        List<CarPointsBatchConfigPo> curBatch = null;
        long queryTime = System.currentTimeMillis() / 1000;

        // 1、扫描
        do {
            curBatch = pointConfigRepository.selectCompletedBatch(startId, queryTime, batchSize);
            if (CollectionUtils.isEmpty(curBatch)) {
                break;
            }

            // 2、遍历记录
            for (CarPointsBatchConfigPo batchConfigPo : curBatch) {
                try {
                    // 有剩余积分且未释放过预算
                    if (checkBudget(batchConfigPo) && batchConfigPo.getApplyCount() - batchConfigPo.getSendCount() > 0
                            && batchConfigPo.getReleaseCount() == 0
                    ) {
                        pointConfigRepository.budgetRelease(batchConfigPo);
                    }
                } catch (Exception e) {
                    log.error("budgetRelease failed. batchConfigPo {}, e ", GsonUtil.toJson(batchConfigPo), e);
                }
            }

            startId = curBatch.get(curBatch.size() - 1).getId();
        } while (curBatch.size() == batchSize);

        log.info("PointBatchScheduleService budgetRelease finished");
    }

    public boolean checkBudget(CarPointsBatchConfigPo batchConfigPo) {
        return StringUtils.isNotEmpty(batchConfigPo.getBrApplyNo()) && batchConfigPo.getLineNum() != null && StringUtils.isNotEmpty(batchConfigPo.getBudgetCreateTime()) && StringUtils.isNotEmpty(batchConfigPo.getBrApplyNo());
    }

    private PostOfficeMsg getMsg(String emailReceivers, Long batchId, String batchName, double remainRatio){
        PostOfficeMsg postOfficeMsg = new PostOfficeMsg();
        postOfficeMsg.setUnikey("point_low_stock_notify_" + batchId + "_" + System.currentTimeMillis());
        postOfficeMsg.setSceneId(pointLowStockNotifySceneId);
        PostOfficeMsgContent postOfficeMsgContent = new PostOfficeMsgContent();
        postOfficeMsgContent.setChannel(PostOfficeMsgChannelEnum.EMAIL.getCode());
        postOfficeMsgContent.setEmailReceivers(emailReceivers);
        Map<String,Object> params = new HashMap<>();
        params.put("batchId", batchId);
        params.put("batchName", batchName);
        params.put("remainRatio", new DecimalFormat("#.##").format(remainRatio) + "%");
        params.put("url", "<a href="+ POINT_ADMIN_URL +">查看</a>");
        postOfficeMsgContent.setParams(params);
        postOfficeMsg.setContent(Lists.newArrayList(postOfficeMsgContent));
        return postOfficeMsg;
    }

}
