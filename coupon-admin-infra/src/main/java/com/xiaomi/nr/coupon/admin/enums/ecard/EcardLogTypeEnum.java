package com.xiaomi.nr.coupon.admin.enums.ecard;

/**
 * 礼品卡日志类型 枚举
 *
 * <AUTHOR>
 */
public enum EcardLogTypeEnum {
    /**
     * 系统异常日志
     */
    EXCEPTION_LOG(-1, "系统异常日志"),

    /**
     * 系统正常日志
     */
    NORMAL_LOG(0, "系统正常日志"),

    /**
     * 消费
     */
    CONSUM_LOG(1, "消费"),

    /**
     * 退款
     * */
    REFUND_LOG(2,"退款");
    private final Integer value;
    private final String name;

    EcardLogTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static EcardLogTypeEnum findByValue(Integer value) {
        EcardLogTypeEnum[] values = EcardLogTypeEnum.values();
        for (EcardLogTypeEnum ecardLogTypeEnum : values) {
            if (value.equals(ecardLogTypeEnum.value)) {
                return ecardLogTypeEnum;
            }
        }
        return null;
    }
}
