package com.xiaomi.nr.coupon.admin.application.dubbo.convert;

import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.SavePointBatchRequest;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.PointBatchStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/5 20:01
 */
@Service
@Slf4j
public class PointBatchConvert {
    public CarPointsBatchConfigPo toPointBatchConfigPo(SavePointBatchRequest request) {
        if (Objects.isNull(request)) {
            return null;
        }

        CarPointsBatchConfigPo batchConfigPo = new CarPointsBatchConfigPo();
        BeanUtils.copyProperties(request, batchConfigPo);
        // 批次名称
        batchConfigPo.setName(request.getBatchName());
        // 已发积分
        batchConfigPo.setSendCount(0L);
        // 状态：上线
        batchConfigPo.setStatus(PointBatchStatusEnum.ONLINE.getCode());
        // 发放场景
        batchConfigPo.setSendScene(request.getSceneCode());
        // 批次id
        if (Objects.nonNull(request.getBatchId())) {
            batchConfigPo.setId(request.getBatchId());
        }
        // 创建人
        batchConfigPo.setCreator(request.getOperator());

        return batchConfigPo;
    }
}
