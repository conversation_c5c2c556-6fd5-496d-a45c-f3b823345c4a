package com.xiaomi.nr.coupon.admin.enums.couponconfig;

import lombok.Getter;

@Getter
public enum CouponScopeTypeEnum {
    /**
     * 商品
     */
    Goods(1, "商品券"),
    /**
     * 分类
     */
    Categories(2, "品类券"),


    ;


    private final int value;
    private final String name;

    CouponScopeTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static CouponScopeTypeEnum getByCode(int code) {
        CouponScopeTypeEnum[] values = CouponScopeTypeEnum.values();
        for (CouponScopeTypeEnum item : values) {
            if (item.getValue() == code) {
                return item;
            }
        }
        return null;
    }


}
