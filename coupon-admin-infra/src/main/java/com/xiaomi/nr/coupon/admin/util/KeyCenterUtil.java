package com.xiaomi.nr.coupon.admin.util;

import com.xiaomi.keycenter.agent.client.DataProtectionProvider;
import com.xiaomi.keycenter.common.iface.DataProtectionException;
import org.apache.commons.codec.Charsets;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;

/**
 * keycent加解密工具
 *
 * <AUTHOR>
 */
public class KeyCenterUtil {

    /**
     * 加密数据
     *
     * @param data 待加密数据
     * @param sId  key center id
     * @return DataProtectionException
     */
    public static String encrypt(String data, String sId) throws DataProtectionException {
        return encrypt(data, sId, false);
    }

    /**
     * 加密数据
     *
     * @param data 待加密数据
     * @param sId  key center id
     * @return DataProtectionException
     */
    public static String encrypt(String data, String sId, Boolean doSnappyCompression) throws DataProtectionException {
        if (null == data) {
            return null;
        }
        DataProtectionProvider provider = DataProtectionProvider.getProvider(sId);
        byte[] cipher = provider.encrypt(data.getBytes(Charsets.UTF_8), "0".getBytes(StandardCharsets.UTF_8), doSnappyCompression);
        return Base64.encodeBase64String(cipher);
    }

    /**
     * 解密数据
     *
     * @param data 待解密数据
     * @return key center id
     * @throws DataProtectionException
     */
    public static String decrypt(String data, String sId) throws DataProtectionException {
        return decrypt(data, sId, false);
    }

    /**
     * 解密数据
     *
     * @param data 待解密数据
     * @return key center id
     * @throws DataProtectionException
     */
    public static String decrypt(String data, String sId, Boolean doSnappyCompression) throws DataProtectionException {
        if (Base64.isBase64(data)) {
            DataProtectionProvider provider = DataProtectionProvider.getProvider(sId);
            byte[] encryptData = Base64.decodeBase64(data);
            byte[] raw = provider.decrypt(encryptData, doSnappyCompression);
            return new String(raw, Charsets.UTF_8);
        } else {
            return data;
        }
    }

    /**
     * 解密数据
     *
     * @param data 待解密数据
     * @return key center id
     * @throws DataProtectionException
     */
    public static String decrypt(String data, String sId, String userOnlySecret, Boolean doSnappyCompression) throws DataProtectionException {
        if (StringUtils.isBlank(userOnlySecret)) {
            return decrypt(data, sId, doSnappyCompression);
        }
        if (Base64.isBase64(data)) {
            DataProtectionProvider provider = DataProtectionProvider.getProvider(sId);
            byte[] encryptData = Base64.decodeBase64(data);
            byte[] raw = provider.decrypt(encryptData, userOnlySecret.getBytes(), doSnappyCompression);
            return new String(raw, Charsets.UTF_8);
        } else {
            return data;
        }
    }

    public static String makeSignatureForSearch(String raw) {
        if (raw == null) {
            return null;
        } else {
            String str = "~!20&^%){#" + raw;
            for (int i = 0; i < 5; ++i) {
                str = DigestUtils.md5DigestAsHex(str.getBytes(StandardCharsets.UTF_8));
            }
            return str.substring(8, 24);
        }
    }
}