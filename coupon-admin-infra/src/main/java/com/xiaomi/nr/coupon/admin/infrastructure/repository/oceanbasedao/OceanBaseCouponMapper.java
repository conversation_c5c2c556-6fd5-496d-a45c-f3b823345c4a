package com.xiaomi.nr.coupon.admin.infrastruture.repository.oceanbasedao;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.tidbdao.po.CouponPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/24 11:16
 */
@Mapper
@Component
public interface OceanBaseCouponMapper {
    /**
     * 获取即将过期的运费券列表
     * @param startTime long 过滤的开始时间戳
     * @param endTime long 过滤的结束时间戳
     * @param configIds List<Long> 运费券配置ID列表
     * @param limitStart int 单次读取的开始位置
     * @param limit int 单次读取的限制数
     * @return List<CouponPo>
     */
    @Select("<script>" +
            "select id as id, type_id as typeId, end_time as endTime, user_id as userId, extend_info as extendInfo " +
            "from tb_coupon " +
            "where stat='unused' and end_time <![CDATA[>=]]> #{startTime,jdbcType=VARCHAR} and end_time <![CDATA[<]]> #{endTime,jdbcType=VARCHAR} " +
            "<if test=\"configIds != null and configIds.size > 0\"> and type_id in " +
            "        <foreach item='configId' index='index' collection='configIds' open='(' separator=',' close=')'>" +
            "            #{configId,jdbcType=INTEGER}" +
            "        </foreach>" +
            "</if> " +
            "order by id asc " +
            "limit #{limitStart}, #{limit}" +
            "</script>")
    List<CouponPo> getExpiringSoonData(@Param("startTime") long startTime, @Param("endTime") long endTime, @Param("configIds") List<Long> configIds, @Param("limitStart") int limitStart, @Param("limit") int limit);

    /**
     * 根据类型ID列表和时间范围批量获取优惠券信息
     *
     * @param typeIdList 类型ID列表
     * @param startTime  开始时间，时间戳格式
     * @param endTime    结束时间，时间戳格式
     * @return 符合条件的优惠券信息列表
     */
    @Select("<script>" +
            "SELECT * FROM tb_coupon " +
            "WHERE type_id IN " +
            "<foreach item='typeId' index='index' collection='typeIdList' open='(' separator=',' close=')'>" +
            "#{typeId}" +
            "</foreach> " +
            "AND use_time <![CDATA[>=]]> #{startTime} " +
            "AND use_time <![CDATA[<]]> #{endTime} " +
            "ORDER BY id ASC" +
            "</script>")
    List<CouponPo> batchGetByTypeId(@Param("typeIdList") List<String> typeIdList, @Param("startTime") long startTime, @Param("endTime") long endTime);
}
