package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponconfig.po.CouponConfigPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponconfig.po.SkuGroupMapPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.goods.po.GoodsPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.goods.po.PackagePo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@Deprecated
public class BaseData {

    /**
     * 货品列表
     */
    private Map<Long, GoodsPo> goodsMap;

    /**
     * 套装列表
     */
    private Map<Long, PackagePo> packageMap;

    /**
     * sku和套装与品类的关系及列表
     */
    private Map<Long, List<SkuGroupMapPo>> skuGroupMap;

    /**
     * 优惠券配置列表
     */
    private List<CouponConfigPo> configList;

    /**
     * 使用渠道与client_id关系
     */
    private Map<String, UseChannelClientRelationDo> useChannelClientRelation;

    /**
     * 根据sku获取相关信息
     *
     * @param sku long
     * @return GoodsPo
     */
    public GoodsPo getSkuInfo(long sku) {
        for (GoodsPo it : this.goodsMap.values()) {
            if (it.getSku() == sku) {
                return it;
            }
        }
        return null;
    }

    /**
     * 根据货品ID获取相关信息
     *
     * @param goodsId long
     * @return GoodsPo
     */
    public GoodsPo getGoodsInfo(long goodsId) {
        GoodsPo item = this.goodsMap.get(goodsId);
        if (item == null || item.getGoodsId() == 0 || item.getSku() == 0 || item.getProductId() == 0) {
            return null;
        }
        return item;
    }

    /**
     * 根据商品ID获取货品相关信息
     *
     * @param commodityId long
     * @return List<GoodsPo>
     */
    public List<GoodsPo> getGoodsInfoByCommodityId(long commodityId) {
        List<GoodsPo> result = new ArrayList<>();
        for (Map.Entry<Long, GoodsPo> item : this.goodsMap.entrySet()) {
            GoodsPo it = item.getValue();
            if (it.getCommodityId() == commodityId) {
                result.add(it);
            }
        }
        return result;
    }

    /**
     * 根据套装ID获取相关信息
     *
     * @return PackagePo
     */
    public PackagePo getPackageInfo(long packageId) {
        PackagePo item = this.packageMap.get(packageId);
        if (item == null || item.getPackageId() == 0 || item.getProductId() == 0) {
            return null;
        }
        return item;
    }

    /**
     * 根据品类ID获取sku或套装列表信息
     *
     * @param groupId long
     * @return List<SkuGroupMapPo>
     */
    public List<SkuGroupMapPo> getGroupInfo(long groupId) {
        List<SkuGroupMapPo> list = this.skuGroupMap.get(groupId);
        if (list == null || list.size() == 0) {
            return null;
        }
        return list;
    }
}
