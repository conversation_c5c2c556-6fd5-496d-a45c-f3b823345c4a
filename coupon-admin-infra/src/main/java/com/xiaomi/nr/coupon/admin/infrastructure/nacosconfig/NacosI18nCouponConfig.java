package com.xiaomi.nr.coupon.admin.infrastructure.nacosconfig;

import com.xiaomi.com.i18n.cfg.Cfg;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * 接入i18n-area-nacos-sdk
 * 模块：国际主数据 + 业务配置中心化
 * 功能：延迟加载 + 热更新
 * @description: 券管理配置
 **/
@Configuration
public class NacosI18nCouponConfig {

    private static final String MODULE_NAME = "coupon_management";
    private static final String SUPPORT_AREAS_CONFIG_NAME = "coupon_support_areaIds";

    public List<String> getSupportAreaIds() {
        return GsonUtil.fromListJson(Cfg.get(MODULE_NAME, SUPPORT_AREAS_CONFIG_NAME), String.class);
    }

}
