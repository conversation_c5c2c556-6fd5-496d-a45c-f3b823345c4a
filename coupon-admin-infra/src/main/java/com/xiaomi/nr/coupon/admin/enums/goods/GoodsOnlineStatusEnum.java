package com.xiaomi.nr.coupon.admin.enums.goods;

import java.util.Objects;

/**
 * 商品线上线下上下架状态
 */
public enum GoodsOnlineStatusEnum {

    /**
     * 线上上架
     */
    ON_SHELF_ONLINE(1,1, "线上上架"),

    /**
     * 线上下架
     */
    DOWN_SHELF_ONLINE(2, 0,"线上下架"),

    /**
     * 线下上架
     */
    ON_SHELF_OFFLINE(3, 1,"线下上架"),

    /**
     * 线下下架
     */
    DOWN_SHELF_OFFLINE(4, 0,"线下下架");

    private final int value;
    private final int stat;
    private final String name;

    GoodsOnlineStatusEnum(int value, int stat, String name) {
        this.value = value;
        this.stat = stat;
        this.name = name;
    }

    public int getValue() {
        return this.value;
    }

    public int getStat() {
        return this.stat;
    }

    public String getName() {
        return name;
    }

    public static boolean findByValue(int value) {
        GoodsOnlineStatusEnum[] values = GoodsOnlineStatusEnum.values();
        for (GoodsOnlineStatusEnum item : values) {
            if (Objects.equals(item.value, value)) {
                return true;
            }
        }
        return false;
    }
}
