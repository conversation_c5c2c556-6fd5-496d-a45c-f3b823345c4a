package com.xiaomi.nr.coupon.admin.domain.ecard;

import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.CleanEcardDataRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.UserEcardListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.response.UserEcardListResponse;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.EcardDto;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.EcardLogDto;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.GetEcardInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.GetEcardLogRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.ListEcardDescResponse;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.ListEcardStatResponse;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecardconfig.po.EcardPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface EcardService {

    /**
     * 礼品卡状态查询
     *
     * @param cardIds 礼品卡ID列表
     * @return List<> 礼品卡状态信息返回列表
     */
    List<ListEcardStatResponse> listEcardStat(List<Long> cardIds) throws BizError;

    /**
     * 礼品卡信息查询
     *
     * @param cardIds 礼品卡ID列表
     * @return List<> 礼品卡信息返回列表
     */
    List<ListEcardDescResponse> listEcardDesc(List<Long> cardIds) throws BizError;


    /**
     * 礼品卡更新或延期检验
     *
     * @param ecardPo   ecardPo
     * @param delayTime 延期时间
     * @throws BizError 业务异常
     */
    void checkUpdateDelayEcard(EcardPo ecardPo, Long delayTime) throws BizError;

    /**
     * 获取ecard日志列表
     *
     * @param request 获取ecard日志的请求参数
     * @return ecard日志列表
     */
    List<EcardLogDto> getEcardLog(GetEcardLogRequest request) throws BizError;

    /**
     * 获取ecard信息
     *
     * @param request 包含获取ecard信息所需参数的请求对象
     * @return 包含ecard信息的列表
     * @throws BizError 业务异常，当获取ecard信息失败时抛出
     */
    List<EcardDto> getEcardInfo(GetEcardInfoRequest request) throws BizError;

    /**
     * 礼品卡信息查询，自动化测试
     * @return List<> 礼品卡信息返回列表
     */
    UserEcardListResponse selectUserEcard(UserEcardListRequest request);


    /**
     * 清除用户礼品卡,自动化测试
     * @param request
     * @return
     */
    void cleanEcardData(CleanEcardDataRequest request);

}
