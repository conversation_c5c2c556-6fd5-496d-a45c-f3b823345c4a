package com.xiaomi.nr.coupon.admin.enums.couponcode;

/**
 * 用户有码券状态枚举
 */
public enum UserCodeStatusEnum {
    /**
     * 未兑换
     */
    UNUSED(1,"unused","未使用"),

    /**
     * 已兑换
     */
    USED(2,"used", "已使用"),

    /**
     * 已过期
     */
    EXPIRED(3,"expired","已过期"),

    /**
     * 全部状态
     */
    ALL(0,null,"全部");



    private final int code;
    private final String value;
    private final String desc;

    UserCodeStatusEnum(int code, String value, String desc) {
        this.code = code;
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return this.value;
    }

    public String getDesc() {
        return this.desc;
    }
    public int getCode() {
        return this.code;
    }

    public static String findValueByCode(int code) {
        UserCodeStatusEnum[] values = UserCodeStatusEnum.values();
        for (UserCodeStatusEnum item : values) {
            if (code == item.getCode()) {
                return item.getValue();
            }
        }
        return null;
    }



}
