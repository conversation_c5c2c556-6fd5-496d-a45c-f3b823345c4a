package com.xiaomi.nr.coupon.admin.task;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task.*;
import com.xiaomi.nr.coupon.admin.api.service.pointadmin.DubboPointTaskService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.FillPointConvert;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.PointFillDetailPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.PointFillPO;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 灌积分接口测试
 * @date 2024-08-16 19:03
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("staging")
public class DubboPointTaskServiceTest {

    @Autowired
    private DubboPointTaskService dubboPointTaskService;

    @Autowired
    private FillPointConvert fillPointConvert;

    @Test
    public void taskListTest() {
        PointFillTaskListRequest request = new PointFillTaskListRequest();
//        request.setTaskId(1566L);
//        request.setPointBatchName("用户关怀");
//        request.setPointBatchId(18L);
//        request.setStatus(2);
        request.setCreator("wanghaotian7");
        Result<BasePageResponse<PointTaskListVO>> result = dubboPointTaskService.pointFillTaskList(request);
        Assert.assertNotNull(result);
        log.info(GsonUtil.toJson(result.getData().getTotalCount()));
        log.info(GsonUtil.toJson(result));
    }

    @Test
    public void createTask() {
        CreateFillPointTaskRequest request = new CreateFillPointTaskRequest();
        request.setTaskName("fillPointTask");
        request.setPointBatchId(14L);
        request.setSendType(1);
        request.setApplyCount(9L);
        request.setApplyPointCount(140L);
        request.setHdfsAddress("address");
        Result<CreatePointTaskResponse> result = dubboPointTaskService.pointFillTaskCreate(request);
        log.info(GsonUtil.toJson(result));
    }

    @Test
    public void taskDetailTest() {
        PointFillTaskDetailRequest request = new PointFillTaskDetailRequest();
        request.setTaskId(1551L);
        Result<PointFillTaskDetailResponse> response = dubboPointTaskService.taskDetail(request);
        log.info(GsonUtil.toJson(response));
    }

    @Test
    public void downloadPointFillDetailTest() {

        System.setProperty("java.security.krb5.conf", "src/main/resources/hdfs/krb5.conf");
        System.setProperty("hadoop.property.hadoop.client.keytab.file", "src/main/resources/hdfs/s_nr_center.keytab");

        DownloadPointFillDetailRequest request = new DownloadPointFillDetailRequest();
        request.setTaskId(1564L);
        Result<DownloadPointFillDetailResponse> result = dubboPointTaskService.downloadPointFillDetail(request);
        Assert.assertNotNull(result.getData());
        log.info(result.getData().getDownloadUrl());
    }

    @Test
    public void downloadPointFillDataTest() {
        System.setProperty("java.security.krb5.conf", "src/main/resources/hdfs/krb5.conf");
        System.setProperty("hadoop.property.hadoop.client.keytab.file", "src/main/resources/hdfs/s_nr_center.keytab");
        DownloadPointFillDataRequest request = new DownloadPointFillDataRequest();
        request.setHdfsAddr("/user/s_nr_center/cardvoucher/coupon/fillpoint/dataset/upload_1724384194049");
        Result<DownloadPointFillDataResponse> result = dubboPointTaskService.downloadPointFillData(request);
        Assert.assertNotNull(result);
        log.info(result.getData().getDownloadUrl());
    }

    @Test
    public void converToPointFillDetailTest() throws BizError {
        List<PointFillPO> pointFillPOList = new ArrayList<>(5);

        // 构造5个PointFillPO对象并添加到列表中
        pointFillPOList.add(new PointFillPO(1L, 100L));
        pointFillPOList.add(new PointFillPO(2L, 200L));
        pointFillPOList.add(new PointFillPO(3L, 300L));
        pointFillPOList.add(new PointFillPO(4L, 400L));
        pointFillPOList.add(new PointFillPO(5L, 500L));
        pointFillPOList.add(new PointFillPO(1L, 100L));

        List<PointFillDetailPO> pointFillDetailPOList = new ArrayList<>(2);
        pointFillDetailPOList.add(new PointFillDetailPO(1L, 100L, "积分批次无效"));
        pointFillDetailPOList.add(new PointFillDetailPO(3L, 300L, "积分批次库存不足"));

        List<PointFillDetailPO> result = fillPointConvert.convertToPointFillDetail(pointFillPOList, pointFillDetailPOList);
        log.info(GsonUtil.toJson(result));
    }

}
