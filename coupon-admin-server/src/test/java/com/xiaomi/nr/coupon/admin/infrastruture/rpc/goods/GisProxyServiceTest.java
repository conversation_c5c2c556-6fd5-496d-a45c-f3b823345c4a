package com.xiaomi.nr.coupon.admin.infrastruture.rpc.goods;

import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoDTO;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsItemTypeEnum;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-20 19:48
*/
@Slf4j
@ActiveProfiles("dev")
@SpringBootTest(classes = CouponAdminBootstrap.class)
public class GisProxyServiceTest {

    @Autowired
    private GisProxyService gisProxyService;

    @Test
    public void queryGoodsMultiInfoByGoodsIdsTest() throws Exception {
        List<Long> goodsIds = Arrays.asList(600013358L);
        List<GoodsMultiInfoDTO> result = gisProxyService.queryGoodsMultiInfoByGoodsIds(goodsIds, GoodsItemTypeEnum.SUIT);
        log.info("result:{}", GsonUtil.toJson(result));
    }
}
