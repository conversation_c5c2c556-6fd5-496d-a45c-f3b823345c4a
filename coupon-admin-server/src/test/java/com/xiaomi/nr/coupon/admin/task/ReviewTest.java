package com.xiaomi.nr.coupon.admin.task;


import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewInfoResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CreateCouponFillReviewRequest;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponTaskReviewService;

import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.retail.application.dubbo.coupon.DubboCouponCallBackServiceImpl;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description:
 * @Date: 2022.05.
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class ReviewTest {


    @Autowired
    private DubboCouponCallBackServiceImpl dubboCouponBpmCallBackService;


    @Autowired
    private DubboCouponTaskReviewService taskReviewService;


    @Test
    public void testQueryReviewInfo() {
        String bpmKey = "7b68ce89-e25b-11ec-b402-0242ac190002";
        Result<CouponReviewInfoResponse> res = dubboCouponBpmCallBackService.queryReviewInfo(bpmKey);
        CouponReviewInfoResponse response = res.getData();
        System.out.println(response.getCouponTypeText());
    }



    @Test
    public void testReview() {
        CreateCouponFillReviewRequest request = new CreateCouponFillReviewRequest();
        request.setTaskName("测试123");
        request.setBatchId(52631L);
        request.setConfigId(30620L);
        request.setBatchName("AB测非米】-低-中-高_拆分-拆分包2");
        request.setPlanCount(90L);
        request.setUserGroupSize(123);

        System.out.println(GsonUtil.toJson(taskReviewService.createTaskReview(request)));
    }

}
