package com.xiaomi.nr.coupon.admin.autotest;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.CleanCouponDataVO;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.CleanEcardDataVO;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.CleanCouponDataRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.CleanEcardDataRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.CleanRedpacketDataRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.UserEcardListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.response.CouponCreateResponse;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.response.UserEcardListResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.dto.redpacket.UserRedpacketVO;
import com.xiaomi.nr.coupon.admin.api.dto.redpacket.request.UserRedpacketListRequest;
import com.xiaomi.nr.coupon.admin.api.service.DubboUserRedpacketService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Description:
 * @Date: 2022.03.
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles({"staging"})
@SpringBootTest(
        classes = {
                CouponAdminBootstrap.class,
                DubboAutoTestService.class
        })
public class DubboAutoTestService {

    @Autowired
    private com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboAutoTestService dubboAutoTestService;

//    @Reference(check = false, interfaceClass = DubboAutoTestCouponService.class, group = "staging", version = "1.0", timeout = 5000)
//    private DubboAutoTestCouponService dubboAutoTestCouponService;

    @Autowired
    private DubboUserRedpacketService dubboUserRedpacketService;

    @Test
    public void userRedpacketList() {
        UserRedpacketListRequest request = new UserRedpacketListRequest();
//        request.setUserId(1306849448L);
//        request.setStatus("available");
//        request.setTypeId();
        request.setRedpacketId(**********);
//        request.setStatus();
        request.setPageSize(500000);

        Result<BasePageResponse<UserRedpacketVO>> responseResult = dubboUserRedpacketService.userRedpacketList(request);
        System.out.println("=====" + GsonUtil.toJson(responseResult));
    }


    @Test
    public void createCoupon() {
        RpcContext.getContext().setAttachment("$upc_account", "wanghaotian7");
        CouponConfigVO request = new CouponConfigVO();

        PromotionRuleVO promotionRuleVO = new PromotionRuleVO();
        promotionRuleVO.setPromotionType(4);
        promotionRuleVO.setPromotionValue(1000);
        promotionRuleVO.setBottomPrice(0);
        promotionRuleVO.setBottomCount(1);
        promotionRuleVO.setBottomType(2);
        promotionRuleVO.setMaxReduce(0);
        request.setPromotionRuleVO(promotionRuleVO);

        request.setSendScene("7C783BEBB0D1C882E09DA5031B8EAEBF");
        request.setCreator("wanghaotian7");
        request.setCost(new BigDecimal("********"));

        DistributionRuleVO distributionRuleVO = new DistributionRuleVO();
        distributionRuleVO.setApplyCount(10000);
        distributionRuleVO.setFetchLimit(10);
        request.setDistributionRuleVO(distributionRuleVO);

        request.setStartFetchTime(new Date(System.currentTimeMillis() - 600L));
        request.setEndFetchTime(new Date(System.currentTimeMillis() + 600000000L));

        ExtPropVO extProp = new ExtPropVO();
        extProp.setArea(0);
        extProp.setPostFree(1);
        extProp.setProMember(0);
        extProp.setShare(0);
        extProp.setSpecialStore(0);
        request.setExtProp(extProp);

        request.setDepartmentId(0);

        Map<Integer, Integer> costShare = new HashMap<>();
        costShare.put(1, 100);
        request.setCostShare(costShare);
        request.setAreaIds(Collections.singletonList(0L));
        request.setSource(2);

        GoodsRuleVO goodsRuleVO = new GoodsRuleVO();
        goodsRuleVO.setScopeType(1);
        goodsRuleVO.setAutoUpdateGoods(2);
        goodsRuleVO.setGoodsDepartments(Lists.newArrayList(1));
        Map<String, List<Long>> goodsInclude = new HashMap<>();
        goodsInclude.put("sku", Lists.newArrayList(6982L));
        goodsInclude.put("package", Lists.newArrayList());
        goodsRuleVO.setGoodsInclude(goodsInclude);
        goodsRuleVO.setGoodsExclude(new HashMap<>());
        goodsRuleVO.setCategoryIds(Collections.singleton(0L));
        request.setGoodsRuleVO(goodsRuleVO);

        request.setSendPurpose(8);

        UseTermVO useTermVO = new UseTermVO();
        useTermVO.setUseTimeType(1);
        useTermVO.setStartUseTime(new Date(System.currentTimeMillis() - 600L));
        useTermVO.setEndUseTime(new Date(System.currentTimeMillis() + 600000000L));
//        useTermVO.setUseDuration(100);
//        useTermVO.setTimeGranularity(1);
        request.setUseTermVO(useTermVO);

        Map<Integer, UseChannelVO> useChannel = new HashMap<>();
        UseChannelVO useChannelVO = new UseChannelVO();
        useChannelVO.setAll(true);
        useChannelVO.setLimitIds(Lists.newArrayList("2", "3", "4"));
        useChannel.put(1, useChannelVO);
        UseChannelVO useChannelVO2 = new UseChannelVO();
        useChannelVO2.setAll(false);
        useChannelVO2.setLimitIds(Lists.newArrayList("MI0101", "MI0102", "MI0201"));
        useChannel.put(2, useChannelVO2);
        request.setUseChannel(useChannel);
        request.setCouponType(1);
        request.setShipmentId(-1);
        request.setName("乾坤立减券-卡券自动化测试专用-wht");
        request.setCouponDesc("乾坤立减券-卡券自动化测试专用-wht");
        request.setId(0L);
        request.setStatus(1);



        Result<CouponCreateResponse> result = dubboAutoTestService.createCoupon(request);

        log.info(GsonUtil.toJson(result));
    }

    @Test
    public void cleanCouponData() {
        CleanCouponDataRequest request = new CleanCouponDataRequest();
        CleanCouponDataVO cleanCouponDataVO = new CleanCouponDataVO();
        cleanCouponDataVO.setUserId(3150420559L);
        request.setCleanCouponDataVOS(Lists.newArrayList(cleanCouponDataVO));
        Result<Void> result = dubboAutoTestService.cleanCouponData(request);
        System.out.println("===============" + result);
    }


    @Test
    public void cleanRedpacketData() {
        CleanRedpacketDataRequest request = new CleanRedpacketDataRequest();
        request.setUserId(123L);
        Result<Void> result = dubboAutoTestService.cleanRedpacketData(request);
        System.out.println("======" + result);
    }

    @Test
    public void queryUserEcard() {
        UserEcardListRequest request = new UserEcardListRequest();
        request.setUserId(2203321L);
//        request.setTypeId();
//        request.setCardId();
        Result<UserEcardListResponse> result = dubboAutoTestService.queryUserEcard(request);
        System.out.println("=======" + GsonUtil.toJson(result));
    }


    @Test
    public void cleanEcardData() {
        CleanEcardDataRequest request = new CleanEcardDataRequest();
        CleanEcardDataVO cleanEcardDataVO = new CleanEcardDataVO();
        cleanEcardDataVO.setCardId(20010126841682L);
        cleanEcardDataVO.setUserId(2203321L);
        request.setCleanEcardDataVOs(Lists.newArrayList(cleanEcardDataVO));
        Result<Void> result = dubboAutoTestService.cleanEcardData(request);
        System.out.println("====" + result);
    }

}
