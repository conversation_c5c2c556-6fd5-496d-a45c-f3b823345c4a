package com.xiaomi.nr.coupon.admin.scene;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.response.*;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponSceneService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.*;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Random;

/**
 * @Description: 优惠券场景
 * @Date: 2022.03.12 15:21
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles({"staging"})
@SpringBootTest(
        classes = {
                CouponAdminBootstrap.class,
                DubboCouponSceneServiceTest.class
        })
public class DubboCouponSceneServiceTest {

    @Autowired
    private DubboCouponSceneService dubboCouponSceneService;



    private String generateString(int length) {
        String str = "zxcvbnmlkjhgfdsaqwertyuiopQWERTYUIOPASDFGHJKLZXCVBNM1234567890";
        Random random = new Random();
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < length; i++) {
            stringBuilder.append(str.charAt(random.nextInt(str.length() - 1)));
        }
        return stringBuilder.toString();
    }

    @Test
    public void testCreateScene() {
        RpcContext.getContext().getAttachments().put("$upc_account", "caoxiaopeng1");
        CreateOrUpdateSendSceneRequest request = new CreateOrUpdateSendSceneRequest();
        request.setType(CreateOrUpdateSendSceneEnum.CREATE.getCode());
        request.setName("汽车测试场景-cxp");
        request.setSceneDesc("汽车测试场景-cxp场景描述");
        request.setApplyMark("汽车测试场景-cxp申请备注");
        request.setCouponTypeList(Lists.newArrayList(CouponTypeEnum.GOODS.getValue()));
        request.setExtProps(Lists.newArrayList("specialStore"));
        request.setIdGenerationType(SceneIdGenerationEnum.SYSTEM.getCode());
        request.setRelationSceneId(SceneEnum.Official_Marking.getCode());
        request.setSendMode(SceneSendModeEnum.COUPON.getCode());
        request.setAssignMode(Arrays.asList(SceneAssignModeEnum.Coupon_Schedule.getCode(), SceneAssignModeEnum.External_System.getCode()));
        request.setBizPlatform(BizPlatformEnum.CAR.getCode());

        Result<CreateOrUpdateSendSceneResponse> result = dubboCouponSceneService.createOrUpdateSendScene(request);
        System.out.println("sceneCode is " + result.getData().getSceneCode());
    }

    @Test
    public void testUpdateScene() {
        RpcContext.getContext().getAttachments().put("$upc_account", "caoxiaopeng1");
        CreateOrUpdateSendSceneRequest request = new CreateOrUpdateSendSceneRequest();
        request.setSceneId(79L);
        request.setType(CreateOrUpdateSendSceneEnum.UPDATE.getCode());
        request.setName("汽车测试场景-cxp1");
        request.setRelationSceneId(SceneEnum.Car.getCode());
        request.setSceneDesc("汽车测试场景-cxp1场景描述");
        request.setApplyMark("汽车测试场景-cxp1申请备注");
        request.setAssignMode(Arrays.asList(SceneAssignModeEnum.Coupon_Schedule.getCode(), SceneAssignModeEnum.External_System.getCode()));
        request.setCouponTypeList(Lists.newArrayList(CouponTypeEnum.GOODS.getValue()));
        request.setExtProps(Lists.newArrayList("specialStore"));
        request.setBizPlatform(BizPlatformEnum.CAR.getCode());

        request.setIdGenerationType(SceneIdGenerationEnum.SYSTEM.getCode());
        request.setSendMode(SceneSendModeEnum.COUPON.getCode());
        request.setSceneCode("11BFB15C0E2BA4D03DFF93D95C843D0E");

        Result<CreateOrUpdateSendSceneResponse> result = dubboCouponSceneService.createOrUpdateSendScene(request);
        System.out.println("sceneCode is " + result.getData().getSceneCode());
    }

    @Test
    public void testQuerySceneList() {
        QuerySceneListRequest request = new QuerySceneListRequest();
        request.setBizPlatform(BizPlatformEnum.CAR.getCode());
        Result<BasePageResponse<SceneListVO>> result = dubboCouponSceneService.querySceneList(request);
        BasePageResponse<SceneListVO> data = result.getData();
        System.out.println("resp is " + data);
    }

    @Test
    public void testSceneDetail() {
        SceneDetailRequest request = new SceneDetailRequest();
        request.setSceneId(79L);
        SceneDetailResponse resp = dubboCouponSceneService.sceneDetail(request).getData();
        System.out.println("resp is " + resp);
    }

    @Test
    public void testOpt() {
        OperateSceneRequest request = new OperateSceneRequest();
        //request.setId(1L);
        request.setType(SceneStatusEnum.ONLINE.getCode());
        dubboCouponSceneService.operateScene(request);
    }


    @Test
    public void testCreatePermission() {
        try {
            CreatePermissionRequest request = new CreatePermissionRequest();
            request.setSceneId(4L);
            request.setAppId("1109");
            request.setAppName("小米金融/小米支付");
            request.setAppContact("mi..");
            boolean res = dubboCouponSceneService.createPermission(request).getData();
            System.out.println(res);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void batchInsertPermission() {
        try {
            CreatePermissionRequest request = new CreatePermissionRequest();
            Random random = new Random();
            for (int i = 0; i < 50; i++) {
                request.setSceneId(random.nextInt(20) + 1L);
                request.setAppId(generateString(12));
                request.setAppName(generateString(10));
                request.setAppContact(generateString(8));
                boolean res = dubboCouponSceneService.createPermission(request).getData();
                System.out.println(res);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testPermissionList() {
        try {
            PermissionListRequest request = new PermissionListRequest();
            request.setSceneId(1L);
            request.setPageNo(1);
            request.setPageSize(20);
            BasePageResponse<PermissionListVO> result = dubboCouponSceneService.queryPermissionList(request).getData();
            System.out.println(result.getPageNo());
            System.out.println(result.getPageSize());
            System.out.println(result.getTotalCount());
            System.out.println(result.getTotalPage());
            System.out.println("list size: " + result.getList().size());
            Iterator<PermissionListVO> iterator = result.getList().iterator();
            while (iterator.hasNext()) {
                System.out.println(iterator.next());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testOptPermission() {
        try {
            OperatePermissionRequest request = new OperatePermissionRequest();
            //request.setId(1L);
            request.setSceneId(1L);
            request.setAppId("GwIwOcZM2TRn");
            request.setType(ScenePermissionStatusEnum.VALID.getCode());
            dubboCouponSceneService.operatePermission(request);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testSceneCat() {
        Result<SearchSceneWithCatResponse> res = dubboCouponSceneService.searchSceneWithCat();
        SearchSceneWithCatResponse response = res.getData();
        List<CouponSceneTypeVO> couponChannelTypeVOList = response.getCouponChannelTypeVOList();
        System.out.println(couponChannelTypeVOList.size());
        for (CouponSceneTypeVO couponSceneTypeVO : couponChannelTypeVOList) {
            System.out.println(couponSceneTypeVO.getType());
            System.out.println(couponSceneTypeVO.getName());
            System.out.println(couponSceneTypeVO.getChannelVOList().size());
            if (couponSceneTypeVO.getType() == 1) {
                for (SceneCatVO sceneCatVO : couponSceneTypeVO.getChannelVOList()) {
                    System.out.println(sceneCatVO.getSceneCode());
                    System.out.println(sceneCatVO.getSceneName());
                    System.out.println(sceneCatVO.getTips());
                }
            }
            System.out.println("---------------------------------");
        }
    }


    @Test
    public void testSearchSceneWithCatV2() {
        SearchSceneWithCatRequest request = new SearchSceneWithCatRequest();
        request.setCouponType(CouponTypeEnum.GOODS.getValue());
        request.setBizPlatform(BizPlatformEnum.CAR.getCode());
        Result<SearchSceneWithCatResponse> res = dubboCouponSceneService.searchSceneWithCatV2(request);
        SearchSceneWithCatResponse resp = res.getData();
        System.out.println("resp is " + resp);
    }
}


