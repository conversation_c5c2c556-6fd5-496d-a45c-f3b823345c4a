package com.xiaomi.nr.coupon.admin.coupon;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewDetailResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponReviewService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * <AUTHOR>
 * @date 2024/9/26
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class DubboCouponReviewServiceTest {

    @Autowired
    private DubboCouponReviewService dubboCouponReviewService;

    @Test
    public void testCouponReviewDetail() {
        CouponReviewRequest request = new CouponReviewRequest();
        request.setReviewId(5058L);
        CouponReviewDetailResponse response = dubboCouponReviewService.couponReviewDetail(request).getData();

        log.info("testCouponReviewDetail res = {}", GsonUtil.toJson(response));
    }
}
