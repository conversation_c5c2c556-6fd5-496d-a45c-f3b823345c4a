package com.xiaomi.nr.coupon.admin.mapper;

import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.coupon.UserCouponCodeMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysqldao.coupon.po.CouponCodePO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24 14:21
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles({"dev"})
@SpringBootTest(
        classes = {
                CouponAdminBootstrap.class
        })
public class UserCouponCodeMapperTest {
    @Resource
    private UserCouponCodeMapper userCouponCodeMapper;

    @Test
    public void getCouponCodeByConfigIdTest() {
        List<CouponCodePO> result = userCouponCodeMapper.getCouponCodeByConfigId(155300L, 0, 10);
        log.info("getCouponCodeByConfigIdTest result = {}", result);
    }
}
