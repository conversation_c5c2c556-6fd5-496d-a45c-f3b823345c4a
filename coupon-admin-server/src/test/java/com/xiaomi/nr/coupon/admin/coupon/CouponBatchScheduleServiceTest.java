package com.xiaomi.nr.coupon.admin.coupon;

import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.domain.coupon.CouponBatchScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/03/25
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class CouponBatchScheduleServiceTest {

    @Resource
    private CouponBatchScheduleService couponBatchScheduleService;

    @Test
    public void budgetRelease() {
        couponBatchScheduleService.budgetRelease();
    }
}
