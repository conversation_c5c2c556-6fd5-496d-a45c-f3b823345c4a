package com.xiaomi.nr.coupon.admin.infrastruture.rpc.cis;

import com.google.common.collect.Lists;
import com.xiaomi.nr.cis.api.dto.GetVinRelationMapResponse;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.cis.api.dto.GetVinRelationMapRequest;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.CisProxy;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR>
 * @date 2024-12-25 21:21
*/
@Slf4j
@ActiveProfiles("dev")
@SpringBootTest(classes = CouponAdminBootstrap.class)
public class CisProxyTest {

    @Autowired
    private CisProxy cisProxy;

    @Test
    public void getVinRelationMapTest() throws BizError {
        String vin = "MOCKN100000008225";
        GetVinRelationMapRequest request = GetVinRelationMapRequest.builder().vins(Lists.newArrayList(vin)).build();
        GetVinRelationMapResponse response = cisProxy.getVinRelationMap(request);
        log.info("response:{}", GsonUtil.toJson(response));
    }
}
