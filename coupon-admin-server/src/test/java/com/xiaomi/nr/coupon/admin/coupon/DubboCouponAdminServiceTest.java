package com.xiaomi.nr.coupon.admin.coupon;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.PageInfo;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponQueryBudgetListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponUpdateStatusRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.CouponInfoResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponAdminService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.posthandler.OmsDataSyncPostHandler;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.CouponConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class DubboCouponAdminServiceTest {

    @Autowired
    private DubboCouponAdminService adminService;

    @Autowired
    private CouponConfigMapper mapper;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private OmsDataSyncPostHandler omsDataSyncPostHandler;

    @Test
    public void  couponDetailTestFor3c(){

        CouponInfoRequest request = new CouponInfoRequest();
        request.setId(172447L);
        request.setAppId("XM2111");
        String token = StringUtil.getToken(request,"XM2111","10506aa82e59096eb55095c44c70cea4");
        request.setToken(token);
        Result<CouponInfoResponse> response = adminService.couponConfigDetail(request);
        Assert.assertNotNull(response);
        log.info(GsonUtil.toJson(response));
    }

    @Test
    public void couponDetailTestForCarShop(){

        CouponInfoRequest request = new CouponInfoRequest();
        request.setId(182788L);
        request.setAppId("XM2111");
        String token = StringUtil.getToken(request,"XM2111","10506aa82e59096eb55095c44c70cea4");
        request.setToken(token);
        Result<CouponInfoResponse> response = adminService.couponConfigDetail(request);
        Assert.assertNotNull(response);
        log.info("couponDetailTestForCarShop res = {}", GsonUtil.toJson(response));
    }


    @Test
    public void couponListTest(){

        CouponListRequest request = new CouponListRequest();
        List<Integer> useChannel = new ArrayList<>();
        useChannel.add(1);
        useChannel.add(3);
        request.setUseChannel(useChannel);
        request.setPageNo(1);
        request.setPageSize(3);

        Result<BasePageResponse<CouponConfigListVO>> result = adminService.couponConfigList(request);
        Assert.assertNotNull(result.getData());
        System.out.println(result);
    }

    @Test
    public void updateStatTest(){
        CouponUpdateStatusRequest request = new CouponUpdateStatusRequest();
        request.setId(1L);
        request.setOperateType(1);
        RpcContext.getContext().getAttachments().put("$upc_account", "caoxiaopeng1");
        System.out.println(adminService.updateStatus(request));
    }


    @Test
    public void t1() {
        CouponConfigPO couponConfigPO = couponConfigRepository.searchCouponById(30227L);
        CouponCreateEvent couponCreateEvent = new CouponCreateEvent();
        couponCreateEvent.setData(couponConfigPO);
        omsDataSyncPostHandler.createPost(couponCreateEvent);
    }

    @Test
    public void queryBudgetList() {
        log.info("======================================================");
        CouponQueryBudgetListRequest request = new CouponQueryBudgetListRequest();
        Result<PageInfo<BudgetInfoDto>> result = adminService.queryBudgetList(request);
        log.info("queryBudgetList result = {}", GsonUtil.toJson(result));
        log.info("======================================================");
    }

}
