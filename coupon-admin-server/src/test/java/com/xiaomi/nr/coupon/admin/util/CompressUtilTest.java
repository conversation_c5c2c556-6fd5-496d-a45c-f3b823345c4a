package com.xiaomi.nr.coupon.admin.util;

import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CarPointsConfigLogRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2023/12/11 16:18
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class CompressUtilTest {
    @Autowired
    private CarPointsConfigLogRepository carPointsConfigLogRepository;

    @Test
    public void decompressTest() throws Exception {
        log.info("==================================================");

        byte[] oldParam = {1,0,0,0,120,-38,61,-115,-63,10,-125,48,16,68,127,69,-10,-100,-125,-119,98,90,-81,-19,-91,-121,66,105,-67,75,-116,-117,13,-40,40,102,69,-92,-12,-33,-101,68,-15,56,111,-33,-50,124,-63,-76,80,-14,-100,-127,85,31,-124,18,8,29,-43,-115,34,-3,-82,3,-55,-127,65,51,-73,29,-46,45,120,12,28,-38,-10,-91,-47,30,110,0,-75,-117,-60,95,73,77,84,-103,-48,-60,101,42,-92,60,-99,11,-55,-64,43,7,-52,-118,92,68,56,59,12,-80,90,71,-116,-51,106,28,-5,-11,50,-52,-106,-96,20,105,-70,77,-19,-39,-89,69,77,-42,-40,-18,-87,-56,12,65,-120,99,52,-69,-8,-85,39,84,-124,-37,6,92,81,39,-100,-77,68,-92,34,75,-14,-110,11,63,-101,60,-18,-80,107,-61,-28,-99,-34,44,104,-5,-63,118,25,-4,-2,-17,-6,80,-39};

        byte[] newParam = {1,0,0,0,120,-38,61,-115,-63,14,-62,32,16,68,-1,101,-49,28,0,73,81,-82,-98,-68,106,-17,-124,-106,77,37,81,-38,-108,37,-90,49,-2,-69,-128,-90,-57,121,-5,118,-26,13,-63,-125,17,-118,65,116,79,4,3,-124,-119,-20,-32,104,-68,-37,74,-108,16,2,24,12,-39,79,72,-105,-86,50,72,24,-3,109,-60,-72,-21,21,-40,-44,72,-71,-110,91,-87,15,-75,76,104,46,-75,62,-98,58,-51,-96,40,59,60,116,74,54,-104,19,86,-40,111,11,-74,102,-73,44,-113,-19,60,-25,72,96,36,-25,-65,-87,127,46,-23,-27,-42,24,-30,116,117,20,-26,42,-76,49,-54,-87,-4,126,-66,23,92,63,-86};

        CarPointsBatchConfigPo oldPo = GsonUtil.fromJson(CompressUtil.decompress(oldParam), CarPointsBatchConfigPo.class);

        CarPointsBatchConfigPo newPo = GsonUtil.fromJson(CompressUtil.decompress(newParam), CarPointsBatchConfigPo.class);

        log.info("decompressTest oldPo = {}, newPo = {}", oldPo, newPo);

        log.info("==================================================");
    }
}