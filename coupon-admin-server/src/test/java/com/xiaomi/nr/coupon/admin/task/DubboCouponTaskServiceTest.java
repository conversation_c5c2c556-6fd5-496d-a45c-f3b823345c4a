package com.xiaomi.nr.coupon.admin.task;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponTaskListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.CouponCodeDownloadResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponCodeTaskListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponFillTaskDetailResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponTaskService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.domain.coupon.fillcoupontask.FillCouponService;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.task.FillCouponTaskMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.http.UserApiService;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class DubboCouponTaskServiceTest {

    @Autowired
    private DubboCouponTaskService dubboCouponTaskService;


    @Autowired
    private FillCouponTaskMapper mapper;

    @Autowired
    private FillCouponService fillCouponService;



    @Test
    public void userTagTest(){
        //userTagProxyService.getUserTagInfo();
    }

    @Test
    public void taskListTest(){

        CouponFillTaskListRequest request = new CouponFillTaskListRequest();
        //request.setCouponType(1);
        request.setBizType(4);
        Result<BasePageResponse<CouponTaskListVO>> result = dubboCouponTaskService.couponFillTaskList(request);
        Assert.assertNotNull(result);
        System.out.println(result);

    }


    @Test
    public void taskDetailTest(){

        CouponFillTaskDetailRequest request = new CouponFillTaskDetailRequest();
        request.setTaskId(12356L);

        Result<CouponFillTaskDetailResponse> result =  dubboCouponTaskService.taskDetail(request);
        Assert.assertNotNull(result);
        System.out.println(result);

    }


    @Test
    public void retryTask(){
        ReStartTaskRequest r = new ReStartTaskRequest();
        r.setTaskId(10L);
        dubboCouponTaskService.taskRetry(r);
    }

    @Test
    public void testCodeTaskList() {
        try {
            CouponCodeTaskListRequest request = new CouponCodeTaskListRequest();
            //request.setConfigId(14780L);
            //request.setStatus(4);
            request.setCouponName("满件");
            Result<BasePageResponse<CouponCodeTaskListVO>> res = dubboCouponTaskService.couponCodeTaskList(request);
            Collection<CouponCodeTaskListVO> list = res.getData().getList();
            System.out.println("===================");
            System.out.println(list.size());
            for (CouponCodeTaskListVO couponCodeTaskListVO : list) {
                System.out.println(couponCodeTaskListVO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Autowired
    private UserApiService userApi;
    @Test
    public void trest() throws BizError {
        System.out.println(userApi.userBatchData("45773"));
    }

    @Test
    public void testDownLoadCode() {
        CouponCodeDownloadRequest request = new CouponCodeDownloadRequest();
        request.setConfigId(30318L);
        request.setIsAdmin(1);
        dubboCouponTaskService.downloadCode(request);
    }

}
