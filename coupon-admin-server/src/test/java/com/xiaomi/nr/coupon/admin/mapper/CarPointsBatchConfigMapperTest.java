package com.xiaomi.nr.coupon.admin.mapper;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.mapper.CarPointsBatchConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles({"staging"})
@SpringBootTest(
        classes = {
                CouponAdminBootstrap.class,
                CarPointsBatchConfigMapperTest.class
        })
public class CarPointsBatchConfigMapperTest {

    @Resource
    private CarPointsBatchConfigMapper carPointsBatchConfigMapper;

    @Test
    public void testInsert() {
        CarPointsBatchConfigPo po = new CarPointsBatchConfigPo();
        po.setName("已结束-下线");
        po.setBudgetId(1L);
        po.setSendScene("cxp-test");
        po.setStartTime(1698768000L);
        po.setEndTime(1701360000L);
        po.setUseTimeType(1);
        po.setApplyCount(10000L);
        po.setSendCount(1000L);
        po.setWarningRatio(10);
        po.setStatus(2);
        po.setCreator("caoxiaopeng1");
        carPointsBatchConfigMapper.insert(po);
        System.out.println("po is " + GsonUtil.toJson(po));
    }

    @Test
    public void testSelectByPeriodType() {
        List<CarPointsBatchConfigPo> carPointsBatchConfigPos = carPointsBatchConfigMapper.selectByPeriodStatus(null, null, null, null);
        System.out.println("全部 carPointsBatchConfigPos is " + GsonUtil.toJson(carPointsBatchConfigPos));
        long queryTime = System.currentTimeMillis() / 1000;
        carPointsBatchConfigPos = carPointsBatchConfigMapper.selectByPeriodStatus(1, queryTime, null, null);
        System.out.println("进行中 carPointsBatchConfigPos is " + GsonUtil.toJson(carPointsBatchConfigPos));
        carPointsBatchConfigPos = carPointsBatchConfigMapper.selectByPeriodStatus(2, queryTime, null, null);
        System.out.println("未开始 carPointsBatchConfigPos is " + GsonUtil.toJson(carPointsBatchConfigPos));
        carPointsBatchConfigPos = carPointsBatchConfigMapper.selectByPeriodStatus(3, queryTime, null, null);
        System.out.println("已结束 carPointsBatchConfigPos is " + GsonUtil.toJson(carPointsBatchConfigPos));

    }

    @Test
    public void testSelectOnlineInProgressBatch() {
        List<CarPointsBatchConfigPo> carPointsBatchConfigPos = carPointsBatchConfigMapper.selectOnlineInProgressBatch(0, System.currentTimeMillis() / 1000, 10);
        System.out.println("carPointsBatchConfigPos is " + GsonUtil.toJson(carPointsBatchConfigPos));
        carPointsBatchConfigPos = carPointsBatchConfigMapper.selectOnlineInProgressBatch(7, System.currentTimeMillis() / 1000, 10);
        System.out.println("carPointsBatchConfigPos is " + GsonUtil.toJson(carPointsBatchConfigPos));
    }

    @Test
    public void testSelectAvailableBatch() {
        List<CarPointsBatchConfigPo> carPointsBatchConfigPos = carPointsBatchConfigMapper.selectAvailableBatch(Lists.newArrayList(7L,8L,9L), null, null, null);
        System.out.println("carPointsBatchConfigPos is " + GsonUtil.toJson(carPointsBatchConfigPos));
        List<String> testList = new ArrayList<>();
        testList.add("cxp-test");
        carPointsBatchConfigPos = carPointsBatchConfigMapper.selectAvailableBatch(null, testList, null, null);
        System.out.println("carPointsBatchConfigPos is " + GsonUtil.toJson(carPointsBatchConfigPos));
        carPointsBatchConfigPos = carPointsBatchConfigMapper.selectAvailableBatch(null, null, true, System.currentTimeMillis() / 1000);
        System.out.println("carPointsBatchConfigPos is " + GsonUtil.toJson(carPointsBatchConfigPos));
    }
}
