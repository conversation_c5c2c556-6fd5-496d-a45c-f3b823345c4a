package com.xiaomi.nr.coupon.admin.coupon;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.UserCouponCodeVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.request.UserCouponCodeListRequest;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponCodeService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.util.AesUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2024/7/4 15:02
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class DubboCouponCodeServiceTest {
    @Autowired
    private DubboCouponCodeService dubboCouponCodeService;

    @Test
    public void userCouponCodeListTest() throws Exception {
        UserCouponCodeListRequest request = new UserCouponCodeListRequest();
        String code = AesUtil.decrypt("M6EED09BCC6539AD", "veHAM7ttrMy7EMWt7JzVGPos3K5vaAO3tO54hiXbzYs=");
        request.setCouponCode(code);
        request.setUseMode(1);

        Result<BasePageResponse<UserCouponCodeVO>> result = dubboCouponCodeService.userCouponCodeList(request);

        log.info("userCouponCodeListTest request = {}, result = {}", GsonUtil.toJson(request), GsonUtil.toJson(result));
    }
}
