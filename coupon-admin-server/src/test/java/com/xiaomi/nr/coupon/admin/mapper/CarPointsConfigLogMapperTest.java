package com.xiaomi.nr.coupon.admin.mapper;

import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.PointsConfigLogOptTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.mapper.CarPointsConfigLogMapper;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.pointadmin.po.CarPointsConfigLogPo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles({"staging"})
@SpringBootTest(
        classes = {
                CouponAdminBootstrap.class,
                CarPointsConfigLogMapperTest.class
        })
public class CarPointsConfigLogMapperTest {

    @Resource
    private CarPointsConfigLogMapper carPointsConfigLogMapper;

    @Test
    public void testInsert() {
        CarPointsConfigLogPo po = new CarPointsConfigLogPo();
        po.setBatchId(7L);
        po.setOptType(PointsConfigLogOptTypeEnum.ONLINE.getCode());
        po.setOperator("caoxiaopeng1");
        po.setContent("test_insert");
        Integer effectNum = carPointsConfigLogMapper.insert(po);
        System.out.println("effectNum is {}" + effectNum);
    }

    @Test
    public void testSelectUpdater() {
        String updater = carPointsConfigLogMapper.selectUpdater(7L);
        System.out.println(updater);
        updater = carPointsConfigLogMapper.selectUpdater(10L);
        System.out.println(updater);
    }

}
