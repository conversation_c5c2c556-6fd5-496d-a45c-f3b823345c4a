package com.xiaomi.nr.coupon.admin.infrastruture.rpc.budget;

import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.BrProxy;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * @description 
 * <AUTHOR>
 * @date 2024-09-19 18:57
*/
@Slf4j
@ActiveProfiles("dev")
@SpringBootTest(classes = CouponAdminBootstrap.class)
public class BrProxyTest {

    @Autowired
    private BrProxy brProxy;

    @Test
    public void queryBrInfoTest() throws BizError {
        String budgetApplyNo = "BR202409190035";
        Long lineNum = 12894L;
        String feeType = "CARSHOP1";
        BudgetInfoDto budgetInfoDto = brProxy.queryBrInfo(budgetApplyNo, lineNum, feeType);
        log.info(GsonUtil.toJson(budgetInfoDto));
    }


}
