package com.xiaomi.nr.coupon.admin.coupon.posthandler;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.posthandler.CouponChangeMQPostHandler;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/9/24
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class CouponChangeMQPostHandlerTest {

    @Resource
    private CouponChangeMQPostHandler couponChangeMQPostHandler;

    CouponConfigPO couponConfigPO;

    @Before
    public void init() {
        couponConfigPO = new CouponConfigPO();
        couponConfigPO.setId(173067L);
        couponConfigPO.setStartFetchTime(1727366400L);
        couponConfigPO.setEndFetchTime(1730390399L);
    }


    @Test
    public void testCreatePost() throws Exception {

        CouponCreateEvent event = new CouponCreateEvent();
        event.setData(couponConfigPO);
        event.setBizPlatform(BizPlatformEnum.CAR_SHOP.getCode());

        couponChangeMQPostHandler.createPost(event);
    }

    @Test
    public void testUpdatePost() throws Exception {

        CouponUpdateEvent event = new CouponUpdateEvent();
        event.setData(couponConfigPO);
        event.setBizPlatform(BizPlatformEnum.CAR_SHOP.getCode());

        couponChangeMQPostHandler.updatePost(event);
    }

    @Test
    public void testUpdateStatusPost() throws Exception {

        CouponUpdateStatusEvent event = new CouponUpdateStatusEvent();
        event.setData(couponConfigPO);
        event.setBizPlatform(BizPlatformEnum.CAR_SHOP.getCode());

        // 上线
        couponConfigPO.setStatus(CouponConfigStatusEnum.ONLINE.code);
        couponChangeMQPostHandler.updateStatusPost(event);

        // 下线
        couponConfigPO.setStatus(CouponConfigStatusEnum.OFFLINE.code);
        couponChangeMQPostHandler.updateStatusPost(event);

        // 终止领取
        couponConfigPO.setStatus(CouponConfigStatusEnum.STOP_FETCHING.code);
        couponChangeMQPostHandler.updateStatusPost(event);
    }
}
