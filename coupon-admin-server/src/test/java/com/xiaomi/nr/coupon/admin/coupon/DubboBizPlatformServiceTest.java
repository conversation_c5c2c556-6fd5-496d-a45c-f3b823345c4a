package com.xiaomi.nr.coupon.admin.coupon;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.bizPlatform.response.BizPlatformListResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboBizPlatformService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class DubboBizPlatformServiceTest {

    @Autowired
    private DubboBizPlatformService bizPlatformService;

    @Test
    public void testQueryList() {
        Result<BizPlatformListResponse> result = bizPlatformService.queryList();
        System.out.println("bizPlatformList is " + result.getData());
    }
}
