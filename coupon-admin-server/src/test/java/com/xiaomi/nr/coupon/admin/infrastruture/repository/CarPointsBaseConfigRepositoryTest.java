package com.xiaomi.nr.coupon.admin.infrastruture.repository;

import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.pointadmin.po.PointBaseConfigCachePo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2024/2/4 14:19
 */
@Slf4j
@ActiveProfiles("dev")
@SpringBootTest(classes = CouponAdminBootstrap.class)
class CarPointsBaseConfigRepositoryTest {
    @Resource
    private CarPointsBaseConfigRepository carPointsBaseConfigRepository;

    @Test
    public void getPointBaseBatchConfigCacheTest() {
        log.info("============================================================");

        PointBaseConfigCachePo res = carPointsBaseConfigRepository.getPointBaseBatchConfigCache();
        log.info("getPointBaseBatchConfigCacheTest res  = {}", res);

        log.info("============================================================");
    }
}