package com.xiaomi.nr.coupon.admin.coupon.posthandler;

/**
 * <AUTHOR>
 * @date 2024/9/29
 */

import com.xiaomi.nr.coupon.admin.BaseTest;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.posthandler.RedisRefreshPostHandler;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/9/24
 */
@Slf4j
public class RedisRefreshPostHandlerTest extends BaseTest {

    @Resource
    private RedisRefreshPostHandler redisRefreshPostHandler;

    CouponConfigPO couponConfigPO;

    @Before
    public void init() {
        couponConfigPO = new CouponConfigPO();
        couponConfigPO.setId(173474L);
    }

    @Test
    public void testUpdatePost() throws Exception {

        CouponUpdateEvent event = new CouponUpdateEvent();
        event.setData(couponConfigPO);
        event.setBizPlatform(BizPlatformEnum.CAR_SHOP.getCode());
        event.setCompensateFlag(true);

        redisRefreshPostHandler.updatePost(event);

    }

}
