package com.xiaomi.nr.coupon.admin.coupon;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.UserCouponVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.CouponInfoResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.request.UserCouponListRequest;
import com.xiaomi.nr.coupon.admin.api.service.DubboUserCouponService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2024/3/9
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class DubboUserCouponServiceTest {

    @Autowired
    private DubboUserCouponService dubboUserCouponService;

    @Test
    public void testUserCouponList(){

        UserCouponListRequest req = new UserCouponListRequest();
        req.setBizType(4);
//        req.setVid("MOCKD000000000001");
        req.setVin("LKBCC2MS2NK201324");
        req.setPageNo(1);
        req.setPageSize(10);

        Result<BasePageResponse<UserCouponVO>> result = dubboUserCouponService.userCouponList(req);

        log.info("result is {}", GsonUtil.toJson(result));

    }

}
