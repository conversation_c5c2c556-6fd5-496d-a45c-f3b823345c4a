package com.xiaomi.nr.coupon.admin.coupon;

import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.budget.request.QueryByUploadUrlFileRequest;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboBudgetService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * @description 
 * <AUTHOR>
 * @date 2024-09-23 11:05
*/
@Slf4j
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class DubboBudgetServiceTest {

    @Autowired
    private DubboBudgetService dubboBudgetService;

    @Test
    public void queryByUploadBudgetFileUrlTest () {
        QueryByUploadUrlFileRequest request = new QueryByUploadUrlFileRequest();
        request.setUrl("https://staging-cnbj2-fds.api.xiaomi.net/nr-coupon-bucket/whttest/预算文件test1.xlsx");
        request.setFeeType("CARSHOP1");
        Result<BudgetInfoDto> result = dubboBudgetService.queryByUploadBudgetFileUrl(request);
        log.info(GsonUtil.toJson(result));
    }
}
