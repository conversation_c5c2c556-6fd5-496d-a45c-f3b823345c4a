package com.xiaomi.nr.coupon.admin.coupondata;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.request.CouponLogListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.request.LogDetailRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.CouponLogListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.ModifyContentVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.statistic.request.CouponDataStatisticRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.statistic.response.CouponDataStatisticVO;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponDataService;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponLogService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Date: 2022.03.
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles({"staging"})
@SpringBootTest(
        classes = {
                CouponAdminBootstrap.class,
                CouponDataTest.class
        })
public class CouponDataTest {

    @Autowired
    private DubboCouponDataService dubboCouponDataService;

    @Test
    public void testGetCouponDataStatistic() {
        CouponDataStatisticRequest request = new CouponDataStatisticRequest();
        request.setStartTime(new Date(1653062400000L));
        request.setEndTime(new Date(1653667200000L));
        //request.setConfigIds("2119,2238");
        //request.setUseChannel("mishop");
        request.setSendType("external");
        Result<BasePageResponse<CouponDataStatisticVO>> couponLogListVOList = dubboCouponDataService.getCouponDataStatistic(request);
        System.out.println(couponLogListVOList);
    }

    @Test
    public void testGetFillCouponDataStatistic() {
        CouponDataStatisticRequest request = new CouponDataStatisticRequest();
        request.setStartTime(new Date(1653062400000L));
        request.setEndTime(new Date(1653667200000L));
        request.setActivity("50416");
        Result<BasePageResponse<CouponDataStatisticVO>> couponLogListVOList = dubboCouponDataService.getFillCouponDataStatistic(request);
        System.out.println(couponLogListVOList);
    }

    @Test
    public void testExportCouponDataStatistic() {
        CouponDataStatisticRequest request = new CouponDataStatisticRequest();
        request.setExportData(1);
        Result<String> result = dubboCouponDataService.exportCouponDataStatistic(request);
        System.out.println(result);

        request.setExportData(2);
        Result<String> result2 = dubboCouponDataService.exportCouponDataStatistic(request);
        System.out.println(result2);
    }
}
