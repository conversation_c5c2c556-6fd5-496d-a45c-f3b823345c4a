package com.xiaomi.nr.coupon.admin.filter;

import com.alibaba.druid.util.StringUtils;
import com.google.common.collect.Sets;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.nacosconfig.NacosI18nCouponConfig;
import com.xiaomi.nr.global.dev.base.HeraContextKeyValueHolder;
import com.xiaomi.nr.global.dev.base.RequestContextInfo;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.Constants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.Filter;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Result;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.RpcResult;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.Set;

/**
 * 券管理端的前置过滤器
 * 限制未开放区域请求
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Setter
@Activate(group = Constants.PROVIDER, order = 2)
public class CouponRegionFilter implements Filter {
    // hera context
    private static final String HERA_RETAIL_DEV_MODE = "mone-retail-dev-mode";
    private static final String HERA_RETAIL_GLOBAL_LANGUAGE = "mone-retail-language-for-global";
    private static final Set<String> IGNORE_INTERFACE_METHOD = Sets.newHashSet("com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponBpmCallBackService.onStatusChangedHttp");
    private NacosI18nCouponConfig nacosI18nCouponConfig;

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        //服务本地调试初始化HeraContext
        initLocalHeraContext();
        //Region过滤器忽略方法
        boolean ignore = shouldIgnoreMethod(invoker, invocation);
        boolean support = nacosI18nCouponConfig.getSupportAreaIds().stream().anyMatch(areaId -> areaId.equals(RequestContextInfo.getAreaId()));
        if (!support && !ignore) {
            RpcResult rpcResult = new RpcResult();
            rpcResult.setValue(com.xiaomi.youpin.infra.rpc.Result.fromException(ExceptionHelper.create(GeneralCodes.Forbidden, TranslationEnum.COUPON_REGION_NOT_SUPPORT.getTranslateContent()))); ;
            return rpcResult;
        }
        return invoker.invoke(invocation);
    }

    /**
     * HeraContext本地初始化Mock
     * language,region
     */
    private void initLocalHeraContext() {
        String devMode = Optional.ofNullable(System.getProperty("dev.mode")).orElse("false");
        if (StringUtils.equals(devMode, "true")) {
            HeraContextKeyValueHolder.put(HERA_RETAIL_DEV_MODE, devMode);
            HeraContextKeyValueHolder.put(HERA_RETAIL_GLOBAL_LANGUAGE, Optional.ofNullable(System.getProperty("global.language")).orElse("zh-CN"));
        }
    }

    private boolean shouldIgnoreMethod(Invoker<?> invoker, Invocation invocation) {
        return IGNORE_INTERFACE_METHOD.contains(invoker.getInterface().getName() + "." + invocation.getMethodName());
    }
}
