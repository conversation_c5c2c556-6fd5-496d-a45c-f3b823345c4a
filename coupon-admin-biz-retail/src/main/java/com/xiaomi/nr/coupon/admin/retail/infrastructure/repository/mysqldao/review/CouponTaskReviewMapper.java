package com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review;

import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity.SearchTaskReviewListParam;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponTaskReviewPO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface CouponTaskReviewMapper {


    /**
     * 插入couponReview
     *
     * @return
     */
    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("insert into nr_coupon_task_review (task_id,task_name,config_id,config_name,plan_count,compress_info,status," +
            "department_id,creator)" +
            " values " +
            "(#{taskId,jdbcType=BIGINT},#{taskName,jdbcType=VARCHAR},#{configId,jdbcType=BIGINT},#{configName,jdbcType=VARCHAR}," +
            "#{planCount,jdbcType=BIGINT},#{compressInfo,jdbcType=VARCHAR},#{status,jdbcType=TINYINT}," +
            "#{departmentId,jdbcType=BIGINT},#{creator,jdbcType=VARCHAR})")
    Long insert(CouponTaskReviewPO po);

    /**
     * 更新审核记录的bpmKey
     * @param id
     * @param bpmKey
     * @return
     */
    @Update("<script>" +
            " update nr_coupon_task_review " +
            "<set>" +
            "<if test='bpmKey!=null'> bpm_key=#{bpmKey},</if>" +
            "</set>" +
            " where id =#{id}" +
            "</script>")
    Long updateBpmKey(@Param("id") long id, @Param("bpmKey")  String bpmKey);

    /**
     * 根据bpmKey查询审核信息
     * @param bpmKey
     * @return
     */
    @Select("<script>select * from nr_coupon_task_review where bpm_key=#{bpmKey}</script>")
    CouponTaskReviewPO selectByBpmKey(@Param("bpmKey") String bpmKey);

    /**
     * 更新灌券审核信息
     * @param po
     * @return
     */
    @Update("<script>" +
            " update nr_coupon_task_review " +
            "<set>" +
            "<if test='taskId!= null and taskId!= 0'> task_id=#{taskId},</if>" +
            "<if test='status!=0'> status=#{status},</if>" +
            "<if test='bpmReason!=null'> bpm_reason=#{bpmReason},</if>" +
            "<if test='approvedId!=null'> approved_id=#{approvedId},</if>" +
            "<if test='approvedTime!=null'> approved_time=#{approvedTime},</if>" +
            "</set>" +
            " where id =#{id}" +
            "</script>")
    int updateReviewStatus(CouponTaskReviewPO po);

    /**
     * 根据审核id查询审核信息
     * @param id
     * @return
     */
    @Select("<script>select * from nr_coupon_task_review where id=#{id}</script>")
    CouponTaskReviewPO selectById(@Param("id") long id);


    /**
     * 灌券任务审核列表查询
     * @param param
     * @return
     */
    @Select("<script>" +
            "select id,task_name,config_id,config_name,plan_count,creator,create_time,status" +
            " from nr_coupon_task_review " +
            " where status!=3 " +
            "<if test='id!= 0 and id != null'> and id=#{id} </if>" +
            "<if test='configId!= 0 and configId != null'> and config_id=#{configId} </if>" +
            "<if test='taskName != null and taskName !=\"\"'> and task_name like concat(concat('%',#{taskName}),'%') </if>" +
            "<if test='creator != null and creator !=\"\" '> and creator=#{creator}</if>" +
            " order by ${orderBy} ${orderDirection}"+
            "</script>")
    List<CouponTaskReviewPO> selectList(SearchTaskReviewListParam param);


    /**
     * 根据任务id查询信息
     * @param id
     * @return
     */
    @Select("<script>select compress_info from nr_coupon_task_review where task_id=#{id}</script>")
    CouponTaskReviewPO selectByTaskId(@Param("id") long id);


    @Update("<script>" +
            " update nr_coupon_task_review " +
            "<set>" +
            "<if test='taskId !=null and taskId!= 0'> task_id=#{taskId},</if>" +
            "</set>" +
            " where bpm_key =#{bpmKey}" +
            "</script>")
    int updateReviewTaskId(@Param("taskId") Long taskId, @Param("bpmKey") String bpmKey);


    /**
     * 根据券配置id查询任务信息
     * @param configId
     * @return
     */
    @Select("select id from nr_coupon_task_review where config_id=#{configId} and status in(1,2)")
    List<Long> selectByConfigId(@Param("configId") long configId);

}
