package com.xiaomi.nr.coupon.admin.retail.infrastructure.repository;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xiaomi.nr.coupon.admin.infrastruture.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.CouponReviewRelMapper;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity.SeachReviewGroupResult;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity.SearchReviewGroupParam;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponReviewRelPO;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CouponReviewRelRepository {

    @Autowired
    private CouponReviewRelMapper couponReviewRelMapper;

    /**
     * 查询创建人对应的审核组
     *
     * @param creator
     * @return
     */
    public CouponReviewRelPO selectByCreator(String creator) {
        return couponReviewRelMapper.selectByCreator(creator);
    }


    /**
     * 保存审核关系
     *
     * @param couponReviewRelPO
     */
    public void saveOrUpdate(CouponReviewRelPO couponReviewRelPO) throws BizError {
        couponReviewRelPO.setAddUser(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());
        if (couponReviewRelPO.getId() == null || couponReviewRelPO.getId() <= 0) {
            try {
                couponReviewRelMapper.insert(couponReviewRelPO);
            } catch (DuplicateKeyException e) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "员工已存在，不能重复插入");
            }
        } else {
            couponReviewRelMapper.update(couponReviewRelPO);
        }
    }

    /**
     * 根据查询条件获取审核关系列表
     *
     * @param param
     * @return
     */
    public SeachReviewGroupResult selectList(SearchReviewGroupParam param) {

        PageHelper.startPage(param.getPageNo(), param.getPageSize());

        List<CouponReviewRelPO> list = couponReviewRelMapper.selectList(param);

        SeachReviewGroupResult result = new SeachReviewGroupResult();
        if (CollectionUtils.isNotEmpty(list)) {
            PageInfo<CouponReviewRelPO> pageInfo = new PageInfo<>(list);
            result.setCouponReviewRelPOList(list);
            result.setTotalCount(pageInfo.getTotal());
            result.setTotalPage(pageInfo.getPages());
        }

        return result;
    }


}
