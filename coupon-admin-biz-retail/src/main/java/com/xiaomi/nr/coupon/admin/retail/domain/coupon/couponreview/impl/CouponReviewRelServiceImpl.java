package com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponreview.impl;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.ReviewGroupSaveRequest;
import com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponreview.CoupoReviewRelService;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: heji<PERSON>eng
 * @Date 2022/7/12 2:54 下午
 * @Version: 1.0
 **/
@Service
public class CouponReviewRelServiceImpl implements CoupoReviewRelService {

    /**
     * 入参校验
     * @param request
     * @return
     * @throws Exception
     */
    @Override
    public void check(ReviewGroupSaveRequest request) throws Exception {
        if (StringUtils.isEmpty(request.getCreator())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "员工名称不能为空");
        }
        if (StringUtils.isEmpty(request.getReviewGroup())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "审核组Id不能为空");
        }
    }
}
