package com.xiaomi.nr.coupon.admin.retail.application.dubbo.coupon.convert;


import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.ReviewGroupDTO;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.ReviewStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseTimeTypeEnum;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponConfigReviewPO;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponReviewRelPO;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;

import java.util.List;

@Slf4j
public class CouponReviewConvert {

    public static CouponConfigReviewPO convertReviewPO(CouponConfigVO couponConfigVO, List<ApplyAttachmentVO> applyAttachment, String creator, Integer bizPlatform) throws Exception{
        CouponConfigReviewPO po =new CouponConfigReviewPO();
        po.setConfigId(couponConfigVO.getId());
        //前端发版后可直接赋值
        po.setCouponType(couponConfigVO.getCouponType());
        po.setCouponName(couponConfigVO.getName());
        po.setBizPlatform(bizPlatform);
        PromotionRuleVO promotionRuleVO = couponConfigVO.getPromotionRuleVO();
        po.setPromotionType(promotionRuleVO.getPromotionType());
        po.setPromotionValue(promotionRuleVO.getPromotionValue());
        po.setBottomPrice(promotionRuleVO.getBottomPrice());
        po.setBottomCount(promotionRuleVO.getBottomCount());
        po.setBottomType(promotionRuleVO.getBottomType());
        DistributionRuleVO distributionRuleVO = couponConfigVO.getDistributionRuleVO();
        po.setApplyCount(distributionRuleVO.getApplyCount());
        UseTermVO useTermVO = couponConfigVO.getUseTermVO();
        if(UseTimeTypeEnum.RELATIVE.getValue() == useTermVO.getUseTimeType()){
            useTermVO.setStartUseTime(couponConfigVO.getStartFetchTime());
            useTermVO.setEndUseTime(DateUtils.addHours(couponConfigVO.getEndFetchTime(), useTermVO.getUseDuration()));
        }
        if (UseTimeTypeEnum.CUSTOM.getValue() == useTermVO.getUseTimeType()) {
            po.setStartUseTime(0L);
            po.setEndUseTime(0L);
        } else {
            po.setStartUseTime(useTermVO.getStartUseTime().getTime() / 1000);
            po.setEndUseTime(useTermVO.getEndUseTime().getTime() / 1000);
        }
        po.setStatus(ReviewStatusEnum.UnderReview.getValue());
        po.setDepartmentId(couponConfigVO.getDepartmentId());
        po.setConfigCompress(CompressUtil.compress(GsonUtil.toJson(couponConfigVO)));
        if(CollectionUtils.isNotEmpty(applyAttachment)){
            po.setApplyAttachment(GsonUtil.toJson(applyAttachment));
        }
        po.setCreator(creator);
        return po;
    }

    public static CouponReviewListVO convertToReviewListVO(CouponConfigReviewPO configReviewPO){
        CouponReviewListVO vo =new CouponReviewListVO();
        try {
            CouponConfigVO couponConfigVO = GsonUtil.fromJson(CompressUtil.decompress(configReviewPO.getConfigCompress()),CouponConfigVO.class);
            vo.setCost(couponConfigVO.getCost());
            vo.setStartFetchTime(couponConfigVO.getStartFetchTime());
            vo.setEndFetchTime(couponConfigVO.getEndFetchTime());
        } catch (Exception e) {
            log.error("CouponReviewConvert convertToReviewListVO decompress error request:{}",configReviewPO,e);
        }
        BeanMapper.copy(configReviewPO,vo);
        vo.setReviewId(configReviewPO.getId());
        return vo;
    }

    public static ReviewGroupDTO convertToReviewRelDTO(CouponReviewRelPO configReviewPO){
        ReviewGroupDTO reviewGroupDTO =new ReviewGroupDTO();
        BeanMapper.copy(configReviewPO,reviewGroupDTO);
        return reviewGroupDTO;
    }

}
