package com.xiaomi.nr.coupon.admin.retail.application.dubbo.coupon.convert;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.CouponFillReviewDetailDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CreateCouponFillReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CreateFillCouponTaskRequest;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.ReviewStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.task.CouponTaskTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.task.UserGroupTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.coupon.ZkPathConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.localcache.LocalCacheCommon;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.CouponConfigRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigInfoCachePo;
import com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponreview.entity.CouponTaskReviewContext;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponTaskReviewPO;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;

@Component
public class TaskReviewConvert {

    @Autowired
    private CouponConfigRedisDao couponConfigRedisDao;

    @Autowired
    private LocalCacheCommon localCacheCommon;

    @Autowired
    private CouponSceneRepository couponSceneRepository;


    /**
     * 灌券任务审核转换
     * @param request
     * @return
     * @throws IOException
     */
    public  CouponTaskReviewPO convertToTaskReviewPO(CreateCouponFillReviewRequest request, String fileName) throws IOException {

        CouponTaskReviewPO po = new CouponTaskReviewPO();
        po.setTaskName(request.getTaskName());
        po.setConfigId(request.getConfigId());
        po.setPlanCount(request.getPlanCount());
        po.setDepartmentId(request.getDepartmentId());
        po.setStatus(ReviewStatusEnum.UnderReview.getValue());
        po.setCreator(request.getCreator());
        if(StringUtils.isNotEmpty(fileName)){
            request.setHdfsAddr(ZkPathConstant.FILL_COUPON_UPLOAD_PATH + fileName);
        }
        if(Objects.equals(UserGroupTypeEnum.CUSTOMIZE_USER_GROUP.getCode(), request.getUserGroupType())){
            request.setUserGroupSize(request.getUidList().size());
        }
        po.setCompressInfo(CompressUtil.compress(GsonUtil.toJson(request)));

        return po;
    }


    /**
     * 灌券审核详情转换 (po -> dto)
     * @param reviewPO
     * @return
     * @throws Exception
     */
    public CouponFillReviewDetailDTO convertToReviewDTO(CouponTaskReviewPO reviewPO) throws Exception {

        if(Objects.isNull(reviewPO)){
            return null;
        }

        CouponFillReviewDetailDTO reviewDetailDTO = new CouponFillReviewDetailDTO();

        reviewDetailDTO.setName(reviewPO.getTaskName());
        reviewDetailDTO.setConfigId(reviewPO.getConfigId());
        reviewDetailDTO.setPlanCount(reviewPO.getPlanCount());
        reviewDetailDTO.setCouponName(reviewPO.getConfigId() + "_" +reviewPO.getConfigName());

        CreateCouponFillReviewRequest reviewTaskRequest = GsonUtil.fromJson(CompressUtil.decompress(reviewPO.getCompressInfo()), CreateCouponFillReviewRequest.class);
        if(!Objects.isNull(reviewTaskRequest)){
            reviewDetailDTO.setUserGroupType(reviewTaskRequest.getUserGroupType());
            reviewDetailDTO.setCustomizeType(reviewTaskRequest.getCustomizeType());
            reviewDetailDTO.setBatchId(reviewTaskRequest.getBatchId());
            reviewDetailDTO.setBatchName(reviewTaskRequest.getBatchName());
            reviewDetailDTO.setDistinct(reviewTaskRequest.getDistinct());
            reviewDetailDTO.setHdfsAddr(reviewTaskRequest.getHdfsAddr());
        }

        return reviewDetailDTO;
    }


    /**
     * 构建灌券提交审核上下文
     * @param reviewPO 审核实体
     * @return CouponTaskReviewModel
     */
    public CouponTaskReviewContext convertToReviewContext(CouponTaskReviewPO reviewPO) throws Exception {

        CreateCouponFillReviewRequest fillReviewRequest = GsonUtil.fromJson(CompressUtil.decompress(reviewPO.getCompressInfo()), CreateCouponFillReviewRequest.class);
        //获取券信息、投放场景
        ConfigInfoCachePo couponConfigCache = localCacheCommon.getConfigInfoCachePo(reviewPO.getConfigId());
        String sceneName = couponSceneRepository.selectNameBySceneCode(couponConfigCache.getSendScene());
        reviewPO.setConfigName(couponConfigCache.getName());
        reviewPO.setTaskId(CommonConstant.ZERO_LONG);

        CouponTaskReviewContext model = new CouponTaskReviewContext(reviewPO, couponConfigCache);
        Integer userCount = fillReviewRequest.getUserGroupSize();
        if(Objects.equals(UserGroupTypeEnum.CUSTOMIZE_USER_GROUP.getCode(), fillReviewRequest.getUserGroupType())){
            userCount = fillReviewRequest.getUidList().size();
        }

        model.setSendSceneText(sceneName);
        model.setUserGroupSize(userCount);
        return model;
    }

    /**
     * 灌券任务落库请求转换
     * @param compressInfo byte[]
     * @return
     * @throws Exception
     */
    public CreateFillCouponTaskRequest convertToCouponTaskRequest(byte[] compressInfo) throws Exception {

        CreateCouponFillReviewRequest fillTaskRequest = GsonUtil.fromJson(CompressUtil.decompress(compressInfo), CreateCouponFillReviewRequest.class);

        if (fillTaskRequest == null) {
            throw ExceptionHelper.create(ErrCode.COUPON, "解析灌券任务数据失败");
        }

        CreateFillCouponTaskRequest createFillCouponTaskRequest = new CreateFillCouponTaskRequest();
        BeanMapper.copy(fillTaskRequest, createFillCouponTaskRequest);
        createFillCouponTaskRequest.setApplyCount(fillTaskRequest.getPlanCount());
        createFillCouponTaskRequest.setUserGroupSize(fillTaskRequest.getUserGroupSize().longValue());
        return createFillCouponTaskRequest;
    }

}
