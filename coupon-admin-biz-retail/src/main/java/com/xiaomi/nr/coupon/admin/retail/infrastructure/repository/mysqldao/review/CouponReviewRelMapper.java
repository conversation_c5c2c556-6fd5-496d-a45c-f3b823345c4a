package com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review;

import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity.SearchReviewGroupParam;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponReviewRelPO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public interface CouponReviewRelMapper {


    /**
     * 查询创建人组织关系
     * @param creator
     * @return
     */
    @Select("<script>select * from nr_coupon_review_rel where creator=#{creator}</script>")
    CouponReviewRelPO selectByCreator(@Param("creator") String creator);

    /**
     * 新增组织关系
     * @param po
     */
    @Options(useGeneratedKeys = true, keyColumn = "id")
    @Insert(" insert into nr_coupon_review_rel" +
            "(creator,review_group,remarks,add_user) values (#{creator}, #{reviewGroup}, #{remarks}, #{addUser})")
    void insert(CouponReviewRelPO po);

    /**
     * 更新组织关系
     * @param po
     * @return
     */
    @Update("<script>update nr_coupon_review_rel " +
            "<set>" +
            "<if test='creator!=null'> creator=#{creator},</if>" +
            "<if test='reviewGroup!=null'> review_group=#{reviewGroup},</if>" +
            "<if test='remarks!=null'> remarks=#{remarks},</if>" +
            "</set>" +
            " where id =#{id}" +
            "</script>")
    Long update(CouponReviewRelPO po);

    /**
     * 查询审核组列表
     * @param param
     * @return
     */
    @Select("<script>select * from nr_coupon_review_rel where  1=1" +
            "<if test='creator != null and creator !=\"\"'>and creator like '%${creator}%'</if>" +
            "<if test='reviewGroup != null and reviewGroup !=\"\" '>and review_group=#{reviewGroup}</if>" +
            "order by id desc"+
            "</script>")
    List<CouponReviewRelPO> selectList(SearchReviewGroupParam param);
}
