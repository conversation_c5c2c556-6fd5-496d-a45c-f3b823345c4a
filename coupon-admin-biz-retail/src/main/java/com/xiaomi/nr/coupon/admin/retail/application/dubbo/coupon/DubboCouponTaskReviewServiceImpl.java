package com.xiaomi.nr.coupon.admin.retail.application.dubbo.coupon;

import com.google.common.collect.Lists;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.CouponFillReviewDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.CouponFillReviewDetailDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillReviewCancelRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillReviewDetailRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillReviewListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CreateCouponFillReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponFillReviewCancelResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponFillReviewDetailResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CreateCouponFillReviewResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponTaskReviewService;
import com.xiaomi.nr.coupon.admin.enums.task.CustomizeGroupEnum;
import com.xiaomi.nr.coupon.admin.enums.task.UserGroupTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponTaskRepository;
import com.xiaomi.nr.coupon.admin.retail.application.dubbo.coupon.convert.TaskReviewConvert;
import com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponreview.CouponTaskReviewService;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponTaskReviewPO;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

@Component
@Slf4j
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
@ApiModule(value = "优惠券灌券任务审核服务接口", apiInterface = DubboCouponTaskReviewService.class)
public class DubboCouponTaskReviewServiceImpl implements DubboCouponTaskReviewService {

    @Autowired
    private CouponTaskReviewService couponTaskReviewService;

    @Autowired
    private TaskReviewConvert taskReviewConvert;

    @Autowired
    private CouponTaskRepository couponTaskRepository;

    @Override
    @ApiDoc("创建灌券任务审核")
    public Result<CreateCouponFillReviewResponse> createTaskReview(CreateCouponFillReviewRequest request) {

        try {
            // 参数检验
            validateCreateFillTaskRequest(request);
            request.setCreator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());

            // 创建审核
            Long reviewId = couponTaskReviewService.createTaskReview(request);

            return Result.success(new CreateCouponFillReviewResponse(reviewId));
        } catch (Exception e) {
            log.error("DubboCouponTaskReviewService.createTaskReview error request:{}", request, e);
            return Result.fromException(e);
        }
    }


    @Override
    @ApiDoc("撤销灌券任务审核")
    public Result<CouponFillReviewCancelResponse> cancelTaskReview(CouponFillReviewCancelRequest request) {

        if (Objects.isNull(request.getReviewId())) {
            Result.fail(GeneralCodes.ParamError, "参数错误：审核ID为空");
        }

        try {

            request.setOperator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());

            Boolean isSuccess = couponTaskReviewService.cancelTaskReview(request);

            return Result.success(new CouponFillReviewCancelResponse(isSuccess));
        } catch (Exception e) {
            log.error("DubboCouponTaskReviewService.cancelTaskReview error request:{}", request, e);
            return Result.fromException(e);
        }

    }

    @Override
    @ApiDoc("查询灌券任务审核列表")
    public Result<BasePageResponse<CouponFillReviewDTO>> taskReviewList(CouponFillReviewListRequest request) {

        try {
            BasePageResponse<CouponFillReviewDTO> response = couponTaskReviewService.taskReviewList(request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponTaskReviewService.taskReviewList error request:{}", request, e);
            return Result.fromException(e);
        }
    }


    @Override
    @ApiDoc("查询灌券任务审核详情")
    public Result<CouponFillReviewDetailResponse> taskReviewDetail(CouponFillReviewDetailRequest request) {

        if (Objects.isNull(request) || Objects.isNull(request.getId()) || request.getId() <= CommonConstant.ZERO_LONG) {
            Result.fail(GeneralCodes.ParamError, "参数错误：审核ID为空");
        }

        try {

            CouponTaskReviewPO reviewPO = couponTaskReviewService.getTaskReviewPO(request.getId());

            CouponFillReviewDetailDTO reviewDetailDTO = taskReviewConvert.convertToReviewDTO(reviewPO);

            return Result.success(new CouponFillReviewDetailResponse(reviewDetailDTO));

        } catch (Exception e) {
            log.error("DubboCouponTaskReviewService.taskReviewDetail error request:{}", request, e);
            return Result.fromException(e);
        }

    }


    /**
     * 创建灌券任务入参校验
     *
     * @param request CreateFillCouponTaskRequest
     * @throws BizError 业务异常
     */
    private void validateCreateFillTaskRequest(CreateCouponFillReviewRequest request) throws BizError {
        // 任务id
        if (org.apache.commons.lang.StringUtils.isEmpty(request.getTaskName())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "任务名称不能为空");
        }

        // 优惠券id
        if (request.getConfigId() <= CommonConstant.ZERO_LONG) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券ID不能为空");
        }

        if (request.getPlanCount() < CommonConstant.ONE_INT) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券申请发放数量不合法");
        }

        // 校验灌券数据合法性
        if (request.getUserGroupType() == UserGroupTypeEnum.CUSTOMIZE_USER_GROUP.getCode()) {

            if (request.getCustomizeType() == CustomizeGroupEnum.UPLOAD_FILE.getCode() && CollectionUtils.isEmpty(request.getUidList())) {
                throw ExceptionHelper.create(ErrCode.COUPON, "TXT文件内容为空");
            }
            if (request.getCustomizeType() == CustomizeGroupEnum.CLIENT_INPUT.getCode() && CollectionUtils.isEmpty(request.getUidList())) {
                throw ExceptionHelper.create(ErrCode.COUPON, "用户UID列表不能为空");
            }

            // 人群mid去重
            if (request.getDistinct() == CommonConstant.ZERO_INT) {
                Set<Long> uidSet = new HashSet<>(request.getUidList());
                request.setUidList(Lists.newArrayList(uidSet));
            }
        } else {
            if (Objects.isNull(request.getBatchId())) {
                throw ExceptionHelper.create(ErrCode.COUPON, "请正确输入人群包ID");
            }
        }


    }


}
