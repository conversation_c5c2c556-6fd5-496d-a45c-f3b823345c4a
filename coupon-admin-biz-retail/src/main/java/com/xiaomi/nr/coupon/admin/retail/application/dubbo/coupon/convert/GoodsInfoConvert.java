package com.xiaomi.nr.coupon.admin.retail.application.dubbo.coupon.convert;

import com.xiaomi.goods.gis.dto.PageInfo;
import com.xiaomi.goods.gis.dto.batched.offline.BatchedAvailablePageRequest;
import com.xiaomi.goods.gis.dto.batched.offline.BatchedDTO;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoDTO;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsPageDTO;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsPageRequest;
import com.xiaomi.goods.gis.dto.sku.SkuBusinessCategory;
import com.xiaomi.goods.gis.dto.sku.offline.SkuAvailablePageDTO;
import com.xiaomi.goods.gis.dto.sku.offline.SkuAvailablePageRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.PackageInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.SkuInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.request.PackagePageListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.request.SkuPageListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.request.SuitPageListRequest;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.BizSubTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsItemTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.coupon.CouponConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 商品信息转换类
 */
@Service
@Slf4j
public class GoodsInfoConvert {

    /**
     * 构造信息查询参数转换
     *
     * @param request GmsGoodsListRequest
     * @return SkuPageRequest
     */
    public SkuAvailablePageRequest convert2SkuPageRequestGis(SkuPageListRequest request) {
        SkuAvailablePageRequest skuPageRequest = new SkuAvailablePageRequest();

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(request.getPageNo());
        pageInfo.setPageSize(request.getPageSize());
        skuPageRequest.setPage(pageInfo);
        skuPageRequest.setNeedStock(false);
        skuPageRequest.setNeedBusinessCategory(true);
        skuPageRequest.setSkuList(request.getSkus());
        skuPageRequest.setGoodsIdList(request.getGids());
        skuPageRequest.setProductIdList(request.getPids());
        skuPageRequest.setName(request.getName());
        // 商品业务类型 仅支持普通3c商品、泛全商品
        skuPageRequest.setBizSubType(Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode(), BizSubTypeEnum.CARRIER_PAN_COMPLETE.getCode()));

        if (CouponTypeEnum.POSTFREE.getValue().equals(request.getCouponType())) {
            skuPageRequest.setBusinessTypeList(Lists.newArrayList(CouponConstant.BUSINESS_TYPE_SELF));
        } else {
            skuPageRequest.setBusinessTypeList(Lists.newArrayList(CouponConstant.BUSINESS_TYPE_SELF, CouponConstant.BUSINESS_TYPE_CAIXIAO));
        }

        skuPageRequest.setThirdBusinessCategoryId(request.getCateId());
        Boolean isSale = Objects.isNull(request.getStatus()) ? null : Objects.equals(GoodsStatusEnum.ONSALE_ONSHELF.getId(), request.getStatus());

        // 是否需要米家大仓发货支持  true-支持大仓发货标识 false-不支持大仓发货标识，默认值 - null,不做是否是大仓发货的过滤
        if (Objects.equals(CouponTypeEnum.POSTFREE.getValue(), request.getCouponType())) {

            skuPageRequest.setMiSupportShipment(false);
            skuPageRequest.setSaleMiStore(isSale);
        } else {

            if (!Objects.isNull(isSale) && isSale) {
                skuPageRequest.setAnySale(true);
            } else {
                skuPageRequest.setSale(isSale);
                skuPageRequest.setSaleMiStore(isSale);
                skuPageRequest.setSaleSqStore(isSale);
            }

        }

        if (!Objects.isNull(request.getSku())) {
            if (Objects.isNull(skuPageRequest.getSkuList())) {
                skuPageRequest.setSkuList(Collections.singletonList(request.getSku()));
            } else {
                skuPageRequest.getSkuList().add(request.getSku());
            }

        }

        if (!Objects.isNull(request.getGid())) {
            if (Objects.isNull(skuPageRequest.getGoodsIdList())) {
                skuPageRequest.setGoodsIdList(Collections.singletonList(request.getGid()));
            } else {
                skuPageRequest.getGoodsIdList().add(request.getGid());
            }

        }

        if (!Objects.isNull(request.getPid())) {
            if (Objects.isNull(skuPageRequest.getProductIdList())) {
                skuPageRequest.setProductIdList(Collections.singletonList(request.getPid()));
            } else {
                skuPageRequest.getProductIdList().add(request.getPid());
            }
        }


        return skuPageRequest;
    }

    /**
     * 构造套装信息查询参数(gis接口)
     *
     * @param request PackagePageListRequest
     * @return BatchedListRequest
     */
    public BatchedAvailablePageRequest convert2PackagePageRequestGis(PackagePageListRequest request) {
        BatchedAvailablePageRequest packageRequest = new BatchedAvailablePageRequest();
        PageInfo pageInfo = new PageInfo();

        pageInfo.setPageNum(request.getPageNo());
        pageInfo.setPageSize(request.getPageSize());
        packageRequest.setPage(pageInfo);
        packageRequest.setBatchedIdList(request.getCids());
        packageRequest.setName(request.getName());
        packageRequest.setBusinessTypeList(Lists.newArrayList(CouponConstant.BUSINESS_TYPE_SELF, CouponConstant.BUSINESS_TYPE_CAIXIAO));
        packageRequest.setNeedStock(false);

        Boolean isSale = Objects.isNull(request.getStatus()) ? null : Objects.equals(GoodsStatusEnum.ONSALE_ONSHELF.getId(), request.getStatus());


        if (!Objects.isNull(isSale) && isSale) {
            packageRequest.setAnySale(true);
        } else {

            packageRequest.setSale(isSale);
            packageRequest.setSaleMiStore(isSale);
            packageRequest.setSaleSqStore(isSale);
        }

        if (!Objects.isNull(request.getCid())) {
            if (Objects.isNull(packageRequest.getBatchedIdList())) {
                packageRequest.setBatchedIdList(Collections.singletonList(request.getCid()));
            } else {
                packageRequest.getBatchedIdList().add(request.getCid());
            }
        }

        return packageRequest;
    }

    /**
     * 商品信息返回信息转换(Gis接口)
     *
     * @param skuInfoDtoList skuInfoDtoList
     * @return GmsGoodsListDto
     */
    public List<SkuInfoVO> convert2SkuInfoVOGis(List<SkuAvailablePageDTO> skuInfoDtoList) {
        if (CollectionUtils.isEmpty(skuInfoDtoList)) {
            return Collections.emptyList();
        }

        List<SkuInfoVO> skuInfoVOList = new ArrayList<>();
        for (SkuAvailablePageDTO skuDto : skuInfoDtoList) {
            SkuInfoVO skuInfoVO = new SkuInfoVO();
            skuInfoVO.setGoodsId(skuDto.getGoodsId());
            skuInfoVO.setProductId(skuDto.getProductId());
            skuInfoVO.setSku(skuDto.getSku());
            skuInfoVO.setGoodsName(skuDto.getGoodsName());


            skuInfoVO.setOnSaleOnline(skuDto.isSale() ? 1 : 0);
            skuInfoVO.setOnSaleOffline(skuDto.isSaleMiStore() ? 1 : 0);
            skuInfoVO.setSqOnSale(skuDto.isSaleSqStore() ? 1 : 0);


            SkuBusinessCategory category = skuDto.getBusinessCategory();
            if (!Objects.isNull(category)) {
                skuInfoVO.setFirstCategoryId(category.getFirstCategoryId());
                skuInfoVO.setFirstCategoryName(category.getFirstCategoryName());
                skuInfoVO.setSecondCategoryId(category.getSecondCategoryId());
                skuInfoVO.setSecondCategoryName(category.getSecondCategoryName());
                skuInfoVO.setThirdCategoryId(category.getThirdCategoryId());
                skuInfoVO.setThirdCategoryName(category.getThirdCategoryName());
            }

            skuInfoVO.setMarketPrice(skuDto.getMarketPrice());
            skuInfoVOList.add(skuInfoVO);
        }

        return skuInfoVOList;
    }

    /**
     * 商品信息返回信息转换
     *
     * @param batchedInfoDtoList batchedInfoDtoList
     * @return GmsGoodsListDto
     */
    public List<PackageInfoVO> convert2PackageDtoGis(List<BatchedDTO> batchedInfoDtoList) {
        if (CollectionUtils.isEmpty(batchedInfoDtoList)) {
            return Collections.emptyList();
        }

        List<PackageInfoVO> packageInfoVOList = new ArrayList<>();
        for (BatchedDTO batchedDto : batchedInfoDtoList) {

            PackageInfoVO packageInfoVO = new PackageInfoVO();
            packageInfoVO.setBatchedId(batchedDto.getBatchedId());
            packageInfoVO.setProductId(batchedDto.getProductId());
            packageInfoVO.setBatchedName(batchedDto.getBatchedName());
            packageInfoVO.setMarketPrice(batchedDto.getMarketPriceMax());
            packageInfoVO.setOnSaleOnline(batchedDto.isSale() ? 1 : 0);
            packageInfoVO.setOnSaleOffline(batchedDto.isSaleMiStore() ? 1 : 0);
            packageInfoVO.setSqOnSale(batchedDto.isSaleSqStore() ? 1 : 0);
            // 老套装
            packageInfoVO.setSsuType(GoodsItemTypeEnum.PACKAGE.getValue());

            packageInfoVOList.add(packageInfoVO);
        }

        return packageInfoVOList;
    }

    /**
     * 商品信息返回信息转换(GIS接口)
     * @param request PackagePageListRequest
     * @return GoodsPageRequest
     */
    public GoodsPageRequest convert2GoodsPageRequest(SuitPageListRequest request) {
        GoodsPageRequest goodsPageRequest = new GoodsPageRequest();

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(request.getPageNo());
        pageInfo.setPageSize(request.getPageSize());
        goodsPageRequest.setPage(pageInfo);

        // 业务平台为车商城
        if (Objects.equals(BizPlatformEnum.CAR_SHOP.getCode(), request.getBizPlatform())) {
            Boolean isSaleCarShop = Objects.isNull(request.getStatus()) ? null : Objects.equals(GoodsStatusEnum.ONSALE_ONSHELF.getId(), request.getStatus());
            goodsPageRequest.setSaleCarShop(isSaleCarShop);
        }

        // 业务平台为新零售
        if (Objects.equals(BizPlatformEnum.RETAIL.getCode(), request.getBizPlatform()) && request.getStatus() != null) {
            if (Objects.equals(GoodsStatusEnum.ONSALE_ONSHELF.getId(), request.getStatus())) {
                // 商城、米家、授权店任意上架
                goodsPageRequest.setAnySale(true);
            } else {
                // 商城、米家、授权店都下架
                goodsPageRequest.setSale(false);
                goodsPageRequest.setSaleMiStore(false);
                goodsPageRequest.setSaleSqStore(false);
            }

        }

        goodsPageRequest.setProductId(request.getProductId());
        goodsPageRequest.setGoodsId(request.getSuitId());
        goodsPageRequest.setName(request.getName());
        goodsPageRequest.setItemType(GoodsItemTypeEnum.SUIT.getValue());
        goodsPageRequest.setNeedPackage(true);

        return goodsPageRequest;
    }

    /**
     * 构造套装信息查询参数(gis接口)
     *
     * @param goodPageList List<GoodsPageDTO>
     * @return List<SuitInfoVO>
     */
    public List<PackageInfoVO> convertGoodsPageDTO2PackageInfoVO(List<GoodsPageDTO> goodPageList) {
        if (CollectionUtils.isEmpty(goodPageList)) {
            return Collections.emptyList();
        }
        List<PackageInfoVO> infoList = new ArrayList<>();
        for (GoodsPageDTO goodsPageDto : goodPageList) {
            PackageInfoVO infoVO = new PackageInfoVO();
            infoVO.setBatchedId(goodsPageDto.getGoodsId());
            infoVO.setBatchedName(goodsPageDto.getName());
            infoVO.setProductId(goodsPageDto.getProductId());
            infoVO.setMarketPrice(goodsPageDto.getMarketPrice());
            infoVO.setOnSaleOnline(goodsPageDto.getSale() ? CommonConstant.ONE_INT : CommonConstant.ZERO_INT);
            infoVO.setOnSaleCarShop(goodsPageDto.getSaleCarShop() ? CommonConstant.ONE_INT : CommonConstant.ZERO_INT);
            infoVO.setOnSaleOffline(goodsPageDto.getSaleMiStore() ? CommonConstant.ONE_INT : CommonConstant.ZERO_INT);
            infoVO.setSqOnSale(goodsPageDto.getSaleSqStore() ? CommonConstant.ONE_INT : CommonConstant.ZERO_INT);
            // ssuType = 新套装
            infoVO.setSsuType(GoodsItemTypeEnum.SUIT.getValue());

            infoList.add(infoVO);
        }
        return infoList;
    }

    /**
     * 套装信息返回信息转换(GIS接口)
     *
     * @param goodsMultiInfo GoodsMultiInfoDTO
     * @return PackageInfoVO
     */
    public PackageInfoVO convertGoodsMultiInfoDTO2PackageInfoVO(GoodsMultiInfoDTO goodsMultiInfo) {
        if (Objects.isNull(goodsMultiInfo)) {
            return null;
        }
        PackageInfoVO infoVO = new PackageInfoVO();
        infoVO.setBatchedId(goodsMultiInfo.getGoodsId());
        infoVO.setBatchedName(goodsMultiInfo.getGoodsName());
        infoVO.setProductId(goodsMultiInfo.getProductId());
        infoVO.setMarketPrice(goodsMultiInfo.getMarketPrice());
        infoVO.setOnSaleOnline(goodsMultiInfo.getSale() ? CommonConstant.ONE_INT : CommonConstant.ZERO_INT);
        infoVO.setOnSaleCarShop(goodsMultiInfo.getSaleCarShop() ? CommonConstant.ONE_INT : CommonConstant.ZERO_INT);
        infoVO.setOnSaleOffline(goodsMultiInfo.getSaleMihome() ? CommonConstant.ONE_INT : CommonConstant.ZERO_INT);
        // 授权上架状态
        infoVO.setSqOnSale(goodsMultiInfo.getSaleSqhome() ? CommonConstant.ONE_INT : CommonConstant.ZERO_INT);
        infoVO.setSsuType(goodsMultiInfo.getItemType());
        return infoVO;
    }
}
