package com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity;

import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponTaskReviewPO;
import lombok.Data;

import java.util.List;

@Data
public class SearchTaskReviewListResult {

    /**
     * 数据
     */
    private List<CouponTaskReviewPO> data;

    /**
     * 总数据量
     */
    private long totalCount;

    /**
     * 总页码
     */
    private int totalPage;
}
