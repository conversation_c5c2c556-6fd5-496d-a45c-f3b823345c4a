package com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CouponConfigReviewPO implements Serializable {

    private static final long serialVersionUID = -7278527488012115290L;

    /**
     * 主键id
     */
    private long id;
    /**
     * 优惠券类型(1:商品券｜2:运费券｜3:超级补贴券)
     */
    private Integer couponType;
    /**
     * 业务平台  0:新零售  3:汽车 5:汽车售后 @BizPlatformEnum
     */
    private Integer bizPlatform;
    /**
     * 优惠券id
     */
    private long configId;
    /**
     * 优惠券名称
     */
    private String couponName;
    /**
     * 优惠类型, 1:满减, 2:满折, 3:N元券, 4:立减
     */
    private int promotionType;
    /**
     * 优惠值(单位分/折)，如8折，传800
     */
    private long promotionValue;
    /**
     * 门槛值满元（单位分)
     */
    private long bottomPrice;
    /**
     * 门槛值满件（单位个)
     */
    private int bottomCount;
    /**
     * 门槛类型, 1:满元, 2:满件, 3:每满元, 4:每满件, 5:满元且满件
     */
    private int bottomType;
    /**
     *发放总量
     */
    private long applyCount;
    /**
     * 优惠券生效开始时间
     */
    private Long startUseTime;
    /**
     * 优惠券生效结束时间
     */
    private Long endUseTime;
    /**
     * 券审核压缩信息
     */
    private byte[] configCompress;
    /**
     * BPM审批流唯一标识
     */
    private String bpmKey;
    /**
     * 审批原因
     */
    private String bpmReason;
    /**
     * 审核状态：1 待审核, 2 审核中, 3 审核通过, 4 已驳回 5 已撤回 6 已过期
     */
    private int  status;
    /**
     * 创建部门
     */
    private long departmentId;
    /**
     * 申请备注
     */
    private String applyMark;
    /**
     * 申请附件地址
     */
    private String applyAttachment;

    /**
     * 创建
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 审批时间
     */
    private Date approvedTime;

    /**
     * 审批人id
     */
    private String  approvedId;


}
