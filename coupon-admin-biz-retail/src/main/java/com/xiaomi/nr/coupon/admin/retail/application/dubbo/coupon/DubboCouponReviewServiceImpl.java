package com.xiaomi.nr.coupon.admin.retail.application.dubbo.coupon;

import com.google.gson.Gson;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.ApplyAttachmentVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponReviewListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GoodsRuleDetailVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.OperateCouponConfigRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponCreateReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponReviewListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponUpdateReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.ReviewGroupDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.ReviewGroupListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.ReviewGroupSaveRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewDetailResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.CouponAdminService;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponReviewService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.CouponConfigCheckService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.CouponCheckFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.check.baseinfo.CouponConfigBaseCheck;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItemFactory;
import com.xiaomi.nr.coupon.admin.domain.coupon.goods.GoodsService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.ReviewStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSketchRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.BrProxy;
import com.xiaomi.nr.coupon.admin.retail.application.dubbo.coupon.convert.CouponAdminConvert;
import com.xiaomi.nr.coupon.admin.retail.application.dubbo.coupon.convert.CouponReviewConvert;
import com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponreview.CoupoReviewRelService;
import com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponreview.CouponConfigReviewService;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.CouponReviewRelRepository;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.CouponReviewRepository;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity.SeachReviewGroupResult;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity.SeachReviewListResult;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity.SearchReviewGroupParam;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity.SearchReviewListParam;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponConfigReviewPO;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponReviewRelPO;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.rpc.bpm.BpmProxy;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
@ApiModule(value = "优惠券审核服务接口", apiInterface = DubboCouponReviewService.class)
public class DubboCouponReviewServiceImpl implements DubboCouponReviewService {

    @Autowired
    private CouponConfigReviewService couponConfigReviewService;

    @Autowired
    private CouponConfigCheckService couponCheckService;

    @Autowired
    private CouponSketchRepository couponSketchRepository;

    @Autowired
    private CouponReviewRepository couponReviewRepository;

    @Autowired
    private CouponReviewRelRepository couponReviewRelRepository;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private BpmProxy bpmProxy;

    @Autowired
    private CoupoReviewRelService coupoReviewRelService;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Autowired
    private CouponAdminService couponAdminService;

    @Autowired
    private CouponCheckFactory couponCheckFactory;

    @Autowired
    private BrProxy brProxy;

    /**
     * 创建券审核提交
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("创建券审核提交")
    public Result<CouponReviewResponse> createCouponReview(CouponCreateReviewRequest request) {
        log.info("DubboCouponReviewServiceImpl.createCouponReview begin, request = {}", GsonUtil.toJson(request));

        try {
            String account = UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix();
            request.getCouponConfigVO().setCreator(account);
            request.setBizType(Optional.ofNullable(request.getBizType()).orElse(BizPlatformEnum.RETAIL.getCode()));
            log.info("DubboCouponConfigReviewService createCouponReview request:{}", GsonUtil.toJson(request));


            CouponConfigItem couponConfigItem = CouponConfigItemFactory.createCouponConfigItem(request.getCouponConfigVO(), request.getBizType());
            //基本信息校验
            CouponBaseInfo baseInfo = couponConfigItem.getCouponBaseInfo();
//            Integer bizPlatform = Optional.ofNullable(baseInfo.getBizPlatform()).orElse(BizPlatformEnum.RETAIL.getCode());
            CouponConfigBaseCheck checkHandler = couponCheckService.getCheckHandler(baseInfo.getPromotionType() + "_" + baseInfo.getBizPlatform());
            if (Objects.isNull(checkHandler)) {
                log.error("DubboCouponConfigReviewService.createCouponReview checkHandler not exist, request = {}", request);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "入参校验失败");
            }

            checkHandler.createCheck(couponConfigItem);

            CouponConfigReviewPO reviewPO = CouponReviewConvert.convertReviewPO(request.getCouponConfigVO(), request.getApplyAttachment(), account, baseInfo.getBizPlatform());
            couponReviewRepository.save(reviewPO);

            //删草稿
            if (request.getSketchId() != null && request.getSketchId() > 0) {
                couponSketchRepository.delete(request.getSketchId());
            }

            CouponReviewResponse response = new CouponReviewResponse();
            response.setReviewId(reviewPO.getId());
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService createCouponReview error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 更新券审核提交
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("更新券审核提交")
    public Result<CouponReviewResponse> updateCouponReview(CouponUpdateReviewRequest request) {
        CouponReviewResponse response = new CouponReviewResponse();
        try {
            String account = UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix();
            request.setBizType(Optional.ofNullable(request.getBizType()).orElse(BizPlatformEnum.RETAIL.getCode()));
            log.info("DubboCouponConfigReviewService updateCouponReview request:{},account:{}", GsonUtil.toJson(request), account);

            CouponConfigItem couponConfigItem = CouponConfigItemFactory.createCouponConfigItem(request.getCouponConfigVO(), request.getBizType());
            //基本信息校验
            CouponBaseInfo baseInfo = couponConfigItem.getCouponBaseInfo();
//            Integer bizPlatform = Optional.ofNullable(baseInfo.getBizPlatform()).orElse(BizPlatformEnum.RETAIL.getCode());
            CouponConfigBaseCheck checkHandler = couponCheckService.getCheckHandler(baseInfo.getPromotionType() + "_" + baseInfo.getBizPlatform());

            if (Objects.isNull(checkHandler)) {
                log.error("DubboCouponConfigReviewService.updateCouponReview checkHandler not exist, request = {}", request);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "入参校验失败");
            }
            checkHandler.updateCheck(couponConfigItem);

            //检查是否可创建审批
            long count = couponReviewRepository.count(request.getCouponConfigVO().getId(), Arrays.asList(ReviewStatusEnum.ToBeReviewed.getValue(), ReviewStatusEnum.UnderReview.getValue()));
            if (count > 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "存在进行中的审批");
            }

            if (couponConfigReviewService.check(request)) {
                CouponConfigReviewPO po = CouponReviewConvert.convertReviewPO(request.getCouponConfigVO(), request.getApplyAttachment(), account, request.getBizType());
                couponReviewRepository.save(po);
                response.setReviewId(po.getId());
            } else {
                // 直接修改
                // 入参转换
                CouponConfigVO couponConfigVO = request.getCouponConfigVO();
                OperateCouponConfigRequest operateCouponConfigReq = CouponAdminConvert.convertOperateCouponConfigReq(couponConfigVO, request.getBizType());
                operateCouponConfigReq.setOperator(account);
                // 更新优惠券
                Result<Void> updateResult = couponAdminService.updateCouponConfig(operateCouponConfigReq);
                if (updateResult.getCode() != GeneralCodes.OK.getCode()) {
                    throw ExceptionHelper.create(ErrCode.COUPON, "更新优惠券失败");
                }
            }
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService updateCouponReview error request:{}", request, e);
            return Result.fromException(e);
        }
    }


    /**
     * 撤销券审核
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("撤销券审核")
    public Result<Void> cancelCouponReview(CouponReviewRequest request) {
        try {
            request.setOperator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());
            log.info("DubboCouponConfigReviewService cancelCouponReview request:{}", GsonUtil.toJson(request));

            CouponConfigReviewPO reviewPO = couponReviewRepository.selectById(request.getReviewId());
            bpmProxy.cancelReview(reviewPO.getBpmKey(), request.getOperator());

            reviewPO.setStatus(ReviewStatusEnum.Canceled.getValue());
            couponReviewRepository.updateReviewStatus(reviewPO);
            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService cancelCouponReview error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 查询券审核列表
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("查询券审核列表")
    public Result<BasePageResponse<CouponReviewListVO>> couponReviewList(CouponReviewListRequest request) {
        BasePageResponse<CouponReviewListVO> response = new BasePageResponse<>();
        try {
            SearchReviewListParam param = new SearchReviewListParam();
            BeanMapper.copy(request, param);
            param.setOrderBy(request.getOrderByMap().containsKey(request.getOrderBy()) ? request.getOrderByMap().get(request.getOrderBy()) : request.getOrderBy());
            SeachReviewListResult result = couponReviewRepository.selectList(param);
            if (CollectionUtils.isNotEmpty(result.getReviewPOList())) {
                response.setList(result.getReviewPOList().stream().map(x -> {
                    CouponReviewListVO couponReviewListVO = CouponReviewConvert.convertToReviewListVO(x);
                    couponReviewListVO.setConfigId(x.getConfigId() == 0 ? null : x.getConfigId());
                    return couponReviewListVO;
                }).collect(Collectors.toList()));
            }

            response.setPageSize(request.getPageSize());
            response.setPageNo(request.getPageNo());
            response.setTotalPage(result.getTotalPage());
            response.setTotalCount(result.getTotalCount());
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService couponReviewList error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 券审核详情
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("券审核详情")
    public Result<CouponReviewDetailResponse> couponReviewDetail(CouponReviewRequest request) {
        try {
            CouponConfigReviewPO reviewPO = couponReviewRepository.selectById(request.getReviewId());
            if (reviewPO == null) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "未找到审批记录");
            }
            CouponReviewDetailResponse response = new CouponReviewDetailResponse();

            CouponConfigVO couponConfigVO = GsonUtil.fromJson(CompressUtil.decompress(reviewPO.getConfigCompress()), CouponConfigVO.class);

            // goods detail
            GoodsRuleDetailVO goodsRuleDetailVO = goodsService.searchGoodsDetailInfo(couponConfigVO.getGoodsRuleVO(), couponConfigVO.getPromotionRuleVO().getPromotionType(), reviewPO.getBizPlatform());

            // 预算信息
            String budgetApplyNo = Optional.ofNullable(couponConfigVO.getBudgetInfoDto()).map(BudgetInfoDto::getBudgetApplyNo).orElse(null);
            Long lineNum = Optional.ofNullable(couponConfigVO.getBudgetInfoDto()).map(BudgetInfoDto::getLineNum).orElse(null);
            BudgetInfoDto budgetInfoDto = brProxy.queryBudgetDetail(budgetApplyNo, lineNum);
            couponConfigVO.setBudgetInfoDto(budgetInfoDto);

            couponConfigVO.setSendMode(couponSceneRepository.selectRelationSceneByCode(couponConfigVO.getSendScene()).getSendMode());
            couponConfigVO.getGoodsRuleVO().setGoodsDiscountLevelVO(null);
            couponConfigVO.getGoodsRuleVO().setGoodsSuitableVOs(null);
            couponConfigVO.getGoodsRuleVO().setGoodsInclude(null);

            response.setReviewId(reviewPO.getId());
            response.setStatus(reviewPO.getStatus());
            response.setApplyAttachment(GsonUtil.fromListJson(reviewPO.getApplyAttachment(), ApplyAttachmentVO.class));
            response.setCouponConfigVO(couponConfigVO);
            response.setGoodsRuleDetailVO(goodsRuleDetailVO);
            response.setBpmReason(reviewPO.getBpmReason());

            log.info("DubboCouponConfigReviewService couponReviewDetail finished, req = {}, resp = {}", GsonUtil.toJson(request), GsonUtil.toJson(response));

            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService couponReviewDetail error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 审核组列表
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("审核组列表")
    public Result<BasePageResponse<ReviewGroupDTO>> reviewGroupList(ReviewGroupListRequest request) {
        BasePageResponse<ReviewGroupDTO> response = new BasePageResponse<>();
        try {
            SearchReviewGroupParam param = SearchReviewGroupParam.buildSearchReviewGroupParam(request);
            SeachReviewGroupResult result = couponReviewRelRepository.selectList(param);
            if (CollectionUtils.isNotEmpty(result.getCouponReviewRelPOList())) {
                response.setList(result.getCouponReviewRelPOList().stream().map(x -> CouponReviewConvert.convertToReviewRelDTO(x)).collect(Collectors.toList()));
            }
            response.setPageSize(request.getPageSize());
            response.setPageNo(request.getPageNo());
            response.setTotalPage(result.getTotalPage());
            response.setTotalCount(result.getTotalCount());
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService reviewGroupList error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 审核组关系保存
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("审核组关系保存")
    public Result<Void> reviewGroupSave(ReviewGroupSaveRequest request) {
        try {
            coupoReviewRelService.check(request);
            couponReviewRelRepository.saveOrUpdate(CouponReviewRelPO.buildCouponReviewRelPO(request));
            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboCouponConfigReviewService reviewGroupSave error request:{}", request, e);
            return Result.fromException(e);
        }
    }
}
