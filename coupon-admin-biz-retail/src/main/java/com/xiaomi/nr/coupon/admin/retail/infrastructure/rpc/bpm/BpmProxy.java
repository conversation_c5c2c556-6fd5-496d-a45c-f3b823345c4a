package com.xiaomi.nr.coupon.admin.retail.infrastructure.rpc.bpm;

import com.xiaomi.newretail.bpm.api.model.dto.ProcessCreateDTO;
import com.xiaomi.newretail.bpm.api.provider.BpmService;
import com.xiaomi.nr.coupon.admin.util.ResultValidator;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

/**
 * 有品bpm请求抽象类
 *
 * <AUTHOR>
 * @date 2020.03.11
 */
@Slf4j
@Component
public  class BpmProxy {


    @Reference(check = false, interfaceClass = BpmService.class, timeout = 10000, retries = 0, group = "${bpm.dubbo.group}")
    private BpmService bpmService;

    /**
     * 提交审核
     * https://xiaomi.f.mioffice.cn/docs/dock422r0mfECGfZq7kGk9g0uwz#
     * @return
     */
    public String submitReview(ProcessCreateDTO request) throws Exception {
        log.info("BpmProxy submitReview begin request:{}", request);
        try {
            Result<String> result = bpmService.processCreate(request);
            ResultValidator.validate(result, "创建审批失败！");
            log.info("BpmProxy submitReview end request:{},result:{}", request,result);
            return result.getData();
        } catch (BizError bizError) {
            log.error("BpmProxy submitReview bizError request:{}", request,bizError);
            throw bizError;
        }catch (Exception e){
            log.error("BpmProxy submitReview Exception request:{}", request,e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "创建审批失败！");
        }
    }

    /**
     * 提交审核
     * https://xiaomi.f.mioffice.cn/docs/dock422r0mfECGfZq7kGk9g0uwz#
     * @return
     */
    public void cancelReview(String processInstanceId,String username) throws Exception {
        log.info("BpmProxy cancelReview begin processInstanceId:{}，username:{}", processInstanceId,username);
        try {
            Result<Boolean> result = bpmService.processCancel(processInstanceId,username);
            ResultValidator.validate(result, result.getMessage());
            log.info("BpmProxy submitReview end processInstanceId:{}，username:{}", processInstanceId,username);
        } catch (BizError bizError) {
            log.error("BpmProxy submitReview bizError processInstanceId:{}，username:{}", processInstanceId,username,bizError);
            throw bizError;
        }catch (Exception e){
            log.error("BpmProxy submitReview Exception processInstanceId:{}，username:{}", processInstanceId,username,e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "取消审批失败！");
        }


    }





}
