package com.xiaomi.nr.coupon.admin.retail.domain.coupon.bpm;

import com.xiaomi.newretail.bpm.api.model.dto.ProcessCreateDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.domain.coupon.goods.GoodsService;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.enums.BpmPageEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsDepartmentEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.CouponReviewRelRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponConfigReviewPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.goods.GmsProxyService;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.rpc.bpm.coupon.CouponBpmExtRequestDTO;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.rpc.bpm.coupon.CouponDepDTO;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.rpc.bpm.coupon.CouponProductDTO;
import com.xiaomi.nr.coupon.admin.util.*;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 超级补贴券
 *
 * <AUTHOR>
 * @date 2023-01-03
 */
@Slf4j
@Component
public class SubsidyCouponPageGenerator extends BpmPageGenerator<CouponConfigReviewPO>{

    @Value("${bpm.coupon.subsidyCouponKey}")
    private String subsidyCouponKey;

    @Value("${subsidyCoupon.review.url}")
    private String reviewUrl;

    @Autowired
    private CouponReviewRelRepository couponReviewRelRepository;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private GmsProxyService gmsProxyService;

    @Override
    public ProcessCreateDTO createRequest(CouponConfigReviewPO reviewPO) throws Exception {
        CouponConfigVO couponConfigVO = GsonUtil.fromJson(CompressUtil.decompress(reviewPO.getConfigCompress()), CouponConfigVO.class);

        ProcessCreateDTO processCreateDTO = new ProcessCreateDTO();
        processCreateDTO.setKey(subsidyCouponKey);

        processCreateDTO.setName(couponConfigVO.getName());
        processCreateDTO.setRequestId("coupon_" + UUID.randomUUID().toString().replaceAll("-", "").toUpperCase());
        processCreateDTO.setCreator(reviewPO.getCreator());

        CouponBpmExtRequestDTO couponBpmExtRequestDTO = getExtInfo(couponConfigVO, reviewPO.getCreator());
        processCreateDTO.setExtra(MapUtils.objectToMap(couponBpmExtRequestDTO));

        Map<String, Object> htmlMap = this.convertMap(reviewPO);
        if (couponConfigVO.getId() > CommonConstant.ZERO_LONG) {
            this.addOriMap(htmlMap, reviewPO);
        }
        processCreateDTO.setHtml(this.renderWebContent(htmlMap));

        return processCreateDTO;
    }

    @Override
    public Map<String, Object> convertMap(CouponConfigReviewPO reviewPO) throws Exception {
        CouponConfigVO couponConfigVO = GsonUtil.fromJson(CompressUtil.decompress(reviewPO.getConfigCompress()), CouponConfigVO.class);

        Map<String, Object> dataMap = new HashMap<>();

        dataMap.put("name", couponConfigVO.getName());
        dataMap.put("startFetchTime", TimeUtil.formatDate(couponConfigVO.getStartFetchTime()));
        dataMap.put("endFetchTime", TimeUtil.formatDate(couponConfigVO.getEndFetchTime()));

        Map<Integer, UseChannelVO> useChannelVOMap = couponConfigVO.getUseChannel();
        List<String> useChannelTexts = useChannelVOMap.keySet().stream().map(x -> UseChannelsEnum.getByValue(x).getName()).collect(Collectors.toList());
        dataMap.put("useChannelTexts", useChannelTexts);
        dataMap.put("useChannelTextStr", StringUtils.join(useChannelTexts, ","));

        UseTermVO useTermVO = couponConfigVO.getUseTermVO();
        dataMap.put("useTimeText", UseTimeTypeEnum.getDesc(UseTimeTypeEnum.getByValue(useTermVO.getUseTimeType()),
                UseTimeGranularityEnum.getByValue(useTermVO.getTimeGranularity()),useTermVO.getStartUseTime(),useTermVO.getEndUseTime(),useTermVO.getUseDuration()));
        dataMap.put("useTimeType", useTermVO.getUseTimeType());
        dataMap.put("useDuration", useTermVO.getUseDuration());
        dataMap.put("startUseTime", TimeUtil.formatDate(useTermVO.getStartUseTime()));
        dataMap.put("endUseTime", TimeUtil.formatDate(useTermVO.getEndUseTime()));

        PromotionRuleVO promotionRuleVO = couponConfigVO.getPromotionRuleVO();
        PromotionTypeEnum promotionTypeEnum = PromotionTypeEnum.getByValue(promotionRuleVO.getPromotionType());
        dataMap.put("promotionText", promotionTypeEnum.getName());
        dataMap.put("bottomText", PromotionTypeEnum.getDesc(promotionTypeEnum, promotionRuleVO.getPromotionValue(),
                BottomTypeEnum.getByValue(promotionRuleVO.getBottomType()), promotionRuleVO.getBottomCount(), promotionRuleVO.getBottomPrice(), promotionRuleVO.getMaxReduce()));
        dataMap.put("promotionValue",PromotionTypeEnum.getPromotionValue(PromotionTypeEnum.getByValue(promotionRuleVO.getPromotionType()),promotionRuleVO.getPromotionValue()));

        dataMap.put("applyCount", couponConfigVO.getDistributionRuleVO().getApplyCount());
        CouponScenePO couponScenePO = couponSceneRepository.selectBySceneCode(couponConfigVO.getSendScene());
        dataMap.put("sendSceneText", couponScenePO.getName());
        dataMap.put("sendPurposeText", SendPurposeEnum.getByValue(couponConfigVO.getSendPurpose()).getName());
        dataMap.put("costText", couponConfigVO.getCost());

        GoodsRuleVO goodsRuleVO = couponConfigVO.getGoodsRuleVO();
        GoodsDiscountLevelVO goodsDiscountLevelVO = goodsRuleVO.getGoodsDiscountLevelVO();
        if (goodsDiscountLevelVO != null) {
            dataMap.put("goodsNotifyInfo", goodsDiscountLevelVO);
        }
        dataMap.put("goodsSuitables", goodsRuleVO.getGoodsSuitableVOs());
        List<Long> packages = Optional.ofNullable(goodsRuleVO.getGoodsInclude().get(GoodsLevelEnum.Package.getValue())).orElse(Lists.newArrayList());
        List<Long> suits = Optional.ofNullable(goodsRuleVO.getGoodsInclude().get(GoodsLevelEnum.Suit.getValue())).orElse(Lists.newArrayList());
        dataMap.put("packageAmount", packages.size() + suits.size());

        Map<Integer, Integer> costShare = couponConfigVO.getCostShare();
        List<CouponCostShareVO> costShareTexts = new ArrayList<>();
        costShare.forEach((k, v) -> {
            CouponCostShareVO couponCostShareVO = new CouponCostShareVO();
            if (CostShareEnum.COUPON_GOODS_BELONG.getValue() == k) {
                StringBuilder departmentText = new StringBuilder();
                for (int i = 0; i < goodsRuleVO.getGoodsDepartments().size(); i++) {
                    departmentText.append(GoodsDepartmentEnum.getByValue(goodsRuleVO.getGoodsDepartments().get(i)).getName());
                    if (i != goodsRuleVO.getGoodsDepartments().size() - 1) {
                        departmentText.append(",");
                    }
                }
                couponCostShareVO.setDepartmentText(departmentText.toString());
            } else {
                couponCostShareVO.setDepartmentText(CostShareEnum.getByValue(k).getName());
            }
            couponCostShareVO.setShare(v);
            costShareTexts.add(couponCostShareVO);
        });
        dataMap.put("costShareTexts", costShareTexts);

        List<String> extPropTexts = Lists.newArrayList();
        ExtPropVO extProp = couponConfigVO.getExtProp();
        for (Field field : extProp.getClass().getDeclaredFields()){
            field.setAccessible(true);
            int value = (int)field.get(extProp);
            if(value == YesNoEnum.Yes.getMysqlValue()){
                extPropTexts.add(ExtPropEnum.getByField(field.getName()).getName());
            }
        }
        dataMap.put("extPropTexts", extPropTexts);
        dataMap.put("extPropStrTexts", StringUtils.join(extPropTexts,","));


        if (StringUtils.isNotBlank(reviewPO.getApplyAttachment())) {
            List<ApplyAttachmentVO> attachmentVOs = GsonUtil.fromListJson(reviewPO.getApplyAttachment(), ApplyAttachmentVO.class);
            for (ApplyAttachmentVO attachmentVO : attachmentVOs) {
                attachmentVO.setPicture(FileUtils.isPicture(attachmentVO.getUrl()));
                attachmentVO.setType(attachmentVO.getPicture() ? "img" : "file");
            }
            dataMap.put("applyAttachments", attachmentVOs);
        }
        dataMap.put("systemUrl", reviewUrl + reviewPO.getId());
        String fileName = "审核" + reviewPO.getId() + "商品";
        dataMap.put("downloadGoodsUrl", goodsService.getBpmGoodsFdsUrl(fileName));
        dataMap.put("couponTypeText", CouponTypeEnum.SUBSIDY.getName());


        Map<String, List<Long>> goodsInclude = couponConfigVO.getGoodsRuleVO().getGoodsInclude();
        List<Long> skuList = Optional.ofNullable(goodsInclude.get(GoodsLevelEnum.Sku.getValue())).orElse(Lists.newArrayList());
        List<Long> packageList = Optional.ofNullable(goodsInclude.get(GoodsLevelEnum.Package.getValue())).orElse(Lists.newArrayList());
        List<Long> suitList = Optional.ofNullable(goodsInclude.get(GoodsLevelEnum.Suit.getValue())).orElse(Lists.newArrayList());
        dataMap.put("totalSku", skuList.size());
        dataMap.put("totalPackage", packageList.size() + suitList.size());
        dataMap.put("totalProduct", packageList.size() + skuList.size() + suitList.size());
        writeExcel(goodsRuleVO.getGoodsInclude(), fileName);
        return dataMap;
    }

    @Override
    public String convertMapJson(CouponConfigReviewPO couponConfigReviewPO) {
        return null;
    }

    @Override
    public BpmPageEnum getType() {
        return BpmPageEnum.Subsidy_Coupon;
    }


    private CouponBpmExtRequestDTO getExtInfo(CouponConfigVO couponConfigVO, String creator) throws Exception {
        CouponBpmExtRequestDTO couponBpmExtRequestDTO = new CouponBpmExtRequestDTO();
        /*CouponReviewRelPO relPO = couponReviewRelRepository.selectByCreator(creator);
        if (relPO == null || StringUtils.isBlank(relPO.getReviewGroup())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "未找到审批人一审组信息");
        }
        couponBpmExtRequestDTO.setGroup(relPO.getReviewGroup());*/
        couponBpmExtRequestDTO.setAmount_flag(BigDecimal.valueOf(10000000).compareTo(couponConfigVO.getCost()) < 0);

        getCouponProductDTO(couponConfigVO, couponBpmExtRequestDTO);

        CouponDepDTO couponDepDTO = getCouponDepDTO(couponConfigVO);
        if (couponDepDTO != null) {
            couponBpmExtRequestDTO.setDepartGroup(MapUtils.objectToMap(couponDepDTO));
        }

        return couponBpmExtRequestDTO;
    }

    private CouponDepDTO getCouponDepDTO(CouponConfigVO couponConfigVO) throws BizError {
        List<CostShareEnum> shareDepartments = couponConfigVO.getCostShare().keySet().stream().filter(x -> StringUtils.isNotBlank(CostShareEnum.getByValue(x).getBpmValue()))
                .map(x -> CostShareEnum.getByValue(x)).collect(Collectors.toList());
        Map<Integer, UseChannelVO> useChannel = couponConfigVO.getUseChannel();
        if (CollectionUtils.isNotEmpty(shareDepartments)) {
            CouponDepDTO couponDepDTO = new CouponDepDTO();
            for (CostShareEnum shareDepartment : shareDepartments) {
                switch (shareDepartment) {
                    case MARKETING_DEPARTMENT:
                        couponDepDTO.setMarket(true);
                        break;
                    case SERVICE_DEPARTMENT:
                        couponDepDTO.setService(true);
                        break;
                    case TIANXINGSHUKE:
                        if (useChannel.containsKey(UseChannelsEnum.XIAOMI_SHOP.getValue())) {
                            couponDepDTO.setTianxingshuke_shangcheng(true);
                        } else {
                            throw ExceptionHelper.create(GeneralCodes.ParamError, "天星数科分摊必须选小米商城渠道");
                        }
                        break;
                    case OFFLINE_SUPERMARKETS:
                        if (useChannel.containsKey(UseChannelsEnum.DIRECTSALE_STORE.getValue())) {
                            couponDepDTO.setXianxia_zhiying(true);
                        }
                        if (useChannel.containsKey(UseChannelsEnum.AUTHORIZED_STORE.getValue())) {
                            couponDepDTO.setXianxia_shouquan(true);
                        }
                        if (useChannel.containsKey(UseChannelsEnum.EXCLUSIVE_SHOP.getValue())) {
                            couponDepDTO.setXianxia_zhuanmai(true);
                        }
                        if (useChannel.containsKey(UseChannelsEnum.FORTRESS_STORE.getValue())) {
                            couponDepDTO.setXianxia_baolei(true);
                        }
                        if (useChannel.containsKey(UseChannelsEnum.XIAOMI_SHOP.getValue()) && useChannel.size() == 1) {
                            throw ExceptionHelper.create(GeneralCodes.ParamError, "线下商超分摊渠道不能只选小米商城渠道");
                        }
                        break;
                    case INSURANCE_COMPANY:
                        if (useChannel.containsKey(UseChannelsEnum.XIAOMI_SHOP.getValue())) {
                            couponDepDTO.setBaoxian_shangcheng(true);
                        } else {
                            throw ExceptionHelper.create(GeneralCodes.ParamError, "保险公司分摊必须选小米商城渠道");
                        }
                        break;
                    case SHANHUISHOU:
                        if (useChannel.containsKey(UseChannelsEnum.XIAOMI_SHOP.getValue())) {
                            couponDepDTO.setShanhuishou_shangcheng(true);
                        } else {
                            throw ExceptionHelper.create(GeneralCodes.ParamError, "闪回收分摊必须选小米商城渠道");
                        }
                        break;
                    case ZHUANZHUAN:
                        if (useChannel.containsKey(UseChannelsEnum.XIAOMI_SHOP.getValue())) {
                            couponDepDTO.setZhuanzhuan_shangcheng(true);
                        } else {
                            throw ExceptionHelper.create(GeneralCodes.ParamError, "转转分摊必须选小米商城渠道");
                        }
                        break;
                    case AIHUISHOU:
                        if (useChannel.containsKey(UseChannelsEnum.XIAOMI_SHOP.getValue())) {
                            couponDepDTO.setAihuishou_shangcheng(true);
                        } else {
                            throw ExceptionHelper.create(GeneralCodes.ParamError, "爱回收分摊必须选小米商城渠道");
                        }
                        break;
                    default:
                        break;
                }

            }

            return couponDepDTO;
        }
        return null;
    }

    private CouponProductDTO getCouponProductDTO(CouponConfigVO couponConfigVO, CouponBpmExtRequestDTO requestDTO) {
        CouponProductDTO couponProductDTO = new CouponProductDTO();
        List<Integer> goodsDepartments = couponConfigVO.getGoodsRuleVO().getGoodsDepartments();
        goodsDepartments.stream().filter(x -> GoodsDepartmentEnum.getByValue(x) != null).forEach(x -> {
            switch (GoodsDepartmentEnum.getByValue(x)) {
                case SALE_ONE:
                    requestDTO.setLevel1(CommonConstant.ONE_INT);
                    break;
                case SALE_TWO:
                    requestDTO.setLevel2(CommonConstant.ONE_INT);
                    break;
                case SALE_THREE:
                    requestDTO.setLevel3(CommonConstant.ONE_INT);
                    break;
            }
        });
        return couponProductDTO;
    }


    private void addOriMap(Map<String, Object> map, CouponConfigReviewPO reviewPO) throws Exception {
        Map<String, Object> mapOrg = this.convertMap(reviewPO);
        mapOrg.forEach((k, v) -> {
            if (!Objects.equals(map.get(k), v)) {
                map.put(k + "_v2", v);
            }
        });
    }

    @Async("asyncExecutor")
    public void writeExcel(Map<String, List<Long>> goodsInclude, String fileName) {
        for (int i = 1; i <= 3; i++) {
            try {
                goodsService.uploadGoodsExcelFile(goodsInclude, fileName);
                return;
            } catch (Exception e) {
                log.error("writeExcel i:{}", i, e);
            }
        }
    }
}
