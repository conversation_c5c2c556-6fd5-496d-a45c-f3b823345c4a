package com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CouponTaskReviewPO implements Serializable {
    private static final long serialVersionUID = 2783840732266640720L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 券配置id
     */
    private Long configId;

    /**
     * 券配置名称
     */
    private String configName;

    /**
     * 计划发放数量
     */
    private Long planCount;

    /**
     * 灌券券审核压缩信息
     */
    private byte[] compressInfo;

    /**
     * bpm审核流key
     */
    private String bpmKey;

    /**
     * bpm审核流返回原因
     */
    private String bpmReason;

    /**
     * 审核状态
     * 1 待审核, 2 审核中, 3 审核通过, 4 已驳回 5 已撤回 6 已过期
     */
    private int status;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 审核时间
     */
    private Date approvedTime;

    /**
     * 审核人id
     */
    private String approvedId;

}
