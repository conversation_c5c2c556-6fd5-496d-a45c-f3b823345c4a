package com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponreview.entity;

import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigInfoCachePo;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponTaskReviewPO;
import lombok.Data;

import java.util.Map;

@Data
public class CouponTaskReviewContext {

    /**
     * 审核实体
     */
    private CouponTaskReviewPO reviewPO;

    /**
     * 券配置信息
     */
    private ConfigInfoCachePo configInfoCachePo;

    /**
     * 灌券人群总数
     */
    private int userGroupSize;

    /**
     * 投放场景名称
     */
    private String sendSceneText;

    /**
     * 扩展信息
     */
    private Map<Object, Object> extra;

    public CouponTaskReviewContext(){}

    public CouponTaskReviewContext(CouponTaskReviewPO reviewPO, ConfigInfoCachePo configPo){
        this.reviewPO = reviewPO;
        this.configInfoCachePo = configPo;
    }

}
