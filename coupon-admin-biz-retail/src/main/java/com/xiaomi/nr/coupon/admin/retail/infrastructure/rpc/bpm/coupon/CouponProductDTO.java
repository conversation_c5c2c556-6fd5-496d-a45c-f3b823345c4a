package com.xiaomi.nr.coupon.admin.retail.infrastructure.rpc.bpm.coupon;

import lombok.Data;

/**
 * bpm商品部门
 */
@Data
public class CouponProductDTO {

    /**
     * 销售运营一部，不属于则不传
     */
    private Boolean coupon_sale1;
    /**
     * 销售运营二部，不属于则不传
     */
    private Boolean coupon_sale2;
    /**
     * 销售运营三部 ，不属于则不传
     */
    private Boolean coupon_sale3;
    /**
     * 保险产品线，不属于则不传
     */
    private Boolean coupon_insurance;
    /**
     * 增值
     */
    private Boolean  coupon_value;
    /**
     * 赠品产品线，不属于则不传
     */
    private Boolean coupon_gift;

    /**
     * 日常元素产品线，不属于则不传
     */
    private Boolean coupon_daily;

    /**
     * 汽车APP部，不属于则不传
     */
    private Boolean coupon_carshop;

}
