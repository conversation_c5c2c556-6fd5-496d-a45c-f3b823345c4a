package com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SearchReviewListParam implements Serializable {
    private static final long serialVersionUID = 8741304516698345964L;
    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    private Integer couponType;
    /**
     * 审核id
     */
    private long reviewId;

    /**
     *券配置id
     */
    private long configId;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 审核状态 1 待审核, 2 审核中, 3 审核通过, 4 已驳回 5 已撤回 6 已过期
     */
    private List<Integer> status;

    /**
     * 提交人
     */
    private String creator;

    /**
     *提交时间起
     */
    private Date startCreateTime;

    /**
     *提交时间止
     */
    private Date endCreateTime;

    /**
     * 当前页码
     */
    private int pageNo = 1;

    /**
     * 页面条数
     */
    private int pageSize = 10;

    /**
     * 排序字段
     */
    private String orderBy = "id";

    /**
     * 排序顺序  desc倒序   asc顺序
     */
    private String orderDirection = "desc";


}
