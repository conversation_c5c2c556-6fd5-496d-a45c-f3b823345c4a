package com.xiaomi.nr.coupon.admin.retail.schedule;

import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import com.xiaomi.nr.coupon.admin.core.constant.ScheduleConstant;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponmission.MakeMissionCache;
import com.xiaomi.nr.infra.aop.clustertask.ClusterTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 券发放任务缓存定时任务
 */
@Slf4j
@Component
public class CouponMissionSchedule {

    @Resource
    private MakeMissionCache makeMissionCache;

    /**
     * 券发放任务缓存定时任务
     */
    @Scheduled(fixedDelay = 1000 * 60, initialDelay = 1000 * 60)
    @ClusterTask(value = "", zkLockPath = ScheduleConstant.COUPON_MISSION_CACHE_GEN_LOCK)
    public void genConfigRedisCacheScheduleTask() {
        long runStartTime = TimeUtil.getNowUnixMillis();

        try {
            log.info("coupon.admin.schedule.genConfigRedisCacheScheduleTask(), 生成优惠券任务缓存信息开始!");
            makeMissionCache.runMissionCache();
            log.info("coupon.admin.schedule.genConfigRedisCacheScheduleTask(), 生成优惠券任务缓存信息结束!, runTime={}ms", TimeUtil.sinceMillis(runStartTime));
        } catch (Exception e) {
            log.error("CouponMissionSchedule.genConfigRedisCacheScheduleTask, 生成优惠券发放任务缓存任务失败, msg={}, runTime={}ms", TimeUtil.sinceMillis(runStartTime), e);
        }
    }
}
