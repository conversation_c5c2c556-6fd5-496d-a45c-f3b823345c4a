package com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponreview;

import com.xiaomi.newretail.bpm.api.model.callback.OnStatusChangedRequest;
import com.xiaomi.newretail.bpm.api.model.callback.OnStatusChangedResponse;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.CouponFillReviewDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillReviewCancelRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillReviewListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CreateCouponFillReviewRequest;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponTaskReviewPO;

/**
 * 灌券审核服务
 */
public interface CouponTaskReviewService {


    /**
     * 创建灌券任务审核
     * @param request request
     * @return 审核记录id
     * @throws Exception 业务异常
     */
    Long createTaskReview(CreateCouponFillReviewRequest request) throws Exception;


    /**
     * 取消灌券任务审核
     * @param request request
     * @return bool
     */
    Boolean cancelTaskReview(CouponFillReviewCancelRequest request) throws Exception;


    /**
     * 灌券任务审核回调接口
     * @param request request
     * @return response
     */
    void onTaskStatusChanged(OnStatusChangedRequest request, OnStatusChangedResponse onStatusChangedResponse, CouponTaskReviewPO reviewPO);


    /**
     * 灌券任务审核列表
     * @param request request
     * @return response
     */
    BasePageResponse<CouponFillReviewDTO> taskReviewList(CouponFillReviewListRequest request);

    /**
     * 获取审核详情
     * @param reviewId 审核id
     * @return 审核信息po
     */
    CouponTaskReviewPO getTaskReviewPO(long reviewId);

}
