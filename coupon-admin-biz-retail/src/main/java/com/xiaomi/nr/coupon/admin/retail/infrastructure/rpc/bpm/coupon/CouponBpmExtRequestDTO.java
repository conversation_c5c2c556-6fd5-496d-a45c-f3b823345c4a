package com.xiaomi.nr.coupon.admin.retail.infrastructure.rpc.bpm.coupon;

import lombok.Data;

import java.util.Map;

/**
 * https://xiaomi.f.mioffice.cn/docs/dock4cQWt7QxM8wshOFv8tC7Mng#
 */
@Data
public class CouponBpmExtRequestDTO {

    /**
     * 审核组：group1、group2、group3、group4、group5······group 21
     */
    private String group;
    /**
     * 预估申请金额是否大于等于1000万：true-是、false-否
     */
    private boolean amount_flag;
    /**
     * 商品部门
     */
    private Map<String,Object> productGroup;

    //固定传参
    private boolean apportion = true;

    /**
     * 分摊渠道
     */
    private Map<String,Object> departGroup;

    /**
     * 销售运营一部
     */
    private Integer level1;

    /**
     * 销售运营二部
     */
    private Integer level2;

    /**
     * 销售运营三部
     */
    private Integer level3;


}
