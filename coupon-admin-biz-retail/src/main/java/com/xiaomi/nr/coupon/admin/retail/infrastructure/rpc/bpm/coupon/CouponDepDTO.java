package com.xiaomi.nr.coupon.admin.retail.infrastructure.rpc.bpm.coupon;

import lombok.Data;

/**
 * bpm分摊部门
 */
@Data
public class CouponDepDTO {
    /**
     * 线下商超直营
     */
    private Boolean xianxia_zhiying;
    /**
     * 线下商超专卖
     */
    private Boolean xianxia_zhuanmai;
    /**
     * 线下商超堡垒
     */
    private Boolean xianxia_baolei;
    /**
     * 线下商超授权
     */
    private Boolean xianxia_shouquan;
    /**
     * 爱回收小米商城
     */
    private Boolean aihuishou_shangcheng;

    /**
     * 转转小米商城
     */
    private Boolean zhuanzhuan_shangcheng;

    /**
     * 闪回收小米商城
     */
    private Boolean shanhuishou_shangcheng;

    /**
     * 保险小米商城
     */
    private Boolean baoxian_shangcheng;

    /**
     * 天星数科小米商城
     */
    private Boolean tianxingshuke_shangcheng;
    /**
     * 大市场部
     */
    private Boolean market;
    /**
     * 服务中心
     */
    private Boolean service;



}
