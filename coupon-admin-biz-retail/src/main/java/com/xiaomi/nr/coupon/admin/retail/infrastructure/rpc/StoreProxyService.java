package com.xiaomi.nr.coupon.admin.retail.infrastructure.rpc;

import com.google.common.collect.Lists;
import com.xiaomi.cnzone.maindataapi.api.StoreProvider;
import com.xiaomi.cnzone.maindataapi.model.OrgDto;
import com.xiaomi.cnzone.maindataapi.model.OrgResp;
import com.xiaomi.cnzone.maindataapi.model.enums.DomainEnum;
import com.xiaomi.cnzone.maindataapi.model.req.store.StoreListRequest;
import com.xiaomi.nr.coupon.admin.util.ResultValidator;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;

/**
 * 门店相关
 * https://xiaomi.f.mioffice.cn/docs/dock4Mt8IAZMyXL8Tatp3ujdnVb
 */
@Slf4j
@Component
public class StoreProxyService {

    @Reference(check = false, interfaceClass = StoreProvider.class, timeout = 1000, version ="1.0" ,group = "${store.group}")
    private StoreProvider storeProvider;

    @Autowired
    private ThreadPoolTaskExecutor queryAsyncExecutor;

    @SuppressWarnings("unchecked")
    public List<OrgDto> queryStoreList(List<String> storeIds) throws Exception {
        List<Future<List<OrgDto>>> futureList = new ArrayList<>();
        List<List<String>> storeIdsList = Lists.partition(storeIds, 500);
        for (List<String> storeIdsPart : storeIdsList) {
            Future<List<OrgDto>> future = queryAsyncExecutor.submit(() -> {
                try {
                    StoreListRequest request = new StoreListRequest();
                    String[] filter = new String[2];
                    filter[0] = DomainEnum.BASE.getName();
                    filter[1] = DomainEnum.CATEGORY.getName();
                    request.setFilter(filter);
                    request.setOrgId(StringUtils.join(storeIdsPart, ","));
                    Result<OrgResp> result = storeProvider.selectStoreList(request);
                    log.info("StoreProxyService queryStoreList storeIds:{},result:{}", storeIds, result);
                    ResultValidator.validate(result, "查询门店失败");
                    if (result.getData() != null) {
                        return result.getData().getOrgList();
                    }
                    return Collections.EMPTY_LIST;
                } catch (Exception e) {
                    log.error("StoreProxyService queryStoreList Exception storeIds:{}", storeIds, e);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "查询门店失败");
                }
            });
            futureList.add(future);
        }

        List<OrgDto> returnList = new ArrayList<>();
        for (Future<List<OrgDto>> future : futureList) {
            if (future != null) {
                List<OrgDto> storeInfoList = future.get();
                if(CollectionUtils.isNotEmpty(storeInfoList)){
                    returnList.addAll(storeInfoList);
                }
            }
        }

        return returnList;
    }


}
