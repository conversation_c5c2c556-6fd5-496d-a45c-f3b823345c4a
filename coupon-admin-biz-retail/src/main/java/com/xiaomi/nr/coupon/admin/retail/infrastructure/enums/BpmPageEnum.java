package com.xiaomi.nr.coupon.admin.retail.infrastructure.enums;

import lombok.Getter;

/**
 * bpm审批页面类型
 */
@Getter
public enum BpmPageEnum {
    /**
     * 券审批模板 商品券
     */
    Coupon("coupon","coupon.html"),

    /**
     * 券审批模板 运费券
     */
    Postfree_Coupon("postfree_coupon","postfree_coupon.html"),

    /**
     * 券审批模板 灌券任务
     */
    TASK_COUPON("coupon","task_coupon.html"),

    /**
     * 券审批模板 超级补贴券
     */
    Subsidy_Coupon("subsidy_coupon","subsidy_coupon.html"),

    ;

    private String value;
    private String name;

    BpmPageEnum(String value,String name) {
        this.value = value;
        this.name = name;
    }

    public static BpmPageEnum getByValue(String value) {
        for (BpmPageEnum templateEnum : BpmPageEnum.values()) {
            if (value == templateEnum.value) {
                return templateEnum;
            }
        }
        return null;
    }

}
