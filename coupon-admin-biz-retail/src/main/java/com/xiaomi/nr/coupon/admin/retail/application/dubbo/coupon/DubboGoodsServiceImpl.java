package com.xiaomi.nr.coupon.admin.retail.application.dubbo.coupon;

import com.xiaomi.goods.gis.dto.batched.offline.BatchedAvailablePageRequest;
import com.xiaomi.goods.gis.dto.batched.offline.BatchedAvailablePageResponse;
import com.xiaomi.goods.gis.dto.batched.offline.BatchedDTO;
import com.xiaomi.goods.gis.dto.category.CategoryResponse;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoDTO;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsPageDTO;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsPageRequest;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsPageResponse;
import com.xiaomi.goods.gis.dto.sku.offline.SkuAvailablePageDTO;
import com.xiaomi.goods.gis.dto.sku.offline.SkuAvailablePageRequest;
import com.xiaomi.goods.gis.dto.sku.offline.SkuAvailablePageResponse;
import com.xiaomi.goods.gms.dto.batch.BatchedInfoDto;
import com.xiaomi.goods.gms.dto.category.BusinessCategoryDto;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.response.*;
import com.xiaomi.nr.coupon.admin.api.enums.BizSubTypeEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboGoodsService;
import com.xiaomi.nr.coupon.admin.domain.coupon.goods.GoodsService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponScopeTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsItemTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.NewGoodsEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneCodeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.coupon.CouponConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.UploadGoodsFailPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.GoodItemPO;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.goods.GisProxyService;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.goods.GmsProxyService;
import com.xiaomi.nr.coupon.admin.retail.application.dubbo.coupon.convert.GoodsInfoConvert;
import com.xiaomi.nr.coupon.admin.util.CouponCollectionUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Component
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
@ApiModule(value = "优惠券商品服务接口", apiInterface = DubboGoodsService.class)
public class DubboGoodsServiceImpl implements DubboGoodsService {

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private GmsProxyService gmsProxyService;

    @Autowired
    private GisProxyService gisProxyService;

    @Autowired
    private GoodsInfoConvert goodsConvert;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    private static final int total = 5000;

    private static final int MAX_SUIT_TOTAL = 500;

    @Override
    @ApiDoc("skuid查询sku列表")
    public Result<SkuListResponse> queryListBySkuIds(SkuListRequest request) {
        try {
            List<Long> skuIds = new LinkedList<>();
            if (StringUtils.isNotEmpty(request.getFileUrl())) {
                skuIds.addAll(goodsService.queryGoodsList(request.getFileUrl(), GoodsLevelEnum.Sku));
            }
            skuIds.addAll(request.getSkuIds());

            if (CollectionUtils.isNotEmpty(skuIds) && skuIds.size() > total) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "上传不能超过" + total + "条");
            }
            boolean miSupportShipment = request.getCouponType().equals(CouponTypeEnum.POSTFREE.getValue());
            List<SkuInfoDto> skuInfoDtos = gmsProxyService.queryListBySkuIds(skuIds, false, true, miSupportShipment, Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode(), BizSubTypeEnum.CARRIER_PAN_COMPLETE.getCode()));

            SkuListResponse response = new SkuListResponse();

            List<Long> existSkuIds = new ArrayList<>(skuInfoDtos.size());
            List<SkuInfoVO> skuInfoList = new ArrayList<>(skuInfoDtos.size());
            List<UploadGoodsFailPO> uploadGoodsFailPOS = new ArrayList<>(skuInfoDtos.size());
            for (SkuInfoDto skuInfoDto : skuInfoDtos) {
                existSkuIds.add(skuInfoDto.getSku());
                if (goodsService.filterSku(request.getCouponType(), skuInfoDto, uploadGoodsFailPOS)) {
                    SkuInfoVO skuInfoVO = new SkuInfoVO();
                    BeanMapper.copy(skuInfoDto, skuInfoVO);
                    if (skuInfoDto.getCategory() != null) {
                        BeanMapper.copy(skuInfoDto.getCategory(), skuInfoVO);
                    }
                    skuInfoList.add(skuInfoVO);
                }
            }
            response.setSkuInfoList(skuInfoList);

            List<Long> notExistSkuIds = CouponCollectionUtil.removeAll(request.getSkuIds(), existSkuIds);
            response.setNotExistSkuIds(notExistSkuIds);
            response.setNotValidSkuIds(uploadGoodsFailPOS.stream().map(UploadGoodsFailPO::getGoodsId).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(notExistSkuIds)) {
                uploadGoodsFailPOS.addAll(notExistSkuIds.stream().map(x -> {
                    return new UploadGoodsFailPO(x, "无效的SKU/套装");
                }).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(uploadGoodsFailPOS)) {
                String fileName = "SKU上传失败详情-" + TimeUtil.getNowUnixSecond();
                response.setValidResultUrl(goodsService.getUploadGoodsFailFdsUrl(fileName));
                writeExcel(uploadGoodsFailPOS, fileName);
            }
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboGoodsService queryListBySkuIds error request:{}", request, e);
            return Result.fromException(e);
        }
    }


    @Override
    @ApiDoc("套装id查询套装列表")
    public Result<PackageListResponse> queryListByPackageIds(PackageListRequest request) {
        try {
            List<Long> packageIds = new LinkedList<>();
            if (StringUtils.isNotEmpty(request.getFileUrl())) {
                packageIds.addAll(goodsService.queryGoodsList(request.getFileUrl(), GoodsLevelEnum.Package));
            }
            packageIds.addAll(request.getPackageIds());

            if (CollectionUtils.isNotEmpty(packageIds) && packageIds.size() > total) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "上传不能超过" + total + "条");
            }

            List<BatchedInfoDto> infoDtos = gmsProxyService.queryListByPackageIds(packageIds, false);

            PackageListResponse response = new PackageListResponse();
            List<Long> existPackageIds = new LinkedList<>();
            List<PackageInfoVO> packageInfoList = new ArrayList<>(infoDtos.size());
            List<UploadGoodsFailPO> uploadGoodsFailPOS = new ArrayList<>(infoDtos.size());
            for (BatchedInfoDto batchedInfoDto : infoDtos) {
                existPackageIds.add(batchedInfoDto.getBatchedId());
                if (goodsService.filterPackage(batchedInfoDto, uploadGoodsFailPOS)) {
                    PackageInfoVO infoVO = new PackageInfoVO();
                    BeanMapper.copy(batchedInfoDto, infoVO);
                    infoVO.setMarketPrice(batchedInfoDto.getMarketPriceMax());
                    packageInfoList.add(infoVO);
                }
            }

            response.setPackageInfoList(packageInfoList);

            List<Long> notExistPackageIds = CouponCollectionUtil.removeAll(packageIds, existPackageIds);
            response.setNotExistPackageIds(notExistPackageIds);
            if (CollectionUtils.isNotEmpty(notExistPackageIds)) {
                uploadGoodsFailPOS.addAll(notExistPackageIds.stream().map(x -> {
                    return new UploadGoodsFailPO(x, "无效的SKU/套装");
                }).collect(Collectors.toList()));
            }

            if (CollectionUtils.isNotEmpty(uploadGoodsFailPOS)) {
                String fileName = "套装上传失败详情-" + TimeUtil.getNowUnixSecond();
                response.setValidResultUrl(goodsService.getUploadGoodsFailFdsUrl(fileName));
                writeExcel(uploadGoodsFailPOS, fileName);
            }
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboGoodsService queryListByPackageIds error request:{}", request, e);
            return Result.fromException(e);
        }
    }


    @Override
    @ApiDoc("分页查询sku列表")
    public Result<BasePageResponse<SkuInfoVO>> querySkuPageList(SkuPageListRequest request) {
        try {

            SkuAvailablePageRequest skuPageRequest = goodsConvert.convert2SkuPageRequestGis(request);
            SkuAvailablePageResponse skuPageResponse = gisProxyService.pageAvailableSkuGis(skuPageRequest);

            List<SkuAvailablePageDTO> skuInfoList = skuPageResponse.getSkuList();
            BasePageResponse<SkuInfoVO> response = new BasePageResponse<>();

            response.setPageNo(skuPageResponse.getPage().getPageNum());
            response.setPageSize(skuPageResponse.getPage().getPageSize());
            response.setTotalCount(skuPageResponse.getPage().getTotal());
            response.setList(goodsConvert.convert2SkuInfoVOGis(skuInfoList));

            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboGoodsService querySkuPageList error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    @ApiDoc("分页查询套装列表")
    public Result<BasePageResponse<PackageInfoVO>> queryPackagePageList(PackagePageListRequest request) {
        try {

            BatchedAvailablePageRequest packageListRequest = goodsConvert.convert2PackagePageRequestGis(request);
            BatchedAvailablePageResponse packageResponse = gisProxyService.pageAvailableBatchedGis(packageListRequest);

            List<BatchedDTO> batchedInfoDtoList = packageResponse.getBatchedList();
            BasePageResponse<PackageInfoVO> response = new BasePageResponse<>();

            response.setPageNo(packageResponse.getPage().getPageNum());
            response.setPageSize(packageResponse.getPage().getPageSize());
            response.setTotalCount(packageResponse.getPage().getTotal());
            response.setList(goodsConvert.convert2PackageDtoGis(batchedInfoDtoList));

            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboGoodsService querySkuPageList error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    @ApiDoc("查询所有类目")
    public Result<CategoryListResponse> getCategoryList(CategoryListRequest request) {
        try {
            if (request.getServiceId() <= 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "参数不正确");
            }
            CategoryListResponse response = new CategoryListResponse();
            List<CategoryVO> categoryVOList = new ArrayList<>();

            Map<Integer, List<CategoryResponse>> cateMap = gisProxyService.getCategoryList(request.getServiceId(), 0);

            Map<Integer, List<CategoryResponse>> cateParentMap = new HashMap<>();
            for (List<CategoryResponse> cates : cateMap.values()) {
                cateParentMap.putAll(cates.stream().collect(Collectors.groupingBy(CategoryResponse::getParentCateId)));
            }

            cateMap.get(1).stream().forEach(x -> {
                CategoryVO categoryVO = new CategoryVO();
                categoryVO.setId(x.getCateId());
                categoryVO.setLabel(x.getName());
                categoryVO.setLevel(x.getLevel());
                categoryVO.setChildren(getChildrens(categoryVO, cateParentMap));
                categoryVOList.add(categoryVO);
            });
            response.setCategoryVOList(categoryVOList);
            response.setThreeCategoryNum(cateMap.get(3).size());
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboGoodsService queryPackagePageList Exception ", e);
            return Result.fromException(e);
        }
    }

    /**
	 * 根据分类ID获取商品列表
	 */
	@Override
    @ApiDoc("根据分类ID获取商品列表")
    public Result<SkuListResponse> getSkuByCategoryId(SkuCategoryIdRequest request) {
        try {
            if (CollectionUtils.isEmpty(request.getCategoryIds())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "参数不正确");
            }

            Set<Integer> categoryIdSet = new HashSet<>(request.getCategoryIds());
            boolean miSupportShipment = CouponTypeEnum.POSTFREE.getValue().equals(request.getCouponType());

            List<SkuInfoDto> skuInfoDtoList = goodsService.getSkuByCategoryId(
                    categoryIdSet,
                    Collections.emptyList(),
                    miSupportShipment,
                    Arrays.asList(BizSubTypeEnum.ORDINARY_3C.getCode(), BizSubTypeEnum.CARRIER_PAN_COMPLETE.getCode())
            );

            List<SkuInfoVO> skuInfoList = skuInfoDtoList.stream()
                    .filter(skuInfoDto -> goodsService.filterSku(request.getCouponType(), skuInfoDto, new ArrayList<>()))
                    .map(this::convertToSkuInfoVO)
                    .collect(Collectors.toList());

            SkuListResponse response = new SkuListResponse();
            response.setSkuInfoList(skuInfoList);

            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboGoodsService getSkuByCategoryId Exception request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
	* 将 SkuInfoDto 转换为 SkuInfoVO
	*/
	private SkuInfoVO convertToSkuInfoVO(SkuInfoDto skuInfoDto) {
        SkuInfoVO skuInfoVO = new SkuInfoVO();
        BeanMapper.copy(skuInfoDto, skuInfoVO);
        if (skuInfoDto.getCategory() != null) {
            BeanMapper.copy(skuInfoDto.getCategory(), skuInfoVO);
        }
        return skuInfoVO;
    }

    private List<CategoryVO> getChildrens(CategoryVO root, Map<Integer, List<CategoryResponse>> cateMap) {
        int rootId = root.getId();
        if (CollectionUtils.isNotEmpty(cateMap.get(rootId))) {
            List<CategoryVO> collect = cateMap.get(rootId).stream().map((menu) -> {
                CategoryVO categoryVO = new CategoryVO();
                categoryVO.setId(menu.getCateId());
                categoryVO.setLabel(menu.getName());
                categoryVO.setLevel(menu.getLevel());
                categoryVO.setParentCateId(rootId);
                categoryVO.setChildren(getChildrens(categoryVO, cateMap));
                return categoryVO;
            }).collect(Collectors.toList());
            return collect;
        }
        return Collections.EMPTY_LIST;
    }


    @Override
    @ApiDoc("查询商品预估折扣力度")
    public Result<GoodsDiscountLevelResponse> getGoodsDiscountLevel(GoodsDiscountLevelRequest request) {
        log.info("DubboGoodsService getGoodsDiscountLevel request:{}", request);
        try {
            if (BigDecimal.TEN.compareTo(request.getDiscountLevel()) < 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "折扣力度有误");
            }
            if (CollectionUtils.isEmpty(request.getUseChannel())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "使用渠道不能为空");
            }
            if (request.getGoodsRuleVO() == null || MapUtils.isEmpty(request.getGoodsRuleVO().getGoodsInclude())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "商品不能为空");
            }
            if (request.getPromotionRuleVO() == null || request.getPromotionRuleVO().getPromotionType() < 0 || request.getPromotionRuleVO().getPromotionValue() < 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠信息不能为空");
            }

            GoodsDiscountLevelResponse response = new GoodsDiscountLevelResponse();

            // 特殊场景跳过分险预警
            if (SceneCodeEnum.COMPENSATE_SCENE.getCode().equals(request.getSendScene())) {
                response.setGoodsDiscountLevelVO(new GoodsDiscountLevelVO());
                return Result.success(response);
            }

            Map<Long, Long> skuPriceMap = new HashMap<>();
            Map<Long, Long> packagePriceMap = new HashMap<>();
            GoodsDiscountLevelVO goodsDiscountLevelVO = goodsService.getGoodsDiscountLevel(request, skuPriceMap, packagePriceMap);


            if (CollectionUtils.isNotEmpty(goodsDiscountLevelVO.getSkuInfoVOList())) {
                Collections.sort(goodsDiscountLevelVO.getSkuInfoVOList());
            }
            if (CollectionUtils.isNotEmpty(goodsDiscountLevelVO.getPackageInfoVOList())) {
                Collections.sort(goodsDiscountLevelVO.getPackageInfoVOList());
            }

            // 超级补贴券需匹配对应商品可参与折扣力度最高的商品券
            Map<Long, Map<Integer, GoodsCouponPromVO>> goodsCouponMap = goodsService.matchGoodsCouponType(skuPriceMap, packagePriceMap, request);
            if (MapUtils.isNotEmpty(goodsCouponMap)) {

                if (CollectionUtils.isNotEmpty(goodsDiscountLevelVO.getSkuInfoVOList())) {
                    goodsDiscountLevelVO.getSkuInfoVOList().forEach(x -> x.setGoodsCouponMap(goodsCouponMap.get(x.getGoodsId())));
                }

                if (CollectionUtils.isNotEmpty(goodsDiscountLevelVO.getPackageInfoVOList())) {
                    goodsDiscountLevelVO.getPackageInfoVOList().forEach(x -> x.setGoodsCouponMap(goodsCouponMap.get(x.getGoodsId())));
                }
            }

            response.setGoodsDiscountLevelVO(goodsDiscountLevelVO);
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboGoodsService getGoodsDiscountLevel error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    @ApiDoc("获取分类新品")
    public Result<LatestGoodsResponse> getLatestGoods(LatestGoodsRequest request) {
        log.info("DubboGoodsService getLatestGoods request:{}", request);
        try {
            CouponConfigPO couponConfigPO = couponConfigRepository.searchCouponById(request.getConfigId());
            if (couponConfigPO == null || couponConfigPO.getId() <= 0 || CouponScopeTypeEnum.Categories.getValue() != couponConfigPO.getScopeType() || StringUtils.isBlank(couponConfigPO.getCategoryIds())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "参数不正确");
            }

            List<Integer> categoryIds = StringUtil.convertToIntegerList(couponConfigPO.getCategoryIds());
            boolean miSupportShipment = couponConfigPO.getCouponType().equals(CouponTypeEnum.POSTFREE.getValue());
            // 更新新品 ，不允许更新非自营品
            Map<Long, List<SkuInfoDto>> skuMapByCategoryId = goodsService.getSkuMapByCategoryId(categoryIds, Lists.newArrayList(CouponConstant.BUSINESS_TYPE_SELF), miSupportShipment);
            List<CategoryNewGoodsVO> newGoodsList = new LinkedList<>();
            List<UploadGoodsFailPO> uploadGoodsFailPOS = new ArrayList<>();
            if (MapUtils.isNotEmpty(skuMapByCategoryId)) {
                GoodItemPO goodItemPO = GsonUtil.fromJson(couponConfigPO.getGoodsInclude(), GoodItemPO.class);
                List<Long> skus = goodItemPO.getSku();
                skuMapByCategoryId.forEach((k, v) -> {
                    Map<Long, SkuInfoDto> skuIds = v.stream().filter(x -> {
                        return goodsService.filterSku(request.getCouponType(), x, uploadGoodsFailPOS);
                    }).collect(Collectors.toMap(SkuInfoDto::getSku, Function.identity(), (k1, k2) -> k2));
                    List<Long> newSkuIds = CouponCollectionUtil.removeAll(new LinkedList<>(skuIds.keySet()), skus);
                    if (CollectionUtils.isNotEmpty(newSkuIds)) {
                        CategoryNewGoodsVO categoryNewGoodsVO = new CategoryNewGoodsVO();
                        SkuInfoDto skuInfoDto = skuIds.get(newSkuIds.get(0));
                        BusinessCategoryDto businessCategoryDto = skuInfoDto.getCategory();
                        if (businessCategoryDto != null) {
                            categoryNewGoodsVO.setFirstCategoryId(businessCategoryDto.getFirstCategoryId());
                            categoryNewGoodsVO.setFirstCategoryName(businessCategoryDto.getFirstCategoryName());
                            categoryNewGoodsVO.setSecondCategoryId(businessCategoryDto.getSecondCategoryId());
                            categoryNewGoodsVO.setSecondCategoryName(businessCategoryDto.getSecondCategoryName());
                            categoryNewGoodsVO.setThirdCategoryId(businessCategoryDto.getThirdCategoryId());
                            categoryNewGoodsVO.setThirdCategoryName(businessCategoryDto.getThirdCategoryName());
                            categoryNewGoodsVO.setChildren(newSkuIds.stream().map(x -> {
                                SkuInfoVO skuInfoVO = new SkuInfoVO();
                                BeanMapper.copy(skuIds.get(x), skuInfoVO);
                                BeanMapper.copy(businessCategoryDto, skuInfoVO);
                                skuInfoVO.setNewFlag(NewGoodsEnum.NEW.getValue());
                                return skuInfoVO;
                            }).collect(Collectors.toList()));
                        }
                        newGoodsList.add(categoryNewGoodsVO);
                    }
                });
            }


            LatestGoodsResponse response = new LatestGoodsResponse();
            response.setNewGoodsList(newGoodsList);
            if (CollectionUtils.isNotEmpty(newGoodsList)) {
                response.setHasNewGoods(true);
            }
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboGoodsService getLatestGoods error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    @ApiDoc("分页查询套装列表")
    public Result<BasePageResponse<PackageInfoVO>> querySuitPageList(SuitPageListRequest request) {
        try {
            GoodsPageRequest goodsPageRequest = goodsConvert.convert2GoodsPageRequest(request);
            GoodsPageResponse goodsPageResponse = gisProxyService.pageGoodsInfoGis(goodsPageRequest);

            List<GoodsPageDTO> goodsPageDTOS = goodsPageResponse.getGoodsList();
            BasePageResponse<PackageInfoVO> response = new BasePageResponse<>();

            response.setPageNo(goodsPageResponse.getPage().getPageNum());
            response.setPageSize(goodsPageResponse.getPage().getPageSize());
            response.setTotalCount(goodsPageResponse.getPage().getTotal());
            response.setList(goodsConvert.convertGoodsPageDTO2PackageInfoVO(goodsPageDTOS));
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboGoodsServiceImpl querySuitPageList error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    @ApiDoc("上传并查询套装")
    public Result<PackageListResponse> queryListBySuitIds(PackageListRequest request) {
        try {
            if (CollectionUtils.isEmpty(request.getPackageIds()) && StringUtils.isBlank(request.getFileUrl())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "packageIds和fileUrl不能同时为空");
            }
            List<Long> packageIds = new LinkedList<>();
            if (StringUtils.isNotBlank(request.getFileUrl())) {
                packageIds.addAll(goodsService.queryGoodsList(request.getFileUrl(), GoodsLevelEnum.Suit));
            }
            if (CollectionUtils.isNotEmpty(request.getPackageIds())) {
                packageIds.addAll(request.getPackageIds());
            }

            if (CollectionUtils.isNotEmpty(packageIds) && packageIds.size() > MAX_SUIT_TOTAL) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "上传不能超过" + MAX_SUIT_TOTAL + "条");
            }

            List<GoodsMultiInfoDTO> goodsMultiInfoList = gisProxyService.queryGoodsMultiInfoByGoodsIds(packageIds, GoodsItemTypeEnum.SUIT);

            PackageListResponse response = new PackageListResponse();
            List<Long> existPackageIds = new LinkedList<>();
            List<PackageInfoVO> infoList = new LinkedList<>();
            List<UploadGoodsFailPO> uploadGoodsFailPOs = new LinkedList<>();
            for (GoodsMultiInfoDTO goodsMultiInfoDTO : goodsMultiInfoList) {
                existPackageIds.add(goodsMultiInfoDTO.getGoodsId());
                if (goodsService.filterSuit(goodsMultiInfoDTO, uploadGoodsFailPOs)) {
                    PackageInfoVO infoVO = goodsConvert.convertGoodsMultiInfoDTO2PackageInfoVO(goodsMultiInfoDTO);
                    infoList.add(infoVO);
                }
            }

            response.setPackageInfoList(infoList);

            List<Long> notExistPackageIds = CouponCollectionUtil.removeAll(packageIds, existPackageIds);
            response.setNotExistPackageIds(notExistPackageIds);
            if (CollectionUtils.isNotEmpty(notExistPackageIds)) {
                uploadGoodsFailPOs.addAll(notExistPackageIds.stream().map(x -> new UploadGoodsFailPO(x, "无效的套装")).collect(Collectors.toList()));
            }

            if (CollectionUtils.isNotEmpty(uploadGoodsFailPOs)) {
                String fileName = "套装上传失败详情-" + TimeUtil.getNowUnixSecond();
                response.setValidResultUrl(goodsService.getUploadGoodsFailFdsUrl(fileName));
                writeExcel(uploadGoodsFailPOs, fileName);
            }
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboGoodsServiceImpl queryListBySuitIds error request:{}", request, e);
            return Result.fromException(e);
        }
    }


    /**
     * 异步写入商品上传失败文件
     *
     * @param fileName
     */
    @Async("asyncExecutor")
    public void writeExcel(List<UploadGoodsFailPO> uploadGoodsFailPOS, String fileName) {
        if (CollectionUtils.isEmpty(uploadGoodsFailPOS)) {
            return;
        }

        for (int i = 1; i <= 3; i++) {
            try {
                goodsService.uploadGoodsFailReason(uploadGoodsFailPOS, fileName);
                return;
            } catch (Exception e) {
                log.error("writeExcel i:{}", i, e);
            }
        }
    }

}
