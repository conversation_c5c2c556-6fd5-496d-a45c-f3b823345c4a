package com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.ReviewGroupListRequest;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class SearchReviewGroupParam implements Serializable {

    /**
     * 提交审批人邮箱前缀
     */
    private String creator;
    /**
     * 一级审批组
     */
    private String reviewGroup;

    /**
     * 当前页码
     */
    private int pageNo = 1;

    /**
     * 页面条数
     */
    private int pageSize = 10;


    public static SearchReviewGroupParam buildSearchReviewGroupParam(ReviewGroupListRequest request){
        SearchReviewGroupParam param = new SearchReviewGroupParam();
        BeanMapper.copy(request, param);
        return param;
    }

}
