package com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity;

import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponReviewRelPO;
import lombok.Data;

import java.util.List;

@Data
public class SeachReviewGroupResult {

    /**
     * 数据集
     */
    private List<CouponReviewRelPO> couponReviewRelPOList;

    /**
     * 总数据量
     */
    private long totalCount;

    /**
     * 总页码
     */
    private long totalPage;


}
