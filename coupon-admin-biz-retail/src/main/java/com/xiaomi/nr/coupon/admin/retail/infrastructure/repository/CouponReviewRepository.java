package com.xiaomi.nr.coupon.admin.retail.infrastructure.repository;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xiaomi.newretail.bpm.api.model.dto.ProcessCreateDTO;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.retail.domain.coupon.bpm.BpmPageGenerator;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.CouponConfigReviewMapper;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity.SeachReviewListResult;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity.SearchReviewListParam;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponConfigReviewPO;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.rpc.bpm.BpmProxy;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 券审批
 */
@Component
@Slf4j
public class CouponReviewRepository {

    @Autowired
    private CouponConfigReviewMapper couponConfigReviewMapper;

    @Autowired
    private BpmPageGenerator couponPageGenerator;

    @Autowired
    private BpmPageGenerator postfreeCouponPageGenerator;

    @Autowired
    private BpmPageGenerator subsidyCouponPageGenerator;

    @Autowired
    private BpmProxy bpmProxy;

    /**
     * 新增申请
     */
    @Transactional(transactionManager = "xmPulseCouponTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void save(CouponConfigReviewPO reviewPO) throws Exception {
        String bpmKey = null;
        try {
            // 业务平台赋值
//            reviewPO.setBizPlatform(BizPlatformEnum.RETAIL.getCode());
            Long insertRows = couponConfigReviewMapper.insert(reviewPO);
            if (insertRows <= 0) {
                log.error("CouponReviewRepository.save 插入审核记录失败！");
                throw ExceptionHelper.create(ErrCode.COUPON, "插入审核记录失败");
            }
            ProcessCreateDTO processRequest = null;
            if (CouponTypeEnum.GOODS.getValue().equals(reviewPO.getCouponType())) {
                processRequest = couponPageGenerator.createRequest(reviewPO);
            } else if (CouponTypeEnum.POSTFREE.getValue().equals(reviewPO.getCouponType())) {
                processRequest = postfreeCouponPageGenerator.createRequest(reviewPO);
            } else if (CouponTypeEnum.SUBSIDY.getValue().equals(reviewPO.getCouponType())) {
                processRequest = subsidyCouponPageGenerator.createRequest(reviewPO);
            }

            bpmKey = bpmProxy.submitReview(processRequest);

            Long updateRows = couponConfigReviewMapper.updateBpm(reviewPO.getId(), bpmKey);
            if (updateRows <= 0) {
                // 更新bpmKey失败，事务回滚
                log.error("CouponReviewRepository.save 更新bpmKey失败！");
                throw ExceptionHelper.create(ErrCode.COUPON, "更新bpmKey失败");
            }
        } catch (Exception e) {
            // 撤销bpm
            if (Objects.nonNull(bpmKey)) {
                try {
                    bpmProxy.cancelReview(bpmKey, "system");
                } catch (Exception ex) {
                    log.error("CouponReviewRepository.save bpm撤销失败, bpmKey = {}", bpmKey);
                }
            }
            log.error("CouponReviewRepository.save error， reviewPO:{}", reviewPO, e);
            throw ExceptionHelper.create(ErrCode.COUPON, "提交券配置审核失败");
        }

    }


    /**
     * 查询申请数量
     *
     * @param configId
     * @param statusList
     * @return
     */
    public long count(long configId, List<Integer> statusList) {
        return couponConfigReviewMapper.count(configId, statusList);
    }

    /**
     * 更新申请状态
     *
     * @return
     */
    public long updateReviewStatus(CouponConfigReviewPO po) {
        return couponConfigReviewMapper.updateReviewStatus(po);
    }

    /**
     * 根据唯一键查询
     *
     * @param bpmKey
     * @return
     */
    public CouponConfigReviewPO selectByBpmKey(String bpmKey) {
        return couponConfigReviewMapper.selectByBpmKey(bpmKey);
    }


    /**
     * 根据id查询
     *
     * @return
     */
    public CouponConfigReviewPO selectById(long id) {
        return couponConfigReviewMapper.selectById(id);
    }


    /**
     * 根据id查询
     *
     * @return
     */
    public SeachReviewListResult selectList(SearchReviewListParam param) {
        PageHelper.startPage(param.getPageNo(), param.getPageSize());
        List<CouponConfigReviewPO> list = couponConfigReviewMapper.selectList(param);

        SeachReviewListResult result = new SeachReviewListResult();
        if (CollectionUtils.isNotEmpty(list)) {
            PageInfo<CouponConfigReviewPO> pageInfo = new PageInfo<>(list);
            result.setReviewPOList(list);
            result.setTotalCount(pageInfo.getTotal());
            result.setTotalPage(pageInfo.getPages());
        }
        return result;
    }

    /**
     * 根据券id查询申请详情
     *
     * @param configId 券id
     * @return 审核附件地址信息
     */
    public String selectByConfigId(Long configId) {
        return couponConfigReviewMapper.selectByConfigId(configId);
    }


}
