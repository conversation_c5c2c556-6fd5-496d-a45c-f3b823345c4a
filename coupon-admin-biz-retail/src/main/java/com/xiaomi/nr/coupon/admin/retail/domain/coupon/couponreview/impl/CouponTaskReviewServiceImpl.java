package com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponreview.impl;

import com.xiaomi.newretail.bpm.api.model.callback.OnStatusChangedRequest;
import com.xiaomi.newretail.bpm.api.model.callback.OnStatusChangedResponse;
import com.xiaomi.newretail.bpm.api.model.callback.ProcessAction;
import com.xiaomi.newretail.bpm.api.model.callback.StatusChangeAction;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.CouponFillReviewDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillReviewCancelRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillReviewListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CreateCouponFillReviewRequest;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponTaskConvert;
import com.xiaomi.nr.coupon.admin.domain.coupon.fillcoupontask.DownLoadHelper;
import com.xiaomi.nr.coupon.admin.domain.coupon.fillcoupontask.FillCouponService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.ReviewStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.task.CustomizeGroupEnum;
import com.xiaomi.nr.coupon.admin.enums.task.UserGroupTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponTaskRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.CouponConfigRedisDao;
import com.xiaomi.nr.coupon.admin.retail.application.dubbo.coupon.convert.TaskReviewConvert;
import com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponreview.CouponTaskReviewService;
import com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponreview.entity.CouponTaskReviewContext;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.CouponTaskReviewRepository;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity.SearchTaskReviewListParam;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.entity.SearchTaskReviewListResult;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponTaskReviewPO;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.rpc.bpm.BpmProxy;
import com.xiaomi.nr.coupon.admin.util.TimeUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 灌券任务审核相关服务
 */
@Slf4j
@Service
public class CouponTaskReviewServiceImpl implements CouponTaskReviewService {

    @Autowired
    private CouponConfigRedisDao couponConfigRedisDao;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Autowired
    private CouponTaskReviewRepository couponTaskReviewRepository;

    @Autowired
    private CouponTaskConvert couponTaskConvert;

    @Autowired
    private CouponTaskRepository fillCouponTaskRepository;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private FillCouponService fillCouponService;

    @Autowired
    private BpmProxy bpmProxy;

    @Autowired
    private TaskReviewConvert taskReviewConvert;

    @Autowired
    private DownLoadHelper downLoadHelper;

    @Override
    public Long createTaskReview(CreateCouponFillReviewRequest request) throws Exception {

        // 券信息校验
        checkCouponConfig(request.getConfigId());

        // 异步上传人群数据
        String fileName = uploadUserHdfs(request.getUserGroupType(), request.getCustomizeType(), request.getUidList());

        // 审核实体转换
        CouponTaskReviewPO reviewPO = taskReviewConvert.convertToTaskReviewPO(request, fileName);

        // 构建上下文对象
        CouponTaskReviewContext reviewContext = taskReviewConvert.convertToReviewContext(reviewPO);

        // 提交bpm审核
        couponTaskReviewRepository.save(reviewContext);

        return reviewContext.getReviewPO().getId();
    }


    @Override
    public Boolean cancelTaskReview(CouponFillReviewCancelRequest request) throws Exception {

        // 获取审核信息
        CouponTaskReviewPO reviewPO = couponTaskReviewRepository.getReviewById(request.getReviewId());

        // 撤销审核请求bpm
        bpmProxy.cancelReview(reviewPO.getBpmKey(), request.getOperator());

        // 审核状态同步审核表
        reviewPO.setStatus(ReviewStatusEnum.Canceled.getValue());
        int updateCount = couponTaskReviewRepository.updateReviewStatus(reviewPO);

        return updateCount > CommonConstant.ZERO_INT;
    }


    @Override
    public BasePageResponse<CouponFillReviewDTO> taskReviewList(CouponFillReviewListRequest request) {

        // 检索参数转换
        SearchTaskReviewListParam param = new SearchTaskReviewListParam();
        BeanMapper.copy(request, param);

        // 查询审核列表
        SearchTaskReviewListResult searchResult = couponTaskReviewRepository.getReviewList(param);

        // 组装返回值
        BasePageResponse<CouponFillReviewDTO> response = new BasePageResponse<>();
        response.setPageSize(request.getPageSize());
        response.setPageNo(request.getPageNo());
        response.setTotalPage(searchResult.getTotalPage());
        response.setTotalCount(searchResult.getTotalCount());
        if (CollectionUtils.isNotEmpty(searchResult.getData())) {
            response.setList(searchResult.getData().stream().map(x -> {
                CouponFillReviewDTO reviewDTO = new CouponFillReviewDTO();
                BeanMapper.copy(x, reviewDTO);
                reviewDTO.setName(x.getTaskName());
                reviewDTO.setCouponName(x.getConfigName());
                return reviewDTO;
            }).collect(Collectors.toList()));
        }

        return response;
    }


    @Override
    public void onTaskStatusChanged(OnStatusChangedRequest request, OnStatusChangedResponse onStatusChangedResponse, CouponTaskReviewPO reviewPO) {

        // 审核表记录状态异常退出
        if (ReviewStatusEnum.ToBeReviewed.getValue() != reviewPO.getStatus() && ReviewStatusEnum.UnderReview.getValue() != reviewPO.getStatus()) {
            log.error("CouponTaskReviewServiceImpl onTaskStatusChanged status error request:{},reviewPO:{}", request, reviewPO);
            onStatusChangedResponse.setCode(GeneralCodes.InternalError.getCode());
            onStatusChangedResponse.setAction(StatusChangeAction.STOP);
            return;
        }

        ProcessAction action = request.getAction();

        // 审核驳回
        if (ProcessAction.Refuse.equals(action)) {

            reviewPO.setStatus(ReviewStatusEnum.Rejected.getValue());
            reviewPO.setApprovedId(request.getOperator());
            reviewPO.setApprovedTime(new Date());
            reviewPO.setBpmReason(request.getRefuseReason());
            couponTaskReviewRepository.updateReviewStatus(reviewPO);
            return;
        }

        // 审核通过
        if (ProcessAction.Accept.equals(action)) {

            if (!request.getFinished()) {
                reviewPO.setStatus(ReviewStatusEnum.UnderReview.getValue());
                reviewPO.setApprovedId(request.getOperator());
                reviewPO.setApprovedTime(new Date());
                couponTaskReviewRepository.updateReviewStatus(reviewPO);
                return;
            }

            reviewPO.setStatus(ReviewStatusEnum.Approved.getValue());
            reviewPO.setApprovedId(request.getOperator());
            reviewPO.setApprovedTime(new Date());
            couponTaskReviewRepository.updateReviewStatus(reviewPO);
            return;
        }

        // 审核取消
        if (ProcessAction.Cancel.equals(action)) {
            reviewPO.setStatus(ReviewStatusEnum.Canceled.getValue());
            reviewPO.setApprovedId(request.getOperator());
            couponTaskReviewRepository.updateReviewStatus(reviewPO);
        }

    }


    @Override
    public CouponTaskReviewPO getTaskReviewPO(long reviewId) {
        return couponTaskReviewRepository.getReviewById(reviewId);
    }


    /**
     * 检验券信息
     *
     * @param configId 券配置id
     * @throws BizError 业务异常
     */
    private void checkCouponConfig(long configId) throws BizError {
        CouponConfigPO couponConfig = couponConfigRepository.searchCouponById(configId);
        if (couponConfig == null) {
            throw ExceptionHelper.create(ErrCode.COUPON, "提交失败,优惠券不存在");
        }

        /*List<Long>  reviewPOList =  couponTaskReviewRepository.selectByConfigId(configId);
        if(CollectionUtils.isNotEmpty(reviewPOList)){
            throw ExceptionHelper.create(ErrCode.COUPON, "提交失败,该优惠券ID下存在正在进行中的灌券审核");
        }

        List<Long> taskIdList = fillCouponTaskRepository.getTaskByConfigId(configId);
        if(CollectionUtils.isNotEmpty(taskIdList)){
            throw ExceptionHelper.create(ErrCode.COUPON, "提交失败,该优惠券ID下存在正在进行中的灌券任务,ID:"+taskIdList+",等任务完成后方可创建新任务!");
        }*/

        if (couponConfig.getStatus() != CouponConfigStatusEnum.ONLINE.getCode()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "提交失败,优惠券活动未上线或已经终止");
        }

        if (couponConfig.getStartFetchTime() > TimeUtil.getNowUnixSecond()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "提交失败,未到优惠券领取时间");
        }

        if (couponConfig.getEndFetchTime() < TimeUtil.getNowUnixSecond()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "提交失败,优惠券领取时间已结束");
        }
        // 判断库存
        Map<Long, Long> sendCountMap = couponConfigRepository.getCouponSendCount(Collections.singletonList(configId));
        if (couponConfig.getApplyCount() <= sendCountMap.get(configId)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "提交失败,优惠券库存为0");
        }
    }


    /**
     * 异步上传人群数据
     *
     * @param userGroupType
     * @param customizeType
     * @param uidList
     * @return
     */
    private String uploadUserHdfs(int userGroupType, int customizeType, List<Long> uidList) {

        if (userGroupType != UserGroupTypeEnum.CUSTOMIZE_USER_GROUP.getCode() && customizeType == CustomizeGroupEnum.HDFS_ADDRESS.getCode()) {
            return null;
        }

        String fileName = null;

        if (Objects.equals(CustomizeGroupEnum.CLIENT_INPUT.getCode(), customizeType)) {
            fileName = CustomizeGroupEnum.CLIENT_INPUT.getPrefix() + TimeUtil.getNowUnixMillis();
        }

        if (Objects.equals(CustomizeGroupEnum.UPLOAD_FILE.getCode(), customizeType)) {
            fileName = CustomizeGroupEnum.UPLOAD_FILE.getPrefix() + TimeUtil.getNowUnixMillis();
        }

        try {
            downLoadHelper.uploadUidToHDFSAsync(uidList, fileName);
        } catch (Exception e) {
            log.info("异步上传用户mid失败：fileName:{}", fileName, e);
            e.printStackTrace();
        }

        return fileName;
    }

}
