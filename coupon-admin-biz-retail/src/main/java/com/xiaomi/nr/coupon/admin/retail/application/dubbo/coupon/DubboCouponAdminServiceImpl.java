package com.xiaomi.nr.coupon.admin.retail.application.dubbo.coupon;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.mi.oa.infra.oaucf.ems.enums.BudgetApplyTypeEnum;
import com.mi.oa.infra.oaucf.ems.enums.SystemSourceEnum;
import com.mi.oa.infra.oaucf.ems.req.br.BudgetApplyReq;
import com.mi.oa.infra.oaucf.ems.req.br.BudgetApplyTypeInfo;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.PageInfo;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponDescRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponUpdateStatusRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.CouponDetailResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.CouponInfoResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponConfigDescResponse;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.UpdateCouponGoodsRequest;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.UpdateCouponGoodsResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.EquityTypeCodeEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.CouponAdminService;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponAdminService;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponConfigConvert;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.CouponGoodsRefreshService;
import com.xiaomi.nr.coupon.admin.domain.coupon.goods.GoodsService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastruture.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.rpc.BrProxy;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.CouponReviewRepository;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hejiapeng
 * @Date 2022/3/3 12:51 下午
 * @Version: 1.0
 **/
@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
@Component
@Slf4j
public class DubboCouponAdminServiceImpl implements DubboCouponAdminService {

    @Autowired
    private CouponConfigConvert couponConfigConvert;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private CouponReviewRepository couponReviewRepository;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Autowired
    private CouponGoodsRefreshService couponGoodsRefreshService;

    @Autowired
    private CouponAdminService couponAdminService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private BrProxy brProxy;

    @Override
    public Result<BasePageResponse<CouponConfigListVO>> couponConfigList(CouponListRequest request) {

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {

            // 1、业务平台赋值
            request.setBizPlatformList(Lists.newArrayList(BizPlatformEnum.RETAIL.getCode(), BizPlatformEnum.CAR_SHOP.getCode()));

            // 2、获取券配置列表
            Result<BasePageResponse<CouponConfigListVO>> resp = couponAdminService.couponConfigList(request);

            log.info("DubboCouponAdminService.couponConfigList execute success, runTime={}ms, request={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), request);

            return resp;
        }catch (Exception e){
            log.error("DubboCouponAdminService.couponConfigList request={}, error: ", request, e);
            return Result.fromException(e);
        }finally {
            stopwatch.stop();
        }
    }

    @Override
    public Result<CouponInfoResponse> couponConfigDetail(@Valid CouponInfoRequest request) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 1、获取券配置详情
            Result<CouponDetailResponse> couponDetailResult = couponAdminService.couponConfigDetail(request);
            if (couponDetailResult.getCode() != GeneralCodes.OK.getCode()) {
                return Result.fail(ErrCode.COUPON, couponDetailResult.getMessage());
            }

            CouponDetailResponse couponDetailResp = couponDetailResult.getData();
            CouponInfoResponse couponInfoResp = new CouponInfoResponse();

            // 2、公共字段赋值
            BeanUtils.copyProperties(couponDetailResp, couponInfoResp);

            // 3、商品配置信息赋值
            CouponConfigDetailVO couponConfigVO = couponDetailResp.getCouponConfigVO();
            GoodsRuleDetailVO goodsRuleDetailVO = goodsService.searchGoodsDetailInfo(couponConfigVO.getGoodsRuleVO(), couponConfigVO.getPromotionRuleVO().getPromotionType(), couponDetailResp.getBizPlatform());
            couponInfoResp.setGoodsRuleDetailVO(goodsRuleDetailVO);

            // 4、设置预估总成本赋值
            couponConfigVO.setCost(couponConfigConvert.estimatedCost(couponConfigVO.getDistributionRuleVO().getApplyCount(), goodsRuleDetailVO, couponConfigVO.getPromotionRuleVO()));

            // 5、填充审核附件地址信息
            Long id = request.getId();
            String applyAttachment = couponReviewRepository.selectByConfigId(id);
            couponInfoResp.setApplyAttachment(GsonUtil.fromListJson(applyAttachment, ApplyAttachmentVO.class));

            log.info("CouponConfigService.couponConfigDetail execute success, runTime={}ms, id={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), id);
            return Result.success(couponInfoResp);
        }catch (Exception e){
            log.error("CouponConfigService.couponConfigDetail request={}, error: ", request, e);
            return Result.fromException(e);
        }finally {
            stopwatch.stop();
        }
    }

    /**
     * 查询券简单信息
     * @param request request
     * @return
     */
    @Override
    public Result<CouponConfigDescResponse> couponConfigDesc(CouponDescRequest request) {
        CouponConfigDescResponse response =new CouponConfigDescResponse();
        try{
            List<CouponConfigPO> configPOList = couponConfigRepository.searchCouponByIdOrName(request.getQueryItem(), request.getBizType());

            if(CollectionUtils.isEmpty(configPOList)){
                response.setCouponConfigDescVOS(Lists.newArrayList());
                return Result.success(response);
            }

            List<String> sceneCodeList = configPOList.stream()
                    .map(CouponConfigPO::getSendScene)
                    .flatMap(sendScene -> Arrays.stream(sendScene.split(",")))
                    .distinct()
                    .collect(Collectors.toList());

            Map<String, String> sceneMap = couponSceneRepository.selectBySceneCodes(sceneCodeList);

            List<CouponConfigDescVO> couponConfigDescVOS = couponConfigConvert.convertCouponConfigDescVOS(configPOList, sceneMap);

            couponConfigDescVOS.sort((o1, o2) -> o2.getId() > o1.getId() ? 1 : -1);

            response.setCouponConfigDescVOS(couponConfigDescVOS);
            return Result.success(response);
        }catch (Exception e){
            log.error("CouponConfigService.couponConfigDesc request={}, error: ",request , e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<String> updateStatus(@Valid CouponUpdateStatusRequest request) {

        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            // 1、入参校验
            if (Objects.isNull(CouponConfigStatusEnum.findByCode(request.getOperateType()))) {
                throw ExceptionHelper.create(ErrCode.COUPON, "状态变更操作非法");
            }

            // 2、更新状态
            request.setOperator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());
            Result<String> res = couponAdminService.updateConfigStatus(request);

            log.info("CouponConfigService.updateStatus execute success, runTime={}ms, request={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), request);
            return res;
        }catch (Exception e){
            log.error("CouponConfigService.updateStatus request={}, error: ", request, e);
            return Result.fromException(e);
        }finally {
            stopwatch.stop();
        }
    }


    @Override
    public Result<UpdateCouponGoodsResponse> updateCouponGoods(UpdateCouponGoodsRequest request) {

        if(CollectionUtils.isEmpty(request.getIds())){
            Result.fail(GeneralCodes.ParamError, "券id为空");
        }

        try {
            String account = UserInfoItemFactory.createUserInfoItem().getEmailPrefix();
            if(StringUtils.isBlank(account)){
                Result.fail(GeneralCodes.ParamError, "未找到登录人信息, request=" + request);
            }

            request.setOperator(account);
            Set<Long> notExistIds = couponGoodsRefreshService.getNotExistIds(request.getIds());
            couponGoodsRefreshService.handRefresh(request, notExistIds);

            return Result.success(new UpdateCouponGoodsResponse(notExistIds));
        } catch (Exception e) {
            log.error("CouponConfigService.updateCouponGoods request={}, error: ", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 查询预算池列表
     *
     * @param
     * @return
     */
    @Override
    public Result<PageInfo<BudgetInfoDto>> queryBudgetList(CouponQueryBudgetListRequest request) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            Pair<Boolean, String> checkParam = request.checkParam();
            if (!checkParam.getLeft()) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, checkParam.getRight());
            }
            PageInfo<BudgetInfoDto> info = queryBudgetListByBr(request);
            log.info("DubboCouponAdminServiceImpl.queryBudgetList is success, request is {}, response is {}", GsonUtil.toJson(request), GsonUtil.toJson(info));
            return Result.success(info);
        } catch (Exception e) {
            log.error("DubboCouponAdminServiceImpl.queryBudgetList request:{}. error:", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 查询预算池详情
     *
     * @param
     * @return
     */
    @Override
    public Result<BudgetInfoDto> queryBudgetDetail(BudgetInfoDto request) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            if(Objects.isNull(request) || StringUtils.isEmpty(request.getBudgetApplyNo()) || Objects.isNull(request.getLineNum())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "提交预算池信息不完整");
            }
            BudgetInfoDto response = brProxy.queryBudgetDetail(request.getBudgetApplyNo(), request.getLineNum());
            log.info("DubboCouponAdminServiceImpl.queryBudgetDetail is success, request is {}, response is {}", GsonUtil.toJson(request), GsonUtil.toJson(response));
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponAdminServiceImpl.queryBudgetDetail request:{}. error:", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 通过br查询预算池列表
     *
     * @param
     * @return
     */
    public PageInfo<BudgetInfoDto> queryBudgetListByBr(CouponQueryBudgetListRequest request) throws BizError {
        log.info("DubboCouponAdminServiceImpl.queryBudgetList begin, request = {}", request);
        try {
            BudgetApplyReq req = new BudgetApplyReq();
            req.setPageNum(request.getPageNum());
            req.setPageSize(20);
            req.setSystemSource(SystemSourceEnum.EQUITY_CENTER);
            req.setKeyword(request.getKeyword());
            List<BudgetApplyTypeInfo> budgetApplyTypeInfoList = convertBudgetApplyList(request.getCarItemIdList());
            req.setBudgetApplyTypeList(budgetApplyTypeInfoList);
            return brProxy.queryBudgetList(req);
        } catch (Exception e) {
            log.error("DubboCouponAdminServiceImpl.queryBudgetList error request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
    }

    private List<BudgetApplyTypeInfo> convertBudgetApplyList(List<Long> carItemIdList) {
        List<BudgetApplyTypeInfo> budgetApplyTypeInfoList = Lists.newArrayList();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(carItemIdList)) {
            BudgetApplyTypeInfo budgetApplyTypeInfo = new BudgetApplyTypeInfo();
            budgetApplyTypeInfo.setBudgetApplyType(BudgetApplyTypeEnum.EQUITY);
            budgetApplyTypeInfo.setFeeTypeList(Lists.newArrayList(EquityTypeCodeEnum.COUPON.getCode()));
            budgetApplyTypeInfoList.add(budgetApplyTypeInfo);
            return budgetApplyTypeInfoList;
        }

        carItemIdList.forEach((carItemId) -> {
            BudgetApplyTypeInfo budgetApplyTypeInfo = new BudgetApplyTypeInfo();
            budgetApplyTypeInfo.setBenefitCarModel(carItemId.toString());
            budgetApplyTypeInfo.setBudgetApplyType(BudgetApplyTypeEnum.EQUITY);
            budgetApplyTypeInfo.setFeeTypeList(Lists.newArrayList(EquityTypeCodeEnum.COUPON.getCode()));
            budgetApplyTypeInfoList.add(budgetApplyTypeInfo);
        });
        return budgetApplyTypeInfoList;
    }
}
