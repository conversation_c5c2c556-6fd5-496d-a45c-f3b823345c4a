package com.xiaomi.nr.coupon.admin.retail.application.dubbo.coupon;


import com.mi.framework.http.HttpClient;
import com.xiaomi.cnzone.maindataapi.model.OrgDto;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgBase;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgCategory;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.store.request.StoreListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.store.response.StoreListResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboStoreService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.excel.StoreIdListPO;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.rpc.StoreProxyService;
import com.xiaomi.nr.coupon.admin.util.CouponCollectionUtil;
import com.xiaomi.nr.coupon.admin.util.FileUtils;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
public class DubboStoreServiceImpl implements DubboStoreService{

    @Autowired
    private StoreProxyService storeProxyService;

    @Override
    public Result<StoreListResponse> queryListByStoreIds(StoreListRequest request) {
        log.info("DubboStoreService queryListByStoreIds request:{}",request);
        try {
            StoreListResponse response =new StoreListResponse();
            byte[] bytes = new HttpClient().get(request.getFileUrl()).executeAsFile();
            ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
            List<StoreIdListPO> listPOs = FileUtils.getExcelList(inputStream,StoreIdListPO.class,StoreIdListPO.headRowNumber);
            if(CollectionUtils.isNotEmpty(listPOs)){
                List<String> storeIds =  listPOs.stream().filter(x->{
                    return x != null && StringUtils.isNotBlank(x.getStoreId());
                }).map(x->x.getStoreId().trim()).distinct().collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(storeIds)){
                    List<OrgDto> storeList = storeProxyService.queryStoreList(storeIds);
                    Map<Integer,List<String>> storeIdMap = new HashMap<>();
                    List<String> existIds = new LinkedList<>();
                    if(CollectionUtils.isNotEmpty(storeList)){
                        storeList.stream().forEach(x->{
                            existIds.add(x.getOrgBase().getOrgId());

                            Integer channel = getChannel(x);
                            if(channel != null){
                                if(!storeIdMap.containsKey(channel)){
                                    storeIdMap.put(channel,new LinkedList<>());
                                }
                                storeIdMap.get(channel).add(x.getOrgBase().getOrgId());
                            }
                        });
                        response.setStoreIds(storeIdMap);
                    }

                    response.setNotExistIds(CouponCollectionUtil.removeAll(storeIds,existIds));
                }
            }
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboStoreService queryListByStoreIds error request:{}",request,e);
            return Result.fromException(e);
        }


    }

    /**
     * 获取门店所属管理渠道
     * @param orgDto
     * @return
     */
    private Integer getChannel(OrgDto orgDto){
        OrgBase orgBase =orgDto.getOrgBase();
        OrgCategory orgCategory = orgDto.getOrgCategory();
        if(orgBase!=null){
            // manage_channel = 1 米家
            if(orgBase.getManageChannel() == 1){
                //manage_type = 1 直营
                if(orgCategory.getManageType() == 1){
                    return UseChannelsEnum.DIRECTSALE_STORE.getValue();
                }
                //manage_type = 2，3，4，5, 35 专卖
                if (orgCategory.getManageType() == 2 || orgCategory.getManageType() == 3 || orgCategory.getManageType() == 4 ||
                        orgCategory.getManageType() == 5 || orgCategory.getManageType() == 35) {
                    return UseChannelsEnum.EXCLUSIVE_SHOP.getValue();
                }
            }
            // manage_channel = 5 授权
            if(orgBase.getManageChannel() == 5){
                return UseChannelsEnum.AUTHORIZED_STORE.getValue();
            }
            // manage_channel = 27 一商一议
            if(orgBase.getManageChannel() == 27 && orgCategory.getManageType() == 61){
                return UseChannelsEnum.FORTRESS_STORE.getValue();
            }
        }
        return null;
    }

}
