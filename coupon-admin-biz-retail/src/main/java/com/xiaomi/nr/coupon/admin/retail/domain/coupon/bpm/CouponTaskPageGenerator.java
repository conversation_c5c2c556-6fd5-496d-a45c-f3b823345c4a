package com.xiaomi.nr.coupon.admin.retail.domain.coupon.bpm;

import com.xiaomi.newretail.bpm.api.model.dto.ProcessCreateDTO;
import com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponreview.entity.CouponTaskReviewContext;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.enums.BpmPageEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.CouponReviewRelRepository;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponReviewRelPO;
import com.xiaomi.nr.coupon.admin.retail.infrastructure.repository.mysqldao.review.po.CouponTaskReviewPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.CouponConfigRedisDao;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.redisdao.couponconfig.po.ConfigInfoCachePo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.MapUtils;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 商品券灌券任务提交审核逻辑
 */
@Slf4j
@Component
public class CouponTaskPageGenerator extends BpmPageGenerator<CouponTaskReviewContext>{

    @Value("${bpm.task.fillTaskRetailKey}")
    private String definitionKey;

    @Autowired
    private CouponConfigRedisDao couponConfigRedisDao;

    @Autowired
    private CouponReviewRelRepository couponReviewRelRepository;

    @Override
    public ProcessCreateDTO createRequest(CouponTaskReviewContext reviewContext) throws Exception {

        CouponTaskReviewPO reviewPO = reviewContext.getReviewPO();
        ProcessCreateDTO processCreateDTO = new ProcessCreateDTO();
        processCreateDTO.setKey(definitionKey);

        processCreateDTO.setName(reviewPO.getTaskName());
        processCreateDTO.setRequestId("coupon_task" + UUID.randomUUID().toString().replaceAll("-", "").toUpperCase());
        processCreateDTO.setCreator(reviewPO.getCreator());
        processCreateDTO.setExtra(getExtInfo(reviewPO.getCreator()));

        String htmlMap = this.convertMapJson(reviewContext);
        processCreateDTO.setHtml(htmlMap);

        return processCreateDTO;
    }


    @Override
    public Map<String, Object> convertMap(CouponTaskReviewContext reviewContext){
        CouponTaskReviewPO reviewPO = reviewContext.getReviewPO();

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("name", reviewPO.getTaskName());
        dataMap.put("configId", reviewPO.getConfigId().toString());
        dataMap.put("couponName", reviewPO.getConfigName());

        ConfigInfoCachePo configInfoCachePo = reviewContext.getConfigInfoCachePo();
        PromotionTypeEnum promotionTypeEnum = PromotionTypeEnum.getByValue(configInfoCachePo.getPromotionType());
        dataMap.put("promotionText", promotionTypeEnum.getName());
        dataMap.put("bottomText", PromotionTypeEnum.getDesc(promotionTypeEnum, configInfoCachePo.getPromotionValue(),
                BottomTypeEnum.getByValue(configInfoCachePo.getBottomType()), configInfoCachePo.getBottomCount(), configInfoCachePo.getBottomPrice(), configInfoCachePo.getMaxReduce()));
        dataMap.put("sendSceneText", reviewContext.getSendSceneText());
        dataMap.put("planCount", reviewPO.getPlanCount().toString());
        dataMap.put("userCount", String.valueOf(reviewContext.getUserGroupSize()));

        return dataMap;
    }

    @Override
    public BpmPageEnum getType() {
        return BpmPageEnum.TASK_COUPON;
    }

    @Override
    public String convertMapJson(CouponTaskReviewContext reviewContext){
        CouponTaskReviewPO reviewPO = reviewContext.getReviewPO();

        Map<String, Object> dataMap = new LinkedHashMap<>();
        dataMap.put("灌券任务名称", reviewPO.getTaskName());
        dataMap.put("优惠券ID", reviewPO.getConfigId().toString());
        dataMap.put("优惠券名称", reviewPO.getConfigName());

        ConfigInfoCachePo configInfoCachePo = reviewContext.getConfigInfoCachePo();
        PromotionTypeEnum promotionTypeEnum = PromotionTypeEnum.getByValue(configInfoCachePo.getPromotionType());
        dataMap.put("折扣类型", promotionTypeEnum.getName());
        dataMap.put("优惠规则", PromotionTypeEnum.getDesc(promotionTypeEnum, configInfoCachePo.getPromotionValue(),
                BottomTypeEnum.getByValue(configInfoCachePo.getBottomType()), configInfoCachePo.getBottomCount(), configInfoCachePo.getBottomPrice(), configInfoCachePo.getMaxReduce()));
        dataMap.put("投放场景", reviewContext.getSendSceneText());
        dataMap.put("计划发放总数", reviewPO.getPlanCount().toString());
        dataMap.put("计划灌券人数", String.valueOf(reviewContext.getUserGroupSize()));


        Map<String, Object> htmlJson = new HashMap<>();
        htmlJson.put("type", "table");
        htmlJson.put("tableName", "重要信息");
        List<Map<String,String>> columnList = new LinkedList<>();

        Map<String, String> map1 = new HashMap<>();
        map1.put("key", "id");
        map1.put("show", "配置项");

        Map<String, String> map2 = new HashMap<>();
        map2.put("key", "name");
        map2.put("show", "配置信息");

        columnList.add(map1);
        columnList.add(map2);

        htmlJson.put("column", columnList);

        htmlJson.put("data", MapUtils.splitMap("id", "name",dataMap));

        return GsonUtil.toJson(htmlJson);

    }


    private Map<String, Object> getExtInfo(String creator) throws BizError {

        CouponReviewRelPO relPO = couponReviewRelRepository.selectByCreator(creator);
        if (relPO == null || StringUtils.isBlank(relPO.getReviewGroup())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "未找到审批人一审组信息");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("group", relPO.getReviewGroup());
        map.put("apportion", true);
        map.put("amount_flag", true);

        return map;
    }
}
