package com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponconfig;

import com.xiaomi.newretail.bpm.api.model.dto.ProcessCreateDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.bpm.BpmPageGenerator;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponreview.CouponReviewRelService;
import com.xiaomi.nr.coupon.admin.domain.coupon.goods.GoodsService;
import com.xiaomi.nr.coupon.admin.enums.BpmPageEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsDepartmentEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponConfigReviewPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponReviewRelPO;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.goods.GmsProxyService;
import com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponconfig.bpm.dto.CouponBpmExtRequestDTO;
import com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponconfig.bpm.dto.CouponDepDTO;
import com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponconfig.bpm.dto.CouponProductDTO;
import com.xiaomi.nr.coupon.admin.util.*;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PostfreeCouponPageGenerator extends BpmPageGenerator<CouponConfigReviewPO> {

    @Value("${bpm.coupon.newRetailKey}")
    private String newRetailKey;

    @Value("${postCoupon.review.url}")
    private String postCouponReviewUrl;

    @Autowired
    private CouponReviewRelService couponReviewRelService;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private GmsProxyService gmsProxyService;

    @Override
    public ProcessCreateDTO createRequest(CouponConfigReviewPO reviewPO) throws Exception {
        CouponConfigVO couponConfigVO = GsonUtil.fromJson(CompressUtil.decompress(reviewPO.getConfigCompress()), CouponConfigVO.class);

        ProcessCreateDTO processCreateDTO = new ProcessCreateDTO();
        processCreateDTO.setKey(newRetailKey);

        processCreateDTO.setName(couponConfigVO.getName());
        processCreateDTO.setRequestId("coupon_" + UUID.randomUUID().toString().replaceAll("-", "").toUpperCase());
        processCreateDTO.setCreator(reviewPO.getCreator());

        CouponBpmExtRequestDTO couponBpmExtRequestDTO = getExtInfo(couponConfigVO, reviewPO.getCreator());
        processCreateDTO.setExtra(MapUtils.objectToMap(couponBpmExtRequestDTO));

        Map<String, Object> htmlMap = this.convertMap(reviewPO);
        if (couponConfigVO.getId() > 0) {
            this.addOriMap(htmlMap, reviewPO);
        }
        processCreateDTO.setHtml(this.renderWebContent(htmlMap));

        return processCreateDTO;
    }

    private CouponBpmExtRequestDTO getExtInfo(CouponConfigVO couponConfigVO, String creator) throws Exception {
        CouponBpmExtRequestDTO couponBpmExtRequestDTO = new CouponBpmExtRequestDTO();
        CouponReviewRelPO relPO = couponReviewRelService.selectByCreator(creator);
        if (relPO == null || StringUtils.isBlank(relPO.getReviewGroup())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "未找到审批人一审组信息");
        }
        couponBpmExtRequestDTO.setGroup(relPO.getReviewGroup());
        couponBpmExtRequestDTO.setAmount_flag(BigDecimal.valueOf(10000000).compareTo(couponConfigVO.getCost()) < 0);

        CouponProductDTO couponProductDTO = getCouponProductDTO(couponConfigVO);
        couponBpmExtRequestDTO.setProductGroup(MapUtils.objectToMap(couponProductDTO));

        CouponDepDTO couponDepDTO = getCouponDepDTO(couponConfigVO);
        if (couponDepDTO != null) {
            couponBpmExtRequestDTO.setDepartGroup(MapUtils.objectToMap(couponDepDTO));
        }

        return couponBpmExtRequestDTO;
    }

    private CouponDepDTO getCouponDepDTO(CouponConfigVO couponConfigVO) throws BizError {
        List<CostShareEnum> shareDepartments = couponConfigVO.getCostShare().keySet().stream().filter(x -> StringUtils.isNotBlank(CostShareEnum.getByValue(x).getBpmValue()))
                .map(x -> CostShareEnum.getByValue(x)).collect(Collectors.toList());
        Map<Integer, UseChannelVO> useChannel = couponConfigVO.getUseChannel();
        if (CollectionUtils.isNotEmpty(shareDepartments)) {
            CouponDepDTO couponDepDTO = new CouponDepDTO();
            for (CostShareEnum shareDepartment : shareDepartments) {
                switch (shareDepartment) {
                    case MARKETING_DEPARTMENT:
                        couponDepDTO.setMarket(true);
                        break;
                    case SERVICE_DEPARTMENT:
                        couponDepDTO.setService(true);
                        break;
                    case TIANXINGSHUKE:
                        if (useChannel.containsKey(UseChannelsEnum.XIAOMI_SHOP.getValue())) {
                            couponDepDTO.setTianxingshuke_shangcheng(true);
                        } else {
                            throw ExceptionHelper.create(GeneralCodes.ParamError, "天星数科分摊必须选小米商城渠道");
                        }
                        break;
                    case OFFLINE_SUPERMARKETS:
                        if (useChannel.containsKey(UseChannelsEnum.DIRECTSALE_STORE.getValue())) {
                            couponDepDTO.setXianxia_zhiying(true);
                        }
                        if (useChannel.containsKey(UseChannelsEnum.AUTHORIZED_STORE.getValue())) {
                            couponDepDTO.setXianxia_shouquan(true);
                        }
                        if (useChannel.containsKey(UseChannelsEnum.EXCLUSIVE_SHOP.getValue())) {
                            couponDepDTO.setXianxia_zhuanmai(true);
                        }
                        if (useChannel.containsKey(UseChannelsEnum.FORTRESS_STORE.getValue())) {
                            couponDepDTO.setXianxia_baolei(true);
                        }
                        if (useChannel.containsKey(UseChannelsEnum.XIAOMI_SHOP.getValue()) && useChannel.size() == 1) {
                            throw ExceptionHelper.create(GeneralCodes.ParamError, "线下商超分摊渠道不能只选小米商城渠道");
                        }
                        break;
                    case INSURANCE_COMPANY:
                        if (useChannel.containsKey(UseChannelsEnum.XIAOMI_SHOP.getValue())) {
                            couponDepDTO.setBaoxian_shangcheng(true);
                        } else {
                            throw ExceptionHelper.create(GeneralCodes.ParamError, "保险公司分摊必须选小米商城渠道");
                        }
                        break;
                    case SHANHUISHOU:
                        if (useChannel.containsKey(UseChannelsEnum.XIAOMI_SHOP.getValue())) {
                            couponDepDTO.setShanhuishou_shangcheng(true);
                        } else {
                            throw ExceptionHelper.create(GeneralCodes.ParamError, "闪回收分摊必须选小米商城渠道");
                        }
                        break;
                    case ZHUANZHUAN:
                        if (useChannel.containsKey(UseChannelsEnum.XIAOMI_SHOP.getValue())) {
                            couponDepDTO.setZhuanzhuan_shangcheng(true);
                        } else {
                            throw ExceptionHelper.create(GeneralCodes.ParamError, "转转分摊必须选小米商城渠道");
                        }
                        break;
                    case AIHUISHOU:
                        if (useChannel.containsKey(UseChannelsEnum.XIAOMI_SHOP.getValue())) {
                            couponDepDTO.setAihuishou_shangcheng(true);
                        } else {
                            throw ExceptionHelper.create(GeneralCodes.ParamError, "爱回收分摊必须选小米商城渠道");
                        }
                        break;
                    default:
                        break;
                }

            }

            return couponDepDTO;
        }
        return null;
    }

    private CouponProductDTO getCouponProductDTO(CouponConfigVO couponConfigVO) {
        CouponProductDTO couponProductDTO = new CouponProductDTO();
        List<Integer> goodsDepartments = couponConfigVO.getGoodsRuleVO().getGoodsDepartments();
        goodsDepartments.stream().filter(x -> GoodsDepartmentEnum.getByValue(x) != null).forEach(x -> {
            switch (GoodsDepartmentEnum.getByValue(x)) {
                case SALE_ONE:
                    couponProductDTO.setCoupon_sale1(true);
                    break;
                case SALE_TWO:
                    couponProductDTO.setCoupon_sale2(true);
                    break;
                case SALE_THREE:
                    couponProductDTO.setCoupon_sale3(true);
                    break;
                case INSURANCE:
                    couponProductDTO.setCoupon_insurance(true);
                    break;
                case INCREMENT:
                    couponProductDTO.setCoupon_value(true);
                    break;
                case GIFT:
                    couponProductDTO.setCoupon_gift(true);
                    break;
            }
        });
        return couponProductDTO;
    }

    @Override
    public Map<String, Object> convertMap(CouponConfigReviewPO reviewPO) throws Exception {
        CouponConfigVO couponConfigVO = GsonUtil.fromJson(CompressUtil.decompress(reviewPO.getConfigCompress()), CouponConfigVO.class);

        Map<String, Object> dataMap = new HashMap<>();

        dataMap.put("name", couponConfigVO.getName());
        dataMap.put("startFetchTime", TimeUtil.formatDate(couponConfigVO.getStartFetchTime()));
        dataMap.put("endFetchTime", TimeUtil.formatDate(couponConfigVO.getEndFetchTime()));

        Map<Integer, UseChannelVO> useChannelVOMap = couponConfigVO.getUseChannel();
        List<String> useChannelTexts = useChannelVOMap.keySet().stream().map(x -> UseChannelsEnum.getByValue(x).getName()).collect(Collectors.toList());
        dataMap.put("useChannelTexts", useChannelTexts);
        dataMap.put("useChannelTextStr", StringUtils.join(useChannelTexts, ","));

        UseTermVO useTermVO = couponConfigVO.getUseTermVO();
        dataMap.put("useTimeText", UseTimeTypeEnum.getDesc(UseTimeTypeEnum.getByValue(useTermVO.getUseTimeType()),
                UseTimeGranularityEnum.getByValue(useTermVO.getTimeGranularity()),useTermVO.getStartUseTime(),useTermVO.getEndUseTime(),useTermVO.getUseDuration()));
        dataMap.put("useTimeType", useTermVO.getUseTimeType());
        dataMap.put("useDuration", useTermVO.getUseDuration());
        dataMap.put("startUseTime", TimeUtil.formatDate(useTermVO.getStartUseTime()));
        dataMap.put("endUseTime", TimeUtil.formatDate(useTermVO.getEndUseTime()));

        PromotionRuleVO promotionRuleVO = couponConfigVO.getPromotionRuleVO();
        PromotionTypeEnum promotionTypeEnum = PromotionTypeEnum.getByValue(promotionRuleVO.getPromotionType());
        dataMap.put("promotionText", promotionTypeEnum.getName());
        dataMap.put("bottomText", PromotionTypeEnum.getDesc(promotionTypeEnum, promotionRuleVO.getPromotionValue(),
                BottomTypeEnum.getByValue(promotionRuleVO.getBottomType()), promotionRuleVO.getBottomCount(), promotionRuleVO.getBottomPrice(), promotionRuleVO.getMaxReduce()));
        dataMap.put("promotionValue",PromotionTypeEnum.getPromotionValue(PromotionTypeEnum.getByValue(promotionRuleVO.getPromotionType()),promotionRuleVO.getPromotionValue()));


        dataMap.put("applyCount", couponConfigVO.getDistributionRuleVO().getApplyCount());
        CouponScenePO couponScenePO = couponSceneRepository.selectBySceneCode(couponConfigVO.getSendScene());
        dataMap.put("sendSceneText", couponScenePO.getName());
        dataMap.put("costText", couponConfigVO.getCost());

        GoodsRuleVO goodsRuleVO = couponConfigVO.getGoodsRuleVO();
        GoodsDiscountLevelVO goodsDiscountLevelVO = goodsRuleVO.getGoodsDiscountLevelVO();
        if (goodsDiscountLevelVO != null) {
            dataMap.put("goodsNotifyInfo", goodsDiscountLevelVO);
        }
        dataMap.put("goodsSuitables", goodsRuleVO.getGoodsSuitableVOs());
        List<Long> packages = goodsRuleVO.getGoodsInclude().get(GoodsLevelEnum.Package.getValue());
        dataMap.put("packageAmount", CollectionUtils.isEmpty(packages) ? 0 : packages.size());

        CouponCostShareVO couponCostShareVO =new CouponCostShareVO();
        couponCostShareVO.setDepartmentText("小米之家");
        couponCostShareVO.setShare(100);
        dataMap.put("costShareTexts", Lists.newArrayList(couponCostShareVO));


        List<String> extPropTexts = Lists.newArrayList();
        ExtPropVO extProp = couponConfigVO.getExtProp();
        for (Field field : extProp.getClass().getDeclaredFields()){
            if(field.isSynthetic()){
                continue;
            }
            field.setAccessible(true);
            int value = (int)field.get(extProp);
            if(value == YesNoEnum.Yes.getMysqlValue()){
                extPropTexts.add(ExtPropEnum.getByField(field.getName()).getName());
            }
        }
        dataMap.put("extPropTexts", extPropTexts);
        dataMap.put("extPropStrTexts", StringUtils.join(extPropTexts,","));

        if (StringUtils.isNotBlank(reviewPO.getApplyAttachment())) {
            List<ApplyAttachmentVO> attachmentVOs = GsonUtil.fromListJson(reviewPO.getApplyAttachment(), ApplyAttachmentVO.class);
            for (ApplyAttachmentVO attachmentVO : attachmentVOs) {
                attachmentVO.setPicture(FileUtils.isPicture(attachmentVO.getUrl()));
                attachmentVO.setType(attachmentVO.getPicture() ? "img" : "file");
            }
            dataMap.put("applyAttachments", attachmentVOs);
        }
        dataMap.put("systemUrl", postCouponReviewUrl + reviewPO.getId());
        String fileName = "审核" + reviewPO.getId() + "商品";
        dataMap.put("downloadGoodsUrl", goodsService.getBpmGoodsFdsUrl(fileName));
        dataMap.put("couponTypeText", CouponTypeEnum.POSTFREE.getName());

        Map<String, List<Long>> goodsInclude = couponConfigVO.getGoodsRuleVO().getGoodsInclude();
        List<Long> skuList = goodsInclude.get(GoodsLevelEnum.Sku.getValue());
        List<Long> packageList = goodsInclude.get(GoodsLevelEnum.Package.getValue());
        dataMap.put("totalSku", skuList.size());
        dataMap.put("totalPackage", packageList.size());
        dataMap.put("totalProduct", packageList.size() + skuList.size());
        writeExcel(goodsRuleVO.getGoodsInclude(), fileName);
        return dataMap;
    }

    @Override
    public String convertMapJson(CouponConfigReviewPO couponConfigReviewPO) {
        return null;
    }


    @Async("asyncExecutor")
    public void writeExcel(Map<String, List<Long>> goodsInclude, String fileName) {
        for (int i = 1; i <= 3; i++) {
            try {
                goodsService.uploadGoodsExcelFile(goodsInclude, fileName);
                return;
            } catch (Exception e) {
                log.error("writeExcel i:{}", i, e);
            }
        }
    }

    private void addOriMap(Map<String, Object> map, CouponConfigReviewPO reviewPO) throws Exception {
        Map<String, Object> mapOrg = this.convertMap(reviewPO);
        mapOrg.forEach((k, v) -> {
            if (!Objects.equals(map.get(k), v)) {
                map.put(k + "_v2", v);
            }
        });
    }

    @Override
    public BpmPageEnum getType() {
        return BpmPageEnum.Postfree_Coupon;
    }

}
