package com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponreview.impl;

import com.google.gson.reflect.TypeToken;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GoodsRuleVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.PromotionRuleVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponUpdateReviewRequest;
import com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponreview.CouponConfigReviewService;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastruture.repository.mysql.coupon.config.po.GoodItemPO;
import com.xiaomi.nr.coupon.admin.util.CouponCollectionUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Service
public class CouponConfigReviewServiceImpl implements CouponConfigReviewService {

    @Autowired
    private CouponConfigRepository couponConfigRepository;



    @Override
    public boolean check(CouponUpdateReviewRequest reviewRequest) throws Exception {
        CouponConfigVO couponConfigVO = reviewRequest.getCouponConfigVO();
        if (couponConfigVO == null || couponConfigVO.getId() <= 0) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "券id不能为空");
        }


        boolean flag = true;
        CouponConfigPO couponPo = couponConfigRepository.searchCouponById(couponConfigVO.getId());

        if(!CouponCollectionUtil.equalsIntegerList(new LinkedList<>(couponConfigVO.getUseChannel().keySet()),StringUtil.convertToIntegerList(couponPo.getUseChannel()))){
            return flag;
        }

        if (couponPo.getApplyCount() != couponConfigVO.getDistributionRuleVO().getApplyCount()) {
            return flag;
        }

        PromotionRuleVO promotionRuleVO = couponConfigVO.getPromotionRuleVO();
        if (promotionRuleVO.getPromotionValue() != couponPo.getPromotionValue()) {
            return flag;
        }
        if (promotionRuleVO.getBottomType() != couponPo.getBottomType()) {
            return flag;
        }
        if (promotionRuleVO.getBottomCount() != couponPo.getBottomCount()) {
            return flag;
        }
        if (promotionRuleVO.getBottomPrice() != couponPo.getBottomPrice()) {
            return flag;
        }
        if (promotionRuleVO.getMaxReduce() != couponPo.getMaxReduce()) {
            return flag;
        }

        // 分摊方增加/删除、或变更分摊比例
        Map<Integer, Integer> costShareNew = couponConfigVO.getCostShare();
        Map<Integer, Integer> costShareOld = GsonUtil.fromMapJson(couponPo.getCostShare(),new TypeToken<Map<Integer, Integer>>() {}.getType());
        if(!costShareNew.equals(costShareOld)){
            return flag;
        }


        GoodsRuleVO goodsRuleVO = couponConfigVO.getGoodsRuleVO();

        if(!CouponCollectionUtil.equalsLongList(new LinkedList<>(goodsRuleVO.getCategoryIds()), StringUtil.convertToLongList(couponPo.getCategoryIds()))){
            return flag;
        }

        if(!CouponCollectionUtil.equalsIntegerList(goodsRuleVO.getGoodsDepartments(), StringUtil.convertToIntegerList(couponPo.getGoodsDepartments()))){
            return flag;
        }


        Map<String,List<Long>>  goodsIncludeNew= goodsRuleVO.getGoodsInclude();
        GoodItemPO goodItemPO = GsonUtil.fromJson(couponPo.getGoodsInclude(), GoodItemPO.class);

        List<Long> skuNew = goodsIncludeNew.get(GoodsLevelEnum.Sku.getValue());
        List<Long> skuOld =goodItemPO.getSku();
        if (!CouponCollectionUtil.equalsLongList(skuNew, skuOld)) {
            return flag;
        }

        List<Long> packageNew = goodsIncludeNew.get(GoodsLevelEnum.Package.getValue());
        List<Long> packageOld =goodItemPO.getPackages();
        if (!CouponCollectionUtil.equalsLongList(packageNew, packageOld)){
            return flag;
        }

        // 新套装
        List<Long> suitNew = goodsIncludeNew.get(GoodsLevelEnum.Suit.getValue());
        List<Long> suitOld = goodItemPO.getSuit();
        if (!CouponCollectionUtil.equalsLongList(suitNew, suitOld)) {
            return flag;
        }

        List<Long> ssuNew = goodsIncludeNew.get(GoodsLevelEnum.Ssu.getValue());
        List<Long> ssuOld = goodItemPO.getSsu();
        if (!CouponCollectionUtil.equalsLongList(ssuNew, ssuOld)){
            return flag;
        }


        Map<String, Integer> extPropOld = GsonUtil.fromMapJson(couponPo.getExtProp(),new TypeToken<Map<String, Integer>>() {}.getType());
        Map<String, Integer> extPropNew = GsonUtil.fromMapJson(GsonUtil.toJson(couponConfigVO.getExtProp()),new TypeToken<Map<String, Integer>>() {}.getType());
        if(!extPropOld.equals(extPropNew)){
            return flag;
        }

        // 领取开始、结束领取时间
        if (couponConfigVO.getStartFetchTime().getTime() / 1000 != couponPo.getStartFetchTime()
                || couponConfigVO.getEndFetchTime().getTime() / 1000 != couponPo.getEndFetchTime()) {
            return flag;
        }

        return false;
    }



}
